{"name": "monitora", "version": "0.0.1", "private": true, "license": "UNLICENSED", "scripts": {"analyze": "ANALYZE=both pnpm build", "analyze:browser": "ANALYZE=browser pnpm build", "analyze:server": "ANALYZE=server pnpm build", "assets": "sh scripts/generate_statics.sh", "build": "node scripts/build.js", "build:ui": "storybook build", "debug:dev": "NODE_OPTIONS='--inspect' next dev", "dev": "next dev", "dev:ui": "storybook dev -p 6006", "extract:500": "node scripts/extract-500-page.js", "fmt": "prettier --ignore-unknown --write", "lingui": "lingui extract --clean", "lint": "eslint .", "lint:css": "stylelint .", "local": "pnpm build && pnpm start", "local:ui": "pnpm build:ui && pnpm start:ui", "mitmproxy": "mitmproxy --mode reverse:http://localhost:3000", "prepare": "husky", "start": "node scripts/start.js", "start:ui": "node server.ui.js", "test": "jest", "test:db:check": "scripts/integration-tests/check.sh", "test:db:stop": "scripts/integration-tests/stop.sh", "test:e2e": "NODE_ENV=test playwright test", "test:e2e:dev": "NODE_ENV=test playwright test --ui", "update:icons": "scripts/update_material_symbols.sh"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.6.0", "@atlaskit/pragmatic-drag-and-drop-auto-scroll": "^2.1.0", "@atlaskit/pragmatic-drag-and-drop-flourish": "^2.0.2", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "@emotion/is-prop-valid": "^1.3.1", "@floating-ui/react": "^0.25.3", "@lingui/core": "^5.3.1", "@lingui/react": "^5.3.1", "@mui/material": "^6.4.1", "@sentry/nextjs": "^8.53.0", "@tiptap/core": "^2.11.5", "@tiptap/extension-bubble-menu": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-dropcursor": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "axios": "^1.8.2", "canvas-size": "^2.0.0", "canvas-toBlob": "^1.0.0", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "color": "^3.1.2", "colorthief": "^2.6.0", "content-disposition": "^0.5.3", "cookies-next": "^6.0.0", "copy-html-to-clipboard": "^4.0.1", "date-fns": "^2.28.0", "e2e": "link:./e2e", "express": "^4.21.2", "file-saver": "^2.0.0", "final-form": "^4.20.9", "final-form-arrays": "^3.1.0", "final-form-focus": "^1.1.2", "fuzzy": "^0.1.3", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "html2canvas": "^1.4.1", "http-proxy-middleware": "^3.0.3", "immutable": "^3.8.2", "intl": "^1.2.5", "is-hotkey": "^0.1.1", "is-localhost-ip": "^2.0.0", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "material-ui-popup-state": "^5.3.3", "mobx": "^6.13.6", "mobx-react": "^9.2.0", "mobx-state-tree": "^7.0.2", "nanoid": "^3.1.31", "next": "15.3.2", "p-progress": "^0.5.1", "particles.js": "^2.0.0", "pretty-bytes": "^5.6.0", "prop-types": "^15.7.2", "query-string": "^6.9.0", "rc-slider": "^11.1.1", "react": "19.1.0", "react-color": "^2.19.3", "react-content-loader": "^6.2.0", "react-day-picker": "^9.3.0", "react-dom": "19.1.0", "react-dropzone": "^14.2.3", "react-final-form": "^6.5.9", "react-final-form-arrays": "^3.1.4", "react-grid-layout": "^1.3.4", "react-h5-audio-player": "^3.8.6", "react-international-phone": "^4.5.0", "react-is": "19.1.0", "react-resizable-panels": "^2.1.7", "react-scroll": "^1.9.3", "react-textarea-autosize": "^8.5.7", "react-use": "^17.6.0", "react-virtual": "^2.10.4", "react-youtube": "^10.1.0", "reconnecting-websocket": "^4.4.0", "request-ip": "^3.3.0", "server-only": "^0.0.1", "styled-components": "^6.1.14", "styled-normalize": "^8.1.1", "tiny-invariant": "^1.3.3", "updeep": "^1.2.0", "~": "link:./src"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@getgrit/cli": "0.1.0-alpha.1743007075", "@lingui/cli": "^5.3.1", "@lingui/format-po-gettext": "^5.3.1", "@lingui/loader": "^5.3.1", "@lingui/swc-plugin": "5.5.2", "@next/env": "15.3.2", "@octokit/core": "^5.2.1", "@playwright/test": "^1.48.0", "@storybook/addon-actions": "^8.6.8", "@storybook/addon-essentials": "^8.6.8", "@storybook/addon-interactions": "^8.6.8", "@storybook/addon-storysource": "^8.6.8", "@storybook/blocks": "^8.6.8", "@storybook/nextjs": "^8.6.8", "@storybook/react": "^8.6.8", "@storybook/test": "^8.6.8", "@svgr/webpack": "^8.1.0", "@swc/core": "1.11.24", "@swc/jest": "^0.2.38", "@testing-library/react": "^16.2.0", "@types/color": "^3.0.6", "@types/facebook-js-sdk": "^3.3.12", "@types/intercom-web": "^2.8.26", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "22.15.3", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vercel/nft": "^0.29.2", "autocannon": "^8.0.0", "chalk": "^4.1.2", "compression-webpack-plugin": "^11.1.0", "ctrf": "^0.0.10", "dotenv": "^16.4.7", "eslint": "^9.22.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.0.2", "eslint-import-resolver-typescript": "^3.8.4", "eslint-plugin-eslint-plugin": "^6.4.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-lingui": "^0.10.0", "eslint-plugin-mntr": "file:./scripts/eslint-plugin-mntr", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-storybook": "^0.11.6", "execa": "^5.1.1", "get-port": "^5.1.1", "globals": "^16.0.0", "globby": "11", "husky": "^9.1.7", "is-docker": "2", "jest": "^29.4.3", "jest-environment-jsdom": "^29.7.0", "jest-styled-components": "^7.2.0", "jscodeshift": "^17.1.2", "lint-staged": "^15.4.3", "nextjs-bundle-analysis": "^0.5.0", "nyc": "^17.1.0", "p-timeout": "4.1.0", "playwright": "^1.48.0", "playwright-ctrf-json-reporter": "^0.0.18", "postcss": "^8.5.3", "postcss-styled-syntax": "^0.7.1", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.10", "prettier-plugin-sh": "^0.15.0", "prettier-plugin-sort-json": "^4.1.1", "require-from-string": "^2.0.2", "rimraf": "6", "shell-quote": "^1.8.2", "storybook": "^8.6.8", "stylelint": "^15.11.0", "stylelint-config-recommended": "^11.0.0", "stylelint-config-styled-components": "^0.1.1", "svgo": "^3.3.2", "swc-plugin-coverage-instrument": "0.0.27", "typescript": "^5.7.3", "typescript-eslint": "^8.26.1", "webpack-bundle-analyzer": "^4.10.2"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912", "engines": {"node": ">=22"}, "pnpm": {"overrides": {"@types/react": "19.1.3", "@types/react-dom": "19.1.3", "elliptic": ">=6.6.1", "use-composed-ref": "1.4.0", "use-isomorphic-layout-effect": "1.2.0", "use-latest": "1.3.0"}, "patchedDependencies": {"playwright": "patches/playwright.patch"}}, "nextBundleAnalysis": {"budget": null, "budgetPercentIncreaseRed": 20, "buildOutputDirectory": ".next/standalone/.next", "minimumChangeThreshold": 0, "showDetails": true}}