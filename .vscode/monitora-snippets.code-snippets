{
  "GHA - Debug": {
    "body": [
      "- name: Debug w/ tmate session",
      "  if: failure()",
      "  uses: mxschmitt/action-tmate@v3",
    ],
    "prefix": "ghadebug",
  },
  "MST - Model": {
    "body": [
      "import { types } from 'mobx-state-tree'",
      "",
      "const ${TM_FILENAME_BASE} = types.model('${TM_FILENAME_BASE}', {",
      "\tid: types.identifierNumber,",
      "\ttext: types.string,",
      "})",
      "",
      "export default ${TM_FILENAME_BASE}",
      "",
    ],
    "prefix": "mstmodel",
  },
  "MST - Stateless Component": {
    "body": [
      "import { observer } from '~/helpers/mst'",
      "",
      "const ${TM_FILENAME_BASE} = ({ appStore: { $1 }) => {",
      "\treturn ($2)",
      "}",
      "export default observer(${TM_FILENAME_BASE})",
    ],
    "prefix": "mstslc",
  },
  "Print to console": {
    "body": ["console.log($1)"],
    "description": "Log output to console",
    "prefix": "cl",
  },
  "Stateless Component": {
    "body": [
      "const ${TM_FILENAME_BASE} = () => {",
      "\treturn ($1)",
      "}",
      "export default ${TM_FILENAME_BASE}",
    ],
    "prefix": "slc",
  },
  "Storybook - Story": {
    "body": [
      "import type { Meta, StoryObj } from '@storybook/react'",
      "import ${TM_FILENAME_BASE} from './${TM_FILENAME_BASE}'",
      "",
      "const meta = {",
      "\ttitle: '${TM_FILENAME_BASE}',",
      "\tcomponent: ${TM_FILENAME_BASE},",
      "} satisfies Meta<typeof ${TM_FILENAME_BASE}>",
      "",
      "export default meta",
      "",
      "type Story = StoryObj<typeof meta>",
      "",
      "export const Default: Story = {",
      "\targs: {},",
      "}",
    ],
    "prefix": "sbs",
  },
}
