// https://nextjs.org/docs/app/guides/debugging#debugging-with-vs-code
{
  "configurations": [
    {
      "command": "pnpm dev",
      "name": "Next.js: debug server-side",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "name": "Next.js: debug client-side",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:3000"
    },
    {
      "name": "Next.js: debug client-side (Firefox)",
      "pathMappings": [
        {
          "path": "${workspaceFolder}",
          "url": "webpack://_N_E"
        }
      ],
      "reAttach": true,
      "request": "launch",
      "type": "firefox",
      "url": "http://localhost:3000"
    },
    {
      "name": "Next.js: debug full stack",
      "program": "${workspaceFolder}/node_modules/next/dist/bin/next",
      "request": "launch",
      "runtimeArgs": ["--inspect"],
      "serverReadyAction": {
        "action": "debugWithEdge",
        "killOnServerStop": true,
        "pattern": "- Local:.+(https?://.+)",
        "uriFormat": "%s",
        "webRoot": "${workspaceFolder}"
      },
      "skipFiles": ["<node_internals>/**"],
      "type": "node"
    }
  ],
  "version": "0.2.0"
}
