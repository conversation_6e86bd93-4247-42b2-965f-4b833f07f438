import nextEnv from '@next/env'
import type { StorybookConfig } from '@storybook/nextjs'

// TODO: Update in ESM (https://github.com/monitora-media/monitora-frontend/issues/2576)
// https://github.com/vercel/next.js/issues/68091
if (nextEnv && 'loadEnvConfig' in nextEnv) {
  nextEnv.loadEnvConfig(process.cwd())
} else {
  require('@next/env').loadEnvConfig(process.cwd())
}

let STORYBOOK_USER_TOKEN: string

const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-essentials',
    '@chromatic-com/storybook',
    '@storybook/addon-interactions',
    '@storybook/addon-storysource',
  ],
  framework: {
    name: '@storybook/nextjs',
    options: {},
  },
  staticDirs: [
    '../public',
    {
      from: '../src/assets',
      to: 'src/assets',
    },
  ],
  async env(config) {
    if (!STORYBOOK_USER_TOKEN) {
      const { token } = await fetch(`${process.env.API_URL}/accounts/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: process.env.STORYBOOK_USERNAME,
          password: process.env.STORYBOOK_PASSWORD,
        }),
      }).then((res) => res.json())

      STORYBOOK_USER_TOKEN = token
    }

    return {
      ...config,
      STORYBOOK_USER_TOKEN,
    }
  },
  async webpackFinal(config) {
    config.module = config.module || {}
    config.module.rules = config.module.rules || []

    // @ts-expect-error: Webpack types: `rule: false | "" | 0 | RuleSetRule | "..." | null | undefined` :D
    // 1. https://storybook.js.org/docs/get-started/frameworks/nextjs#custom-webpack-config
    // 2. https://react-svgr.com/docs/next/#usage
    const imageRule = config.module.rules.find((rule) => rule.test?.test?.('.svg'))
    if (imageRule) {
      // @ts-expect-error: ditto
      imageRule['exclude'] = /\.svg$/
    }

    config.module.rules.push(
      {
        test: /\.po$/,
        use: ['@lingui/loader'],
      },
      {
        test: /\.svg$/,
        use: ['@svgr/webpack'],
      },
    )

    return config
  },
}

export default config
