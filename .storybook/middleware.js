// This middleware handles proxying BE API requests when developing in Storybook
// via `pnpm dev:ui`, like we do in the app middleware. Must remain as JS.

import { createProxyMiddleware } from 'http-proxy-middleware'

const apiUrl = new URL(process.env.API_URL)

export default function expressMiddleware(router) {
  router.use(
    '/api-proxy',
    createProxyMiddleware({
      target: apiUrl.href,
      changeOrigin: true,
    }),
  )
}
