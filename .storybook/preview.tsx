import '~/styles/material-symbols.css'

import type { Preview } from '@storybook/react'
import MntrProviders from '~/components/misc/MntrProviders/MntrProviders'
import { sites } from '~/config'
import { initStore } from '~/store/create'

const preview: Preview = {
  decorators: [
    (Story, { loaded: { store } }) => {
      return (
        <MntrProviders store={store}>
          <Story />
        </MntrProviders>
      )
    },
  ],
  loaders: [
    async () => {
      const store = initStore({
        account: {
          token: process.env.STORYBOOK_USER_TOKEN,
        },
        appLanguage: 'cs',
        appSettings: sites[0],
        emailing: {
          campaign_detail: {
            result: {
              id: 746,
              can_delete: false,
              created_at: '',
              name: '',
              updated_at: '',
              mentioned_articles: {
                isLoaded: true,
                results: Array.from({ length: 4 }, (_, i) => ({
                  title: `Title of mentioned article ${i + 1}`,
                  published_at: '2025-01-21T10:49:05+01:00',
                  cover_image_url: 'ee',
                  visits: 22 * (i + 1),
                  unique_visits: 12 * (i + 1),
                })),
              },
            },
          },
        },
        filter: {},
        route: { asPath: '/', pathname: '/', query: {} },
      })

      await store.appStore.account.user.init()

      return {
        store,
      }
    },
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  tags: ['autodocs'],
}

export default preview
