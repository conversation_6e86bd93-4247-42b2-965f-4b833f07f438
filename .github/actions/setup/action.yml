name: Setup
description: 'Setup Node.js, restore caches, install'

runs:
  using: composite
  steps:
    - name: Get package.json
      run: |
        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "pkg<<${delimiter}" >> $GITHUB_OUTPUT
        cat package.json >> $GITHUB_OUTPUT
        echo "${delimiter}" >> $GITHUB_OUTPUT
      shell: bash
      id: get_pkg

    - name: Install pnpm
      uses: pnpm/action-setup@v4

    - uses: actions/setup-node@v4
      with:
        node-version: ${{ fromJson(steps.get_pkg.outputs.pkg).engines.node }}
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install
      shell: bash
