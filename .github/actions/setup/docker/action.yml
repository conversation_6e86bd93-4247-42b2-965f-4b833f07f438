name: Docker
description: 'Set up environment, log in to GHCR, Docker Build Cloud'

runs:
  using: composite
  steps:
    - name: List Git branches
      run: git branch -a
      shell: bash

    - name: Get fetch depth
      run: echo commit_count=$(git log --oneline | wc -l) >> $GITHUB_OUTPUT
      shell: bash
      id: get_fetch_depth

    - name: Get revisions
      if: |
        steps.get_fetch_depth.outputs.commit_count > 1 ||
        github.ref_name == github.event.repository.default_branch
      run: |
        read -r BASE_REV HEAD_REV <<< "$(
          scripts/set_commits.sh origin/${{ github.ref_name == 'master' && 'master' || github.base_ref || github.event.repository.default_branch }}
        )"

        echo "BASE_REV=$BASE_REV" >> $GITHUB_ENV
        echo "HEAD_REV=$HEAD_REV" >> $GITHUB_ENV
      shell: bash

    - name: Log in to GitHub Packages
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ env.GITHUB_TOKEN }}

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ env.DOCKER_BUILD_CLOUD_USER }}
        password: ${{ env.DOCKER_BUILD_CLOUD_PAT }}

    - # https://docs.docker.com/build/ci/github-actions/cache/#cache-mounts
      # https://github.com/reproducible-containers/buildkit-cache-dance
      # https://github.com/moby/buildkit/issues/1512#issuecomment-1319736671
      # (couldn't get the workarounds working for Yarn 😪)
      # https://github.com/moby/buildkit/issues/1512#issuecomment-1618573637
      # (workaround authors bailed...)
      # https://docs.docker.com/build-cloud/ci/
      uses: docker/setup-buildx-action@v3
      with:
        driver: cloud
        endpoint: mediaboard/frontend
        install: true

    - name: Docker meta
      uses: docker/metadata-action@v5
      with:
        tags: |
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value=production,enable=${{ github.ref_name == 'master' || github.event_name == 'workflow_dispatch' }}
          type=ref,event=branch,enable=${{ github.ref_name != github.event.repository.default_branch }}
          type=ref,event=pr
          type=sha,prefix=git-,format=long,enable=${{ github.ref_name == 'master' || github.event_name == 'workflow_dispatch' }}
        images: |
          ghcr.io/monitora-media/monitora-frontend/app
          ghcr.io/monitora-media/monitora-frontend/bundle-analyzer
          ghcr.io/monitora-media/monitora-frontend/e2e-proxy
          ghcr.io/monitora-media/monitora-frontend/e2e-runner
          ghcr.io/monitora-media/monitora-frontend/ui
      id: meta

    - name: Filter tags for images
      run: |
        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "APP_DOCKER_TAGS<<${delimiter}" >> $GITHUB_ENV
        echo "${{ steps.meta.outputs.tags }}" \
          | grep 'ghcr.io/monitora-media/monitora-frontend/app' \
            >> $GITHUB_ENV
        echo "${delimiter}" >> $GITHUB_ENV

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "BUNDLE_ANALYZER_DOCKER_TAGS<<${delimiter}" >> $GITHUB_ENV
        echo "${{ steps.meta.outputs.tags }}" \
          | grep 'ghcr.io/monitora-media/monitora-frontend/bundle-analyzer' \
            >> $GITHUB_ENV
        echo "${delimiter}" >> $GITHUB_ENV

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "E2E_PROXY_DOCKER_TAGS<<${delimiter}" >> $GITHUB_ENV
        echo "${{ steps.meta.outputs.tags }}" \
          | grep 'ghcr.io/monitora-media/monitora-frontend/e2e-proxy' \
            >> $GITHUB_ENV
        echo "${delimiter}" >> $GITHUB_ENV

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "E2E_RUNNER_DOCKER_TAGS<<${delimiter}" >> $GITHUB_ENV
        echo "${{ steps.meta.outputs.tags }}" \
          | grep 'ghcr.io/monitora-media/monitora-frontend/e2e-runner' \
            >> $GITHUB_ENV
        echo "${delimiter}" >> $GITHUB_ENV

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "UI_DOCKER_TAGS<<${delimiter}" >> $GITHUB_ENV
        echo "${{ steps.meta.outputs.tags }}" \
          | grep 'ghcr.io/monitora-media/monitora-frontend/ui' \
            >> $GITHUB_ENV
        echo "${delimiter}" >> $GITHUB_ENV
      shell: bash
