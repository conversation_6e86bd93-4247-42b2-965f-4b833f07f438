name: Build and push image
description: Build and push an image to GHCR

inputs:
  image:
    description: The image to build
    required: true
    default: apps
  push:
    description: Whether to push the image to GHCR
    required: false
    default: 'true'

runs:
  using: composite
  steps:
    - name: Fail fast if BASE_REV or HEAD_REV are not set
      if: |
        (inputs.image == 'app' || inputs.image == 'apps') &&
        (env.BASE_REV && env.HEAD_REV) == false
      run: |
        echo "BASE_REV=$BASE_REV"
        echo "HEAD_REV=$HEAD_REV"
        echo "BASE_REV or HEAD_REV not set, exiting..."
        exit 1
      shell: bash

    - uses: docker/build-push-action@v6
      if: inputs.image == 'app' || inputs.image == 'apps'
      with:
        context: .
        target: app
        build-args: |
          BASE_REV=${{ env.BASE_REV }}
          HEAD_REV=${{ env.HEAD_REV }}
          NEXT_PUBLIC_SENTRY_ENV=${{ env.DOCKER_METADATA_OUTPUT_VERSION == 'master' && 'production' || 'preview' }}
          SENTRY_AUTH_TOKEN=${{ env.SENTRY_AUTH_TOKEN }}
        push: ${{ inputs.push }}
        tags: ${{ env.APP_DOCKER_TAGS }}

    - uses: docker/build-push-action@v6
      if: inputs.image == 'bundle-analyzer'
      with:
        context: .
        target: bundle-analyzer
        push: ${{ inputs.push }}
        tags: ${{ env.BUNDLE_ANALYZER_DOCKER_TAGS }}

    - uses: docker/build-push-action@v6
      if: inputs.image == 'e2e-proxy'
      with:
        context: .
        target: e2e-proxy
        push: ${{ inputs.push }}
        tags: ${{ env.E2E_PROXY_DOCKER_TAGS }}

    - uses: docker/build-push-action@v6
      if: inputs.image == 'e2e-runner'
      with:
        context: .
        target: e2e-runner
        push: ${{ inputs.push }}
        tags: ${{ env.E2E_RUNNER_DOCKER_TAGS }}

    - uses: docker/build-push-action@v6
      if: inputs.image == 'ui' || inputs.image == 'apps'
      with:
        context: .
        target: ui
        build-args: |
          STORYBOOK_PASSWORD=${{ env.STORYBOOK_PASSWORD }}
        push: ${{ inputs.push }}
        tags: ${{ env.UI_DOCKER_TAGS }}
