name: Commit
description: 'Git add, commit'

inputs:
  add:
    description: 'Arguments for the `git add` command'
    required: false
    default: '.'
  commit:
    description: 'Arguments for the `git commit` command'
    required: true
  lint:
    description: 'Run lint-staged before commit'
    required: false
    default: true

runs:
  using: composite
  steps:
    - name: Git add
      if: |
        !contains(inputs.commit, '--allow-empty')
      run: git add ${{ inputs.add }}
      shell: bash

    - name: Lint staged
      if: inputs.lint == 'true'
      run: pnpm lint-staged --allow-empty -q
      shell: bash

    - name: Get working directory changes
      run: |
        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "changes<<${delimiter}" >> $GITHUB_OUTPUT
        git status --porcelain --untracked-files=no >> $GITHUB_OUTPUT
        echo "${delimiter}" >> $GITHUB_OUTPUT
      shell: bash
      id: git_status

    - name: Git config
      if: steps.git_status.outputs.changes != ''
      run: |
        git config user.name "github-actions"
        git config user.email <EMAIL>
      shell: bash

    - name: Git commit
      if: steps.git_status.outputs.changes != ''
      run: git commit --no-verify ${{ inputs.commit }}
      shell: bash
