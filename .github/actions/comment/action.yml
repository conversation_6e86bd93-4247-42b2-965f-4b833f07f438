name: Comment
description: 'Find, create or update comment'

inputs:
  body_includes:
    description: 'String for finding existing comment'
    required: true
  body:
    description: 'Comment body'
    required: true

runs:
  using: composite
  steps:
    - name: Find comment
      uses: peter-evans/find-comment@v3
      with:
        issue-number: ${{ github.event.pull_request.number }}
        body-includes: ${{ inputs.body_includes }}
      id: existing_comment

    - name: Create or update comment
      uses: peter-evans/create-or-update-comment@v4
      with:
        comment-id: ${{ steps.existing_comment.outputs.comment-id }}
        issue-number: ${{ github.event.pull_request.number }}
        body: ${{ inputs.body }}
        edit-mode: replace
