name: Codemod
description: 'Run codemod transforms, return applied transforms'

inputs:
  transforms:
    description: 'Transforms to run'
    required: true
  paths:
    description: 'Paths to run transforms on'
    required: true
    default: 'src/'

outputs:
  transforms:
    description: 'Transforms that were applied'
    value: ${{ steps.transforms.outputs.list }}

runs:
  using: composite
  steps:
    - name: Run transforms
      run: |
        transforms=(${{ inputs.transforms }})
        transforms_applied=()

        git diff > last.diff

        for t in "${transforms[@]}"; do
          echo "${{ inputs.paths }}" | (grep -Ev "\.ts(x)?$" || true) \
            | pnpm jscodeshift --parser=babel -t "$t" --stdin

          echo "${{ inputs.paths }}" | (grep "\.ts$" || true) \
            | pnpm jscodeshift --parser=ts -t "$t" --stdin

          echo "${{ inputs.paths }}" | (grep "\.tsx$" || true) \
            | pnpm jscodeshift --parser=tsx -t "$t" --stdin

          git ls-files -m | pnpm fmt

          if [[ "$(git diff)" != "$(cat last.diff)" ]]; then
            transforms_applied+=("$t")
          fi

          git diff > last.diff
        done

        git clean -f last.diff

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "list<<${delimiter}" >> $GITHUB_OUTPUT
        printf "%s\n" "${transforms_applied[@]}" >> $GITHUB_OUTPUT
        echo "${delimiter}" >> $GITHUB_OUTPUT
      shell: bash
      id: transforms
