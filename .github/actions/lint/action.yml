name: Lint
description: 'Run codemods, linters, commit changes'

runs:
  using: composite
  steps:
    - name: Get codemod transforms
      run: |
        transforms='
          scripts/codemod/transforms/absolute-imports.js
          scripts/codemod/transforms/jsx-return-statement.js
          scripts/codemod/transforms/modular-lodash.js
        '

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "list<<${delimiter}" >> $GITHUB_OUTPUT
        echo "$transforms" >> $GITHUB_OUTPUT
        echo "${delimiter}" >> $GITHUB_OUTPUT
      shell: bash
      id: get_transforms

    - name: Get changed files
      run: |
        files=$(
          git --no-pager diff --diff-filter=d --name-only -z origin/${{ github.base_ref }}... \
            | tr '\0' '\n'
        )

        js=$(echo "$files" | grep "\.\(js\|mjs\|ts\|tsx\)$" || true)

        if git log -1 --pretty=%B | grep -q "\[lint all\]"; then
          files=$(git ls-files -z | tr '\0' '\n')
          js=$(git ls-files "*.js" "*.mjs" "*.ts" "*.tsx")
        fi

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "files<<${delimiter}" >> $GITHUB_OUTPUT
        echo "$files" >> $GITHUB_OUTPUT
        echo "${delimiter}" >> $GITHUB_OUTPUT

        delimiter="$(cat /proc/sys/kernel/random/uuid)"

        echo "js<<${delimiter}" >> $GITHUB_OUTPUT
        echo "$js" >> $GITHUB_OUTPUT
        echo "${delimiter}" >> $GITHUB_OUTPUT
      shell: bash
      id: get_changed_files

    - name: Run codemods
      if: steps.get_changed_files.outputs.js != ''
      uses: ./.github/actions/codemod
      with:
        transforms: ${{ steps.get_transforms.outputs.list }}
        paths: ${{ steps.get_changed_files.outputs.js }}
      id: codemod

    - name: Commit changes
      if: steps.get_changed_files.outputs.js != ''
      uses: ./.github/actions/commit
      with:
        commit: '-m "refactor(app): Apply codemods" -m "${{ steps.codemod.outputs.transforms }}"'

    - name: ESLint
      if: steps.get_changed_files.outputs.js != ''
      run: pnpm eslint --fix --quiet $(paste -s -d ' ' - <<< "${{ steps.get_changed_files.outputs.js }}")
      shell: bash

    - name: Commit changes
      if: steps.get_changed_files.outputs.js != ''
      uses: ./.github/actions/commit
      with:
        commit: '-m "refactor(app): ESLint fixes"'

    - name: Stylelint
      if: steps.get_changed_files.outputs.js != ''
      run: pnpm stylelint --fix --quiet $(paste -s -d ' ' - <<< "${{ steps.get_changed_files.outputs.js }}")
      shell: bash

    - name: Commit changes
      if: steps.get_changed_files.outputs.js != ''
      uses: ./.github/actions/commit
      with:
        commit: '-m "refactor(app): Stylelint fixes"'

    - name: Prettier
      run: pnpm fmt $(paste -s -d ' ' - <<< "${{ steps.get_changed_files.outputs.files }}")
      shell: bash

    - name: Commit changes
      uses: ./.github/actions/commit
      with:
        commit: '-m "chore(app): Prettier formatting"'

    - name: Push changes
      run: git push
      shell: bash
