name: Next.js Bundle Analysis

on:
  workflow_call:
    inputs:
      ref:
        required: false
        default: ${{ github.ref }}
        type: string
  workflow_dispatch:

env:
  DOCKER_BUILD_CLOUD_PAT: ${{ secrets.DOCKER_BUILD_CLOUD_PAT }}
  DOCKER_BUILD_CLOUD_USER: ${{ vars.DOCKER_BUILD_CLOUD_USER }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - uses: ./.github/actions/setup/docker

      - run: docker compose pull app bundle-analyzer

      - name: Copy app build from container
        run: |
          docker compose up app -d
          mkdir -p .next/standalone
          docker compose cp app:/srv/.next .next/standalone
          docker compose cp app:/srv/server.js .next/standalone

      - name: Analyze bundle
        run: docker compose run --user "$(id -u):$(id -g)" bundle-analyzer report

      - name: Upload bundle stats
        uses: actions/upload-artifact@v4
        with:
          name: bundle
          path: .next/standalone/.next/analyze/__bundle_analysis.json

      - name: Download base branch bundle stats
        if: success() && github.event.number
        uses: dawidd6/action-download-artifact@v6
        with:
          workflow: main.yml
          branch: ${{ github.event.pull_request.base.ref }}
          name: bundle
          path: .next/standalone/.next/analyze/base/bundle

      - name: Compare with base branch bundle
        if: success() && github.event.number
        run: |
          ls -laR .next/standalone/.next/analyze/base \
            && docker compose run --user "$(id -u):$(id -g)" bundle-analyzer compare

      - name: Update summary
        if: success() && github.event.number
        run: cat .next/standalone/.next/analyze/__bundle_analysis_comment.txt >> $GITHUB_STEP_SUMMARY
