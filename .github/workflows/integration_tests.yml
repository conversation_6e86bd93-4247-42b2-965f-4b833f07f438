name: E2E Tests

on:
  schedule:
    # every day at 5:30 and 17:30 UTC+1
    - cron: '30 4,16 * * *'
  workflow_call:
    inputs:
      ref:
        required: true
        type: string
      sha:
        required: true
        type: string
      update_snapshots:
        required: false
        type: boolean
  workflow_dispatch:

env:
  DOCKER_BUILD_CLOUD_PAT: ${{ secrets.DOCKER_BUILD_CLOUD_PAT }}
  DOCKER_BUILD_CLOUD_USER: ${{ vars.DOCKER_BUILD_CLOUD_USER }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  prepare:
    runs-on: ubuntu-latest
    env:
      BACKEND_INTERVAL_SECS: 10
      BACKEND_TIMEOUT_SECS: 600
    permissions:
      actions: read
      contents: read
      id-token: write
      packages: write
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - uses: ./.github/actions/setup

      - uses: ./.github/actions/setup/docker

      - name: Build and push E2E proxy
        uses: ./.github/actions/setup/build
        with:
          image: e2e-proxy

      - name: Build and push E2E runner
        uses: ./.github/actions/setup/build
        with:
          image: e2e-runner

      - name: Prepare environment for user setup and tests
        run: |
          {
            echo API_URL=${{ vars.INTEGRATION_TESTS_API_URL }}
            echo E2E_PASSWORD="$(openssl rand -base64 12)"
            grep E2E_USERNAME .env.test
          } | tee -a $GITHUB_ENV .env.test.local

      - name: Download previous CTRF report
        uses: dawidd6/action-download-artifact@v6
        with:
          name: ctrf
          path: ctrf
          search_artifacts: true
          if_no_artifact_found: warn

      - name: Generate test matrix
        run: |
          ./scripts/generate-test-matrix.js ctrf/ctrf-report.json 12
          jq < matrix.json
          echo "matrix=$(cat matrix.json)" >> $GITHUB_OUTPUT

          shard_tests_eta=$(
            jq '(.shard | map(.durationEstimate // 0) | add) / (.shard | length) | round' < matrix.json \
              | awk '{printf "%dm %ds\n", ($1/(1000*60)%60), ($1/1000)%60}'
          )

          echo "ETA of tests in a shard: $shard_tests_eta" >> $GITHUB_STEP_SUMMARY
        id: generate_test_matrix

      - name: Tailscale
        uses: tailscale/github-action@v3
        with:
          oauth-client-id: ${{ secrets.TS_OAUTH_CLIENT_ID }}
          oauth-secret: ${{ secrets.TS_OAUTH_SECRET }}
          tags: tag:ci-frontend
          use-cache: 'true'

      - name: Prepare backend
        env:
          DB_SNAPSHOT_HOST: ${{ vars.DB_SNAPSHOT_HOST }}
          MEDIABOARD_BACKEND_REV: ${{ vars.MEDIABOARD_BACKEND_REV }}
          TZ: Europe/Prague
        run: |
          eval $(ssh-agent)
          ssh-add - <<< '${{ secrets.INTEGRATION_TESTS_PRIVATE_KEY }}'

          ./scripts/integration-tests/start.js
        id: prepare_backend

      - uses: actions/github-script@v6
        if: steps.prepare_backend.outputs.status == 'super_busy'
        with:
          script: |
            core.setFailed(`Backend is still busy, timing out after ${{ steps.prepare_backend.outputs.timeout }}.`)

      - name: Run non-hero user setup
        run: |
          docker compose --profile e2e pull
          ./docker compose run e2e --project='setup non-hero users'

      - name: Upload blob report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: blob-report-setup-non-hero-users
          path: blob-report
          retention-days: 1

      - name: Upload environment
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: environment
          include-hidden-files: true
          path: |
            .env.test.local
            e2e/.auth
            e2e/artifacts/params.env
          retention-days: 1
    outputs:
      locked: ${{ steps.prepare_backend.outputs.status == 'locked' }}
      matrix: ${{ steps.generate_test_matrix.outputs.matrix }}
      success: ${{ steps.prepare_backend.conclusion == 'success' }}

  run_tests:
    needs: prepare
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.prepare.outputs.matrix) }}
    name: ${{ matrix.shard.name }}
    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true
          ref: ${{ inputs.ref }}

      - uses: ./.github/actions/setup/docker

      - name: Download environment
        uses: actions/download-artifact@v4
        with:
          name: environment

      - name: Tailscale
        uses: tailscale/github-action@v3
        with:
          oauth-client-id: ${{ secrets.TS_OAUTH_CLIENT_ID }}
          oauth-secret: ${{ secrets.TS_OAUTH_SECRET }}
          tags: tag:ci-frontend
          use-cache: 'true'

      - name: Run E2E tests
        env:
          E2E_RUNNER_SHARD_NUMBER: ${{ matrix.shard.number }}
        run: |
          docker compose --profile e2e pull

          if [[ '${{ inputs.update_snapshots }}' == 'true' ]]; then
            ./docker compose run e2e \
              --grep @screenshot \
              --shard=${{ matrix.shard.number }}/${{ matrix.shardTotal }} \
              --update-snapshots
          else
            ./docker compose run e2e \
              ${{ matrix.shard.grep && format('--grep "{0}"', matrix.shard.grep) || format('--shard {0}/{1}', matrix.shard.number, matrix.shardTotal) }}
          fi
        id: e2e

      - name: Upload blob report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: blob-report-${{ matrix.shard.number }}
          path: blob-report
          retention-days: 1

      - name: Get snapshot changes
        if: always() && (steps.e2e.conclusion == 'failure' || steps.e2e.conclusion == 'success')
        run: |
          mktemp dummy.XXXXXX

          delimiter="$(cat /proc/sys/kernel/random/uuid)"

          echo "changes<<${delimiter}" >> $GITHUB_OUTPUT
          git ls-files --modified e2e/*.png >> $GITHUB_OUTPUT
          echo "${delimiter}" >> $GITHUB_OUTPUT
        id: get_snapshot_changes

      - name: Upload changed snapshots
        if: steps.get_snapshot_changes.outputs.changes != ''
        uses: actions/upload-artifact@v4
        with:
          name: changed_snapshots-${{ matrix.shard.number }}
          path: |
            ${{ steps.get_snapshot_changes.outputs.changes }}
            dummy.*
          retention-days: 1

      - name: Upload CTRF report
        if: success() && !inputs.update_snapshots
        uses: actions/upload-artifact@v4
        with:
          name: ctrf-report-${{ matrix.shard.number }}
          path: ctrf
          retention-days: 1
    # outputs: Don't rely on matrix job outputs
    # (https://github.com/community/community/discussions/17245)

  call_cleanup:
    needs: [prepare, run_tests]
    if: always() && needs.prepare.outputs.locked == 'true'
    uses: ./.github/workflows/integration_tests_cleanup.yml
    secrets: inherit
    with:
      ref: ${{ inputs.ref }}

  tests:
    needs: [prepare, run_tests]
    if: (failure() || success()) && needs.prepare.outputs.success == 'true'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - uses: ./.github/actions/setup

      - name: Download blob reports
        uses: actions/download-artifact@v4
        with:
          pattern: blob-report-*
          path: all-blob-reports
          merge-multiple: true

      - name: Merge into HTML report & report visual changes
        run: |
          pnpm playwright merge-reports \
            --reporter e2e/visual-changes-reporter.ts,html \
            ./all-blob-reports

      - name: Package & upload report
        run: |
          tar cf report.tar playwright-report

          echo report_url=$(
            curl \
              -F "id=${{ github.run_id }}-${{ github.run_attempt }}" \
              -F "report=@report.tar" \
              https://${{ secrets.PREVIEW_HTTP_AUTH }}@www.preview.monitora.cz/upload-report/
          ) >> $GITHUB_OUTPUT
        id: pw_report

      - name: Get visual changes
        run: |
          if [[ -f visual_changes.json ]]; then
            echo visual_changes=$(cat visual_changes.json) >> $GITHUB_OUTPUT
          fi
        id: get_visual_changes

      - name: Update summary
        run: |
          report_url='${{ steps.pw_report.outputs.report_url }}'

          echo '## 🎭 Playwright report' >> $GITHUB_STEP_SUMMARY
          echo '' >> $GITHUB_STEP_SUMMARY
          echo "$report_url" >> $GITHUB_STEP_SUMMARY
          echo '' >> $GITHUB_STEP_SUMMARY

          if [[ $(echo '${{ steps.get_visual_changes.outputs.visual_changes }}' | jq length) -gt 0 ]]; then
            echo '### 📸 Visual comparisons' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo '1. Review the report' >> $GITHUB_STEP_SUMMARY
            echo '2. Update screenshots [locally](https://github.com/monitora-media/monitora-frontend?tab=readme-ov-file#run-e2e-tests) or add the [`ci: update snapshots`](https://github.com/monitora-media/monitora-frontend/labels/ci%3A%20update%20snapshots) label to the PR' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo 'The following tests report visual changes:' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo '| Test | Change Type |' >> $GITHUB_STEP_SUMMARY
            echo '| - | - |' >> $GITHUB_STEP_SUMMARY

            echo '${{ steps.get_visual_changes.outputs.visual_changes }}' | jq -c '.[]' | while read -r t; do
              echo "| [$(echo "$t" | jq -r '.titlePath')]($report_url#\?$(echo "$t" | jq -r '.query')) | $(echo "$t" | jq -r '.changeType') |" >> $GITHUB_STEP_SUMMARY
            done
          else
            echo '### 📷 Visual comparisons' >> $GITHUB_STEP_SUMMARY
            echo '' >> $GITHUB_STEP_SUMMARY
            echo 'No visual changes detected.' >> $GITHUB_STEP_SUMMARY
          fi

      - name: Download changed snapshots
        uses: actions/download-artifact@v4
        with:
          pattern: changed_snapshots-*
          merge-multiple: true
        continue-on-error: true

      - name: Get snapshot changes
        run: |
          mktemp dummy.XXXXXX

          delimiter="$(cat /proc/sys/kernel/random/uuid)"

          echo "changes<<${delimiter}" >> $GITHUB_OUTPUT
          git ls-files --modified e2e/*.png >> $GITHUB_OUTPUT
          echo "${delimiter}" >> $GITHUB_OUTPUT
        id: get_snapshot_changes

      - name: Upload changed snapshots
        if: steps.get_snapshot_changes.outputs.changes != ''
        uses: actions/upload-artifact@v4
        with:
          name: changed_snapshots
          path: |
            ${{ steps.get_snapshot_changes.outputs.changes }}
            dummy.*
          retention-days: 1

      - name: Download CTRF reports
        uses: actions/download-artifact@v4
        with:
          pattern: ctrf-report-*
          path: all-ctrf-reports
          merge-multiple: true
        continue-on-error: true

      - name: Verify report count
        run: |
          mkdir -p all-ctrf-reports
          total_count=$(find ./all-ctrf-reports -maxdepth 1 -type f | wc -l | tr -d ' ')
          expected_count=$(echo '${{ needs.prepare.outputs.matrix }}' | jq '.shardTotal[0]')

          if [[ "$total_count" != "$expected_count" ]]; then
            echo "Expected $expected_count reports but found $total_count - skipping timing data update"
            echo "skip_timing_update=true" >> $GITHUB_OUTPUT
          fi
        id: verify_report_count

      - name: Merge CTRF reports
        if: steps.verify_report_count.outputs.skip_timing_update != 'true'
        run: pnpm ctrf merge ./all-ctrf-reports --output-dir ctrf

      - name: Upload CTRF report
        if: steps.verify_report_count.outputs.skip_timing_update != 'true'
        uses: actions/upload-artifact@v4
        with:
          name: ctrf
          path: ctrf/ctrf-report.json

      - name: Remove label
        if: success()
        run: |
          gh api --method DELETE \
            /repos/${{ github.repository }}/issues/${{ github.event.number }}/labels/ci%3A%20run
        continue-on-error: true
