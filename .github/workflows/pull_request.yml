name: Pull Request

on:
  pull_request:
    paths-ignore: # skip execution if file changes are only in these paths https://docs.github.com/en/actions/writing-workflows/workflow-syntax-for-github-actions#example-excluding-paths
      - 'docs/**'
      - README.md
    types:
      - labeled
      - opened
      - ready_for_review
      - reopened
      - synchronize

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  basics:
    if: |
      !startsWith(github.head_ref, 'dependabot/npm_and_yarn/') || github.actor != 'dependabot[bot]'
    runs-on: ubuntu-latest
    concurrency:
      group: docker-build-${{ github.head_ref == 'preview/dev' && github.event.pull_request.base.ref || github.event.pull_request.number }}
    env:
      DOCKER_BUILD_CLOUD_PAT: ${{ secrets.DOCKER_BUILD_CLOUD_PAT }}
      DOCKER_BUILD_CLOUD_USER: ${{ vars.DOCKER_BUILD_CLOUD_USER }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      STORYBOOK_PASSWORD: ${{ secrets.STORYBOOK_PASSWORD }}
    permissions:
      actions: write
      checks: read
      contents: write
      id-token: write
      packages: write
      pull-requests: write
    steps:
      - name: Generate token for mediaboard-frontend[bot]
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}
        id: get_bot_token

      - uses: actions/checkout@v4
        with:
          token: ${{ steps.get_bot_token.outputs.token }}
          ref: ${{ github.event.pull_request.head.ref }}
          fetch-depth: 0

      - name: Get current PR labels
        run: |
          echo labels=$(
            gh api \
              /repos/${{ github.repository }}/issues/${{ github.event.number }}/labels \
              --jq 'map(.name)'
          ) >> $GITHUB_OUTPUT
        id: get_current_labels

      - name: Rebase and push
        if: contains(steps.get_current_labels.outputs.labels, format('ci{0} rebase', ':'))
        run: |
          git config user.name "github-actions"
          git config user.email <EMAIL>
          git rebase origin/${{ github.base_ref }}
          git push --force

      - name: Remove label
        if: always()
        run: |
          gh api --method DELETE \
            /repos/${{ github.repository }}/issues/${{ github.event.number }}/labels/ci%3A%20rebase
        continue-on-error: true

      - uses: ./.github/actions/setup
        if: |
          github.actor == 'mediaboard-frontend[bot]' ||
          contains(steps.get_current_labels.outputs.labels, 'ci:') ||
          !github.event.pull_request.draft
        id: setup

      # Lint phase
      - name: Run custom tooling tests
        if: |
          steps.setup.conclusion != 'skipped' &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':'))
        run: pnpm test scripts/

      - uses: ./.github/actions/lint
        if: |
          steps.setup.conclusion != 'skipped' &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':')) &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} update snapshots', ':'))

      # Test phase (unit tests)
      - name: Run unit tests
        if: |
          steps.setup.conclusion != 'skipped' &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':'))
        run: pnpm test src/

      # Build phase
      - if: steps.setup.conclusion != 'skipped'
        uses: ./.github/actions/setup/docker

      - name: Build and push app & UI
        if: steps.setup.conclusion != 'skipped'
        uses: ./.github/actions/setup/build
        id: build

      # Test phase (integration tests)
      - name: Copy app build from container
        if: |
          steps.setup.conclusion != 'skipped' &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':'))
        run: |
          docker compose up --pull always app -d
          mkdir -p .next/standalone
          docker compose cp app:/srv/.next .next/standalone
          docker compose cp app:/srv/server.js .next/standalone

      - name: Run integration tests
        if: |
          steps.setup.conclusion != 'skipped' &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':'))
        run: pnpm test integration/

      # Deploy phase
      - name: Deploy to preview
        if: steps.build.conclusion != 'skipped'
        run: |
          json_payload=$(
            cat << EOF
          {
            "apps": ["app", "ui"],
            "ghcr_password": "${{ env.GITHUB_TOKEN }}",
            "ghcr_username": "${{ github.repository_owner }}",
            "github_pr_id": "${{ github.event.number }}",
            "github_ref": "${{ github.event.pull_request.head.ref }}",
            "github_run_id": "${{ github.run_id }}",
            "github_sha": "${{ github.event.pull_request.head.sha }}",
            "version": "${{ env.DOCKER_METADATA_OUTPUT_VERSION }}"
          }
          EOF
          )

          curl -X POST https://${{ secrets.PREVIEW_HTTP_AUTH }}@www.preview.monitora.cz/start-instance/ \
            -H 'Content-Type: application/json' \
            -d "$json_payload"
        id: deploy_preview

      # Conflicts phase
      - name: Get latest commit hash
        if: |
          steps.setup.conclusion != 'skipped' &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':'))
        run: echo "hash=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
        id: rev

      - name: Get comment body
        if: github.event.pull_request.head.sha == steps.rev.outputs.hash
        env:
          HEAD_REF: ${{ github.event.pull_request.head.ref }}
        run: |
          git config user.name "github-actions"
          git config user.email <EMAIL>
          git config advice.detachedHead false

          pr_list=$(gh pr list --json headRefName,mergeable,number,title --jq 'map(select(.mergeable == "MERGEABLE") | select(.headRefName | startswith("'$HEAD_REF'") | not) | select(.headRefName | startswith("dependabot") | not))')

          git fetch

          cat > conflicts_report.md << EOF
          <!-- CONFLICTS_REPORT -->
          ## 🔀 Conflicting Pull Requests
          EOF

          while read -r pr; do
            ref=$(echo "$pr" | jq -r '.headRefName')
            git checkout -f "origin/$ref"

            set +e
            merge_output=$(git merge --no-commit $HEAD_REF)
            status=$?
            set -e

            if [[ $status -gt 0 ]]; then
              conflicts=$(echo "$merge_output" | grep "CONFLICT")

              # Unindented for heredoc
              cat >> conflicts_report.md << EOF

          ### #$(echo "$pr" | jq -r '.number') $(echo "$pr" | jq -r '.title')

          > branch: [\`$ref\`](../tree/$ref)
          > conflict count: $(echo "$conflicts" | wc -l)

          \`\`\`
          $conflicts
          \`\`\`
          EOF
            else
              pr_list=$(echo "$pr_list" | jq 'del(.[] | select(.headRefName == "'$ref'"))')
            fi

            git checkout -f -
          done < <(echo "$pr_list" | jq -c '.[]')

          if [[ $(echo "$pr_list" | jq length) -eq 0 ]]; then
            # Unindented for heredoc
            cat >> conflicts_report.md << EOF
          No conflicting PRs found 🎉
          EOF
          fi

          delimiter="$(cat /proc/sys/kernel/random/uuid)"

          echo "body<<${delimiter}" >> $GITHUB_OUTPUT
          cat conflicts_report.md >> $GITHUB_OUTPUT
          echo "${delimiter}" >> $GITHUB_OUTPUT
        continue-on-error: true
        id: get_comment_body

      - name: Update PR conflicts report
        if: github.event.pull_request.head.sha == steps.rev.outputs.hash
        uses: ./.github/actions/comment
        with:
          body_includes: <!-- CONFLICTS_REPORT -->
          body: ${{ steps.get_comment_body.outputs.body }}

      # Additional lower priority builds
      - name: Build and push bundle analyzer
        if: |
          steps.setup.conclusion != 'skipped' &&
          !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':'))
        uses: ./.github/actions/setup/build
        with:
          image: bundle-analyzer

      # This prevents premature GHCR logout and token invalidation during post
      # job cleanup of setup/docker
      - name: Wait for preview
        if: always() && steps.deploy_preview.conclusion == 'success'
        run: |
          SECONDS=0

          until
            preview_url=$(
              gh api \
                /repos/${{ github.repository }}/commits/${{ github.event.pull_request.head.sha }}/check-runs \
                --jq '.check_runs[] | select((.app.name=="preview.monitora.cz") and (.name=="instance") and (.status=="completed")).details_url'
            )

            [[ -n $preview_url ]] && echo url="$preview_url" >> $GITHUB_OUTPUT
          do
            if [ "$SECONDS" -ge 600 ]; then
              echo "10 minutes have elapsed. Exiting."
              exit 1
            fi

            echo 'Retrying in 5 seconds...'
            sleep 5
          done
        id: preview_instance

      - name: Update PR preview
        if: always() && steps.preview_instance.conclusion == 'success'
        uses: ./.github/actions/comment
        with:
          body_includes: <!-- PREVIEW -->
          body: |
            <!-- PREVIEW -->
            ## 🎬 Preview

            (${{ github.event.pull_request.head.sha }}) ${{ steps.preview_instance.outputs.url }}
    outputs:
      hash: ${{ steps.rev.outputs.hash }}
      should_continue: ${{ steps.setup.conclusion != 'skipped' && !contains(steps.get_current_labels.outputs.labels, format('ci{0} basics', ':')) && !contains(toJSON(steps.get_current_labels.outputs.labels), format('ci{0} build', ':')) }}
      update_snapshots: ${{ contains(steps.get_current_labels.outputs.labels, format('ci{0} update snapshots', ':')) }}

  call_tests:
    needs: basics
    if: needs.basics.outputs.should_continue == 'true'
    uses: ./.github/workflows/integration_tests.yml
    secrets: inherit
    with:
      ref: ${{ github.event.pull_request.head.ref }}
      sha: ${{ needs.basics.outputs.hash }}
      update_snapshots: ${{ needs.basics.outputs.update_snapshots == 'true' }}

  commit_snapshots:
    needs: [basics, call_tests]
    if: needs.basics.outputs.update_snapshots == 'true'
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Generate token for mediaboard-frontend[bot]
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}
        id: get_bot_token

      - uses: actions/checkout@v4
        with:
          token: ${{ steps.get_bot_token.outputs.token }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Download changed snapshots
        uses: actions/download-artifact@v4
        with:
          name: changed_snapshots

      - uses: actions/github-script@v7
        if: failure()
        with:
          script: |
            core.setFailed(`No changed snapshots found.`)

      - name: Commit changes
        if: success()
        uses: ./.github/actions/commit
        with:
          add: 'e2e/*.png'
          commit: '-m "test(e2e): Update snapshots"'
          lint: false

      - name: Push changes
        if: success()
        run: git push

      - name: Remove label
        if: success()
        run: |
          gh api --method DELETE \
            /repos/${{ github.repository }}/issues/${{ github.event.number }}/labels/ci%3A%20update%20snapshots

  call_analyze:
    needs: basics
    if: |
      needs.basics.outputs.should_continue == 'true' &&
      github.base_ref == github.event.repository.default_branch &&
      needs.basics.outputs.update_snapshots != 'true'
    uses: ./.github/workflows/nextjs_bundle_analysis.yml
    secrets: inherit
    with:
      ref: ${{ github.event.pull_request.head.ref }}
