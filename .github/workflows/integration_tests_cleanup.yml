name: 'Integration Tests: Cleanup'

on:
  workflow_call:
    inputs:
      ref:
        required: true
        type: string
  workflow_dispatch:

jobs:
  cleanup:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: Tailscale
        uses: tailscale/github-action@v3
        with:
          oauth-client-id: ${{ secrets.TS_OAUTH_CLIENT_ID }}
          oauth-secret: ${{ secrets.TS_OAUTH_SECRET }}
          tags: tag:ci-frontend
          use-cache: 'true'

      - name: Prepare environment
        run: |
          {
            echo DB_SNAPSHOT_HOST=${{ vars.DB_SNAPSHOT_HOST }}
          } | tee -a $GITHUB_ENV .env.test.local

      - name: Cleanup backend
        run: |
          eval $(ssh-agent)
          ssh-add - <<< '${{ secrets.INTEGRATION_TESTS_PRIVATE_KEY }}'

          ./scripts/integration-tests/stop.sh --force
