name: Build, analyze, preview/dev

on:
  push:
    paths-ignore: # skip execution if file changes are only in these paths https://docs.github.com/en/actions/writing-workflows/workflow-syntax-for-github-actions#example-excluding-paths
      - 'docs/**'
      - '.github/**'
    branches:
      - dev
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    concurrency:
      group: docker-build-${{ github.ref_name }}
    env:
      DOCKER_BUILD_CLOUD_PAT: ${{ secrets.DOCKER_BUILD_CLOUD_PAT }}
      DOCKER_BUILD_CLOUD_USER: ${{ vars.DOCKER_BUILD_CLOUD_USER }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      STORYBOOK_PASSWORD: ${{ secrets.STORY<PERSON><PERSON>_PASSWORD }}
    permissions:
      contents: read
      id-token: write
      packages: write
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/setup/docker

      - name: Build and push app & UI
        uses: ./.github/actions/setup/build

      - name: Build and push bundle analyzer
        uses: ./.github/actions/setup/build
        with:
          image: bundle-analyzer

      - name: Build and push E2E proxy
        uses: ./.github/actions/setup/build
        with:
          image: e2e-proxy

      - name: Build and push E2E runner
        uses: ./.github/actions/setup/build
        with:
          image: e2e-runner

  call_analyze:
    needs: build
    uses: ./.github/workflows/nextjs_bundle_analysis.yml
    secrets: inherit

  preview_dev:
    runs-on: ubuntu-latest
    steps:
      - name: Generate token for mediaboard-frontend[bot]
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GH_APP_ID }}
          private-key: ${{ secrets.GH_APP_PRIVATE_KEY }}
        id: get_bot_token

      - name: Checkout base
        uses: actions/checkout@v4

      - name: Checkout preview branch
        uses: actions/checkout@v4
        with:
          token: ${{ steps.get_bot_token.outputs.token }}
          ref: preview/dev

      - name: Update preview branch
        run: git reset --hard origin/${{ github.event.repository.default_branch }}

      - name: Make a dummy file
        if: success()
        run: mktemp dummy.XXXXXX

      - name: Commit dummy file
        if: success()
        uses: ./.github/actions/commit
        with:
          add: dummy.*
          commit: '-m "Dev preview [lint all]"'
          lint: false

      - name: Force push
        if: success()
        run: git push --force
