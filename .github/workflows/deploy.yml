name: Deploy Dockerized Frontend to Production

on:
  push:
    paths-ignore: # skip execution if file changes are only in these paths https://docs.github.com/en/actions/writing-workflows/workflow-syntax-for-github-actions#example-excluding-paths
      - 'docs/**'
      - '.github/**'
    branches:
      - master
  workflow_dispatch:
    inputs:
      image_sha:
        required: false
        type: string

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      DOCKER_BUILD_CLOUD_PAT: ${{ secrets.DOCKER_BUILD_CLOUD_PAT }}
      DOCKER_BUILD_CLOUD_USER: ${{ vars.DOCKER_BUILD_CLOUD_USER }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      STORYBOOK_PASSWORD: ${{ secrets.STORYBOOK_PASSWORD }}
    permissions:
      contents: read
      id-token: write
      packages: write
    steps:
      - if: inputs.image_sha == ''
        uses: actions/checkout@v4
        with:
          fetch-depth: 50

      - if: inputs.image_sha == ''
        uses: ./.github/actions/setup/docker

      - name: Build and push app
        if: inputs.image_sha == ''
        uses: ./.github/actions/setup/build
        with:
          image: app

  trigger_deployment:
    needs: build
    runs-on: ubuntu-latest
    concurrency:
      group: deploy-prod
    env:
      SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    environment: production
    permissions:
      packages: read
      contents: read

    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4

      - name: Checkout master
        uses: actions/checkout@v4
        with:
          ref: master
          fetch-depth: 50

      - name: List Git commits
        id: commits
        run: |
          git --no-pager log $(git merge-base HEAD^1 HEAD^2)..HEAD^2 --oneline
          echo "changes=$(git --no-pager log $(git merge-base HEAD^1 HEAD^2)..HEAD^2 --oneline | awk '{printf "%s\\n", $0}')" | sed 's/"/\\"/g' >> $GITHUB_OUTPUT
        shell: bash

      - name: Send start notification to Slack
        id: slack
        uses: slackapi/slack-github-action@v1.26.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*[Frontend]* :rocket: Starting Docker deployment: <https://github.com/monitora-media/monitora-frontend/commit/${{ github.sha }}|${{ github.sha }}>"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Commits included in this deployment:* \n${{ steps.commits.outputs.changes }}"
                  }
                }
              ]
            }

      - name: Tailscale
        uses: tailscale/github-action@v3
        with:
          oauth-client-id: ${{ secrets.TS_OAUTH_CLIENT_ID_DEPLOY }}
          oauth-secret: ${{ secrets.TS_OAUTH_SECRET_DEPLOY }}
          tags: tag:production-user
          use-cache: 'true'

      # ssh into the machine, log in to GHCR, trigger the deployment script
      - name: Pull and deploy the production image
        run: |
          set -xeuo
          echo "Preparing the ssh-agent."
          eval $(ssh-agent)
          ssh-add - <<< '${{ secrets.GHA_DEPLOYMENT_PRIVATE_KEY }}'

          echo "SSH into production & pull the production image."
          ssh -o UserKnownHostsFile=scripts/known_hosts -T frontend@frontend << EOF
          set -xeuo pipefail
          docker login https://ghcr.io -u token --password-stdin <<< $GITHUB_TOKEN

          sudo APP_TAG=git-\${{ inputs.image_sha || github.sha }} deploy-frontend.sh
          EOF

      - name: Send success notification to Slack
        if: success()
        uses: slackapi/slack-github-action@v1.26.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*[Frontend]* :green_apple: Docker deployment successful <https://github.com/monitora-media/monitora-frontend/actions/runs/${{ github.run_id }}|${{ github.sha }}>. Rejoice! :space_invader:"
                  }
                }
              ]
            }

      - name: Send failure notification to Slack
        if: failure()
        uses: slackapi/slack-github-action@v1.26.0
        with:
          payload: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*[Frontend]* :apple: Docker deployment failed. See the logs for more details: <https://github.com/monitora-media/monitora-frontend/actions/runs/${{ github.run_id }}|${{ github.sha }}>."
                  }
                }
              ]
            }
