version: 2
updates:
  - package-ecosystem: npm
    directory: '/'
    schedule:
      interval: weekly
      day: saturday
    open-pull-requests-limit: 10
    allow:
      - dependency-type: development
      - dependency-name: '@lingui/*'
      - dependency-name: '@sentry/*'
      - dependency-name: next
    ignore:
      - dependency-name: react-is
      - dependency-name: typescript
    groups:
      eslint:
        patterns:
          - '*eslint*'
        exclude-patterns:
          - '*eslint-config*'
          - 'eslint-plugin-playwright'
          - 'eslint-plugin-storybook'
      nextjs-lingui-swc:
        patterns:
          - '@lingui/*'
          - '@next/*'
          - '*swc*'
          - eslint-config-next
          - next
          - nextjs-bundle-analysis
      playwright:
        patterns:
          - '*playwright*'
      prettier:
        patterns:
          - '*prettier*'
      storybook:
        patterns:
          - '*storybook*'
    labels:
      - dependencies
