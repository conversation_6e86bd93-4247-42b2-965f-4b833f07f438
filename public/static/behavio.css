* {
  font-family: '__lato_8459f9', '__lato_Fallback_8459f9', system-ui, sans-serif !important;
}

body {
  --primary: #0d61ff !important;
  --primary-darker: #0d61ff !important;
  --primary-inverse: #0d61ff !important;
}

#AppLayout,
#Content,
[class^='BrandTracking_BrandTrackingSlides'] {
  background-color: #f5f7f9 !important;
  background-image: none !important;
}

#SideMenu,
[class^='BrandTrackingSlidesDownload_BrandTrackingSlidesDownload'] {
  display: none !important;
}

#Content {
  padding-left: 0 !important;
}

[class^='BrandTracking_BrandTracking'] {
  --width-const: 0rem !important;
}

[class^='BrandTracking_BrandTrackingScroller'] [fill='#0798e0'] {
  fill: #0d61ff;
}

[class^='BrandTracking_BrandTrackingScroller'] [stroke='#0798e0'] {
  stroke: #0d61ff;
}
