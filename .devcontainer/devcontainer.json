{"build": {"dockerfile": "../Dockerfile", "target": "devcontainer"}, "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "editorconfig.editorconfig", "esbenp.prettier-vscode", "formulahendry.auto-complete-tag", "github.vscode-pull-request-github", "gruntfuggly.todo-tree", "mrorz.language-gettext", "ms-azuretools.vscode-docker", "ms-vscode.vscode-js-profile-flame", "ms-vsliveshare.vsliveshare", "styled-components.vscode-styled-components", "stylelint.vscode-stylelint"]}}, "mounts": ["source=mbfe_node_modules,target=${containerWorkspaceFolder}/node_modules,type=volume"], "name": "mbfe-dev", "workspaceFolder": "/srv", "workspaceMount": "source=${localWorkspaceFolder},target=${containerWorkspaceFolder},type=bind,consistency=cached"}