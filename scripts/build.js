const { loadEnvConfig } = require('@next/env')
const { rimraf } = require('rimraf')
const execa = require('execa')
const fs = require('fs/promises')
const os = require('os')
const path = require('path')

loadEnvConfig(process.cwd())

const { username } = os.userInfo()

;(async () => {
  const [BASE_REV, HEAD_REV] = (await execa('scripts/set_commits.sh')).stdout.split(' ')
  const buildInfoDir = path.join(os.tmpdir(), 'monitora-frontend-builds')
  const buildNumberFilename = path.join(buildInfoDir, HEAD_REV)
  let buildNumber = 1

  try {
    await fs.access(buildNumberFilename)
    buildNumber = parseInt(await fs.readFile(buildNumberFilename), 10) + 1
  } catch (ignoreErr) {
    //
  }

  await execa('next', ['build'], {
    env: {
      BASE_REV,
      HEAD_REV,
      NEXT_PUBLIC_SENTRY_DIST: `${username}-${buildNumber}`,
      SENTRY_RELEASE: HEAD_REV,
    },
    stdio: 'inherit',
  })

  // Sentry's `deleteSourcemapsAfterUpload` option doesn't work with standalone
  // output, so we do it ourselves
  await rimraf(path.join(process.env.DIST_DIR, '**', '*.map'), { glob: true })

  fs.cp(
    path.resolve(`${process.env.DIST_DIR}/static`),
    path.resolve(`${process.env.DIST_DIR}/standalone/${process.env.DIST_DIR}/static`),
    { recursive: true },
  )

  fs.cp(path.resolve('public'), path.resolve(`${process.env.DIST_DIR}/standalone/public`), {
    recursive: true,
  })

  if (process.env.NODE_ENV === 'production') {
    await fs.mkdir(buildInfoDir, { recursive: true })
    await fs.writeFile(buildNumberFilename, `${buildNumber}`)
    // await execa('node', ['scripts/extract-500-page.js'], { stdio: 'inherit' })
  }
})()
