#!/usr/bin/env node

const { loadEnvConfig } = require('@next/env')
const execa = require('execa')
const fs = require('fs/promises')

process.env.NODE_ENV = 'test'
loadEnvConfig(process.cwd())

function partitionTests(tests, numShards) {
  // Sort tests by duration (descending) to help with balanced distribution
  const sortedTests = [...tests].sort((a, b) => b.duration - a.duration)

  // Initialize shards array with empty arrays and their total durations
  const shards = Array.from({ length: numShards }, () => ({
    tests: [],
    totalDuration: 0,
  }))

  // Distribute tests using a greedy approach
  sortedTests.forEach((test) => {
    // Find shard with minimum total duration
    const targetShard = shards.reduce(
      (min, current) => (current.totalDuration < min.totalDuration ? current : min),
      shards[0],
    )

    // Add test to that shard
    targetShard.tests.push(test)
    targetShard.totalDuration += test.duration
  })

  return shards
}

function findSpecIdsInPwSuite(suite) {
  let ids = []

  // Get ids from specs in current suite
  if (suite.specs) {
    ids = suite.specs.map((spec) => {
      return spec.id
    })
  }

  // Recursively search nested suites
  if (suite.suites) {
    suite.suites.forEach((nestedSuite) => {
      ids = [...ids, ...findSpecIdsInPwSuite(nestedSuite)]
    })
  }

  return ids
}

async function createMatrix(shards, untimedTests = []) {
  const shardConfig = {
    shard: shards.map((shard, index) => {
      const shardNumber = index + 1

      return {
        name: `${shardNumber}${shard.tests?.length > 0 ? '' : ' (untimed)'}`,
        number: shardNumber,
        durationEstimate: shard.totalDuration,
        grep: shard.tests
          ?.map((test) => {
            return test.extra.annotations.find((annotation) => annotation.type === 'id').description
          })
          .join('|'),
      }
    }),
  }

  if (untimedTests.length > 0) {
    const shardCount = shardConfig.shard.length

    shardConfig.shard.push({
      number: shardCount + 1,
      name: `${shardCount + 1} (untimed)`,
      grep: untimedTests.join('|'),
    })
  }

  const heroClones = process.env.E2E_USERNAME_HERO_CLONES.split(',')

  if (shardConfig.shard.length > heroClones.length) {
    console.log(
      '\n',
      `Not enough hero clones for ${shardConfig.shard.length} shards. Follow the guide linked below to add more:`,
      '\n',
    )

    await execa('cat', ['.env.test'], { stdio: 'inherit' })
    process.exit(1)
  }

  shardConfig.shardTotal = [shardConfig.shard.length]
  return shardConfig
}

async function main() {
  const shardCount = parseInt(process.argv[3])
  let shardConfig = await createMatrix(Array.from({ length: shardCount }, () => ({})))

  try {
    const {
      results: { tests },
    } = JSON.parse(await fs.readFile(process.argv[2], 'utf-8'))

    const shards = partitionTests(
      tests.filter((test) => !/auth\.setup\.ts/.test(test.suite)),
      shardCount,
    )

    const allTimedTestsGrep = shards
      .flatMap((shard) => {
        return shard.tests.map((test) => {
          return test.extra.annotations.find((annotation) => annotation.type === 'id').description
        })
      })
      .join('|')

    const { suites } = JSON.parse(
      (
        await execa(
          'playwright',
          [
            'test',
            '--pass-with-no-tests',
            '--grep-invert',
            allTimedTestsGrep,
            '--list',
            '--reporter',
            'json',
          ],
          { env: { PLAYWRIGHT_CONFIG_TEST_IGNORE: 'auth.setup.ts' }, preferLocal: true },
        )
      ).stdout,
    )

    shardConfig = await createMatrix(
      shards,
      suites.flatMap((suite) => findSpecIdsInPwSuite(suite)),
    )
  } catch (error) {
    console.error(error)
  } finally {
    await fs.writeFile('matrix.json', JSON.stringify(shardConfig))
  }
}

main()
