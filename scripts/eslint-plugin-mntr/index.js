import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __dirname = fileURLToPath(new URL('.', import.meta.url))

const ESLintPluginMntr = {
  // Dynamically load and register all ESLint rules from the rules directory
  rules: Object.fromEntries(
    await Promise.all(
      (
        await fs.readdir(path.join(__dirname, 'rules')).then((files) => {
          // Only process .js files that aren't tests (don't end in .test.js)
          return files.filter((file) => {
            return !file.endsWith('.test.js') && file.endsWith('.js')
          })
        })
      ).map(async (file) => {
        // For each rule file:
        // - Use the filename (without .js) as the rule name
        // - Import and use the default export as the rule implementation
        return [
          path.basename(file, '.js'),
          (await import(path.join(__dirname, 'rules', file))).default,
        ]
      }),
    ),
  ),
}

export default ESLintPluginMntr
