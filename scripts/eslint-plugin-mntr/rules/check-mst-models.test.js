import { RuleTester } from 'eslint'
import rule from './check-mst-models'

const ruleTester = new RuleTester()

ruleTester.run('check-mst-models', rule, {
  valid: [
    {
      filename: '/path/to/src/store/models/ModelName.ts',
      code: `
        const ModelName = types.model('ModelName', {});
        export default ModelName;
      `,
    },
    {
      filename: '/path/to/src/store/models/ModelName.ts',
      code: `
        const ModelName = types.model('ModelName', {
          insets: types.model('Insets', {}),
        });
        export default ModelName;
      `,
    },

    {
      filename: '/path/to/src/store/models/ModelName.ts',
      code: `
        const SecondaryModel = types.model('SecondaryModel', {});

        const ModelName = types.model('ModelName', {
          insets: types.model('Insets', {}),
        });
        export default ModelName;
      `,
    },
  ],
  invalid: [
    {
      filename: '/path/to/src/store/models/ModelName.ts',
      code: `
        const ModelName = types.model({});
        export default ModelName;
      `,
      errors: [
        {
          message: 'Expected mobx-state-tree model name to be ModelName, but got undefined.',
        },
      ],
    },
    {
      filename: '/path/to/src/store/models/ModelName.ts',
      code: `
        const ModelName = types.model('OtherName', {});
        export default ModelName;
      `,
      errors: [
        {
          message: 'Expected mobx-state-tree model name to be ModelName, but got OtherName.',
        },
      ],
    },
    {
      filename: '/path/to/src/store/models/ModelName.ts',
      code: `
        const OtherName = types.model('ModelName', {});
        export default OtherName;
      `,
      errors: [
        {
          message: 'Expected default export to be named ModelName, but got OtherName.',
        },
      ],
    },
    {
      filename: '/path/to/src/store/models/ModelName.ts',
      code: `
        const ModelName = types.model('ModelName', {
          insets: types.model('SafeAreaInsets', {}),
        });
        export default ModelName;
      `,
      errors: [
        {
          message: 'Expected nested model name to be Insets, but got SafeAreaInsets.',
        },
      ],
    },
  ],
})
