import path from 'path'

const rule = {
  meta: {
    type: 'suggestion',
    docs: {
      description:
        'disallow export of the component to be named diffenently as component file itself.',
      category: 'Best Practices',
      recommended: false,
    },
    messages: {
      fileName:
        'Expected default export to be named {{filenameWithoutExtension}}, but got {{fileName}}.',
    },
    fixable: null, // This rule does not automatically fix code because of the complexity involved
    schema: [], // This rule does not have options
  },
  create(context) {
    const filename = context.getFilename()
    const filenameWithoutExtension = path.basename(filename, path.extname(filename))
    let declaredNames = {}

    function storeDeclaredNames(node) {
      if (node.declarations) {
        node.declarations.forEach((declaration) => {
          if (declaration.id && declaration.id.name) {
            declaredNames[declaration.id.name] = true
          }
        })
      }
    }

    function checkExportName(node, name) {
      if (name !== filenameWithoutExtension) {
        context.report({
          node: node.declaration,
          messageId: 'fileName',
          data: {
            filenameWithoutExtension: filenameWithoutExtension,
            fileName: name,
          },
        })
      }
    }

    return {
      VariableDeclaration: storeDeclaredNames,
      ExportDefaultDeclaration(node) {
        if (node.declaration.type === 'Identifier' && declaredNames[node.declaration.name]) {
          checkExportName(node, node.declaration.name)
        } else if (
          node.declaration.type === 'FunctionExpression' ||
          node.declaration.type === 'FunctionDeclaration'
        ) {
          const name = node.declaration.id && node.declaration.id.name
          if (name) {
            checkExportName(node, name)
          }
        } else if (
          node.declaration.type === 'ArrowFunctionExpression' &&
          node.declaration.id === null
        ) {
          const name = Object.keys(declaredNames)[0]
          checkExportName(node, name)
        } else if (
          node.declaration.type === 'CallExpression' &&
          node.declaration.arguments.length > 0
        ) {
          const innerComponent = node.declaration.arguments[0]
          if (innerComponent.type === 'Identifier' && declaredNames[innerComponent.name]) {
            checkExportName(node, innerComponent.name)
          }
        }
      },
    }
  },
}

export default rule
