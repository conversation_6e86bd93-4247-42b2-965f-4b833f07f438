import { RuleTester } from 'eslint'
import rule from './store-injection-depth'

const ruleTester = new RuleTester()

ruleTester.run('store-injection-depth', rule, {
  valid: [
    // This example should pass as it adheres to the depth rule
    `
    const FeedItemMetaData = ({
      appStore: {
        account,
        authors: { isOpenInspector },
        monitoring: { inspector },
        viewport,
        router: { makeRouteWithQuery },
      },
    }) => {}
    `,
  ],

  invalid: [
    // This example should fail as it involves first-level nested properties and suggests flattening
    {
      code: `
      const FeedItemMetaData = ({
        appStore: {
          account,
          authors: {
            name: { firstName, lastName },
          },
          monitoring: { inspector },
          viewport,
          router: { makeRouteWithQuery },
        },
      }) => {}
      `,
      errors: [
        {
          message: "Consider flattening this deeply nested destructuring of 'name' manually.",
        },
      ],
    },
    // This example should fail as it involves deeply nested properties and suggests manual review
    {
      code: `
      const FeedItemMetaData = ({
        appStore: {
          account,
          account: {
            user: {
              frontend_storage: { feedListView },
            },
          },
          authors: { isOpenInspector },
          monitoring: {
            inspector: { isOpenArticle, mentions },
          },
          viewport,
          router: { makeRouteWithQuery },
        },
      }) => {}
      `,
      errors: [
        {
          message: "Consider flattening this deeply nested destructuring of 'user' manually.",
        },
        {
          message: "Consider flattening this deeply nested destructuring of 'inspector' manually.",
        },
      ],
    },
  ],
})
