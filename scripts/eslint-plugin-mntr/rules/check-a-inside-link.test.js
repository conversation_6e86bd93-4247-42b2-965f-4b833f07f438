import { RuleTester } from 'eslint'
import rule from './check-a-inside-link'

const ruleTester = new RuleTester({
  languageOptions: {
    parserOptions: {
      ecmaFeatures: {
        jsx: true,
      },
    },
  },
})

ruleTester.run('check-a-inside-link', rule, {
  valid: [
    // Link with text content - should pass
    {
      code: `
        <Link href="/path">
          Click here
        </Link>
      `,
    },
    // Link with other JSX elements but no anchor - should pass
    {
      code: `
        <Link href="/path">
          <span>Some text</span>
          <div>More content</div>
        </Link>
      `,
    },
    // Link with complex nested elements but no anchor - should pass
    {
      code: `
        <Link href="/path">
          <div>
            <span>Nested content</span>
            <p>More nested content</p>
          </div>
        </Link>
      `,
    },
    // Anchor tag outside of Link - should pass
    {
      code: `
        <div>
          <a href="/path">External link</a>
          <Link href="/internal">Internal link</Link>
        </div>
      `,
    },
    // Empty Link - should pass
    {
      code: `
        <Link href="/path" />
      `,
    },
    // Link with icon components - should pass
    {
      code: `
        <Link href="/path">
          <Icon name="home" />
          <Text>Home</Text>
        </Link>
      `,
    },
  ],
  invalid: [
    // Link containing an anchor tag - should fail
    {
      code: `
        <Link href="/path">
          <a href="/other">Nested anchor</a>
        </Link>
      `,
      errors: [
        {
          messageId: 'noAnchorInsideLink',
        },
      ],
    },
    // Link with multiple children including anchor - should fail
    {
      code: `
        <Link href="/path">
          <span>Some text</span>
          <a href="/other">Nested anchor</a>
          <div>More content</div>
        </Link>
      `,
      errors: [
        {
          messageId: 'noAnchorInsideLink',
        },
      ],
    },
    // Link with multiple anchor tags - should fail with multiple errors
    {
      code: `
        <Link href="/path">
          <a href="/first">First anchor</a>
          <a href="/second">Second anchor</a>
        </Link>
      `,
      errors: [
        {
          messageId: 'noAnchorInsideLink',
        },
        {
          messageId: 'noAnchorInsideLink',
        },
      ],
    },
    // Link with anchor that has attributes - should fail
    {
      code: `
        <Link href="/path">
          <a href="/other" target="_blank">
            External link
          </a>
        </Link>
      `,
      errors: [
        {
          messageId: 'noAnchorInsideLink',
        },
      ],
    },
    // Link with self-closing anchor - should fail
    {
      code: `
        <Link href="/path">
          <a href="/other" />
        </Link>
      `,
      errors: [
        {
          messageId: 'noAnchorInsideLink',
        },
      ],
    },
  ],
})
