import path from 'path'

const rule = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'enforce the naming of MST models',
      category: 'Best Practices',
      recommended: false,
    },
    messages: {
      nestedModel:
        'Expected nested model name to be {{expectedName}}, but got {{nestedModelName}}.',
      modelName: 'Expected mobx-state-tree model name to be {{filename}}, but got {{modelName}}.',
      defaultExport: 'Expected default export to be named {{filename}}, but got {{exportedName}}.',
    },
    fixable: null, // This rule does not automatically fix code because of the complexity involved
    schema: [], // This rule does not have options
  },

  create(context) {
    const filename = context.getFilename()
    const filenameComponents = filename.split(path.sep)
    const filenameWithoutExtension = filenameComponents[filenameComponents.length - 1].split('.')[0]
    let rootLevelModels = []

    function toPascalCase(str) {
      return str
        .split(/[-_ ]+/)
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join('')
    }

    function checkNestedModelNames(node, properties) {
      for (const property of properties) {
        if (
          property.value.type === 'CallExpression' &&
          property.value.callee.property.name === 'model'
        ) {
          const nestedModelNameArg = property.value.arguments[0]
          if (nestedModelNameArg && nestedModelNameArg.type === 'Literal') {
            const expectedName = toPascalCase(property.key.name)
            if (nestedModelNameArg.value !== expectedName) {
              context.report({
                node: nestedModelNameArg,
                messageId: 'nestedModel',
                data: {
                  expectedName: expectedName,
                  nestedModelName: nestedModelNameArg.value,
                },
              })
            }
          }
        }
      }
    }

    return {
      CallExpression(node) {
        const callee = node.callee
        if (
          callee.type === 'MemberExpression' &&
          callee.property &&
          callee.property.name === 'model'
        ) {
          if (node.parent.type !== 'Property') {
            rootLevelModels.push(node)
            const propertiesArg = node.arguments[1]
            if (propertiesArg && propertiesArg.type === 'ObjectExpression') {
              checkNestedModelNames(node, propertiesArg.properties)
            }
          }
        }
      },
      'Program:exit': function () {
        if (rootLevelModels.length > 0) {
          const lastModel = rootLevelModels[rootLevelModels.length - 1]
          const modelNameArg = lastModel.arguments[0]
          if (modelNameArg.value !== filenameWithoutExtension) {
            context.report({
              node: modelNameArg,
              messageId: 'modelName',
              data: {
                filename: filenameWithoutExtension,
                modelName: modelNameArg.value,
              },
            })
          }
        }
      },
      ExportDefaultDeclaration(node) {
        const exportedName =
          node.declaration.type === 'Identifier'
            ? node.declaration.name
            : node.declaration.type === 'FunctionDeclaration' ||
                node.declaration.type === 'ArrowFunctionExpression'
              ? node.declaration.id.name
              : null

        if (exportedName && exportedName !== filenameWithoutExtension) {
          context.report({
            node: node.declaration,
            messageId: 'defaultExport',
            data: {
              filename: filenameWithoutExtension,
              exportedName: exportedName,
            },
          })
        }
      },
    }
  },
}

export default rule
