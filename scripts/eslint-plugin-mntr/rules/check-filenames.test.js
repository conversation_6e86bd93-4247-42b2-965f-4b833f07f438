import { RuleTester } from 'eslint'
import rule from './check-filenames'

const ruleTester = new RuleTester({
  languageOptions: {
    parserOptions: {
      ecmaFeatures: {
        jsx: true,
      },
    },
  },
})

ruleTester.run('check-filenames', rule, {
  valid: [
    {
      filename: '/path/to/src/components/ComponentName.tsx',
      code: `
        function ComponentName() {}
        export default ComponentName
      `,
    },
    {
      filename: '/path/to/src/components/ComponentName.tsx',
      code: `
        export default function ComponentName() {}
      `,
    },
    {
      filename: '/path/to/src/components/reports/subfolder/RecipientsList.tsx',
      code: `
        const RecipientsList = ({
          appStore: { emailing },
        }) => {
          return <div />
        }
        export default observer(RecipientsList)
      `,
    },
    {
      filename: '/path/to/src/components/layout/RandomComponent.tsx',
      code: `
        const ComponentB = ({}) => {
          return <div />
        }

        const RandomComponent = ({
          appStore: { emailing },
        }) => {
          return <ComponentB />
        }
        export default RandomComponent
      `,
    },
  ],
  invalid: [
    {
      filename: '/path/to/src/components/ComponentName.tsx',
      code: `
        const AnotherName = () => {}
        export default AnotherName;
      `,
      errors: [
        {
          message: 'Expected default export to be named ComponentName, but got AnotherName.',
        },
      ],
    },
    {
      filename: '/path/to/src/components/reports/subfolder/RecipientsList.tsx',
      code: `
        const RecipientsListB = ({
          appStore: { emailing },
        }) => {
          return null 
        }
        export default observer(RecipientsListB)
      `,
      errors: [
        {
          message: 'Expected default export to be named RecipientsList, but got RecipientsListB.',
        },
      ],
    },
  ],
})
