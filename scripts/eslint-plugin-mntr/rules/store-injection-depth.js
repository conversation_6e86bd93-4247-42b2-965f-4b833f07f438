const rule = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'disallow  the depth of imports from appStore to be more than level 2',
      category: 'Best Practices',
      recommended: false,
    },
    messages: {
      manually: "Consider flattening this deeply nested destructuring of '{{messageId}}' manually.",
      suggestedImports:
        'Consider flattening the depth of appStore imports. Suggested imports: {{suggestedImports}}',
    },
    fixable: null, // This rule does not automatically fix code because of the complexity involved
    schema: [], // This rule does not have options
  },
  create(context) {
    return {
      ObjectPattern(node) {
        // Ensure we are destructuring from a property and it's named 'appStore'
        if (node.parent && node.parent.key && node.parent.key.name === 'appStore') {
          let needsFlattening = false
          let flatImports = []

          node.properties.forEach((prop) => {
            // Check for nested destructuring
            if (prop.value.type === 'ObjectPattern') {
              needsFlattening = true
              prop.value.properties.forEach((nestedProp) => {
                if (nestedProp.value.type === 'ObjectPattern') {
                  // For deeply nested structures, suggest manual review
                  context.report({
                    node: nestedProp,
                    messageId: 'manually',
                    data: {
                      messageId: nestedProp.key.name,
                    },
                  })
                } else {
                  // For first-level nested properties, suggest flattening
                  flatImports.push(`${prop.key.name}.${nestedProp.key.name}`)
                }
              })
            } else {
              flatImports.push(prop.key.name)
            }
          })

          if (needsFlattening && node.parent.key.name !== 'appStore') {
            context.report({
              node,
              messageId: 'suggestedImports',
              data: {
                suggestedImports: flatImports.join(', '),
              },
            })
          }
        }
      },
    }
  },
}

export default rule
