const rule = {
  meta: {
    type: 'problem',
    docs: {
      description: 'disallow using an <a> tag inside a <Link> component',
      category: 'Best Practices',
      recommended: true,
    },
    messages: {
      noAnchorInsideLink:
        'Avoid using <a> tags inside <Link> components to prevent routing issues.',
    },
    schema: [], // No options for this rule
  },
  create(context) {
    return {
      JSXOpeningElement(node) {
        if (node.name.name !== 'Link') {
          return
        }

        const children = node.parent.children.filter((child) => child.type === 'JSXElement')

        for (const child of children) {
          if (child.openingElement.name.name === 'a') {
            context.report({
              node: child,
              messageId: 'noAnchorInsideLink',
            })
          }
        }
      },
    }
  },
}

export default rule
