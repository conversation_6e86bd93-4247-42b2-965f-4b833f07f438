#!/usr/bin/env node

// This could be a shell script, but was easier in node.

/* eslint-disable no-await-in-loop */
const { formatDuration, intervalToDuration } = require('date-fns')
const { loadEnvConfig } = require('@next/env')
const { Octokit } = require('@octokit/core')
const { userInfo } = require('os')
const chalk = require('chalk')
const execa = require('execa')
const fs = require('fs/promises')

process.env.NODE_ENV = 'test'
loadEnvConfig(process.cwd())

// Set up timing variables and user type
const isPriorityUser = !process.env.CI
const queueIntervalSecs = isPriorityUser ? 5 : 10
const timeoutMins = isPriorityUser ? 10 : 15
const timeoutMs = timeoutMins * 60 * 1000
let runStartedAt = Date.now()
const queueTimestamp = runStartedAt

const timeout = setTimeout(async () => {
  if (process.env.GITHUB_ACTIONS) {
    await fs.appendFile(process.env.GITHUB_OUTPUT, 'lock=super_busy')
    await fs.appendFile(
      process.env.GITHUB_OUTPUT,
      `timeout=${formatDuration({ minutes: timeoutMins })}`,
    )
  }

  process.exit(1)
}, timeoutMs)

const timeoutCheck = setInterval(() => {
  const { minutes, seconds } = intervalToDuration({
    start: new Date(),
    end: new Date(queueTimestamp + timeoutMs),
  })

  console.log(`\x1b[2mTimeout in ${formatDuration({ minutes, seconds })}\x1b[0m`)
}, 30000)

let lockFileHandle

// Clear timeouts and open file handles on exit
process.on('beforeExit', () => {
  clearTimeout(timeout)
  clearInterval(timeoutCheck)
  lockFileHandle.close()
})

// Make sure commands gracefully respect termination signals (e.g. from Docker)
process.on('SIGTERM', () => process.exit(0))
process.on('SIGINT', () => process.exit(0))

const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
})

const rev = process.env.MEDIABOARD_BACKEND_REV || 'origin/master'

async function start(attemptNumber = 1) {
  if (attemptNumber > 1) {
    console.log(`Running start command... ${chalk.bold(`(attempt #${attemptNumber})`)}`)
  } else {
    console.log('Running start command...')
  }

  const startProcess = execa(
    'ssh',
    [
      '-o',
      'UserKnownHostsFile=scripts/known_hosts',
      `${process.env.DB_SNAPSHOT_USER}@${process.env.DB_SNAPSHOT_HOST}`,
      'start',
      rev,
      process.env.E2E_PASSWORD,
      process.env.GITHUB_RUN_ID || `priority:${process.env.WHOAMI || userInfo().username}`,
      ...Object.entries(process.env).reduce((usernames, [key, value]) => {
        if (key.startsWith('E2E_USERNAME')) {
          return usernames.concat(value.split(','))
        }

        return usernames
      }, []),
    ],
    { all: true, reject: false },
  )

  // Interleave output from `stdout` and `stderr` similar to terminal
  startProcess.all.pipe(process.stdout)

  startProcess.all.on('data', async (chunk) => {
    if (chunk.toString().includes('Snapshotting database...')) {
      // Write lock locally, to be evaluated when stopping later on
      if (process.env.GITHUB_ACTIONS) {
        fs.appendFile(process.env.GITHUB_OUTPUT, 'status=locked\n')
      } else {
        lockFileHandle = await fs.open('e2e/artifacts/integration_tests_start.lock', 'w')
        lockFileHandle.write((await checkUnlocked(true)).lockedAt)
      }
    }
  })

  const { all } = await startProcess

  if (all.includes("Can't init snapshot")) {
    if (attemptNumber === 3) {
      throw new Error('Failed to init snapshot after 3 attempts')
    } else {
      console.log('Failed to init snapshot. Retrying...')

      await execa(
        'ssh',
        [
          '-o',
          'UserKnownHostsFile=scripts/known_hosts',
          `${process.env.DB_SNAPSHOT_USER}@${process.env.DB_SNAPSHOT_HOST}`,
          'stop',
        ],
        { stdio: 'inherit' },
      )

      return start(attemptNumber + 1)
    }
  }

  return {
    alreadyLocked: all.includes('already locked'),
    started: all.endsWith('Django setup [OK]'),
  }
}

async function queue() {
  if (isPriorityUser) {
    console.log('Wielding priority...')
  }

  let { isPriority, isUnlocked, lockedAt, runId } = await checkUnlocked()
  let competingRuns = await getCompetingRuns({ isPriority, runId })

  while (!isUnlocked && competingRuns.length > 0) {
    // Format queue message
    const { minutes, seconds } = intervalToDuration({
      start: new Date(Date.parse(lockedAt)),
      end: Date.now(),
    })

    console.log(
      `You're ${
        competingRuns.length === 1
          ? 'up next, after'
          : `${formatOrdinal(competingRuns.length)} in queue, after these runs`
      }`,
    )

    console.log(
      competingRuns
        .map(({ active, actor, url }) => {
          return `- ${
            url
              ? `${url} (${actor})`
              : `${actor}, who squeezed in ${formatDuration({ minutes, seconds })} ago`
          }${active ? ` ${chalk.bold('[ACTIVE]')}` : ''}`
        })
        .join('\n'),
    )

    console.log(`Retrying in ${formatDuration({ seconds: queueIntervalSecs })}...\n`)

    await sleep(queueIntervalSecs * 1000)
    ;({ isPriority, isUnlocked, lockedAt, runId } = await checkUnlocked())
    if (isUnlocked && isPriorityUser) break
    competingRuns = await getCompetingRuns({ isPriority, runId })
  }
}

// Main
;(async () => {
  if (process.env.GITHUB_ACTIONS) {
    console.log('Checking Tailscale...')
    await execa('tailscale', ['status'], { stdio: 'inherit' })
  }

  console.table({
    'DB snapshot host': process.env.DB_SNAPSHOT_HOST,
    'BE revision': rev,
  })

  // await execa('ssh-add', ['-l'], { stdio: 'inherit' })

  console.log('Checking SSH...')

  const { stderr: sshError } = await execa(
    'ssh',
    [
      '-o',
      'UserKnownHostsFile=scripts/known_hosts',
      `${process.env.DB_SNAPSHOT_USER}@${process.env.DB_SNAPSHOT_HOST}`,
      'exit',
    ],
    { reject: false },
  )

  if (sshError) {
    if (sshError.includes('Could not resolve hostname')) {
      console.log(chalk.yellow(`Make sure you're connected to the VPN. Exiting.`))
    }

    if (sshError.includes('Permission denied (publickey)')) {
      console.log(
        chalk.yellow(
          `Make sure your SSH key was added to integration_tests' authorized_keys. Exiting.`,
        ),
      )
    }

    process.exit(1)
  }

  let { alreadyLocked, started } = await start()

  if (!started && alreadyLocked && process.env.GITHUB_RUN_ID) {
    // Update `runStartedAt` with real time available from GH workflow run
    const {
      data: { run_started_at },
    } = await octokit.request(
      `GET /repos/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}`,
      {
        headers: {
          'X-GitHub-Api-Version': '2022-11-28',
        },
      },
    )

    runStartedAt = Date.parse(run_started_at)
  }

  while (!started) {
    if (alreadyLocked) await queue()
    else process.exit(1)
    ;({ alreadyLocked, started } = await start())
  }

  process.exit(0)
})()

// Utils
async function checkUnlocked(silent) {
  if (!silent) console.log('Checking lock...')

  const { stdout } = await execa(
    'ssh',
    [
      '-o',
      'UserKnownHostsFile=scripts/known_hosts',
      `${process.env.DB_SNAPSHOT_USER}@${process.env.DB_SNAPSHOT_HOST}`,
      'check-unlocked',
    ],
    { reject: false },
  )

  try {
    const { locked_at, run_id } = JSON.parse(stdout)

    return {
      isPriority: run_id.startsWith('priority:'),
      isUnlocked: false,
      lockedAt: locked_at,
      runId: run_id,
    }
  } catch (ignoreErr) {
    //
  }

  return {
    isUnlocked: true,
  }
}

async function getCompetingRuns(lockInfo) {
  console.log('Checking queue...')

  const {
    data: { workflow_runs },
  } = await octokit.request(`GET /repos/${process.env.GITHUB_REPOSITORY}/actions/runs`, {
    headers: {
      'X-GitHub-Api-Version': '2022-11-28',
    },
  })

  const inProgressRuns = []

  if (lockInfo) {
    inProgressRuns.push({
      id: lockInfo.runId,
      active: lockInfo.isPriority,
      actor: lockInfo.runId,
    })
  }

  const ghaRuns = workflow_runs.reduce(
    (runs, { id: ghId, actor: { login }, html_url, run_started_at, status }) => {
      if (
        ['in_progress', 'queued', 'waiting'].includes(status) &&
        Date.parse(run_started_at) < runStartedAt
      ) {
        const id = ghId.toString() // important for Map merge/overwrite

        return runs.concat({
          id,
          active: id === lockInfo?.runId,
          actor: login,
          url: html_url,
        })
      }

      return runs
    },
    [],
  )

  return Array.from(
    new Map([
      ...new Map(inProgressRuns.map((item) => [item.id, item])),
      ...new Map(ghaRuns.reverse().map((item) => [item.id, item])),
    ]).values(),
  )
}

function formatOrdinal(n) {
  return `${n}${new Map([
    ['one', 'st'],
    ['two', 'nd'],
    ['few', 'rd'],
    ['other', 'th'],
  ]).get(new Intl.PluralRules('en-US', { type: 'ordinal' }).select(n))}`
}

async function sleep(timeout) {
  return new Promise((resolve) => {
    setTimeout(resolve, timeout)
  })
}
