#!/usr/bin/env bash

# shellcheck source=../../.env.test
source .env.test
source .env.test.local

# Initialize force flag to false by default. When set to true via --force/-f
# flag, it will bypass lock file checks and force stop the integration tests.
force=false

# Process command line arguments in any order. Currently supports:
# --force, -f: Force stop integration tests without checking lock files
while [[ $# -gt 0 ]]; do
  case "$1" in
    --force | -f)
      force=true
      shift
      ;;
    *)
      shift
      ;;
  esac
done

if [[ -e e2e/artifacts/integration_tests_start.lock || $force == true ]]; then
  if [[ $force != true ]]; then
    remote_locked_at=$(
      ssh -o UserKnownHostsFile=scripts/known_hosts $DB_SNAPSHOT_USER@$DB_SNAPSHOT_HOST check-unlocked \
        | grep -o '"locked_at": "[^"]*"' \
        | cut -d'"' -f4
    )

    local_locked_at=$(cat e2e/artifacts/integration_tests_start.lock)

    if [[ "$remote_locked_at" != "$local_locked_at" ]]; then
      echo "Locked at time mismatch detected. Exiting."
      echo "Diff between remote and local lock times:"
      diff <(echo "$remote_locked_at") <(echo "$local_locked_at")
      exit 1
    fi
  fi

  echo "Running stop command..."
  ssh -o UserKnownHostsFile=scripts/known_hosts $DB_SNAPSHOT_USER@$DB_SNAPSHOT_HOST stop \
    && rm -f e2e/artifacts/integration_tests_start.lock
fi
