#!/usr/bin/env bash

paths_to_remove=$(
  git status --ignored --short \
    | awk '$1 == "!!" && $2 !~ /\.env.*\.local/ { print $2 }' \
    | paste -s -d ' ' -
)

if [[ -n $paths_to_remove ]]; then
  tput setaf 3
  # shellcheck disable=2086
  git clean -xdn $paths_to_remove
  read -p "Proceed? (y/n): $(tput sgr0)" -r

  if [[ $REPLY =~ ^[Yy]$ ]]; then
    # shellcheck disable=2086
    git clean -xdf $paths_to_remove
    git config --unset core.hooksPath
  fi
else
  echo "Repo is already clean 🫧"
fi
