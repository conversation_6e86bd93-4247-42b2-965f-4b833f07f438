#!/usr/bin/env bash

set -x

# Convert SVGs to favicons using ImageMagick
convert_icon_to_favicon() {
  local src=$1
  local dest=$2

  convert \
    -resize 256 \
    -extent 256x256 \
    -gravity center \
    -background none \
    -define icon:auto-resize="256,48,32,16" \
    "$src" \
    "$dest"
}

convert_icon_to_favicon src/assets/mediaboard/reference/icon-rounded.svg public/static/mediaboard/favicon.ico
convert_icon_to_favicon src/assets/sita/reference/icon-rounded.svg public/static/sita/favicon.ico

# Optimize SVGs using SVGO
pnpm svgo \
  src/assets/mediaboard/pwa-icon-mac.svg \
  src/assets/mediaboard/pwa-icon-maskable.svg \
  src/assets/mediaboard/pwa-icon-windows.svg \
  src/assets/sita/pwa-icon-mac.svg \
  src/assets/sita/pwa-icon-maskable.svg \
  src/assets/sita/pwa-icon-windows.svg \
  -o \
  public/static/mediaboard/pwa-icon-mac.svg \
  public/static/mediaboard/pwa-icon-maskable.svg \
  public/static/mediaboard/pwa-icon-windows.svg \
  public/static/sita/pwa-icon-mac.svg \
  public/static/sita/pwa-icon-maskable.svg \
  public/static/sita/pwa-icon-windows.svg
