const { Job } = require('@vercel/nft/out/node-file-trace')
const { nodeFileTrace } = require('@vercel/nft')
const fs = require('fs/promises')
const globby = require('globby')
const path = require('path')
const ts = require('typescript')

;(async () => {
  const job = new Job({})

  const { fileList } = await nodeFileTrace([...(await globby(process.argv.slice(3)))], {
    async readFile(fsPath) {
      const source = await job.readFile(fsPath)

      if ((fsPath.endsWith('.ts') && !fsPath.endsWith('.d.ts')) || fsPath.endsWith('.tsx')) {
        // https://github.com/microsoft/TypeScript/wiki/Using-the-Compiler-API#a-simple-transform-function
        const result = ts.transpileModule(source, {
          compilerOptions: { module: ts.ModuleKind.CommonJS },
        })

        return result.outputText
      }

      return source
    },
  })

  const outputDir = path.join('standalone', process.argv[2])

  await fs.mkdir(outputDir, { recursive: true })

  fileList.forEach((file) => {
    fs.cp(file, path.join(outputDir, file), { recursive: true })
  })
})()
