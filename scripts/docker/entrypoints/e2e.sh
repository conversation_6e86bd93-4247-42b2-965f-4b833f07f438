#!/usr/bin/env bash

cmd='pnpm test:e2e'

mkdir -p e2e/artifacts

if [[ -n $GITHUB_ACTIONS || -n $PLAYWRIGHT_CONFIG_BASE_URL ]]; then
  # GHA workflows take care of starting/stopping `integration-tests`
  # before/after running tests in a shard, so we execute the (test runner)
  # command immediately.
  exec $cmd "$@"
fi

unset CI

cleanup() {
  trap - EXIT
  ./scripts/integration-tests/stop.sh
  exit
}

trap cleanup EXIT SIGTERM SIGINT

rm -rf e2e/artifacts/*

if ./scripts/integration-tests/start.js; then
  echo 'Running' "$cmd" "$@"
  $cmd "$@" &
  wait
fi
