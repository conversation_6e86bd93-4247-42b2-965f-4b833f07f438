import path from 'path'
import { dependencies } from '../../../package.json'

const links = Object.entries(dependencies).filter(([, version]) => version.startsWith('link:'))
const linkTargets = links.map(([, link]) => link.replace('link:./', ''))

export default function transformer(file, api) {
  const dirname = path.dirname(file.path)
  const aliasTarget = dirname.split(path.sep).reverse().pop()
  const j = api.jscodeshift
  const root = j(file.source)

  root.find(j.ImportDeclaration).replaceWith(({ node }) => {
    if (node.source.value.includes('../') && linkTargets.includes(aliasTarget)) {
      node.source.value = path.resolve(dirname, node.source.value).replace(
        path.join(process.cwd(), aliasTarget),
        links.reduce((output, [alias, link]) => {
          if (link.endsWith(aliasTarget)) {
            return alias
          }

          return output
        }, ''),
      )
    }

    return node
  })

  return root.toSource()
}
