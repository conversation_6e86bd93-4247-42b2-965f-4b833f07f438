// /**
//  * fuck, i need to rewrite dis shit to log or use my shit... then Prettier will
//  * go and fuck with all of my shit, nooooo 😖 fuck this, will go make coffee
//  * instead, then look outside teh window and contemplate why i chose this path
//  * in life
//  */
// const WillBreakYourDiffUglyAssComponent = () => (
//   <>
//     <span>prdel</span>
//     <span>prdel</span>
//     <span>prdel</span>
//     {/* gravyboatloads of other JSX */}
//   </>
// )

// 👇👇👇

// const ReliableSexyComponent = () => {
//   console.log('i can haz console.logs 🤩')
//   const ref = useRef('i can haz hooks 😍')
//   /* or i can delete this useless shit */

//   /**
//    * teh block stays put... it's like magic 🥺
//    */
//   return (
//     <>
//       <span>foo</span>
//       <span>bar</span>
//       <span>baz</span>
//       {/* tonnes of lines of JSX similar to WillBreakYourDiffUglyAssComponent's */}
//     </>
//   )
// }

export default function transformer(file, api) {
  const j = api.jscodeshift
  const root = j(file.source)

  root
    .find(j.ArrowFunctionExpression)
    .filter(
      ({
        value: {
          body: { type },
        },
      }) => {
        return ['JSXElement', 'JSXFragment'].includes(type)
      },
    )
    .replaceWith(({ node }) => {
      return j.arrowFunctionExpression(
        node.params,
        node.body.loc.start.line === node.body.loc.end.line
          ? node.body
          : j.blockStatement([j.returnStatement(node.body)]),
      )
    })

  return root.toSource()
}
