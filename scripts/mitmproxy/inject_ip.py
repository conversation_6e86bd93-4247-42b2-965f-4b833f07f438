from mitmproxy import http, ctx

# IP addresses that will resolve to specific countries in GeoIP
COUNTRY_IPS = {
    "cz": "***************",
    "sk": "**************",
    "pl": "*************",
    "hr": "************",
    "si": "***********",
}

# Default to Czech Republic if no country specified
DEFAULT_COUNTRY = "cz"

def load(loader):
    """Called when the script is loaded"""
    loader.add_option(
        name = "country",
        typespec = str,
        default = DEFAULT_COUNTRY,
        help = "Country code to use for IP spoofing (cz, sk, pl, hr, si)",
    )

def request(flow: http.HTTPFlow) -> None:
    country = ctx.options.country
    ip = COUNTRY_IPS.get(country, COUNTRY_IPS[DEFAULT_COUNTRY])

    # Add or replace the x-forwarded-for header
    flow.request.headers["x-forwarded-for"] = ip
