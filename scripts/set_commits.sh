#!/usr/bin/env bash

# https://chatgpt.com/share/4e5862e0-1bc6-4605-9f7a-7c9727428c6d
base_ref="${1:-origin/dev}"

if [[ -z "$BASE_REV" ]]; then
  parent_commits=$(git log -1 --pretty=%P)

  if [[ "$parent_commits" =~ " " ]]; then
    # HEAD is a merge commit
    BASE_REV=$(echo "$parent_commits" | awk '{print $1}')
  else
    BASE_REV=$(git merge-base "$base_ref" HEAD)
  fi
fi

if [[ -z "$HEAD_REV" ]]; then
  HEAD_REV=$(git rev-parse HEAD)
fi

echo "$BASE_REV $HEAD_REV"
