#!/usr/bin/env bash

set -xeuo pipefail

user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36' # https://github.com/marella/material-design-icons/blob/e38157df0c4989a92adf25ed95e44a3e4d22a07d/scripts/font.js#L24-L25
variable_icon_font_css='https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,300,0..1,0'   # https://fonts.google.com/icons

pnpm dlx @material-design-icons/scripts download font \
  --symbols \
  --to public/static/ \
  --weight 300 \
  --grade 0 \
  --size 24

font_hash=$(sha256sum public/static/material-symbols-outlined.woff2 | awk '{print $1}')

cp public/static/{material-symbols-outlined,"$font_hash"}.woff2

curl \
  --silent \
  --user-agent "$user_agent" \
  "$variable_icon_font_css" \
  | sed -r "s|(https.*)(\.woff2)|\/static\/$font_hash\2|g" \
    > src/styles/material-symbols.css \
  && pnpm fmt src/styles/material-symbols.css

git clean -f public/static/material-symbols-*
