const { chromium } = require('playwright')
const { hostname } = require('os')
const { loadEnvConfig } = require('@next/env')
const execa = require('execa')
const fs = require('fs/promises')
const getPort = require('get-port')
const isDocker = require('is-docker')
const path = require('path')
const pTimeout = require('p-timeout')

loadEnvConfig(process.cwd())

const outputDir = path.join('standalone', '500')
const exclusionPattern =
  'google-analytics|gtag|track|analytics|facebook|seznam|linkedin|leady|dreamdata|fbq|gtm'

const modifyContent = (content) => {
  return content
    .replace(new RegExp(`<script[^>]*(${exclusionPattern})[^>]*>[\\s\\S]*?<\\/script>`, 'gi'), '')
    .replace(/src="\/([^"]+)"/g, 'src="./$1"')
    .replace(/href="\/([^"]+)"/g, 'href="./$1"')
}

const downloadAssets = async (page, urls) => {
  for (const urlString of urls) {
    const url = new URL(urlString)
    const filePath = path.join(outputDir, url.pathname)
    await fs.mkdir(path.dirname(filePath), { recursive: true })
    const response = await page.goto(urlString, { waitUntil: 'load', timeout: 0 })
    if (response && response.ok()) {
      await fs.writeFile(filePath, await response.body())
    }
  }
}

async function main() {
  const PORT = await getPort()

  const serverProcess = execa(
    'node',
    [path.join(process.env.DIST_DIR, 'standalone', 'server.js')],
    {
      env: {
        PORT,
      },
    },
  )

  const url = `http://${isDocker() ? hostname() : 'localhost'}:${PORT}`

  await isServerReady(url)

  const browser = await chromium.launch()
  const page = await browser.newPage()

  await page.goto(`${url}/500`, { waitUntil: 'load' })

  let content = await page.content()
  content = modifyContent(content)

  await fs.rm(outputDir, { recursive: true, force: true })
  await fs.mkdir(outputDir, { recursive: true })
  await fs.writeFile(path.join(outputDir, 'index.html'), content)

  const urls = await page.evaluate((exclusionPattern) => {
    const urls = []
    document.querySelectorAll('link[rel="stylesheet"], script[src], img[src]').forEach((el) => {
      const url = el.src || el.href
      if (url && !url.match(new RegExp(exclusionPattern))) {
        urls.push(url)
      }
    })
    return urls
  }, exclusionPattern)

  await downloadAssets(page, urls)

  await browser.close()

  console.log(
    `"${outputDir}" directory was created containing index.html and all static files needed to serve the page`,
  )

  serverProcess.cancel()
}

main()

// Utils
async function isServerReady(url) {
  try {
    await execa('curl', ['--output', '/dev/null', '--silent', '--head', '--fail', url])
  } catch (ignoreErr) {
    return pTimeout(
      retry(() => isServerReady(url)),
      30000,
    ).catch(() => {
      throw new Error(`${url} is not ready after 30 seconds`)
    })
  }

  return true
}

async function retry(fn, delay = 1000) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(fn())
    }, delay)
  })
}
