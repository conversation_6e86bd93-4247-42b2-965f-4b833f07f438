/* eslint-disable
playwright/no-conditional-expect,
playwright/no-conditional-in-test,
*/
import { expect } from '@playwright/test'
import { test } from 'e2e/pages.support'

test('logout', async ({ guest: { page }, isMobile }) => {
  await page.goto('/logout')
  await expect(page.getByText('Log In').first()).toBeVisible()
  await page.getByLabel('Email').click()
  await page.getByLabel('Email').fill(<string>process.env.E2E_USERNAME)
  await page.getByLabel('Email').press('Tab')
  await page.getByLabel('Password').fill(<string>process.env.E2E_PASSWORD)
  await page.getByRole('button', { name: 'Log In' }).click()

  if (isMobile) {
    await expect(page.getByRole('button', { name: 'menu' })).toBeVisible()
    await page.getByRole('button', { name: 'menu' }).click()
  } else {
    await expect(page.getByRole('link', { name: 'view_stream Articles' })).toBeVisible()
  }

  await page.getByRole('button', { name: 'account_circle' }).click()
  await page.getByRole('link', { name: 'exit_to_app Logout' }).click()
  await expect(page.getByText('Log In').first()).toBeVisible()
  await expect(page.getByRole('button', { name: 'en' })).toBeVisible()
})
