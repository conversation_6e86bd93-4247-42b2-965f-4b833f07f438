/* eslint-disable
playwright/no-conditional-in-test,
*/
import { expect } from '@playwright/test'
import { test } from 'e2e/pages.support'

test('(un)impersonate', async ({ staff: { page }, isMobile }) => {
  await page.goto(`/staff/admin/users/${process.env.E2E_PARAM_IMPERSONATE_USER_ID}`)
  await page.getByText('Login as this user').click()
  if (isMobile) await page.getByRole('button', { name: 'menu' }).click()
  await page.getByRole('button', { name: 'build' }).click()
  await page.getByText('Log back in').click()
  await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible()
})
