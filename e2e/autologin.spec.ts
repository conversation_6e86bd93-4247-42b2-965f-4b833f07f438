/* eslint-disable
playwright/no-conditional-expect,
playwright/no-conditional-in-test,
*/
import { expect } from '@playwright/test'
import { test } from 'e2e/pages.support'

test('autologin', async ({ guest: { page }, isMobile }) => {
  await page.goto(
    `/user/autologin?token=${encodeURIComponent(<string>process.env.E2E_PARAM_AUTOLOGIN_TOKEN)}`,
  )

  if (isMobile) {
    await expect(page.getByRole('button', { name: 'menu' })).toBeVisible()
  } else {
    await expect(page.getByRole('link', { name: 'view_stream Articles' })).toBeVisible()
  }
})
