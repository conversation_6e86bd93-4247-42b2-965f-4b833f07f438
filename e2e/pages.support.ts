import {
  test as baseTest,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>tOptions,
  <PERSON><PERSON>,
  expect,
  Page,
  PageAssertionsToHaveScreenshotOptions,
} from '@playwright/test'
import { randomBytes } from 'crypto'
import fs from 'fs'
import groupBy from 'lodash/groupBy'
import merge from 'lodash/merge'
import path from 'path'
import { topicColors } from '~/constants/colors'
import { ITopicMonitorsStoreArrItem } from '~/store/models/account/topicMonitors/TopicMonitorsStoreArrItem'
import { pages } from './pages.fixture'

export type Options = {
  isProduction: boolean
}

export type User =
  | 'customerDefault'
  | 'customerGlobal'
  | 'customerHero'
  | 'customerPForbidden'
  | 'customerPFullAccess'
  | 'customerPReadOnly'
  | 'guest'
  | 'staff'

export class MediaboardPage {
  page: Page
  user: User

  constructor(page: Page, user: User) {
    this.page = page
    this.user = user
  }

  async setupRoutes() {
    return Promise.all([
      this.page.route(
        ({ hostname, pathname }) => {
          return (
            (pathname.endsWith('/accounts/logout/') && !this.page.url().endsWith('/logout')) ||
            ['www.googletagmanager.com'].includes(hostname) ||
            pathname.startsWith('/ws')
          )
        },
        (route) => route.abort(),
      ),
      this.page.route('**/init/', async (route) => {
        const response = await this.page.request.fetch(route.request())
        const body = await response.json()

        await route.fulfill({
          response,
          body: JSON.stringify(
            merge(body, {
              topic_monitors: (body.topic_monitors as ITopicMonitorsStoreArrItem[]).map(
                (_, index) => {
                  return {
                    color: topicColors[index],
                    daily_count: 123,
                  }
                },
              ),
              user: {
                app_language: 'en',
                frontend_storage: {
                  appFeatures: {
                    crisisCommunication: false,
                  },
                },
              },
              workspace: {
                monitored_media: {
                  matrix: body.workspace.monitored_media.matrix.map(() => {
                    return [true, false, null, true, false, null, true]
                  }),
                },
              },
            }),
          ),
        })
      }),
      this.page.route('**/news-feed/', async (route) => {
        const response = await this.page.request.fetch(route.request())
        const body = await response.json()

        await route.fulfill({
          response,
          body: JSON.stringify(
            merge(body, {
              feed_stories: body.feed_stories.map(() => {
                return {
                  news_source: {
                    daily_ru: 27010,
                  },
                }
              }),
            }),
          ),
        })
      }),
      this.page.route(/\/staff\/customer\/\d+\/$/, async (route) => {
        const response = await this.page.request.fetch(route.request())
        const body = await response.json()

        await route.fulfill({
          response,
          body: JSON.stringify({
            ...body,
            users: body.users.slice(0, 3),
            workspaces: body.workspaces.slice(0, 3),
          }),
        })
      }),
    ])
  }

  static async create({
    browser,
    browserContext,
    cookies,
    user,
  }: {
    browser: Browser
    browserContext: BrowserContextOptions
    cookies?: {
      name: string
      value: string
      url: string
    }[]
    user: User
  }) {
    const context = await browser.newContext(browserContext)
    if (cookies) await context.addCookies(cookies)
    const page = new MediaboardPage(await context.newPage(), user)
    await page.setupRoutes()
    return page
  }
}

const hero2topicMonitor: {
  [key: number]: number[]
} = {
  1: [
    83443, // Penny
    83448, // Penny | neočištěné zmínky
  ],
  2: [83491, 83496],
  3: [83539, 83544],
  4: [83587, 83592],
  5: [89416, 89417],
  6: [90184, 90189],
  7: [90232, 90237],
  8: [90281, 90286],
  9: [90328, 90333],
  10: [90375, 90380],
  11: [92441, 92446],
  12: [92488, 92493],
  13: [94552, 94557],
}

const hero2dynamicSegments: {
  [key: number]: {
    campaignId: number
    emailId: number
  }
} = {
  1: {
    campaignId: 1067, // E2E Test Campaign: Toyota SEMA 2024 Prototypes
    emailId: 2645, // E2E Test: Exclusive Toyota Prototypes at SEMA 2024
  },
  2: { campaignId: 1068, emailId: 2646 },
  3: { campaignId: 1069, emailId: 2647 },
  4: { campaignId: 1070, emailId: 2648 },
  5: { campaignId: 1083, emailId: 2722 },
  6: { campaignId: 1152, emailId: 2911 },
  7: { campaignId: 1153, emailId: 2913 },
  8: { campaignId: 1154, emailId: 2914 },
  9: { campaignId: 1155, emailId: 2915 },
  10: { campaignId: 1156, emailId: 2916 },
  11: { campaignId: 1231, emailId: 3225 },
  12: { campaignId: 1232, emailId: 3226 },
  13: { campaignId: 1331, emailId: 3466 },
}

class HeroMediaboardPage extends MediaboardPage {
  dynamicSegments: {
    [key: string]: number | string
  }

  workspace: {
    topicMonitors: number[]
  }

  constructor(page: Page, user: User, cloneNumber: number) {
    super(page, user)

    this.dynamicSegments = hero2dynamicSegments[cloneNumber]

    this.workspace = {
      topicMonitors: hero2topicMonitor[cloneNumber],
    }
  }

  static async create({
    browser,
    browserContext,
    cloneNumber,
    user,
  }: {
    browser: Browser
    browserContext: BrowserContextOptions
    cloneNumber: number
    user: User
  }) {
    const context = await browser.newContext(browserContext)
    await context.grantPermissions(['clipboard-read'])
    const page = new HeroMediaboardPage(await context.newPage(), user, cloneNumber)
    await page.setupRoutes()
    return page
  }
}

async function setupUserFixture<T extends MediaboardPage>({
  browser,
  page,
  use,
  user,
}: {
  use: (page: T) => Promise<void>
} & (
  | {
      browser: Browser
      page?: never
      user: User
    }
  | {
      browser?: never
      page: T
      user?: never
    }
)) {
  const userPage =
    page ||
    ((await MediaboardPage.create({
      browser: browser!,
      browserContext: { storageState: `./e2e/.auth/${user}.json` },
      user: user!,
    })) as T)

  // https://github.com/mxschmitt/playwright-test-coverage/blob/main/e2e/baseFixtures.ts
  const context = userPage.page.context()

  if (process.env.COVERAGE) {
    await context.addInitScript(() =>
      window.addEventListener('beforeunload', () =>
        window.collectIstanbulCoverage(JSON.stringify(window.__coverage__)),
      ),
    )

    await context.exposeFunction('collectIstanbulCoverage', (coverageJSON: string) => {
      if (coverageJSON)
        fs.writeFileSync(
          path.join('e2e/artifacts/.nyc_output', `playwright_coverage_${generateUUID()}.json`),
          coverageJSON,
        )
    })
  }

  const authTokenCookie = await context
    .cookies()
    .then((cookies) => <Cookie>cookies.find(({ name }) => name === 'monitora-token'))

  if (authTokenCookie) {
    await context.setExtraHTTPHeaders({
      Authorization: `Token ${authTokenCookie.value}`,
    })
  }

  await use(userPage)

  if (process.env.COVERAGE) {
    for (const page of context.pages()) {
      // eslint-disable-next-line no-await-in-loop
      await page.evaluate(() => window.collectIstanbulCoverage(JSON.stringify(window.__coverage__)))
    }
  }

  await userPage.page.unrouteAll({ behavior: 'ignoreErrors' })
  await context.close()
}

export const test = baseTest.extend<
  Options & {
    customerDefault: MediaboardPage
    customerGlobal: MediaboardPage
    customerHero: HeroMediaboardPage
    customerPForbidden: MediaboardPage
    customerPFullAccess: MediaboardPage
    customerPReadOnly: MediaboardPage
    guest: MediaboardPage
    staff: MediaboardPage
  }
>({
  customerDefault({ browser }, use) {
    return setupUserFixture({
      browser,
      use,
      user: 'customerDefault',
    })
  },
  customerGlobal({ browser }, use) {
    return setupUserFixture({
      browser,
      use,
      user: 'customerGlobal',
    })
  },
  async customerHero({ browser }, use, testInfo) {
    const user = 'customerHero'

    const cloneNumber =
      parseInt(<string>process.env.E2E_RUNNER_SHARD_NUMBER) || testInfo.parallelIndex + 1

    return setupUserFixture({
      page: await HeroMediaboardPage.create({
        browser,
        browserContext: {
          storageState: `./e2e/.auth/${user}${cloneNumber}.json`,
        },
        cloneNumber,
        user,
      }),
      use,
    })
  },
  customerPForbidden({ browser }, use) {
    return setupUserFixture({
      browser,
      use,
      user: 'customerPForbidden',
    })
  },
  customerPFullAccess({ browser }, use) {
    return setupUserFixture({
      browser,
      use,
      user: 'customerPFullAccess',
    })
  },
  customerPReadOnly({ browser }, use) {
    return setupUserFixture({
      browser,
      use,
      user: 'customerPReadOnly',
    })
  },
  guest({ browser }, use) {
    return setupUserFixture({
      browser,
      use,
      user: 'guest',
    })
  },
  isProduction: [false, { option: true }],
  staff({ browser }, use) {
    return setupUserFixture({
      browser,
      use,
      user: 'staff',
    })
  },
})

function getFiles(entryPoint = '/', extRegExp = '.(js|tsx)$'): string[] {
  return fs
    .readdirSync(entryPoint, { withFileTypes: true })
    .reduce((output: string[], dirEntry) => {
      const entry = path.join(entryPoint, dirEntry.name)

      if (dirEntry.isDirectory()) {
        return output.concat(...getFiles(entry))
      }

      if (new RegExp(extRegExp).test(entry)) {
        return output.concat(entry)
      }

      return output
    }, [])
}

export function getPathnames() {
  const ignoredPathnames = [
    '/logout', // has a dedicated spec
    //
  ]

  const pagesRouterFilesToPathnames = getFiles('src/pages')
    .reduce((output: string[], page) => {
      if (page.includes('_')) {
        // Filter internal pages - `_app.js`, `_dojo/`, etc.
        return output
      }

      // Tweak output for production-like pathnames
      return output
        .concat(
          page
            .replace('analytcs', 'analytics')
            .replace('src/pages', '')
            .replace(/(index|)\.(js|tsx)/, '')
            .replace(/(\/.*)\/$/, '$1'),
        )
        .filter((pathname) => !ignoredPathnames.includes(pathname))
    }, [])
    .sort()

  const appRouterFilesToPathnames = getFiles('src/app').reduce((output: string[], page) => {
    if (!page.endsWith('page.tsx')) {
      // Filter files pages not ending with `page.tsx`
      return output
    }

    // Tweak output for production-like pathnames
    return output.concat(
      page
        .replace('page.tsx', '')
        .replace('src/app', '')
        .replace(/\(\w+\)\//, '')
        .replace(/(\/.*)\/$/, '$1'),
    )
  }, [])

  return groupBy(
    [...pagesRouterFilesToPathnames, ...appRouterFilesToPathnames].sort(),
    (pathname) => {
      return !/\[|dashboard|newsroom|push-notifications/.test(pathname) ||
        pages[pathname as keyof typeof pages] ||
        pathname.startsWith('/staff')
        ? 'compatible'
        : 'incompatible'
    },
  ) as {
    compatible: Array<keyof typeof pages>
    incompatible: Array<keyof typeof pages>
  }
}

export async function screenshot(
  page: Page,
  isAppRouter: boolean | undefined,
  options?: PageAssertionsToHaveScreenshotOptions,
  match = true,
) {
  if (!isAppRouter) {
    // We need to wait for all image resources to load
    // eslint-disable-next-line playwright/no-networkidle
    await page.waitForLoadState('networkidle')
  }

  // Try to favour mocking data with routes over modifying the DOM
  await page.evaluate(
    () => {
      const animatedImages: NodeListOf<HTMLImageElement> = document.querySelectorAll(
        'img[src="/static/404.gif"], img[src="/static/fine.webp"]',
      )

      animatedImages.forEach((image) => {
        image.style.opacity = '0'
      })

      document.querySelectorAll<HTMLElement>('[id^="particles-js-node-"]').forEach((element) => {
        element.style.opacity = '0'
      })

      document.querySelectorAll<HTMLElement>('[data-e2e="hide_row_table"]').forEach((element) => {
        element.style.visibility = 'collapse'
      })

      document.querySelectorAll<HTMLElement>('[data-e2e="media_card"]').forEach((mediaCard) => {
        mediaCard.textContent = '1234/9876'
      })

      document.querySelectorAll<HTMLElement>('[data-e2e="replace_content"]').forEach((element) => {
        element.textContent = 'content replaced by E2E test runner'
      })

      document.querySelectorAll<HTMLElement>('[data-e2e="counter"]').forEach((element) => {
        element.textContent = '123'
      })

      document.querySelectorAll<HTMLElement>('[data-e2e="iso_datetime"]').forEach((element) => {
        element.textContent = '1970-01-01 00:00'
      })

      document
        .querySelectorAll<HTMLElement>('[data-e2e="datetime+from_now"]')
        .forEach((element) => {
          element.textContent = '00. 00. 0000 00:00 - 0 days ago'
        })

      document.querySelectorAll<HTMLElement>('[data-e2e="hidden"]').forEach((element) => {
        element.style.display = 'none'
      })
    },
    [
      //
    ],
  )

  await expect(page.getByTestId('notification')).toBeHidden()

  const screenshotBuffer = await page.screenshot({
    animations: 'disabled',
    ...options,
  })

  if (match) {
    await expect(screenshotBuffer).toMatchSnapshot()
  }

  return screenshotBuffer
}

export function generateUUID(): string {
  return randomBytes(16).toString('hex')
}
