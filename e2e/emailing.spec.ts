/* eslint-disable
playwright/no-conditional-in-test,
*/
import { expect, Page } from '@playwright/test'
import { test as baseTest } from 'e2e/pages.support'
import { nanoid } from 'nanoid'

const test = baseTest.extend<{
  campaign: { name: string; page: Page }
  campaignCleanup: boolean
  draftEmail: { campaignName: string; name: string; page: Page }
  emailing: { page: Page }
}>({
  campaign: async ({ campaignCleanup, emailing: { page } }, use) => {
    const campaignName = `Test campaign ${nanoid()}`
    await page.getByRole('button', { name: 'New Campaign' }).click()
    await page.getByPlaceholder('Label').fill(campaignName)
    await page.getByRole('button', { name: 'Continue' }).click()
    const campaignResponsePromise = page.waitForResponse('**/emailing/campaign/')
    await page.getByRole('link', { name: campaignName }).click()
    const campaignResponse = await campaignResponsePromise
    const { id } = await campaignResponse.json()
    await use({ name: campaignName, page })
    if (campaignCleanup) await page.context().request.delete(`/api-proxy/emailing/campaign/${id}/`)
  },
  campaignCleanup: true,
  draftEmail: async ({ campaign: { name, page }, isMobile }, use) => {
    const emailName = `Test email ${nanoid()}`
    await page.getByRole('button', { name: 'add New Email' }).click()
    await page.getByText('Manual writing').click()
    await page.getByRole('button', { name: 'Continue arrow_forward_ios' }).click()
    await page.getByPlaceholder('Insert internal name of email').click()
    await page.getByPlaceholder('Insert internal name of email').fill(emailName)
    if (!isMobile) {
      await page.getByRole('button', { name: 'Toyota test <martin.stovicek+' }).click()
      await page.getByRole('list').getByText('Toyota test <martin.stovicek+').click()
    }
    await page.getByPlaceholder('Insert subject...').click()
    await page.getByPlaceholder('Insert subject...').fill('Test subject')
    await page.locator('.ProseMirror').click()
    await page.locator('.ProseMirror').fill('Wazzap!')
    await page.getByRole('button', { name: 'Save' }).click()
    await expect(page.getByText('Email was saved')).toBeVisible()
    await page.getByRole('button', { name: 'close' }).click()
    await page.getByRole('link', { name: 'chevron_left Back' }).click()
    await page.getByRole('link', { name: emailName }).click()
    await expect(page.getByText('Wazzap!')).toBeVisible()
    await use({ campaignName: name, name: emailName, page })
  },
  emailing: async ({ customerHero: { page } }, use) => {
    await page.goto('/emailing')
    await use({ page })
  },
})

test('add/edit/remove sender', async ({ emailing: { page }, isMobile }) => {
  await page
    .getByRole('link', { name: 'settings Settings' })
    .nth(isMobile ? 0 : 1)
    .click()
  await page.getByLabel('Name').click()
  await page.getByLabel('Name').fill('Another sender')
  await page.getByLabel('Name').press('Tab')
  await page.getByLabel('Email').fill('<EMAIL>')
  await page.getByRole('button', { name: 'Add' }).click()
  await expect(
    page.getByText('Another sender <<EMAIL>>'),
  ).toBeVisible()
  await page.getByTestId(`<EMAIL>`).click()
  await page.getByText('Edit', { exact: true }).click()
  await page.locator('#portal-modal #name-input').click()
  await page.locator('#portal-modal #name-input').fill('Another sender edit')
  await page.getByRole('button', { name: 'Save' }).click()
  await expect(
    page.getByText('Another sender edit <<EMAIL>>'),
  ).toBeVisible()
  await page.getByTestId(`<EMAIL>`).click()
  await page.getByText('Delete', { exact: true }).click()
  await page.getByRole('button', { name: 'Remove' }).click()
  await expect(
    page.getByText('Another sender edit <<EMAIL>>'),
  ).toBeHidden()
})

test.describe('add/edit/remove campaign', () => {
  test.use({
    campaignCleanup: false,
  })

  test('from list', async ({ campaign: { name, page } }) => {
    const editedCampaignName = `${name} edit`
    await page.getByRole('link', { name: 'Campaigns', exact: true }).click()
    await expect(page.getByRole('link', { name })).toBeVisible()
    await page.locator(`[data-name="${name}"]`).getByRole('button', { name: 'more_vert' }).click()
    await page.getByText('Edit Campaign').click()
    await page.getByPlaceholder('Label').fill(editedCampaignName)
    await page.getByRole('button', { name: 'Continue' }).click()
    await expect(page.getByRole('link', { name: editedCampaignName })).toBeVisible()
    await page
      .locator(`[data-name="${editedCampaignName}"]`)
      .getByRole('button', { name: 'more_vert' })
      .click()
    await page.getByText('Remove Campaign').click()
    await page.getByRole('button', { name: 'Remove' }).click()
    await expect(page.getByRole('link', { name: 'E E2E Test Campaign: Toyota' })).toBeVisible()
    await expect(page.getByRole('link', { name: editedCampaignName })).toBeHidden()
  })

  test('from detail', async ({ campaign: { name, page } }) => {
    const editedCampaignName = `${name} edit`
    await expect(page.getByRole('heading', { name: 'No emails yet' })).toBeVisible()
    await page.getByRole('button', { name: 'more_vert' }).click()
    await page.getByText('Edit Campaign').click()
    await page.getByPlaceholder('Label').fill(editedCampaignName)
    await page.getByRole('button', { name: 'Continue' }).click()
    await expect(page.getByRole('heading', { name: editedCampaignName })).toBeVisible()
    await page.getByRole('button', { name: 'more_vert' }).click()
    await page.getByText('Remove Campaign').click()
    await page.getByRole('button', { name: 'Remove' }).click()
    await expect(page.getByRole('link', { name: 'E E2E Test Campaign: Toyota' })).toBeVisible()
    await expect(page.getByRole('link', { name: editedCampaignName })).toBeHidden()
  })
})

test.fixme('draft email', async ({ draftEmail: { name, page }, isMobile }) => {
  // Replace text with custom HTML
  await page
    .locator('div')
    .filter({ hasText: /^Wazzap!$/ })
    .nth(2)
    .fill('')
  await page.getByRole('button', { name: 'add', exact: true }).click()
  await page.getByText('Custom HTML Code').click()
  await page.locator('#code-input').click()
  await page
    .locator('#code-input')
    .fill(
      '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>E2E Test: Exclusive Toyota Prototypes at SEMA 2024</title></head><body><table align="center" width="600" cellpadding="0" cellspacing="0" style="font-family:Arial,sans-serif;width:100%;max-width:600px;"><tr><td align="center" style="padding:10px 0;font-size:24px;font-weight:bold;">🚗 E2E Test: Exclusive Toyota Prototypes at SEMA 2024 🚙</td></tr><tr><td style="font-size:16px;line-height:1.5;color:#333333;padding:20px;"><p>Dear Enthusiast,</p><p>We are excited to announce that Toyota will be showcasing some exceptional prototypes at the SEMA 2024 show. This includes a convertible Land Cruiser, an off-road RAV4, and a rally-inspired GR86.</p><p>These prototypes highlight Toyota\'s commitment to innovation and performance, offering a glimpse into the future of automotive design and engineering.</p><p>Stay tuned for more updates and exclusive insights from the SEMA 2024 event.</p><p>Best regards,</p><p>The Toyota Team</p></td></tr><tr><td align="center" style="font-size:12px;color:#777777;padding:10px 0;">This email is for E2E testing purposes only. 📧</td></tr></table></body></html>',
    )
  if (!isMobile) await page.getByRole('button', { name: 'smartphone' }).click()
  await page.locator('#portal-modal').getByRole('button', { name: 'Save' }).click()
  await page.getByRole('button', { name: 'Save' }).click()
  await expect(page.getByText('Email was saved')).toBeVisible()
  await page.getByRole('button', { name: 'close' }).click()
  await page.getByRole('link', { name: 'chevron_left Back' }).click()
  await page.getByRole('link', { name: name }).click()
  await expect(
    page
      .locator('#wrapper-container iframe')
      .contentFrame()
      .getByRole('cell', { name: '🚗 E2E Test: Exclusive Toyota' }),
  ).toBeVisible()
})

test('duplicate email', async ({ draftEmail: { name, page } }) => {
  await page.getByRole('link', { name: 'chevron_left Back' }).click()
  await page.getByRole('button', { name: 'more_vert' }).nth(1).click()
  await page.getByText('Duplicate').click()
  await expect(page.getByRole('link', { name })).toHaveCount(2)
})

test.fixme(
  'copy to another campaign',
  async ({ draftEmail: { campaignName: draftCampaignName, name, page } }) => {
    await page.getByRole('link', { name: 'chevron_left Back' }).click()
    await page.getByRole('link', { name: 'Campaigns', exact: true }).click()
    const campaignName = `Test campaign ${nanoid()}`
    await page.getByRole('button', { name: 'New Campaign' }).click()
    await page.getByPlaceholder('Label').fill(campaignName)
    await page.getByRole('button', { name: 'Continue' }).click()
    await page.getByRole('link', { name: draftCampaignName }).click()
    await page.getByRole('button', { name: 'more_vert' }).nth(1).click()
    await page.getByText('Copy to another campaign').click()
    await page.getByPlaceholder('Filter').fill(campaignName)
    await page.getByText(campaignName, { exact: true }).click()

    // TODO: Fails to continue in the menu because of action calls on destroyed
    // MST nodes (campaign detail)
    await expect(page.getByText(`Email copied to ${campaignName}`)).toBeVisible()
    await page.getByRole('button', { name: 'close' }).click()

    await page.getByRole('link', { name: 'Campaigns', exact: true }).click()
    await page.getByRole('link', { name: campaignName }).click()
    await expect(page.getByRole('link', { name })).toBeVisible()
  },
)

test.fixme('add/edit/remove signature', async ({ draftEmail: { page } }) => {
  // add
  await page.getByRole('button', { name: 'add Add signature' }).click()
  await page.getByLabel('Name', { exact: true }).click()
  await page.getByLabel('Name', { exact: true }).fill('Martin Stovicek')
  await page.getByLabel('Name', { exact: true }).press('Tab')
  await page.getByLabel('Position').fill('Frontend Developer')
  await page.getByLabel('Position').press('Tab')
  await page.getByLabel('Email', { exact: true }).fill('<EMAIL>')
  await page.getByLabel('Email', { exact: true }).press('Tab')
  await page.getByLabel('Country selector').click()
  await page.getByText('Czech Republic').click()
  await page.locator('input[name="signature_phone"]').fill('+420 602 111 222')
  await page.getByRole('button', { name: 'Submit' }).click()
  await expect(page.getByText('Martin Stovicek')).toBeVisible()
  await expect(page.getByText('Frontend Developer')).toBeVisible()

  // edit
  await page.getByRole('button', { name: 'more_vert' }).click()
  await page.getByText('Edit', { exact: true }).click()
  await page.getByLabel('Position').click()
  await page.getByLabel('Position').fill('Frontend Clown')
  await page.getByRole('button', { name: 'Submit' }).click()
  await expect(page.getByText('Frontend Clown')).toBeVisible()

  // remove
  await page.getByRole('button', { name: 'more_vert' }).click()
  await page.getByText('Delete', { exact: true }).click()
  await page.getByRole('button', { name: 'Remove' }).click()

  // add again
  await page.getByRole('button', { name: 'add Add signature' }).click()
  await page.getByLabel('HTML').click()
  await page
    .getByLabel('HTML')
    .fill(
      '<table border=0 cellpadding=0 cellspacing=0 style=border-collapse:collapse;border:0;border-spacing:0;vertical-align:-webkit-baseline-middle;font-size:15px;line-height:20px;font-family:Arial;color:#202226;background-color:#fff;height:246.43px width=360><tr style=height:20px><td style=padding:0;margin:0;width:355px;height:20px height=8 colspan=4> <tr style=height:206.43px><td style=padding:0;margin:0;width:8px;height:206.43px height=200 width=8> <td style=width:24px;height:206.43px height=200 width=24><img alt=stripe height=200 src=https://mediaboard.com/wp-content/themes/monitora.cz/assets/mail/stripe.png width=24 style=display:block;border:0><td style=padding:0;margin:0;width:18px;height:206.43px height=200 width=18> <td style=width:305px;height:206.43px><table border=0 cellpadding=0 cellspacing=0 style=border-collapse:collapse;border:0;border-spacing:0;vertical-align:-webkit-baseline-middle;font-size:15px;line-height:20px;font-family:Arial;color:#202226;background-color:#fff;width:98.6842%;height:231.43px><tr style=height:28.6172px><td style=padding:0;margin:0;width:100%;height:28.6172px height=4> <tr style=height:22.9375px><td style=padding:0;margin:0;width:100%;height:22.9375px height=20><h2 style=font-size:15px;line-height:20px;font-family:Arial;color:#202226;font-weight:700;padding:0;margin:0><span style=font-size:14pt>Foo Bar</span></h2><tr style=height:17.625px><td style=padding:0;margin:0;width:100%;height:17.625px height=16><p style=font-size:12px;line-height:16px;font-family:Arial;color:#202226;font-weight:600;padding:0;margin:0><span style=font-size:10pt>Baz</span><tr style=height:17.625px><td style=padding:0;margin:0;width:100%;height:17.625px><p style=font-size:12px;line-height:16px;font-family:Arial;color:#202226;font-weight:600;padding:0;margin:0><span style=font-size:10pt> </span><tr style=height:17.625px><td style=padding:0;margin:0;width:100%;height:17.625px height=16><p style=font-size:12px;line-height:16px;font-family:Arial;color:#202226;font-weight:400;padding:0;margin:0><span style=font-size:10pt><EMAIL></span><tr style=height:17.625px><td style=padding:0;margin:0;width:100%;height:17.625px height=16><p style=font-size:12px;line-height:16px;font-family:Arial;color:#202226;font-weight:400;padding:0;margin:0><span style=font-size:10pt>M: 602 111 222</span><tr style=height:17.625px><td style=padding:0;margin:0;width:100%;height:17.625px height=16><p style=font-size:12px;line-height:16px;font-family:Arial;color:#202226;font-weight:400;padding:0;margin:0><span style=font-size:10pt><a href=https://www.mediaboard.com rel=noopener target=_blank style=line-height:16px;font-family:Arial;color:#202226;font-weight:400>www.mediaboard.com</a></span><tr style=height:49.75px><td style=padding:0;margin:0;width:100%;height:49.75px height=28 width=134><a href=https://www.mediaboard.com/ rel=noopener target=_blank title=https://www.mediaboard.com/ ><img alt="logo mediaboard"height=18 src=https://mediaboard.com/wp-content/uploads/2024/10/logo_mediaboard_blue-1.png width=135 style=display:block;border:0></a><tr style=height:22px><td style=padding:0;margin:0;width:100%;height:22px height=20 width=134><table border=0 cellpadding=0 cellspacing=0 style=border-collapse:collapse;border:0;border-spacing:0;vertical-align:-webkit-baseline-middle;font-size:15px;line-height:20px;font-family:Arial;color:#202226;background-color:#fff;width:30.3054%;height:20px><tr style=height:20px><td style=width:24.6369%;height:20px height=20 width=20><a href=https://www.facebook.com/monitoracz rel=noopener target=_blank style=font-size:12px;line-height:20px><img alt=facebook height=20 src=https://mediaboard.com/wp-content/themes/monitora.cz/assets/mail/icon_fb.png width=20 style=display:block;border:0></a><td style=width:13.4383%;height:20px height=20 width=12> <td style=width:24.6369%;height:20px height=20 width=20><a href=https://www.linkedin.com/company/mediaboardcomm/ rel=noopener target=_blank style=font-size:12px;line-height:20px><img alt=linkedin height=20 src=https://mediaboard.com/wp-content/themes/monitora.cz/assets/mail/icon_li.png width=20 style=display:block;border:0></a><td style=width:13.4383%;height:20px height=20 width=12> <td style=width:24.6369%;height:20px height=20 width=20><a href=https://twitter.com/mediaboard_com rel=noopener target=_blank style=font-size:12px;line-height:20px><img alt=x height=20 src=https://mediaboard.com/wp-content/themes/monitora.cz/assets/mail/icon_x.png width=20 style=display:block;border:0></a></table><tr style=height:20px><td style=padding:0;margin:0;width:100%;height:20px height=4> </table><tr style=height:20px><td style=padding:0;margin:0;width:355px;height:20px height=8 colspan=4> </table><p><a href=https://mediaboard.com/cs rel=noopener target=_blank><img alt=""height=138 src=https://mediaboard.com/wp-content/uploads/2024/10/dotaznik-emailing.jpg width=450></a>',
    )
  await page.getByRole('button', { name: 'Submit' }).click()
  await expect(page.locator('#signature iframe').contentFrame().getByText('Foo Bar')).toBeVisible()

  // remove to clean up
  await page.getByRole('button', { name: 'more_vert' }).click()
  await page.getByText('Delete', { exact: true }).click()
  await page.getByRole('button', { name: 'Remove' }).click()
})
