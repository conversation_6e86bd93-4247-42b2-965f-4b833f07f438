/* eslint-disable
playwright/no-conditional-in-test,
*/
import { expect, Locator, Page } from '@playwright/test'
import { test as baseTest } from 'e2e/pages.support'
import { nanoid } from 'nanoid'

type PartialFeedStory = {
  article_id: number
  public_access_url: string
  title: string
  topic_monitor?: {
    id: number
  }
}

const test = baseTest.extend<{
  feedPath: string
  feedStoryDetail: {
    data: PartialFeedStory
    page: Page
  }
  firstFeedStory: {
    data: PartialFeedStory
    locator: Locator
  }
  goto: boolean
}>({
  feedPath: async (
    {
      customerHero: {
        workspace: { topicMonitors },
      },
      goto,
    },
    use,
  ) => {
    await use(
      `/?lower_date=2024-08-01&topic_monitors=${topicMonitors[goto ? 1 : 0]}&upper_date=2024-08-31`,
    )
  },
  firstFeedStory: async ({ feedPath, customerHero: { page } }, use) => {
    const feedResponsePromise = page.waitForResponse('**/news-feed/')
    await page.goto(feedPath)
    const feedResponse = await feedResponsePromise

    const {
      feed_stories: [firstFeedStory],
    } = await feedResponse.json()

    await use({
      data: firstFeedStory,
      locator: page.locator(
        `#feed-item-focus-${firstFeedStory.article_id}${firstFeedStory.topic_monitor?.id}`,
      ),
    })
  },
  feedStoryDetail: async ({ firstFeedStory, goto, customerHero: { page } }, use) => {
    const linkLocator = firstFeedStory.locator
      .getByRole('link', { name: firstFeedStory.data.title })
      .first()

    const articleDetailResponsePromise = page.waitForResponse('**/article/*/detail/**')

    if (goto) {
      await page.goto((await linkLocator.getAttribute('href')) as string)
      const articleDetailResponse = await articleDetailResponsePromise
      await expect(page.getByTestId('skeleton')).toBeHidden()
      await use({ data: await articleDetailResponse.json(), page })
    } else {
      await linkLocator.click()
      await articleDetailResponsePromise
      await expect(page.getByTestId('skeleton')).toBeHidden()
      await use({ data: firstFeedStory.data, page })
    }
  },
  goto: false,
})

;[
  {
    description: 'after feed story click',
  },
  {
    description: 'after nav to feed story URL',
    goto: true,
  },
].forEach(({ description, goto }) => {
  test.describe(`${description}`, () => {
    test.slow()
    test.use({ goto })

    test('add/edit/remove note', async ({ feedStoryDetail: { page } }) => {
      // add
      await page.locator('#mainContainer').getByRole('button', { name: 'edit_note' }).click()
      await page.getByPlaceholder('Add note to article...').click()
      await page.getByPlaceholder('Add note to article...').fill('test note')
      await page.getByPlaceholder('Add note to article...').press('Enter')
      await expect(page.locator('#mainContainer').getByText('test note')).toBeVisible()

      // edit
      await page.locator('#mainContainer').getByRole('button', { name: 'edit_note' }).click()
      await page.getByPlaceholder('Add note to article...').click()
      await page.getByPlaceholder('Add note to article...').fill('test note edit')
      await page.getByRole('button', { name: 'Save' }).click()
      await expect(page.locator('#mainContainer').getByText('test note edit')).toBeVisible()

      // remove
      await page.locator('#mainContainer').getByRole('button', { name: 'edit_note' }).click()
      await page.getByRole('button', { name: 'Remove' }).click()
      await expect(page.locator('#mainContainer').getByText('test note edit')).toBeHidden()

      // add again
      await page.locator('#mainContainer').getByRole('button', { name: 'edit_note' }).click()
      await page.getByPlaceholder('Add note to article...').click()
      await page.getByPlaceholder('Add note to article...').fill('test note again')
      await page.getByRole('button', { name: 'Submit' }).click()

      // edit again
      await expect(page.locator('#mainContainer').getByText('test note again')).toBeVisible()
      await page.locator('#mainContainer').getByRole('button', { name: 'edit_note' }).click()
      await page.getByPlaceholder('Add note to article...').click()
      await page.getByPlaceholder('Add note to article...').press('ControlOrMeta+a')
      await page.getByPlaceholder('Add note to article...').fill('test note edit again')
      await page.getByPlaceholder('Add note to article...').press('Enter')
      await expect(page.locator('#mainContainer').getByText('test note edit again')).toBeVisible()

      // remove to clean up
      await page.locator('#mainContainer').getByRole('button', { name: 'edit_note' }).click()
      await page.getByRole('button', { name: 'Remove' }).click()
      await expect(page.locator('#mainContainer').getByText('test note edit')).toBeHidden()
    })

    test('add tag', async ({ feedStoryDetail: { page } }) => {
      await page.locator('#mainContainer').getByRole('button', { name: 'new_label' }).click()
      const tagName = `test tag ${nanoid(5)}`
      await page.getByPlaceholder('Tag name').fill(tagName)
      await page.getByPlaceholder('Tag name').press('Enter')
      await expect(page.locator('#mainContainer').getByText(tagName)).toBeVisible()
    })

    test('copy article text to clipboard', async ({
      browserName,
      feedStoryDetail: {
        data: { title },
        page,
      },
    }) => {
      test.skip(browserName === 'webkit', 'https://github.com/microsoft/playwright/issues/13037')
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Copy article to clipboard').click()
      const clipboardContent = await page.evaluate(() => navigator.clipboard.readText())
      // Replace non-breaking spaces in title with regular spaces to avoid false
      // positives. Known issue in browsers:
      // - https://stackoverflow.com/a/73584742
      // - https://bugzilla.mozilla.org/show_bug.cgi?id=1769534
      await expect(clipboardContent).toContain(title.replace(/\u00A0/g, ' '))
    })

    test('copy public url to clipboard', async ({
      browserName,
      feedStoryDetail: {
        data: { public_access_url },
        page,
      },
    }) => {
      test.skip(browserName === 'webkit', 'https://github.com/microsoft/playwright/issues/13037')
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Copy public URL to clipboard').click()
      const clipboardContent = await page.evaluate(() => navigator.clipboard.readText())
      await expect(clipboardContent).toBe(public_access_url)
    })

    test('send article email preview', async ({
      feedStoryDetail: {
        data: { title },
        page,
      },
      goto,
    }) => {
      test.fixme(goto, 'https://github.com/monitora-media/monitora-frontend/issues/3214')
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Send article').click()
      await page.getByRole('button', { name: 'preview' }).click()
      await expect(
        page
          .frameLocator('iframe[title="Email preview"]')
          .getByRole('link', { name: `1. ${title}` }),
      ).toBeVisible()
    })

    test('add to export', async ({
      feedStoryDetail: {
        data: { title },
        page,
      },
      firstFeedStory,
      isMobile,
    }) => {
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Add to export').click()
      await expect(page.getByText('Added to export.')).toBeVisible()
      await page
        .locator('div')
        .filter({ hasText: /^close$/ })
        .getByRole('button')
        .click()
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Add to export').click()
      await expect(page.getByText('Article is already in export.')).toBeVisible()
      await page.locator('#article-drawer').getByRole('button', { name: 'close' }).click()
      if (isMobile) await page.getByRole('button', { name: 'menu' }).click()
      await page.getByRole('link', { name: 'inbox Export' }).click()
      await expect(
        firstFeedStory.locator
          .getByRole('link', { name: title })
          .filter({ hasNotText: /\(full text; \d+ words\)/ }),
      ).toBeVisible()
      await page.getByRole('button', { name: 'delete' }).click()
      await page.getByRole('button', { name: 'Remove', exact: true }).click()
      await expect(page.getByRole('heading', { name: 'Export list is empty' })).toBeVisible()
    })

    test.describe('find similar articles', () => {
      test.use({
        feedPath:
          '/?lower_date=2024-08-01&query=Mazac%C3%AD%20tramvaj%20s%20formul%C3%AD%20%E2%80%9Ena%20z%C3%A1dech%E2%80%9C%20br%C3%A1zd%C3%AD%20Prahu.%20L%C3%A1k%C3%A1%20na%20adrenalinovou%20show&upper_date=2024-08-31',
      })

      test('on custom feed path', async ({ feedStoryDetail: { page } }) => {
        await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
        await page.getByRole('link', { name: 'compare Find similar articles' }).click()
        await expect(page.getByText('compare')).toBeVisible()
        await expect(
          page.getByRole('link', {
            name: 'TRAMVAJOVÝ NEWSLETTER – srpnový souhrn událostí u pražských tramvají',
          }),
        ).toBeVisible()
      })
    })

    test('add article to topic', async ({ customerHero: { page }, firstFeedStory, isMobile }) => {
      if (isMobile)
        await page
          .locator('[data-chip="true"]')
          .filter({ hasText: /Penny.*cancel/ })
          .click()
      await page.locator('ul').filter({ hasText: 'more_vertTopics' }).getByRole('button').click()
      await page.getByText('Add New Topic').click()
      await page.getByPlaceholder('Topic Name').click()
      const topicName = `test topic ${nanoid(5)}`
      await page.getByPlaceholder('Topic Name').fill(topicName)
      await page.getByPlaceholder('Topic Name').press('Enter')
      await expect(page.getByText(topicName)).toHaveCount(isMobile ? 2 : 3)
      await page.goBack()
      await firstFeedStory.locator
        .getByRole('link', { name: firstFeedStory.data.title })
        .first()
        .click()
      await expect(page.getByTestId('skeleton')).toBeHidden()
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Add article to topic').click()
      await page.getByTestId('force-add-to-topic-monitor').getByPlaceholder('Filter topics').click()
      await page
        .getByTestId('force-add-to-topic-monitor')
        .getByPlaceholder('Filter topics')
        .fill(topicName)
      await page.getByTestId('force-add-to-topic-monitor').getByText(topicName).click()
      await expect(page.getByText('The article was added to the')).toBeVisible()
      await page.locator('#article-drawer').getByRole('button', { name: 'close' }).click()
      const feedResponsePromiseOnTopicClick = page.waitForResponse('**/news-feed/')
      if (isMobile)
        await page
          .locator('[data-chip="true"]')
          .filter({ hasText: /Penny.*cancel/ })
          .click()
      await page.getByRole('link', { name: `T ${topicName}` }).click()
      await feedResponsePromiseOnTopicClick
      const feedResponsePromiseOnPageReload = page.waitForResponse('**/news-feed/')
      await page.reload()
      const feedResponse = await feedResponsePromiseOnPageReload
      const {
        filters: {
          topic_monitors: [{ value: topicId }],
        },
      } = await feedResponse.json()
      await expect(
        page
          .locator(`#feed-item-focus-${firstFeedStory.data.article_id}${topicId}`)
          .getByRole('link', { name: firstFeedStory.data.title })
          .filter({ hasNotText: /\(full text; \d+ words\)/ }),
      ).toBeVisible()
      await page
        .getByText(topicName)
        .nth(isMobile ? 0 : 1)
        .click()
      await page
        .getByPlaceholder('Filter')
        .nth(isMobile ? 0 : 1)
        .click()
      await page
        .getByPlaceholder('Filter')
        .nth(isMobile ? 0 : 1)
        .fill(topicName)
      await page.getByRole('dialog').getByRole('button', { name: 'more_vert' }).last().click()
      await page.getByRole('link', { name: 'settings Settings' }).click()
      await expect(page.getByRole('heading', { name: /Topics \(\d+\)/ })).toBeVisible()
      await page.getByRole('button', { name: 'more_vert' }).last().click()
      await page.getByText('Remove').click()
      await page.getByRole('button', { name: 'Remove' }).click()
    })

    test('report problem', async ({ feedStoryDetail: { page } }) => {
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Report problem').click()
      await page.getByText('Other', { exact: true }).click()
      await page.getByLabel('Reason').click()
      await page.getByLabel('Reason').fill('test reason')
      await page.getByRole('button', { name: 'Report problem' }).click()
      await expect(page.getByText('Article was reported')).toBeVisible()
    })

    test('unsubscribe news source', async ({ feedStoryDetail: { page } }) => {
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Unsubscribe news source').click()
      await page.getByRole('button', { name: 'Confirm' }).click()
      await expect(page.getByText('Source removed')).toBeVisible()
      // This is where we end our test because:
      // - On 'feed story click', the feed story stays open.
      // - On 'nav to feed story URL', feed story closes and navigates to `/`.
    })

    test('delete article', async ({
      feedStoryDetail: {
        data: { title },
        page,
      },
      firstFeedStory,
    }) => {
      await page.locator('#article-drawer').getByRole('button', { name: 'more_vert' }).click()
      await page.getByText('Delete Article').click()
      await page.getByRole('button', { name: 'Delete' }).click()
      await expect(page.getByText('Article Removed')).toBeVisible()
      await page.locator('#article-drawer').getByRole('button', { name: 'close' }).click()
      await expect(firstFeedStory.locator.getByRole('link', { name: title })).toBeHidden()
    })
  })
})
