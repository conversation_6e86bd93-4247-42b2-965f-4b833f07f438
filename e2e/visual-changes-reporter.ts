import { Reporter, TestCase, TestResult } from '@playwright/test/reporter'
import fs from 'fs/promises'
import queryString from 'query-string'

export default class VisualChangesReporter implements Reporter {
  visualChanges: {
    changeType: 'created' | 'modified'
    query: string
    titlePath: string
  }[]

  constructor() {
    this.visualChanges = []
  }

  async onBegin() {
    await fs.rm('visual_changes.json', { force: true })
  }

  onTestEnd(test: TestCase, result: TestResult) {
    if (result.status === 'failed' && result.error?.message?.includes('.png')) {
      this.visualChanges.push({
        changeType: result.error.message.includes(`A snapshot doesn't exist`)
          ? 'created'
          : 'modified',
        query: queryString.stringify({ testId: test.id }),
        titlePath: `${test.titlePath().filter(Boolean).join(' > ')}${
          result.retry ? ` (retry #${result.retry})` : ''
        }`,
      })
    }
  }

  async onEnd() {
    if (this.visualChanges.length) {
      await fs.writeFile('visual_changes.json', JSON.stringify(this.visualChanges))
    }
  }
}
