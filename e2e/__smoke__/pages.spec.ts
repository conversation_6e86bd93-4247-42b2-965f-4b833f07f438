/* eslint-disable
playwright/expect-expect,
playwright/no-conditional-expect,
playwright/no-conditional-in-test,
*/
import { expect, Page } from '@playwright/test'
import { pages, StaffTestType } from 'e2e/pages.fixture'
import { test as baseTest, getPathnames, screenshot, User } from 'e2e/pages.support'
import identityFn from 'lodash/identity'
import noopFn from 'lodash/noop'

const test = baseTest.extend<{
  asPath: string
  pathname: keyof typeof pages
}>({
  asPath: async ({ pathname, customerHero: { dynamicSegments } }, use) => {
    await use(
      pathname.replace(/\[(.*?)]/g, (_, cg) => {
        return dynamicSegments[cg] as string
      }),
    )
  },
  pathname: '/',
})

const { compatible, incompatible } = getPathnames()

compatible.forEach((pathname) => {
  const isStaffPage = pathname.startsWith('/staff')

  test.describe(`${pathname}`, { tag: ['@smoke'] }, () => {
    const {
      baseTestBody,
      customerDefaultTestBody,
      customerGlobalTestBody,
      customerHeroTestBody,
      customerPForbiddenTestBody,
      customerPFullAccessTestBody,
      customerPReadOnlyTestBody,
      isAppRouter,
      pathname: pathnameWithDynamicSegments,
      screenshotConfig,
      staffTestBody,
      tag,
    } = { tag: [], ...(pages[pathname] as StaffTestType) }

    test.use({ pathname: (pathnameWithDynamicSegments || pathname) as keyof typeof pages })

    if (screenshotConfig) {
      tag.push('@screenshot')
    }

    test(
      'as guest',
      {
        tag: screenshotConfig?.include?.includes('guest')
          ? tag
          : tag.filter((tag) => tag !== '@screenshot'),
      },
      async ({ asPath, guest: { page } }) => {
        await page.goto(asPath)

        if (baseTestBody) {
          await baseTestBody(page)
        } else {
          await expect(page.getByText('Log In').first()).toBeVisible()
        }

        if (screenshotConfig?.include?.includes('guest')) {
          await screenshot(page, isAppRouter, screenshotConfig.options)
        }
      },
    )

    async function customerStep(
      asPath: string,
      isAppRouter: boolean | undefined,
      page: Page,
      user: User,
      isMobile: boolean,
      testBody = customerDefaultTestBody,
    ) {
      if (isAppRouter) {
        await page.goto(asPath)
      } else {
        const initResponsePromise = page
          .waitForResponse('**/init/', { timeout: 5000 })
          // Ignore error if response is not found
          .catch(identityFn)

        await page.goto(asPath)
        await initResponsePromise
      }

      let teardown = noopFn

      if (isStaffPage) {
        await expect(page).toHaveURL(/\/f404/) // Faux 404
      } else if (testBody) {
        const result = await testBody(page, isMobile)
        teardown = typeof result === 'function' ? result : noopFn
      } else if (baseTestBody) {
        await baseTestBody(page)
      } else if (isMobile) {
        await expect(page.getByRole('button', { name: 'menu' })).toBeVisible()
      } else {
        await expect(page.getByRole('link', { name: 'view_stream Articles' }).first()).toBeVisible()
      }

      if (
        // Take screenshot if either:
        // 1. E2E_FORCE_CUSTOMER_SCREENSHOTS is 'all' or matches current user
        // 2. User is explicitly included in screenshotConfig.include
        // But skip screenshots for staff pages since they redirect to 404
        (['all', user].includes(<string>process.env.E2E_FORCE_CUSTOMER_SCREENSHOTS) ||
          (screenshotConfig && screenshotConfig.include?.includes(user))) &&
        !isStaffPage
      ) {
        await screenshot(page, isAppRouter, screenshotConfig?.options)
        await teardown()
      }
    }

    test('as customer', { tag }, async ({ asPath, customerDefault: { page, user }, isMobile }) => {
      await customerStep(asPath, isAppRouter, page, user, isMobile)
    })

    test(
      'as global customer',
      { tag },
      async ({ asPath, customerGlobal: { page, user }, isMobile }) => {
        await customerStep(asPath, isAppRouter, page, user, isMobile, customerGlobalTestBody)
      },
    )

    test(
      'as hero customer',
      { tag },
      async ({ asPath, customerHero: { page, user }, isMobile }) => {
        await customerStep(asPath, isAppRouter, page, user, isMobile, customerHeroTestBody)
      },
    )

    test.fixme(
      'as customer p:forbidden',
      { tag },
      async ({ asPath, customerPForbidden: { page, user }, isMobile }) => {
        await customerStep(asPath, isAppRouter, page, user, isMobile, customerPForbiddenTestBody)
      },
    )

    test(
      'as customer p:read-only',
      { tag },
      async ({ asPath, customerPReadOnly: { page, user }, isMobile }) => {
        await customerStep(asPath, isAppRouter, page, user, isMobile, customerPReadOnlyTestBody)
      },
    )

    test(
      'as customer p:full access',
      { tag },
      async ({ asPath, customerPFullAccess: { page, user }, isMobile }) => {
        await customerStep(asPath, isAppRouter, page, user, isMobile, customerPFullAccessTestBody)
      },
    )

    if (isStaffPage) {
      // Only run staff tests on `/staff` pages.
      test('as staff', { tag }, async ({ asPath, staff: { page } }) => {
        await page.goto(asPath)
        await staffTestBody(page)

        if (screenshotConfig) {
          await screenshot(page, isAppRouter, screenshotConfig.options)
        }
      })
    }
  })
})

test('incompatible pages', async ({}, testInfo) => {
  await testInfo.attach('These pages are currently not compatible, coming soon...', {
    body: incompatible.join('\n'),
  })
})
