import { expect } from '@playwright/test'
import { screenshot, test } from 'e2e/pages.support'

test('/emailing/unsubscribe', { tag: ['@smoke', '@screenshot'] }, async ({ guest: { page } }) => {
  // TODO: Real `token`, `recipientId` and `email`, so we could actually test an
  // unsubscribe.

  // This tests that the page renders what it should when provided with required
  // query params.

  await page.goto('/emailing/unsubscribe/abc/123/<EMAIL>/')
  await expect(page.getByText('<EMAIL>')).toBeVisible()
  await screenshot(page)
})
