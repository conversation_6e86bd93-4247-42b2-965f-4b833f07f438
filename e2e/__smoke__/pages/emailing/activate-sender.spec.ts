import { expect } from '@playwright/test'
import { screenshot, test } from 'e2e/pages.support'
import queryString from 'query-string'

test(
  '/emailing/activate-sender',
  { tag: ['@smoke', '@screenshot'] },
  async ({ guest: { page } }) => {
    // TODO: Real `email`, `sid` and `token`, so we could actually test a sender
    // activation.

    // This tests that the page renders what it should when provided with
    // required query params.

    await page.goto(
      `/emailing/activate-sender?${queryString.stringify({
        email: '<EMAIL>',
        sid: 'foo',
        token: 'bar',
      })}`,
    )

    await expect(page.getByText('Verify your email', { exact: true })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Activate' })).toBeVisible()
    await screenshot(page)
  },
)
