import { expect } from '@playwright/test'
import { screenshot, test } from 'e2e/pages.support'
import queryString from 'query-string'

test(
  '/user/reset-password/new',
  { tag: ['@smoke', '@screenshot'] },
  async ({ guest: { page } }) => {
    // TODO: Real `token` and `uid`, so we could actually test a password reset.

    // This smoke test, with invalid query param values, was created because we
    // ran into an issue when `getInitialPropsNoSSR` was added to the page and
    // rendering the page returned `null` because of this condition:
    // https://github.com/monitora-media/monitora-frontend/blob/5fcd19b66055c33230f3b1f3d589ea3bab56ac3c/helpers/hoc/getInitialPropsNoSSR.js#L48-L50

    // We reverted the change before merging. This tests that the page renders
    // what it should when provided with required query params.

    await page.goto(
      `/user/reset-password/new?${queryString.stringify({
        token: 'prdel',
        uid: 'prdel',
      })}`,
    )

    await expect(page.getByText('New password')).toBeVisible()
    await screenshot(page)
  },
)
