import execa from 'execa'
import fs from 'fs/promises'
import isDocker from 'is-docker'

process.on('SIGINT', () => {
  execa.sync('scripts/integration-tests/stop.sh', { stdio: 'inherit' })
  process.exit(0)
})

async function globalSetup() {
  if (!isDocker()) {
    await fs.rm('e2e/artifacts', { recursive: true, force: true })
    await fs.mkdir('e2e/artifacts', { recursive: true })

    try {
      await execa('scripts/integration-tests/start.js', { stdio: 'inherit' })
    } catch (ignoreErr) {
      await execa('scripts/integration-tests/stop.sh', { stdio: 'inherit' })
      throw new Error('Failed to start `integration-tests`')
    }
  }

  if (process.env.COVERAGE) {
    await fs.mkdir('e2e/artifacts/.nyc_output', { recursive: true })
  }
}

export default globalSetup
