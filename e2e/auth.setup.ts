import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, TestInfo, expect } from '@playwright/test'
import fs from 'fs/promises'
import os from 'os'
import { MediaboardPage, User, test as setup } from './pages.support'

async function setupUser({
  afterLogin,
  browser,
  cloneNumber,
  testInfo,
  user = 'guest',
  username,
}: {
  afterLogin?: (page: Page) => Promise<void>
  browser: Browser
  cloneNumber?: number
  testInfo: TestInfo
  user?: User
  username?: string
}) {
  const {
    project: {
      use: { baseURL: configBaseURL },
    },
  } = testInfo

  const baseURL = configBaseURL as string

  const { page } = await MediaboardPage.create({
    browser,
    browserContext: { baseURL },
    cookies: [
      { name: 'monitora-e2e-animation', value: 'disabled', url: baseURL },
      { name: 'monitora-language', value: 'en', url: baseURL },
    ],
    user,
  })

  await page.goto('/')

  if (username) {
    await page.fill('input[name=email]', username)
    await page.fill('input[name=password]', <string>process.env.E2E_PASSWORD)
    const loginResponsePromise = page.waitForResponse('**/accounts/login/')
    const initResponsePromise = page.waitForResponse('**/init/')
    await page.click('button[type=submit]')
    await loginResponsePromise
    await initResponsePromise
    await expect(page.getByRole('link', { name: 'view_stream Articles' })).toBeVisible()
  }

  const context = page.context()

  const authTokenCookie = await context
    .cookies()
    .then((cookies) => <Cookie>cookies.find(({ name }) => name === 'monitora-token'))

  if (authTokenCookie) {
    await context.setExtraHTTPHeaders({
      Authorization: `Token ${authTokenCookie.value}`,
    })
  }

  if (afterLogin) await afterLogin(page)

  await context.storageState({ path: `./e2e/.auth/${user}${cloneNumber ? cloneNumber : ''}.json` })
}

setup.describe('save sessions', () => {
  setup('as guest @prod', async ({ browser }, testInfo) => {
    await setupUser({ browser, testInfo })
  })

  setup('as customer', async ({ browser }, testInfo) => {
    await setupUser({
      browser,
      testInfo,
      user: 'customerDefault',
      username: process.env.E2E_USERNAME,
    })
  })

  setup('as global customer', async ({ browser }, testInfo) => {
    await setupUser({
      browser,
      testInfo,
      user: 'customerGlobal',
      username: process.env.E2E_USERNAME_GLOBAL,
    })
  })

  if (process.env.E2E_USERNAME_HERO_CLONES) {
    process.env.E2E_USERNAME_HERO_CLONES.split(',')
      .filter((_, index) => {
        if (process.env.CI) {
          return parseInt(<string>process.env.E2E_RUNNER_SHARD_NUMBER) - 1 === index
        }

        return (
          index >= 0 &&
          index <
            // https://github.com/microsoft/playwright/blob/733f9a2926a86374e71823150484c7c2c34d162f/packages/playwright/src/common/config.ts#L116
            Math.max(
              1,
              Math.floor(
                os.cpus().length *
                  (parseInt(<string>process.env.PLAYWRIGHT_CONFIG_WORKERS, 10) / 100),
              ),
            )
        )
      })
      .forEach((username) => {
        const cloneNumber = parseInt(<string>username.match(/hero_c(\d+)@/)?.[1])

        setup(`as @hero customer clone #${cloneNumber}`, async ({ browser }, testInfo) => {
          await setupUser({
            browser,
            cloneNumber,
            testInfo,
            user: 'customerHero',
            username,
            async afterLogin(page) {
              const {
                feed_stories: [mazackaFeedStory],
              } = await page
                .context()
                .request.post(`${process.env.API_URL}/news-feed/`, {
                  data: {
                    include_chart: true,
                    is_news_feed_medialist: false,
                    query:
                      'Mazací tramvaj s formulí „na zádech“ brázdí Prahu. Láká na adrenalinovou show',
                  },
                })
                .then((res) => res.json())

              const feedStoryUrl = new URL(
                `/article/${mazackaFeedStory.article_id}/${mazackaFeedStory.token}`,
                testInfo.project.use.baseURL,
              )

              process.env.E2E_PARAM_FEED_STORY_PATHNAME = feedStoryUrl.pathname
            },
          })
        })
      })
  }

  setup.fixme('customer p:forbidden', async ({ browser }, testInfo) => {
    await setupUser({
      browser,
      testInfo,
      user: 'customerPForbidden',
      username: process.env.E2E_USERNAME_FORBIDDEN,
    })
  })

  setup('customer p:read-only', async ({ browser }, testInfo) => {
    await setupUser({
      browser,
      testInfo,
      user: 'customerPReadOnly',
      username: process.env.E2E_USERNAME_READ_ONLY,
    })
  })

  setup('customer p:full access', async ({ browser }, testInfo) => {
    await setupUser({
      browser,
      testInfo,
      user: 'customerPFullAccess',
      username: process.env.E2E_USERNAME_FULL_ACCESS,
    })
  })

  setup('as staff', async ({ browser }, testInfo) => {
    await setupUser({
      browser,
      testInfo,
      user: 'staff',
      username: process.env.E2E_USERNAME_STAFF,
      async afterLogin(page) {
        const userId = 12061 // <EMAIL>

        const { autologin_token } = await page
          .context()
          .request.post(`${process.env.API_URL}/staff/user/${userId}/generate-autologin/`)
          .then((res) => res.json())

        process.env.E2E_PARAM_AUTOLOGIN_TOKEN = autologin_token
        process.env.E2E_PARAM_IMPERSONATE_USER_ID = userId.toString()
      },
    })
  })
})

setup.afterAll(async () => {
  await fs.appendFile(
    'e2e/artifacts/params.env',
    Object.entries(process.env)
      .filter(([key]) => key.startsWith('E2E_PARAM_'))
      .map(([key, value]) => `${key}=${value}\n`)
      .join(''),
  )
})
