import { expect } from '@playwright/test'
import { randomBytes } from 'crypto'
import { test } from 'e2e/pages.support'
import { nanoid } from 'nanoid'

test('sign up @prod @foreign', async ({ guest: { page } }) => {
  await page.goto('/')
  await page.getByRole('button', { name: 'Create an account for free' }).click()
  await page.getByLabel('First Name').click()
  await page.getByLabel('First Name').fill('Playwright')
  await page.getByLabel('First Name').press('Tab')
  await page.getByLabel('Last Name').fill('Test')
  await page.getByLabel('Last Name').press('Tab')
  await page.getByLabel('Email').fill(`playwright+sign_up_${nanoid(5)}@mediaboard.com`)
  await page.getByLabel('Email').press('Tab')
  await page.getByLabel('Password').fill(randomBytes(12).toString('base64'))
  await page.getByLabel('Country selector').click()
  await page.getByText('Czech Republic').click()
  // TODO: `await page.locator('input[name="phone"]').fill('+*********** 222')`
  // from codegen didn't work for this input :(
  await page.locator('input[name="phone"]').pressSequentially('*********')
  await page.locator('input[name="phone"]').press('Enter')
  await page.getByLabel('Company (Name or CRN)').click()
  await page.getByLabel('Company (Name or CRN)').fill('Mediaboard devel')
  await page.getByLabel('Company (Name or CRN)').press('Tab')
  await page.getByLabel('Position').press('Tab')
  await page.getByLabel('Partner Code (optional)').fill('<EMAIL>')
  const initResponsePromise = page.waitForResponse('**/init/')
  await page.getByLabel('Partner Code (optional)').press('Enter')
  await initResponsePromise
  await expect(page.getByText('Your account is being prepared')).toBeVisible()
})

test('sign up @prod @domestic', async ({ guest: { page } }) => {
  await page.goto('/?pal=cs')
  await page.getByRole('button', { name: 'Vytvořit účet zdarma' }).click()
  await page.getByLabel('Jméno').click()
  await page.getByLabel('Jméno').fill('Playwright')
  await page.getByLabel('Jméno').press('Tab')
  await page.getByLabel('Příjmení').fill('Test')
  await page.getByLabel('Příjmení').press('Tab')
  await page.getByLabel('Email').fill(`playwright+sign_up_${nanoid(5)}@mediaboard.com`)
  await page.getByLabel('Email').press('Tab')
  await page.getByLabel('Heslo').fill(randomBytes(12).toString('base64'))
  await page.getByLabel('Country selector').click()
  await page.getByText('Czech Republic').click() // TODO: Missing i18n
  // TODO: `await page.locator('input[name="phone"]').fill('+*********** 222')`
  // from codegen didn't work for this input :(
  await page.locator('input[name="phone"]').pressSequentially('*********')
  await page.locator('input[name="phone"]').press('Enter')
  // TODO: No Merk API in db-dev => no company prefill
  await page.getByLabel('Firma (název nebo IČO)').click()
  await page.getByLabel('Firma (název nebo IČO)').fill('Mediaboard devel')
  await page.getByLabel('Firma (název nebo IČO)').press('Escape') //dismiss autocomplete dropdown, that is empty
  await page.getByLabel('Partnerský kód (nepovinné)').click()
  await page.getByLabel('Partnerský kód (nepovinné)').fill('<EMAIL>')
  const initResponsePromise = page.waitForResponse('**/init/')
  await page.getByLabel('Partnerský kód (nepovinné)').press('Enter')
  await initResponsePromise
  await expect(page.getByText('Účet se připravuje')).toBeVisible()
})
