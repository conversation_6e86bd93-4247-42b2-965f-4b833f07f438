import { Trans } from '@lingui/react/macro'
import UsageTracker from '~/components/tariff/UsageTracker/UsageTracker'
import { observer } from '~/helpers/mst'

const ExportLimit = ({ appStore: { account }, plain }) => {
  const isTraditionalMediaActive = account.workspace.is_traditional_media_active
  const isSocialMediaActive = account.workspace.is_social_media_active

  if (!isTraditionalMediaActive && !isSocialMediaActive) {
    return null
  }

  const limits = []

  if (isTraditionalMediaActive) {
    limits.push({
      label: <Trans>Traditional Media</Trans>,
      limitReachedMessage: (
        <Trans>You have reached 30-day limit. You cannot export any new articles.</Trans>
      ),
      maxValue: account.workspace.limits.export_monthly_limit,
      value: account.workspace.limits.export_monthly_limit_used,
    })
  }

  if (!isTraditionalMediaActive && isSocialMediaActive) {
    limits.push({
      label: <Trans>Social Media</Trans>,
      limitReachedMessage: (
        <Trans>
          You have reached 30-day limit. You cannot export any new social media mentions.
        </Trans>
      ),
      maxValue: account.workspace.limits.social_export_monthly_limit,
      value: account.workspace.limits.social_export_monthly_limit_used,
    })
  }

  return <UsageTracker plain={plain} limits={limits} />
}

export default observer(ExportLimit)
