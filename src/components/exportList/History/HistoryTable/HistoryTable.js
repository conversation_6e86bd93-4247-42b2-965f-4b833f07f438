import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import copy from 'copy-html-to-clipboard'
import { styled } from 'styled-components'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { Table, TableBody, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import formatDate from '~/helpers/date/format'
import { observer } from '~/helpers/mst'

const Wrapper = styled.div`
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background: ${({ theme }) => theme.paper.background};
  border: 1px solid ${({ theme }) => theme.paper.border};
`

const ButtonLinkWrapper = styled.span`
  & a {
    color: #fff;
  }
`

const Header = styled(Tr)`
  height: 60px !important;
`

const Row = styled(Tr)`
  height: 55px !important;
`

const HistoryTable = ({
  list,
  appStore: {
    topics: { getTopicNameById },
    notification,
  },
}) => {
  return (
    <Wrapper>
      <div style={{ minWidth: '1000px' }}>
        <Table light>
          <TableBody>
            <Header>
              <Th>
                <Trans>Date</Trans>
              </Th>
              <Th>
                <Trans>Format</Trans>
              </Th>
              <Th>
                <Trans>Source</Trans>
              </Th>
              <Th>
                <Trans>Topic</Trans>
              </Th>
              <Th>
                <Trans>Period</Trans>
              </Th>
              <Th>
                <Trans>Articles</Trans>
              </Th>
              <Th width={200} />
            </Header>
            {list.map((item) => {
              return (
                <Row key={`${item.id}exporttable`}>
                  <Td>{formatDate(item.created, 'yyyy-MM-dd HH:mm')}</Td>
                  <Td>{item.file_format_text}</Td>
                  <Td>{item.source_text}</Td>
                  <Td>{getTopicNameById(item.topic_monitor)}</Td>
                  <Td>
                    {item.lower_date} - {item.upper_date}
                  </Td>
                  <Td>{item.generated_count}</Td>
                  <Td width={180}>
                    <Box textAlign="right" mr={4}>
                      {item.can_materialize && (
                        <MntrButton
                          bg="flat"
                          mr={2}
                          icon="view_stream"
                          href={`/export/materialize/${item.id}/`}
                          tooltip={<Trans>Show articles</Trans>}
                        />
                      )}

                      <ButtonLinkWrapper>
                        <MntrButton
                          mr={2}
                          bg="secondary"
                          href={item.url}
                          icon="download"
                          tooltip={<Trans>Download</Trans>}
                        />
                      </ButtonLinkWrapper>

                      <MntrButton
                        mr={2}
                        icon="more_vert"
                        bg="flat"
                        popup={(closePopup) => {
                          const actions = [
                            {
                              leftIcon: 'content_copy',
                              onClick: () => {
                                copy(item.url)
                                notification.add(
                                  t`Link has been copied to the clipboard.`,
                                  'success',
                                )
                              },
                              label: t`Copy link to clipboard`,
                            },
                          ]
                          return <MntrMenu menuItems={actions} closePopup={closePopup} />
                        }}
                        popupPlacement={'bottom-end'}
                      />
                    </Box>
                  </Td>
                </Row>
              )
            })}
          </TableBody>
        </Table>
      </div>
    </Wrapper>
  )
}

export default observer(HistoryTable)
