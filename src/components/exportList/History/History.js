import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import PageContent from '~/components/Content/Content'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Box, Heading } from '~/components/misc/Mntr'
import Pagination from '~/components/misc/Pagination/Pagination'
import { getConfigTagsTopicsDisabled } from '~/helpers/getActiveFiltersConfig'
import { observer } from '~/helpers/mst'
import HistoryTable from './HistoryTable/HistoryTable'

const PageExportHistory = ({
  appStore: {
    exportList,
    router: { redirectTo },
    account,
  },
}) => {
  const list = get(exportList, 'history.visibleList') || []

  return (
    <PageContent>
      <MntrFiltersBar
        filters={['Date']}
        backButton={
          account.workspace?.permissions.export.can_read
            ? { text: t`Export`, href: '/export' }
            : null
        }
      />
      <MntrActiveFilters config={getConfigTagsTopicsDisabled()} />
      <Box my={2} textAlign="center" position="relative">
        <Heading as="h1" fontSize={4}>
          <Trans>Export History</Trans> ({exportList.history.count})
        </Heading>
      </Box>
      <HistoryTable list={list} />
      <Box textAlign="center">
        <Pagination
          activePage={exportList.history.page}
          itemsCountPerPage={exportList.history.size}
          totalItemsCount={exportList.history.count}
          pageRangeDisplayed={5}
          onChange={(page) => {
            redirectTo({
              pathname: '/export/history',
              query: {
                page,
              },
            })
          }}
        />
      </Box>
    </PageContent>
  )
}

export default observer(PageExportHistory)
