import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const Wrapper = styled(MntrPaper)`
  max-width: 350px;
  margin: auto;
`

const ExportHistory = () => {
  return (
    <Wrapper>
      <Flex flexDirection="column" gap={2} p={2}>
        <Heading fontSize="14px" as="h3" color="black">
          <Trans>Exports to download</Trans>
        </Heading>
        <Text fontSize="14px" color="lightGrey">
          <Trans>The list of already exported articles can be downloaded without limitation.</Trans>
        </Text>
        <Text fontSize="14px" alignSelf="center">
          <MntrButton
            rounded
            bg="primary"
            icon="history"
            href="/export/history"
            label={<Trans>History</Trans>}
          />
        </Text>
      </Flex>
    </Wrapper>
  )
}

export default ExportHistory
