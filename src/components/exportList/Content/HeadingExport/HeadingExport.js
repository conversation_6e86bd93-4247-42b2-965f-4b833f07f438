import { Trans } from '@lingui/react/macro'
import ExportHistory from '~/components/exportList/Sidebar/ExportHistory/ExportHistory'
import { Box, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import * as format from '~/helpers/formatNumber'
import { observer } from '~/helpers/mst'

const HeadingExport = ({
  appStore: {
    viewport: { isMobile, isTablet },
  },
  feed,
}) => {
  const { getFeedList, isEmptyFeedList } = feed
  const isLoading = feed.loader.isLoading('feed-loading')

  return (
    <Box mb={3} mt={3}>
      <Box style={{ textAlign: 'center', position: 'relative' }}>
        <Heading as="h1" fontSize={4} mt={isEmptyFeedList ? 4 : 0} color="black">
          {isEmptyFeedList && !isLoading && <Trans>Export list is empty</Trans>}
          {!isEmptyFeedList &&
            !isLoading &&
            format.formatArticles(getFeedList && getFeedList.length)}
          {isLoading && <Trans>Loading...</Trans>}
        </Heading>
        {isEmptyFeedList && !isLoading && (
          <Box>
            <Text mb={4} mt={2} color="lightGrey">
              <Trans>You can add items in Articles section.</Trans>
            </Text>
            <MntrButton
              rounded
              bg="tertiary"
              href="/"
              label={<Trans>Articles</Trans>}
              icon="view_stream"
            />
            {(isMobile || isTablet) && (
              <Box mt={5}>
                <ExportHistory />
              </Box>
            )}
          </Box>
        )}
      </Box>
    </Box>
  )
}

export default observer(HeadingExport)
