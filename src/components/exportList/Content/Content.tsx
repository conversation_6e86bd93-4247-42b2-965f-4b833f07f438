import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import PageContent from '~/components/Content/Content'
import DashboardInspectorWrapper from '~/components/dashboards/DashboardInspectorWrapper/DashboardInspectorWrapper'
import HeadingExport from '~/components/exportList/Content/HeadingExport/HeadingExport'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import SortExport from '~/components/misc/ActionsBar/Sort/SortExport'
import ActionsBarSticky from '~/components/misc/ActionsBar/Sticky/Sticky'
import View from '~/components/misc/ActionsBar/View/View'
import Head from '~/components/misc/Head/Head'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import PortableExport from '~/components/misc/portable/PortableExport/PortableExport'
import PortableResend from '~/components/misc/portable/PortableResend/PortableResend'
import FeedMapActionsBar from '~/components/monitoring/FeedActionsBar/FeedMapActionsBar'
import events from '~/constants/gtm'
import staticFeeds from '~/constants/staticFeeds'
import { pushEvent } from '~/helpers/gtm'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/msts'
import ExportFeedList from './ExportFeedList/ExportFeedList'

const Content = observer(
  ({
    appStore: {
      monitoring: { feedMap, isFeedVisible, activeFeedMapItem },
      viewport,
    },
  }) => {
    // const { permissions } = account.workspace
    const { isMobile, isTablet } = viewport

    const feedId = staticFeeds.EXPORT_FEED
    const feed = feedMap.get(feedId)

    if (!feed) {
      return
    }

    const { exportStore } = feed

    const actions = [
      'download',
      'send',
      'add_sentiment',
      'add_tag',
      'remove_tag',
      'remove_from_export',
    ]

    if (exportStore.isMaterialized) actions.push('export')
    const isLoading = feed.loader.isLoading('feed-loading')

    return (
      <PageContent>
        <Head
          title={
            feed.getFeedList.length > 0 ? `(${feed.getFeedList.length}) ${t`Export`}` : t`Export`
          }
        />
        <Box>
          {!feed.isEmptyFeedList && (
            <ActionsBarSticky>
              <MntrPaper padding={isMobile ? 3 : 0}>
                <Flex justifyContent="space-between">
                  <Flex flex={1} justifyContent={isMobile ? 'space-between' : 'flex-start'}>
                    {isTablet && (
                      <MntrButton
                        bg="flat"
                        href="/export/history"
                        icon="history"
                        label={!isMobile ? <Trans>History</Trans> : undefined}
                      />
                    )}
                    <MntrButton
                      bg="flat"
                      icon="download"
                      label={!isTablet ? <Trans>Download</Trans> : undefined}
                      onClick={() => {
                        exportStore.markExportOpen(true)
                      }}
                    />
                    <MntrButton
                      bg="flat"
                      icon="send"
                      label={!isTablet ? <Trans>Send</Trans> : undefined}
                      onClick={() => {
                        exportStore.markResendOpen(true)
                      }}
                    />
                    {/* @ts-expect-error TODO refactor MntrButton */}
                    <MntrButton
                      bg="flat"
                      icon="delete"
                      label={!isTablet ? <Trans>Remove All</Trans> : undefined}
                      {...withModalRemove({
                        message: t`All articles will be removed from export.`,
                        onSubmit: () => {
                          exportStore.submitClearExportBasket(true)
                          pushEvent(events.EXPORT_REMOVED)
                        },
                      })}
                    />
                    {isMobile && <SortExport />}
                  </Flex>
                  {!isMobile && (
                    <Flex>
                      <SortExport />
                      <View />
                    </Flex>
                  )}
                </Flex>
              </MntrPaper>
            </ActionsBarSticky>
          )}
          <HeadingExport feed={feed} />
          <div style={{ display: 'none' }}>{feed.getFeedList.length}</div>

          {feed.getFeedList.length > 0 && !isLoading && (
            <FeedMapActionsBar
              feedId={feedId}
              actions={actions}
              items={feed.getFeedList}
              isExportList
              withSort={false}
              withView={false}
            />
          )}

          {isFeedVisible && !isLoading && feed.getFeedList.length > 0 && (
            // @ts-expect-error TODO refactor store for feed/story to TS
            <ExportFeedList feed={feed} feedId={feedId} list={feed.getFeedList} />
          )}

          <PortableExport feedId={feedId} />
          <PortableResend feedId={feedId} />

          {/* Article Inspector */}
          {activeFeedMapItem && <DashboardInspectorWrapper isDashboard={false} feedId={feedId} />}
        </Box>
      </PageContent>
    )
  },
)

export default Content
