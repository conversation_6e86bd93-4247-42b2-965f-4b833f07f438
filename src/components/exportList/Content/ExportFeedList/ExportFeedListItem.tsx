import {
  attachClosestEdge,
  type Edge,
  extractClosestEdge,
} from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { draggable, dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { useEffect, useRef, useState } from 'react'
import { styled } from 'styled-components'
import invariant from 'tiny-invariant'
import { DropIndicator } from '~/components/dnd/DropIndicator'
import FeedListItem from '~/components/monitoring/FeedList/FeedListItem/FeedListItem'
import { observer } from '~/helpers/msts'
import { routerPush, routerReplace } from '~/helpers/router'

export type TItem = { id: number }

const itemDataKey = Symbol('export-item')

type TItemData = { [itemDataKey]: true; itemId: TItem['id'] }

function getItemData(item: TItem): TItemData {
  return { [itemDataKey]: true, itemId: item.id }
}

export function isItemData(data: Record<string | symbol, unknown>): data is TItemData {
  return data[itemDataKey] === true
}

type ItemState =
  | {
      type: 'idle'
    }
  | {
      type: 'preview'
      container: HTMLElement
    }
  | {
      type: 'is-dragging'
    }
  | {
      type: 'is-dragging-over'
      closestEdge: Edge | null
    }

const idle: ItemState = { type: 'idle' }

const Item = styled.div`
  &.is-dragging {
    opacity: 0.4;
  }
`

const ExportFeedListItem = observer<{
  feedId: string
  index: number
}>(
  ({
    appStore: {
      account,
      monitoring: { inspector, setActiveFeed },
      router: { makeRouteWithQuery },
      viewport,
    },
    // @ts-expect-error TODO refactor store for feed/story to TS
    feed,
    feedId,
    index,
    // @ts-expect-error TODO refactor store for feed/story to TS
    item,
  }) => {
    const ref = useRef<HTMLDivElement | null>(null)
    const dragHandleRef = useRef<HTMLElement | null>(null)
    const [state, setState] = useState<ItemState>(idle)

    useEffect(() => {
      const element = ref.current
      invariant(element)
      const dragHandle = dragHandleRef.current
      invariant(dragHandle)
      return combine(
        draggable({
          element,
          dragHandle,
          getInitialData() {
            return getItemData(item)
          },
          onDragStart() {
            setState({ type: 'is-dragging' })
          },
          onDrop() {
            setState(idle)
          },
        }),
        dropTargetForElements({
          element,
          canDrop({ source }) {
            // not allowing dropping on yourself
            if (source.element === element) {
              return false
            }
            // only allowing export items to be dropped on me
            return isItemData(source.data)
          },
          getData({ input }) {
            const data = getItemData(item)
            return attachClosestEdge(data, {
              element,
              input,
              allowedEdges: ['top', 'bottom'],
            })
          },
          getIsSticky() {
            return true
          },
          onDragEnter({ self }) {
            const closestEdge = extractClosestEdge(self.data)
            setState({ type: 'is-dragging-over', closestEdge })
          },
          onDrag({ self }) {
            const closestEdge = extractClosestEdge(self.data)

            // Only need to update react state if nothing has changed.
            // Prevents re-rendering.
            setState((current) => {
              if (current.type === 'is-dragging-over' && current.closestEdge === closestEdge) {
                return current
              }
              return { type: 'is-dragging-over', closestEdge }
            })
          },
          onDragLeave() {
            setState(idle)
          },
          onDrop() {
            setState(idle)
          },
        }),
      )
    }, [item])

    const as = {
      pathname: `/article/${item.article_id}/${item.token}`,
      query: {
        ...(item.topic_monitor ? { topic_monitor: item.topic_monitor.id } : {}),
      },
    }

    return (
      <div style={{ position: 'relative' }}>
        <Item
          // Adding data-attribute as a way to query for this for our post drop flash
          data-dnd-item-id={item.id}
          className={state.type}
          ref={ref}
        >
          <FeedListItem
            dragHandleRef={dragHandleRef}
            key={`feed-item${item.article_id}${item.topic_monitor?.id}`}
            item={item}
            index={index}
            isExportList
            isMaterializedExportList={feed.exportStore.isMaterialized}
            moveToIndex={feed.exportItemMoveTo}
            openArticleInspector={() => {
              setActiveFeed(feedId)
              const currRoute = makeRouteWithQuery()
              routerReplace(currRoute, currRoute, { shallow: true })
              routerPush(currRoute, as, { shallow: true })
            }}
            setTag={(model: unknown) => feed.setTag(index, model)} // TODO: unknown
            removeTag={(model: unknown) => feed.removeTag(index, model)} // TODO: unknown
            setSentiment={(model: unknown) => feed.setSentiment(index, model)} // TODO: unknown
            fastExport={(fileFormatId: number) => feed.fastExport(fileFormatId, index)}
            feedId={feedId}
            addToExportBasket={() =>
              feed.exportStore.addToExportBasket(feed.feedStories[index], true, true)
            }
            removeItem={() => feed.removeItem(index)}
            removeSource={() => feed.removeSource(index)}
            removeFromExport={feed.removeFromExport}
            reportProblem={() => feed.reportProblem(index)}
            isChecked={feed.selector.isChecked}
            viewport={viewport}
            toggleCheck={feed.selector.toggleCheck}
            feedItemRoute={as}
            exportFileFormat={account.enums.export.export_file_format}
            translationLanguage={account.enums.translation_language}
            activeTranslation={inspector.translationLanguage}
            allowForceArticleToEmailReport={
              account.workspace.limits.allow_force_article_to_email_report
            }
            getPublished={feed.getPublished(index)}
            isFirst={index === 0}
            isVisibleDate={feed.isVisibleDate(index)}
            permissions={account.workspace?.permissions}
            setAveWeight={(weight: number) => feed.setAveWeight(index, weight)}
          />
        </Item>
        {state.type === 'is-dragging-over' && state.closestEdge ? (
          <DropIndicator edge={state.closestEdge} gap={'12px'} />
        ) : null}
      </div>
    )
  },
)

export default ExportFeedListItem
