import { autoScrollWindowForElements } from '@atlaskit/pragmatic-drag-and-drop-auto-scroll/element'
import { triggerPostMoveFlash } from '@atlaskit/pragmatic-drag-and-drop-flourish/trigger-post-move-flash'
import { extractClosestEdge } from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge'
import { getReorderDestinationIndex } from '@atlaskit/pragmatic-drag-and-drop-hitbox/util/get-reorder-destination-index'
import { monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { useEffect, useState } from 'react'
import { flushSync } from 'react-dom'
import { observer } from '~/helpers/msts'
import ExportFeedListItem, { isItemData, type TItem } from './ExportFeedListItem'

const ExportFeedList = observer<{
  feedId: string
}>(
  ({
    appStore: {
      monitoring: { inspector },
    },
    // @ts-expect-error TODO refactor store for feed/story to TS
    feed,
    feedId,
    // @ts-expect-error TODO refactor store for feed/story to TS
    list,
  }) => {
    const [items, setItems] = useState<TItem[]>(list)

    useEffect(() => {
      setItems(list)
    }, [list])

    useEffect(() => {
      return monitorForElements({
        canMonitor({ source }) {
          return isItemData(source.data)
        },
        onDrop({ location, source }) {
          const target = location.current.dropTargets[0]

          if (!target) {
            return
          }

          const sourceData = source.data
          const targetData = target.data

          if (!isItemData(sourceData) || !isItemData(targetData)) {
            return
          }

          const indexOfSource = items.findIndex((item) => item.id === sourceData.itemId)
          const indexOfTarget = items.findIndex((item) => item.id === targetData.itemId)

          if (indexOfTarget < 0 || indexOfSource < 0) {
            return
          }

          const closestEdgeOfTarget = extractClosestEdge(targetData)

          // Using `flushSync` so we can query the DOM straight after this line
          flushSync(() => {
            feed.exportItemMoveTo(
              sourceData.itemId,
              indexOfSource,
              getReorderDestinationIndex({
                startIndex: indexOfSource,
                indexOfTarget,
                closestEdgeOfTarget,
                axis: 'vertical',
              }),
            )

            inspector.resetLastOpenFeedItem()
          })

          // Being simple and just querying for the item after the drop.
          // We could use react context to register the element in a lookup,
          // and then we could retrieve that element after the drop and use
          // `triggerPostMoveFlash`. But this gets the job done.
          const element = document.querySelector(
            `[data-dnd-item-id="${sourceData.itemId}"] [data-dnd-flash]`,
          )

          if (element instanceof HTMLElement) {
            triggerPostMoveFlash(element)
          }
        },
      })
    }, [feed, inspector, items])

    useEffect(() => {
      return autoScrollWindowForElements()
    })

    return items.map((item, index) => {
      return (
        <ExportFeedListItem
          // @ts-expect-error TODO refactor store for feed/story to TS
          feed={feed}
          feedId={feedId}
          index={index}
          item={item}
          // @ts-expect-error TODO refactor store for feed/story to TS
          key={`feed-item${item.article_id}${item.topic_monitor?.id}`}
        />
      )
    })
  },
)

export default ExportFeedList
