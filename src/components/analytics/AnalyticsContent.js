import { t } from '@lingui/core/macro'
import { Plural, Trans, useLingui } from '@lingui/react/macro'
import { useState } from 'react'
import PageContent from '~/components/Content/Content'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import Capture from '~/components/misc/Capture/Capture'
import MediaArchiveMessage from '~/components/misc/MediaArchiveMessage/MediaArchiveMessage'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import TopicsMultiSelector from '~/components/misc/TopicsMultiSelector/TopicsMultiSelector'
import {
  ANALYTICS_PRIME_TAB_IDX,
  ANALYTICS_SOCIAL_MEDIA_TAB_IDX,
  ANALYTICS_TRADITIONAL_MEDIA_TAB_IDX,
  FEAT_REQ_SOCIAL_MEDIA,
  FEAT_REQ_TRADITIONAL_MEDIA,
  MAX_SELECTED_TOPICS,
} from '~/constants'
import getGranularityLabel from '~/helpers/charts/getGranularityLabel'
import makeGranularityMenu from '~/helpers/charts/makeGranularityMenu'
import { getConfigAnalytics } from '~/helpers/getActiveFiltersConfig'
import withModalRequestFeature from '~/helpers/modal/withModalRequestFeature'
import { observer } from '~/helpers/mst'
import PrimeAnalyticsContent from './PrimeAnalyticsContent'
import SocialMedia from './SocialMedia'
import TraditionalMedia from './TraditionalMedia'

function TabPanel({ children, value, index }) {
  return value === index && <Box>{children}</Box>
}

const HeadingLoader = ({ height }) => {
  return (
    <Flex style={{ height }} centerX>
      <Heading as="h1" fontSize={4}>
        <Trans>Loading...</Trans>
      </Heading>
    </Flex>
  )
}

const AnalyticsContent = ({
  appStore: {
    analytics: {
      filenameSuffix,
      granularity,
      isLoadingChartData,
      periodSubTitle,
      setGranularity,
      shouldSelectTopic,
      totalCount,
    },
    featureRequest: { hasRequested, sendFeatureRequest },
    filter,
    topics,
    router: { redirectTo },
    account,
    viewport: { isMobile },
  },
}) => {
  const { i18n } = useLingui()

  const topicIds =
    filter.data?.topic_monitors
      ?.split(',')
      .map((id) => Number(id.trim()))
      .filter(Boolean) || []

  const hasAnyPrimeScore =
    topics.list.some((item) => topicIds.includes(item.id) && item.data.has_prime_score) &&
    account.enums.prime_score.prime_score_definitions?.length > 0

  const handleSubmitTopicSelector = (model) => {
    return redirectTo(`/analytics?${filter.urlWithParam({ topic_monitors: model.join(',') })}`)
  }

  const isActiveTraditionalMedia = account.workspace?.is_traditional_media_active
  const isActiveSocialMedia = account.workspace?.is_social_media_active
  const [selectedTab, changeTab] = useState(0)

  const filters = [
    'Date',
    'Source',
    'LanguageMultiselect',
    'CountryMultiselect',
    'Author',
    'Sentiment',
    'Notes',
    'ArticleType',
  ]

  if (hasAnyPrimeScore) {
    filters.push('Prime')
  }

  return (
    <PageContent>
      <MntrFiltersBar filters={filters} />
      {shouldSelectTopic && !isLoadingChartData ? (
        (account.workspace?.permissions.analytics.can_read && (
          <Box ml={1}>
            <TopicsMultiSelector
              header={<Trans>Analytics</Trans>}
              subHeader={<Trans>Select at least one topic</Trans>}
              maxSelectedLimit={MAX_SELECTED_TOPICS}
              onSubmit={handleSubmitTopicSelector}
            />
          </Box>
        )) || <MediaArchiveMessage />
      ) : (
        <>
          <MntrActiveFilters config={getConfigAnalytics()} />
          <Flex flexDirection="column" gap={4}>
            <Flex flexDirection="column" gap={1} mt={4} textAlign="center">
              {isLoadingChartData ? (
                <HeadingLoader width={280} height={60} />
              ) : (
                <>
                  <Heading as="h1" fontSize={4}>
                    <Plural value={parseInt(totalCount)} one="# article" other="# articles" />
                  </Heading>
                  <Text color="lightGrey">{periodSubTitle}</Text>
                </>
              )}
            </Flex>
            {Boolean(totalCount) && (
              <>
                {isMobile ? (
                  <>
                    {hasAnyPrimeScore && <PrimeAnalyticsContent />}
                    {isActiveTraditionalMedia && <TraditionalMedia />}
                    {isActiveSocialMedia && <SocialMedia />}
                  </>
                ) : (
                  <>
                    <Flex justifyContent={'space-between'} centerY data-html2canvas-ignore>
                      <MntrButton
                        bg="flat"
                        hoverable
                        icon="filter_list"
                        label={(() => {
                          switch (selectedTab) {
                            case ANALYTICS_TRADITIONAL_MEDIA_TAB_IDX:
                              return t`Traditional Media`

                            case ANALYTICS_SOCIAL_MEDIA_TAB_IDX:
                              return t`Social Media`

                            case ANALYTICS_PRIME_TAB_IDX:
                              return t`PRIMe`

                            case 0:
                            default:
                              return t`All`
                          }
                        })()}
                        popup={(closePopup) => {
                          return (
                            <MntrMenu
                              closePopup={closePopup}
                              menuItems={[
                                {
                                  hoverVariant: 'secondary',
                                  label: t`All`,
                                  leftIcon:
                                    selectedTab === 0
                                      ? 'radio_button_checked'
                                      : 'radio_button_unchecked',
                                  onClick: () => {
                                    changeTab(0)
                                  },
                                },
                                hasAnyPrimeScore
                                  ? [
                                      {
                                        hoverVariant: 'secondary',
                                        label: `${t`PRIMe`}`,
                                        onClick() {
                                          changeTab(ANALYTICS_PRIME_TAB_IDX)
                                        },
                                        leftIcon:
                                          selectedTab === ANALYTICS_PRIME_TAB_IDX
                                            ? 'radio_button_checked'
                                            : 'radio_button_unchecked',
                                      },
                                    ]
                                  : {},
                                {
                                  disabled:
                                    !isActiveTraditionalMedia &&
                                    hasRequested(FEAT_REQ_TRADITIONAL_MEDIA),
                                  hoverVariant: 'secondary',
                                  label: `${t`Traditional Media`}${
                                    isActiveTraditionalMedia
                                      ? ''
                                      : hasRequested(FEAT_REQ_TRADITIONAL_MEDIA)
                                        ? ` (${t({
                                            id: 'featureRequest.Requested',
                                            message: 'Requested',
                                          })})`
                                        : ` ✨`
                                  }`,
                                  leftIcon:
                                    selectedTab === ANALYTICS_TRADITIONAL_MEDIA_TAB_IDX
                                      ? 'radio_button_checked'
                                      : 'radio_button_unchecked',
                                  ...(isActiveTraditionalMedia
                                    ? {
                                        onClick() {
                                          changeTab(ANALYTICS_TRADITIONAL_MEDIA_TAB_IDX)
                                        },
                                      }
                                    : {
                                        ...withModalRequestFeature({
                                          title: t`Request traditional media?`,
                                          onSubmit() {
                                            sendFeatureRequest(FEAT_REQ_TRADITIONAL_MEDIA)
                                          },
                                        }),
                                      }),
                                },
                                {
                                  disabled:
                                    !isActiveSocialMedia && hasRequested(FEAT_REQ_SOCIAL_MEDIA),
                                  hoverVariant: 'secondary',
                                  label: `${t`Social Media`}${
                                    isActiveSocialMedia
                                      ? ''
                                      : hasRequested(FEAT_REQ_SOCIAL_MEDIA)
                                        ? ` (${t({
                                            id: 'featureRequest.Requested',
                                            message: 'Requested',
                                          })})`
                                        : ` ✨`
                                  }`,
                                  leftIcon:
                                    selectedTab === ANALYTICS_SOCIAL_MEDIA_TAB_IDX
                                      ? 'radio_button_checked'
                                      : 'radio_button_unchecked',
                                  ...(isActiveSocialMedia
                                    ? {
                                        onClick() {
                                          changeTab(ANALYTICS_SOCIAL_MEDIA_TAB_IDX)
                                        },
                                      }
                                    : {
                                        ...withModalRequestFeature({
                                          title: t`Request social media?`,
                                          onSubmit() {
                                            sendFeatureRequest(FEAT_REQ_SOCIAL_MEDIA)
                                          },
                                        }),
                                      }),
                                },
                              ]}
                            />
                          )
                        }}
                        popupPlacement="bottom-start"
                        transformOrigin="0 0"
                      />
                      <Flex gap={2}>
                        <Capture
                          disabled={isLoadingChartData}
                          exportFilenameLabel="analytics"
                          exportFilenameSuffix={filenameSuffix}
                          imageFilenamePrefix="chart"
                        />
                        <MntrButton
                          bg="flat"
                          hoverable
                          icon="bar_chart"
                          label={i18n._(getGranularityLabel(granularity))}
                          popup={() => {
                            return (
                              <MntrMenu
                                menuItems={makeGranularityMenu({
                                  granularity,
                                  setGranularity,
                                })}
                              />
                            )
                          }}
                          popupPlacement="bottom-end"
                          transformOrigin="100% 0"
                        />
                      </Flex>
                    </Flex>
                    <TabPanel value={selectedTab} index={0}>
                      <Flex flexDirection="column" gap={4}>
                        {hasAnyPrimeScore && <PrimeAnalyticsContent />}
                        {isActiveTraditionalMedia && <TraditionalMedia />}
                        {isActiveSocialMedia && <SocialMedia />}
                      </Flex>
                    </TabPanel>
                    <TabPanel value={selectedTab} index={1}>
                      {isActiveTraditionalMedia && <TraditionalMedia />}
                    </TabPanel>
                    <TabPanel value={selectedTab} index={2}>
                      {isActiveSocialMedia && <SocialMedia />}
                    </TabPanel>
                    {hasAnyPrimeScore && (
                      <TabPanel value={selectedTab} index={3}>
                        <PrimeAnalyticsContent />
                      </TabPanel>
                    )}
                  </>
                )}
              </>
            )}
          </Flex>
        </>
      )}
    </PageContent>
  )
}

export default observer(AnalyticsContent)
