import { msg } from '@lingui/core/macro'
import { ave as aveHint, grp as grpHint, socialShares } from '~/components/OurChart/hints'
import { Box, Flex, Grid } from '~/components/misc/Mntr'
import dataTypes from '~/constants/analytics'
import analyticsSectionTypes from '~/constants/analyticsSectionTypes'
import { observer } from '~/helpers/mst'
import AnalyticsChart from './AnalyticsChart'

const TraditionalMedia = ({
  appStore: {
    account,
    analytics: { isMultiTopic },
    viewport: { isMobile },
  },
}) => {
  const commonSwitches = { sentiment: true, fullScreen: true, print: true }
  const allowAveLimit = account.workspace.limits.allow_ave

  const timelineChartsConfig = [
    {
      id: 'count',
      stackSeries: !isMultiTopic,
      switches: { ...commonSwitches, type: true, granularity: true },
    },
    {
      id: 'grp',
      dataLabel: 'GRP',
      hint: grpHint,
      stackSeries: !isMultiTopic,
      switches: { ...commonSwitches, type: true, granularity: true },
    },
    {
      id: 'reach',
      dataLabel: msg`Reach`,
      stackSeries: !isMultiTopic,
      switches: { ...commonSwitches, type: true, granularity: true },
    },
    {
      id: 'adjusted_reach_pl',
      dataLabel: msg`Reach`,
      stackSeries: !isMultiTopic,
      switches: { fullScreen: true, print: true, type: true, granularity: true },
    },
    ...(allowAveLimit
      ? [
          {
            id: 'ave',
            dataLabel: account.user.currency.text,
            hint: aveHint,
            unit: account.user.currency.code,
          },
        ]
      : []),
    ...(!isMultiTopic && allowAveLimit
      ? [
          {
            id: 'count_grp_ave',
          },
        ]
      : []),
    {
      id: 'social_shares',
      hint: socialShares,
      options: { xAxis: { type: 'datetime' } },
      type: 'line',
      stackSeries: !isMultiTopic,
      switches: { ...commonSwitches, type: true, granularity: true },
    },
  ]

  const traditionalMediaPieKeys = Object.keys(dataTypes).filter(
    (key) => dataTypes[key].analyticsSectionToShow === analyticsSectionTypes.TRADITIONAL_MEDIA_PIE,
  )

  const traditionalMediaTopsBarKeys = Object.keys(dataTypes).filter(
    (key) => dataTypes[key].analyticsSectionToShow === analyticsSectionTypes.TRADITIONAL_MEDIA_BAR,
  )

  const traditionalMediaTopsBarCategoryKeys = Object.keys(dataTypes).filter(
    (key) =>
      dataTypes[key].analyticsSectionToShow ===
      analyticsSectionTypes.TRADITIONAL_MEDIA_BAR_CATEGORY,
  )

  const traditionalMediaPieIsMultitopicKeys = Object.keys(dataTypes).filter(
    (key) =>
      dataTypes[key].analyticsSectionToShow === analyticsSectionTypes.TRADITIONAL_MEDIA_PIE &&
      dataTypes[key].isMultiTopic,
  )

  const renderAnalyticsCharts = (keys, additionalProps = {}) =>
    keys.map((key, index) => {
      return (
        <AnalyticsChart
          key={index}
          id={key}
          filename={`analytics-${additionalProps.filenamePrefix || ''}${key}`}
          {...additionalProps}
          unit={dataTypes[key].chartPreset.unit || additionalProps.unit}
        />
      )
    })

  return (
    <Flex
      flexDirection="column"
      gap={4}
      className="capture-bundle"
      data-capture-label="traditional-media"
    >
      {timelineChartsConfig.map((config, index) => {
        return (
          <AnalyticsChart
            key={index}
            allowNullDataPoints
            container={<Box />}
            filename={`analytics-timeline-${config.id}`}
            {...config}
            options={{ xAxis: { type: 'datetime' } }}
          />
        )
      })}

      {isMultiTopic ? (
        <Grid cols={[1, 1, 2]} gap={4}>
          {renderAnalyticsCharts(traditionalMediaPieIsMultitopicKeys, {
            height: isMobile ? 250 : 400,
            switches: commonSwitches,
            unit: account.user.currency.code,
          })}
          {renderAnalyticsCharts(traditionalMediaTopsBarCategoryKeys, {
            stackSeries: !isMultiTopic,
            switches: { ...commonSwitches, fullView: true },
          })}
        </Grid>
      ) : (
        <>
          <AnalyticsChart
            container={<Box />}
            filename="analytics-top_authors"
            id="top_authors"
            switches={commonSwitches}
          />

          <Grid cols={[1, 1, 2]} gap={4}>
            {renderAnalyticsCharts(traditionalMediaTopsBarKeys, { switches: commonSwitches })}
            {renderAnalyticsCharts(traditionalMediaTopsBarCategoryKeys, {
              stackSeries: !isMultiTopic,
              switches: { ...commonSwitches, fullView: true },
            })}
          </Grid>
          <Grid cols={[1, 2, 2, 3]} gap={4}>
            {renderAnalyticsCharts(traditionalMediaPieKeys, {
              height: isMobile ? 250 : 400,
              switches: { fullScreen: true, print: true },
            })}
          </Grid>
          <AnalyticsChart
            container={<Box />}
            filename="analytics-keywords"
            id="keywords"
            switches={{ fullScreen: true, print: true }}
          />
          <AnalyticsChart
            container={<Box />}
            filename="analytics-most_common_terms"
            height={isMobile ? 300 : 500}
            id="most_common_terms"
            switches={{ fullScreen: true, print: true }}
          />
        </>
      )}
    </Flex>
  )
}

export default observer(TraditionalMedia)
