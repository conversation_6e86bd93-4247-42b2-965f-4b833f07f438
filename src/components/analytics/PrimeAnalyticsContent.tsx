import { prime as primeHint } from '~/components/OurChart/hints'
import { Box, Flex, Grid } from '~/components/misc/Mntr'
import { ObservedFC, observer } from '~/helpers/msts'
import AnalyticsChart from './AnalyticsChart'

const PrimeAnalyticsContent: ObservedFC = ({
  appStore: {
    analytics: { isMultiTopic },
  },
}) => {
  const primeCharts01 = [
    {
      id: 'prime_score__total_value',
      // eslint-disable-next-line no-constant-binary-expression
      hint: false && primeHint, // TODO: enable prime hint
      stackSeries: !isMultiTopic,
    },
    {
      id: 'pie__prime_score_category_type__total_value',
      // eslint-disable-next-line no-constant-binary-expression
      hint: false && primeHint, // TODO: enable prime hint
    },
  ]

  const primeCharts02 = [
    {
      id: 'prime_score__total_value__category_type',
      // eslint-disable-next-line no-constant-binary-expression
      hint: false && primeHint, // TODO: enable prime hint
    },
    {
      id: 'top_n__prime_score__total_value',
      // eslint-disable-next-line no-constant-binary-expression
      hint: false && primeHint, // TODO: enable prime hint
    },
  ]

  return (
    <Flex
      flexDirection="column"
      gap={4}
      className="capture-bundle"
      data-capture-label="traditional-media"
    >
      <AnalyticsChart
        key="timeline__prime_score__total_value"
        allowNullDataPoints
        container={<Box />}
        filename={`analytics-timeline__prime_score__total_value`}
        id="prime_score"
        options={{ xAxis: { type: 'datetime' } }}
      />

      {!isMultiTopic && (
        <>
          <Grid cols={[1, 1, 2]} gap={4}>
            {primeCharts01.map((config, index) => {
              return (
                <AnalyticsChart
                  key={index}
                  allowNullDataPoints
                  container={<Box />}
                  filename={`analytics-timeline-${config.id}`}
                  {...config}
                  height={300}
                />
              )
            })}
          </Grid>

          <AnalyticsChart
            key="timeline__prime_score__relevance"
            allowNullDataPoints
            container={<Box />}
            filename={`analytics-timeline__prime_score__relevance`}
            id="timeline__prime_score__relevance"
            options={{ xAxis: { type: 'datetime' } }}
          />

          <Grid cols={[1, 1, 2]} gap={4}>
            {primeCharts02.map((config, index) => {
              return (
                <AnalyticsChart
                  key={index}
                  allowNullDataPoints
                  container={<Box />}
                  filename={`analytics-timeline-${config.id}`}
                  {...config}
                />
              )
            })}
          </Grid>
        </>
      )}
    </Flex>
  )
}

export default observer(PrimeAnalyticsContent)
