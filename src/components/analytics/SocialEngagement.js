import { Plural, useLingui } from '@lingui/react/macro'
import { styled } from 'styled-components'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import MediaIcon from '~/components/misc/MediaIcon/MediaIcon'
import { Box, Col, Flex, Grid } from '~/components/misc/Mntr'
import socialNetwork from '~/constants/socialNetwork'
import getSocialIcon from '~/helpers/getSocialIcon'
import { observer } from '~/helpers/mst'
import socialIconsOrder from '~/helpers/socialIconsOrder'

const PlatformContent = styled(Box)`
  min-height: 75px;
  min-width: ${({ isFacebook }) => (isFacebook ? '175px' : 'auto')};
  width: ${({ isFacebook }) => (isFacebook ? '175px' : 'auto')};
  line-height: 1.8;
  opacity: ${({ isLoading }) => (isLoading ? 0.33 : 1)};
`

const FacebookReactions = styled(Box)`
  min-height: 75px;
  opacity: ${({ isLoading }) => (isLoading ? 0.33 : 1)};

  ${Flex} {
    display: inline-flex;
    margin-top: 1px;
  }

  img {
    display: block;
  }
`

const PlatformItemRaw = ({
  appStore: {
    account: { enums },
  },
  borderless,
  isLoading,
  platform,
}) => {
  const { i18n } = useLingui()
  const Wrapper = borderless ? 'div' : MntrPaper
  const isFacebook = platform.id === socialNetwork.FACEBOOK

  return (
    <Wrapper>
      <MntrPaperToolbar
        icon={
          <MediaIcon
            color={enums.news_source_category.find(({ id }) => id === platform.id).color}
            id={platform.id}
            type="social_news_source_category"
          />
        }
        title={platform.text}
      />
      <Flex flexWrap="nowrap">
        <PlatformContent color="grey" isFacebook={isFacebook} isLoading={isLoading} pb={2} px={2}>
          {Object.keys(platform.interactions)
            .sort((a, b) => socialIconsOrder.indexOf(a) - socialIconsOrder.indexOf(b))
            .map((interaction) => {
              switch (interaction) {
                case 'like':
                case 'heart':
                case 'favourite':
                  return (
                    <Box key={`${platform.id}-${interaction}`}>
                      <Plural
                        value={platform.interactions[interaction]}
                        one="# like"
                        other="# likes"
                      />
                    </Box>
                  )
                case 'retweet':
                  return (
                    <Box key={`${platform.id}-${interaction}`}>
                      <Plural
                        value={platform.interactions[interaction]}
                        one="# retweet"
                        other="# retweets"
                      />
                    </Box>
                  )
                case 'comment':
                  return (
                    <Box key={`${platform.id}-${interaction}`}>
                      <Plural
                        value={platform.interactions[interaction]}
                        one="# comment"
                        other="# comments"
                      />
                    </Box>
                  )
                case 'view':
                  return (
                    <Box key={`${platform.id}-${interaction}`}>
                      <Plural
                        value={platform.interactions[interaction]}
                        one="# view"
                        other="# views"
                      />
                    </Box>
                  )
                case 'dislike':
                  return (
                    <Box key={`${platform.id}-${interaction}`}>
                      <Plural
                        value={platform.interactions[interaction]}
                        one="# dislike"
                        other="# dislikes"
                      />
                    </Box>
                  )
                case 'share':
                  return (
                    <Box key={`${platform.id}-${interaction}`}>
                      <Plural
                        value={platform.interactions[interaction]}
                        one="# share"
                        other="# shares"
                      />
                    </Box>
                  )
                default:
                  return ''
              }
            })}
        </PlatformContent>
        {isFacebook && (
          <FacebookReactions isLoading={isLoading} mt={-1} px={2} py={1}>
            {Object.keys(platform.interactions)
              .filter((interaction) => {
                return (
                  platform.interactions[interaction] > 0 &&
                  !['like', 'comment', 'share'].includes(interaction)
                )
              })
              .sort((a, b) => socialIconsOrder.indexOf(a) - socialIconsOrder.indexOf(b))
              .map((interaction) => {
                return (
                  <>
                    <Flex centerY key={`${platform.id}-${interaction}`}>
                      <Box>
                        <img
                          alt={interaction}
                          height={24}
                          src={getSocialIcon(interaction)}
                          width={24}
                        />
                      </Box>
                      <Box ml={1} mr={2} color="grey">
                        {i18n.number(platform.interactions[interaction])}
                      </Box>
                    </Flex>
                  </>
                )
              })}
          </FacebookReactions>
        )}
      </Flex>
    </Wrapper>
  )
}

const PlatformItem = observer(PlatformItemRaw)

const getColumnSize = (platformId) => {
  if (platformId === socialNetwork.FACEBOOK) {
    return [12, 12, 8, 8, 4]
  } else if (platformId === socialNetwork.YOUTUBE) {
    return [12, 6, 4, 4, 2]
  } else {
    return [12, 6, 4, 4, 2]
  }
}

const Container = ({ borderless, children }) => {
  if (borderless) {
    return (
      <Flex gap={3} flexWrap="wrap">
        {children}
      </Flex>
    )
  } else {
    return (
      <Grid cols={[12]} gap={4}>
        {children}
      </Grid>
    )
  }
}
const SocialEngagement = ({ borderless, data, isLoadingChartData }) => {
  return (
    <Container borderless={borderless}>
      {data.map((platform) => {
        if (borderless) {
          return (
            <Box key={platform.id} flex={platform.id === socialNetwork.FACEBOOK ? 1 : 0}>
              <PlatformItem
                borderless={borderless}
                isLoading={isLoadingChartData}
                platform={platform}
              />
            </Box>
          )
        }

        return (
          <Col key={platform.id} colSize={getColumnSize(platform.id)}>
            <PlatformItem
              borderless={borderless}
              isLoading={isLoadingChartData}
              platform={platform}
            />
          </Col>
        )
      })}
    </Container>
  )
}

export default SocialEngagement
