import { cloneElement } from 'react'
import { Box } from '~/components/misc/Mntr'
import OurChart from '~/components/OurChart/OurChart'
import dataTypes from '~/constants/analytics'
import { observer } from '~/helpers/mst'

const AnalyticsChart = ({
  appStore: {
    analytics: { charts, isLoadingChartData, isMultiTopic, periodSubTitle },
    account: { enums },
  },
  id,
  container = <Box />,
  ...otherProps
}) => {
  if (
    dataTypes[id].dataSets.some((dataset) => {
      return (
        isMultiTopic ? enums.analytics.multi_topic_charts : enums.analytics.single_topic_charts
      ).includes(dataset)
    })
  ) {
    return cloneElement(container, {
      children: (
        <OurChart
          {...dataTypes[id].chartPreset}
          clickRoute="/"
          id={id}
          isLoading={isLoadingChartData}
          store={charts.get(id)}
          subtitle={periodSubTitle}
          title={dataTypes[id].title.primary}
          titleForSentiment={dataTypes[id].title.bySentiment}
          titleWithSuffix
          view="advanced"
          {...otherProps}
        />
      ),
    })
  }

  return null
}

export default observer(AnalyticsChart)
