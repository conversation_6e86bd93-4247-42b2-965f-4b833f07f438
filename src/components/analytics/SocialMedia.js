import { Trans } from '@lingui/react/macro'
import { engagementRate, influenceScore, socialInteractions } from '~/components/OurChart/hints'
import { Box, Flex, Grid } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'
import AnalyticsChart from './AnalyticsChart'
import SocialEngagement from './SocialEngagement'

function SocialMedia({
  appStore: {
    analytics: { isLoadingChartData, isMultiTopic, socialEngagement },
    viewport: { isMobile },
  },
}) {
  return (
    <Flex
      flexDirection="column"
      gap={4}
      className="capture-bundle"
      data-capture-label="social-media"
    >
      {socialEngagement && (
        <Box className="capture-single">
          {isMobile && (
            <Box my={2} color="heading" textAlign="center" textTransform="uppercase">
              <Trans>Engagement summary</Trans>
            </Box>
          )}
          <SocialEngagement data={socialEngagement.data} isLoadingChartData={isLoadingChartData} />
        </Box>
      )}
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-social_network__count"
        id="social_news_source_category__count"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
      />
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-social_network__influence_score"
        hint={influenceScore}
        id="social_news_source_category__influence_score"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
      />
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-social_network__social_shares"
        hint={socialInteractions}
        id="social_news_source_category__social_shares"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
      />
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-social_article_type__count"
        id="social_article_type__count"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
      />
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-social_article_type__influence_score"
        hint={influenceScore}
        id="social_article_type__influence_score"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
      />
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-social_article_type__social_shares"
        hint={socialInteractions}
        id="social_article_type__social_shares"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
      />
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-influence_score"
        hint={influenceScore}
        id="influence_score"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
        type="line"
      />
      <AnalyticsChart
        allowNullDataPoints
        container={<Box />}
        filename="analytics-timeline-engagement_score"
        hint={engagementRate}
        id="engagement_rate"
        options={{ xAxis: { type: 'datetime' } }}
        stackSeries={!isMultiTopic}
        switches={{
          sentiment: true,
          type: true,
          granularity: true,
          fullScreen: true,
          print: true,
        }}
        type="line"
      />
      <AnalyticsChart
        container={<Box />}
        filename="analytics-top_profiles"
        id="top_n__social_network_profile__count"
        switches={{ sentiment: true, fullScreen: true, print: true }}
      />
      <Grid cols={[1, 2, 2]} gap={4}>
        {!isMultiTopic && (
          <AnalyticsChart
            filename="analytics-top_hashtags"
            id="top_n__hashtag__count"
            switches={{ sentiment: true, fullScreen: true, print: true }}
          />
        )}
        {isMultiTopic ? (
          <>
            <AnalyticsChart
              filename="analytics-pie-pie__influence_score"
              height={isMobile ? 250 : 400}
              hint={influenceScore}
              id="pie__influence_score"
              switches={{ fullScreen: true, print: true }}
            />
            <AnalyticsChart
              filename="analytics-pie-pie__engagement_rate"
              height={isMobile ? 250 : 400}
              hint={engagementRate}
              id="pie__engagement_rate"
              switches={{ fullScreen: true, print: true }}
            />
            <AnalyticsChart
              filename="analytics-social"
              id="social_categories"
              stackSeries={!isMultiTopic}
              switches={{ sentiment: true, fullScreen: true, print: true }}
            />
          </>
        ) : (
          <>
            <AnalyticsChart
              filename="analytics-social"
              id="social_categories"
              stackSeries={!isMultiTopic}
              switches={{ sentiment: true, fullScreen: true, print: true }}
            />
            <AnalyticsChart
              filename="analytics-pie-pie__social_news_source_category__count"
              height={isMobile ? 250 : 400}
              id="pie__social_news_source_category__count"
              switches={{ fullScreen: true, print: true }}
            />
            <AnalyticsChart
              filename="analytics-pie-pie__social_article_type__count"
              height={isMobile ? 250 : 400}
              id="pie__social_article_type__count"
              switches={{ fullScreen: true, print: true }}
            />
          </>
        )}
      </Grid>
    </Flex>
  )
}

export default observer(SocialMedia)
