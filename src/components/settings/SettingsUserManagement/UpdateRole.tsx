import { t } from '@lingui/core/macro'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { ObservedFC, observer } from '~/helpers/msts'
import withModalUpdateRole, { IInitialValues } from './withModalUpdateRole'

interface IUpdateRoleProps {
  onSubmit: (values: Record<string, unknown>) => Promise<void>
  initialValues: IInitialValues
  isAdd?: boolean
}

const UpdateRole: ObservedFC<IUpdateRoleProps> = ({
  appStore: {
    account: { enums },
    viewport: { isTablet },
  },
  onSubmit,
  initialValues,
}) => {
  const label = t`Change role`

  return (
    // @ts-expect-error: modal

    <MntrButton
      rounded
      bg="secondary"
      icon="settings"
      {...(!isTablet ? { label } : { tooltip: label })}
      {...withModalUpdateRole({
        onSubmit,
        modalTitle: label,
        initialValues,
        userRoles: enums.workspaces.user_role,
      })}
    />
  )
}

export default observer(UpdateRole)
