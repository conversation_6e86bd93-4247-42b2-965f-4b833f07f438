import { msg, t } from '@lingui/core/macro'
import { Field } from 'react-final-form'
import InstructionsPanel from '~/components/emailing/forms/InstructionsPanel'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import Permissions from '~/components/tariff/Permissions/Permissions'
import { IUserRoleArrItem } from '~/store/models/account/enums/UserRoleArrItem'
import { IPermissionItem } from './SettingsUserManagement'

export const ROLE_CUSTOM = 4

export interface IInitialValues extends Record<string, string | number | IPermissionItem> {}

interface IWithModalUpdateRoleProps {
  onSubmit: (values: Record<string, unknown>) => Promise<void>
  initialValues: IInitialValues
  isAdd?: boolean
  userRoles: IUserRoleArrItem[]
  modalTitle?: string | React.ReactNode
}

const withModalUpdateRole = ({
  onSubmit,
  isAdd,
  initialValues,
  userRoles,
  modalTitle = t`Change role`,
}: IWithModalUpdateRoleProps) => {
  const instructions = {
    title: msg`For each functionality choose one of three levels:`,
    points: [
      { title: msg`Full access:`, point: msg`Everything enabled` },
      {
        title: msg`Read only:`,
        point: msg`Cannot delete articles, run manual sentiment, create/edit topics or reports, change account settings, delete TV/radio stories, or edit CRM info.`,
      },

      {
        title: msg`Forbidden:`,
        point: msg`No access to monitoring feeds, archive search, analytics, topic or report settings, crisis communications, medialist, or user settings.`,
      },
    ],
  }

  return {
    modalTitle,
    modal: (closeModal: () => void) => {
      const submitForm = async (values: Record<string, unknown>) => {
        await onSubmit(values)
          .then(() => closeModal())
          .catch((errors: Record<string, string>) => errors)
      }

      const formSchema: IFormSchemaItem[] = [
        {
          name: 'emails',
          type: 'textarea',
          multiline: true,
          label: t`User emails`,
          helpText: t`Separated by space, newline, comma, or semicolon.`,
          disabled: !isAdd,
        },
        {
          name: 'role',
          label: t`Role`,
          adapter: 'select',
          //   @ts-expect-error TODO refactor MntrForm and select
          items: userRoles.map((role: IUserRoleArrItem) => ({
            label: role.text,
            value: role.id,
          })),
        },
        {
          customComponent: ({ form, values }) => {
            if (values.role !== ROLE_CUSTOM) {
              return null // Skip rendering Permissions if role is not custom
            }
            return <Field name="permissions" form={form} component={Permissions} withNullOption />
          },
        },
        {
          customComponent: ({ values }) => {
            if (values.role !== ROLE_CUSTOM) {
              return null // Skip rendering Permissions info if role is not custom
            }
            return (
              <MntrPaper>
                <InstructionsPanel instructions={instructions} title={t`How permissions work`} />
              </MntrPaper>
            )
          },
        },
      ]

      return (
        <MntrForm
          contentPadding={3}
          schema={formSchema}
          onSubmit={submitForm}
          onCancel={closeModal}
          initialValues={initialValues}
        />
      )
    },
  }
}

export default withModalUpdateRole
