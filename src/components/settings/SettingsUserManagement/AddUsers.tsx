import { t } from '@lingui/core/macro'
import Mntr<PERSON>utton from '~/components/misc/MntrButton/MntrButton'
import { ObservedFC, observer } from '~/helpers/msts'
import withModalUpdateRole, { IInitialValues } from './withModalUpdateRole'

interface IUpdateRoleProps {
  onSubmit: (values: Record<string, unknown>) => Promise<void>
  initialValues: IInitialValues
  isAdd?: boolean
}

const AddUsers: ObservedFC<IUpdateRoleProps> = ({
  appStore: {
    account: { enums, workspace },
    viewport: { isTablet },
  },
  onSubmit,
  initialValues,
}) => {
  const { user_accounts_limit, user_accounts_limit_used } = workspace.limits
  const limitUsed = user_accounts_limit_used >= user_accounts_limit

  const label = t`Add users`

  return (
    // @ts-expect-error: modal
    <MntrButton
      disabled={limitUsed}
      rounded
      icon="group_add"
      bg="secondary"
      {...(!isTablet ? { label } : { tooltip: label })}
      {...withModalUpdateRole({
        modalTitle: label,
        isAdd: true,
        onSubmit,
        initialValues,
        userRoles: enums.workspaces.user_role,
      })}
    />
  )
}

export default observer(AddUsers)
