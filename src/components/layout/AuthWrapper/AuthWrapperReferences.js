import color from 'color'
import { useCallback, useState } from 'react'
import { styled } from 'styled-components'
import { Box, Text } from '~/components/misc/Mntr'
import AuthDivider from './modules/AuthDivider'
import {
  ContainerRight,
  DescriptionWrapper,
  HeadingGradient,
  Pattern,
  Section,
  SlideshowWrapper,
  TextDescription,
} from './styles/StyledAuthWrapper'

const AlignCenter = styled(Box)`
  text-align: center;
`

const AuthWrapperReferences = ({ dividerDelta, animated, slides }) => {
  const [current, setCurrent] = useState(Math.floor(Math.random() * slides.length))
  const length = slides.length
  const currentSlideContent = slides[current]

  const nextSlide = useCallback(() => {
    setCurrent(current === length - 1 ? 0 : current + 1)
  }, [current, length])

  const textColor = color(currentSlideContent.color).darken(0.8).toString()

  return (
    <ContainerRight bg={currentSlideContent.color} onClick={() => nextSlide()}>
      <AuthDivider
        animated={animated}
        bg={currentSlideContent.color}
        opacityLevel={currentSlideContent.opacityLevel}
        dividerDelta={dividerDelta}
      />
      <Pattern bg={currentSlideContent.color} />
      <Section>
        <Box style={{ position: 'relative', zIndex: 2 }}>
          <SlideshowWrapper animated={animated}>
            <Box
              className="slides"
              position="relative"
              height="250px"
              width="250px"
              margin="0 auto"
            >
              {slides.map((slide) => {
                return (
                  <Box
                    className={slide.slide === current ? 'slideshow active' : 'slideshow'}
                    key={slide.slide.toString()}
                    top="0px"
                    height="250px"
                    width="250px"
                    position="absolute"
                  >
                    {slide.body(animated)}
                  </Box>
                )
              })}
            </Box>
            <AlignCenter>
              <DescriptionWrapper minheight="auto" margintop={70}>
                <TextDescription color={textColor}>
                  {currentSlideContent.description}
                </TextDescription>
              </DescriptionWrapper>
              <HeadingGradient
                mt={40}
                fontSize={4}
                color={color(textColor).darken(0.4).toString()}
                gradient={color(currentSlideContent.color).darken(0.5).toString()}
                animated={animated}
              >
                {currentSlideContent.name}
              </HeadingGradient>
              <Box>
                <Text fontSize="18px" color={textColor}>
                  {currentSlideContent.occupation}
                </Text>
              </Box>
              <Box>
                <Text fontSize="18px" color={textColor}>
                  {currentSlideContent.domain}
                </Text>
              </Box>
            </AlignCenter>
          </SlideshowWrapper>
        </Box>
      </Section>
    </ContainerRight>
  )
}

export default AuthWrapperReferences
