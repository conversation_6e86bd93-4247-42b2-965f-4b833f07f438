import { t } from '@lingui/core/macro'
import { BlobsList } from '~/components/layout/AuthWrapper/modules/Blob'
import { Box } from '~/components/misc/Mntr'

const referencesSlidesCom = () => [
  {
    slide: 0,
    color: '#ffbbfc',
    opacityLevel: 0.7,
    name: '<PERSON><PERSON>',
    occupation: t`Head of External and Internal Communication`,
    domain: '<PERSON><PERSON><PERSON>',
    description: t`In three years with Mediaboard, our experience has been exceptional. Their professionalism, wide range of services, and top-notch quarterly and annual analyses are highly valuable. We recommend Mediaboard for quality and reliability.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-130, 160, '#ffd1fd', -74, 2],
              [150, -180, '#ffd1fd', 78, 1],
              [240, -20, '#ffd1fd', 130, 1],
              [-150, 20, '#ffd1fd', 342, 2],
              [160, 160, '#ffd1fd', 0, 1],
            ]}
          />
          <img
            className="item full-width "
            src="/static/auth/references/renata_maierl.png"
            alt="Renata Maierl"
          />
        </Box>
      )
    },
  },
  {
    slide: 1,
    color: '#aed6e3',
    opacityLevel: 0.7,
    name: 'Vaclav Koukolicek',
    occupation: t`External Communication Manager`,
    domain: 'Coca-Cola HBC',
    description: t`Mediaboard transformed our communication at Coca-Cola HBC! A daily essential for top-notch media monitoring, with a user-friendly interface and insightful analytics. Their exceptional customer support makes it a joy to work with Mediaboard.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-140, 190, '#c3e1ea', -74, 2],
              [160, -170, '#c3e1ea', 84, 1],
              [260, -10, '#c3e1ea', 105, 2],
              [-150, 20, '#c3e1ea', 342, 2],
              [160, 160, '#c3e1ea', 0, 1],
            ]}
          />
          <img
            className="item full-width"
            src="/static/auth/references/vaclav_koukolicek.png"
            alt="Václav Koukolíček"
          />
        </Box>
      )
    },
  },
]

export default referencesSlidesCom
