import { msg, t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { BlobsList } from '~/components/layout/AuthWrapper/modules/Blob'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import CategoryTypeIcon from '~/components/misc/CategoryTypeIcon/CategoryTypeIcon'
import { Box, Flex, Text } from '~/components/misc/Mntr'

const blobColors = {
  color1: '#0b61ff',
  color2: '#ba66bf',
  color3: '#46bfea',
  color4: '#7ccb6b',
  color5: '#e9c561',
  color6: '#5983b3',
  color7: '#f8f7e5',
}

const featuresSlides = [
  {
    slide: 0,
    title: msg`Monitoring`,
    description: msg`Monitor newspapers, magazines, radios, TV stations or the entire online world. Reach out to media, react, track, analyze, and build your brand.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-100, 280, blobColors.color3, 275],
              [220, -180, blobColors.color2, 78],
              [300, -110, blobColors.color5, 105],
              [-150, 20, blobColors.color4, 342, 2],
              [-190, 270, blobColors.color1, 277],
            ]}
          />
          <div>
            <Box className="item anim-down">
              <Box mb={3} transform="rotate(-4deg)">
                <MntrPaper>
                  <MntrPaperToolbar title={t`Traditional Media`} />
                  <Flex width={1} mx={2}>
                    <Box width={1 / 3}>
                      <Flex centerY>
                        <CategoryTypeIcon categoryType={{ color: '#13cdf0', icon: 'web' }} />
                        <Text px={2} py={1} color="mediumGrey">
                          <Trans>Online</Trans>
                        </Text>
                      </Flex>
                    </Box>
                    <Box width={1 / 3}>
                      <Flex centerY>
                        <CategoryTypeIcon
                          categoryType={{ color: '#c829a8', icon: 'chrome_reader_mode' }}
                        />
                        <Text px={2} py={1} color="mediumGrey">
                          <Trans>Print</Trans>
                        </Text>
                      </Flex>
                    </Box>
                    <Box width={1 / 3}>
                      <Flex centerY>
                        <CategoryTypeIcon categoryType={{ color: '#febd35', icon: 'tv' }} />
                        <Text px={2} py={1} color="mediumGrey">
                          <Trans>TV</Trans>
                        </Text>
                      </Flex>
                    </Box>
                  </Flex>
                  <Flex width={1} mx={2} mb={2}>
                    <Box width={1 / 3}>
                      <Flex centerY>
                        <CategoryTypeIcon categoryType={{ color: '#6cc829', icon: 'radio' }} />
                        <Text px={2} py={1} color="mediumGrey">
                          <Trans>Radio</Trans>
                        </Text>
                      </Flex>
                    </Box>
                  </Flex>
                </MntrPaper>
              </Box>
            </Box>

            <Box className="item anim-down">
              <Box width="230px" transform="rotate(2deg)" position="relative" top="30px">
                <MntrPaper>
                  <MntrPaperToolbar title={t`Social Media`} />
                  <Box px={2} mb={2}>
                    <img
                      src="/static/social/facebook.png"
                      width={26}
                      height={26}
                      alt="facebook"
                      style={{ marginRight: 8 }}
                    />
                    <img
                      src="/static/social/instagram.png"
                      width={26}
                      height={26}
                      alt="instagram"
                      style={{ marginRight: 8 }}
                    />
                    <img
                      src="/static/social/twitter.png"
                      width={26}
                      height={26}
                      alt="twitter"
                      style={{ marginRight: 8 }}
                    />
                    <img
                      src="/static/social/linkedin.png"
                      width={26}
                      height={26}
                      alt="linkedin"
                      style={{ marginRight: 8 }}
                    />
                    <img
                      src="/static/social/youtube.png"
                      width={26}
                      height={26}
                      alt="youtube"
                      style={{ marginRight: 8 }}
                    />
                    <img
                      src="/static/social/tiktok.png"
                      width={26}
                      height={26}
                      alt="tiktok"
                      style={{ marginRight: 8 }}
                    />
                  </Box>
                </MntrPaper>
              </Box>
            </Box>

            <Box>
              <Box
                className="item"
                width="200px"
                transform="rotate(3deg)"
                position="absolute"
                right="-160px"
                top="170px"
              >
                <MntrPaper>
                  <MntrPaperToolbar title={t`Foreign Media`} />
                  <Box px={2} mb={2} height="80px">
                    <img
                      src="/static/auth/map.png"
                      height={110}
                      alt="map"
                      style={{ margin: '0 auto', position: 'relative', top: -20, display: 'block' }}
                    />
                  </Box>
                </MntrPaper>
              </Box>
            </Box>
          </div>
        </Box>
      )
    },
  },
  {
    slide: 1,
    title: msg`Analytics`,
    description: msg`Tracking, analysis, and reporting are an integral part of PR. Use comprehensible charts that make data analysis easier. Compare your media output with your competition.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-150, 260, blobColors.color3, 84],
              [-140, -190, blobColors.color2, 18],
              [400, -110, blobColors.color5, 105],
              [400, 80, blobColors.color4, 342, 2],
              [350, 280, blobColors.color6, -17],
            ]}
          />
          <Box className="item anim-up">
            <Box mb={3} transform="rotate(6deg)" position="absolute" top="0px">
              <MntrPaper>
                <MntrPaperToolbar title={t`Media reach (GRP)`} />
                <img src="/static/auth/features/analytics_grp.png" width={420} alt="GRP" />
              </MntrPaper>
            </Box>
          </Box>
          <Box className="item">
            <Box mb={3} transform="rotate(1deg)" position="absolute" top="100px">
              <MntrPaper>
                <MntrPaperToolbar title={t`Advertising Value Equivalent (AVE)`} />
                <img src="/static/auth/features/analytics_ave.png" width={420} alt="AVE" />
              </MntrPaper>
            </Box>
          </Box>
          <Box className="item anim-down">
            <Box mb={3} transform="rotate(-3deg)" position="absolute" top="200px">
              <MntrPaper>
                <MntrPaperToolbar title={t`Interactions on social networks`} />
                <img
                  src="/static/auth/features/analytics_social-share.png"
                  width={420}
                  alt="Social Media"
                />
              </MntrPaper>
            </Box>
          </Box>
        </Box>
      )
    },
  },
  {
    slide: 2,
    title: msg`Medialist`,
    description: msg`With Medialist, you don't send your media output to randomly selected journalists. You only send it to those who are most likely to publish it.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-170, -150, blobColors.color3, 5],
              [-100, 140, blobColors.color2, 80],
              [440, 80, blobColors.color4, 342, 2],
              [250, 200, blobColors.color5, -17],
            ]}
          />
          <Box position="relative">
            <Box position="absolute" left="-152px" top="34px" className="item">
              <img
                src="/static/auth/features/medialist_01.png"
                width={(456 / 3) * 2}
                alt="Medialist"
              />
            </Box>

            <Box position="absolute" left="-20px" top="0px" className="item">
              <img
                src="/static/auth/features/medialist_02.png"
                width={(570 / 3) * 2}
                alt="Medialist"
              />
            </Box>

            <Box position="absolute" left="190px" top="-36px" className="item">
              <img
                src="/static/auth/features/medialist_03.png"
                width={(460 / 3) * 2}
                alt="Medialist"
              />
            </Box>
          </Box>
        </Box>
      )
    },
  },
  {
    slide: 3,
    title: msg`Emailing`,
    description: msg`Streamline communication efforts and maximize your PR impact.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-150, 260, blobColors.color3, 74],
              [180, -220, blobColors.color2, 58],
              [300, -110, blobColors.color5, 105],
              [-200, 80, blobColors.color4, 342, 2],
              [340, 180, blobColors.color6, -37],
            ]}
          />

          <Box position="relative">
            <Box position="absolute" left="-192px" top="-94px" className="item">
              <img
                src="/static/auth/features/emailing_01.png"
                width={(726 / 4) * 3}
                alt="Emailing"
              />
            </Box>

            <Box position="absolute" left="116px" top="220px" className="item">
              <img
                src="/static/auth/features/emailing_02.png"
                width={(238 / 4) * 3}
                alt="Emailing"
              />
            </Box>

            <Box position="absolute" left="276px" top="184px" className="item">
              <img
                src="/static/auth/features/emailing_03.png"
                width={(118 / 3) * 2}
                alt="Emailing"
              />
            </Box>
          </Box>
        </Box>
      )
    },
  },
  {
    slide: 4,
    title: msg`Newsroom`,
    description: msg`Dynamic platform for creating, curating, and sharing captivating content.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-150, 290, blobColors.color3, 74],
              [180, -120, blobColors.color2, 18],
              [330, -130, blobColors.color5, 65],
              [-200, 70, blobColors.color4, 342, 2],
              [340, 220, blobColors.color1, -37],
            ]}
          />

          <Box position="relative">
            <Box position="absolute" left="-130px" top="200px" className="item">
              <img
                src="/static/auth/features/newsroom_01.png"
                width={(286 / 4) * 3}
                alt="Newsroom"
              />
            </Box>

            <Box position="absolute" left="10px" top="40px" className="item">
              <img
                src="/static/auth/features/newsroom_02.png"
                width={(646 / 4) * 3}
                alt="Newsroom"
              />
            </Box>

            <Box position="absolute" left="276px" top="54px" className="item">
              <img
                src="/static/auth/features/newsroom_03.png"
                width={(212 / 4) * 3}
                alt="Newsroom"
              />
            </Box>
          </Box>
        </Box>
      )
    },
  },
  {
    slide: 5,
    title: msg`Reports and exports`,
    description: msg`Set any number of reports that will be sent to any number of contacts. Everyone receives the correct info at the right time and in the format you choose.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-160, 270, blobColors.color7, 84],
              [-190, -140, blobColors.color6, 0],
              [340, -140, blobColors.color3, 342, 2],
              [350, 280, blobColors.color7, -17, 2],
            ]}
          />

          <Box position="relative">
            <Box position="absolute" left="100px" top="20px" className="item">
              <img
                src="/static/auth/features/reports_exports_02.png"
                width={(434 / 3) * 2}
                alt="Medialist"
              />
            </Box>

            <Box position="absolute" left="280px" top="186px" className="item">
              <img
                src="/static/auth/features/reports_exports_03.png"
                width={(162 / 3) * 2}
                alt="Medialist"
              />
            </Box>

            <Box position="absolute" left="-82px" top="64px" className="item">
              <img
                src="/static/auth/features/reports_exports_01.png"
                width={(540 / 3) * 2}
                alt="Medialist"
              />
            </Box>
          </Box>
        </Box>
      )
    },
  },
  {
    slide: 6,
    title: msg`Mobile Apps`,
    description: msg`All features of the browser app are accessible on a mobile device. The app keeps you informed even when you are drinking a morning cup of coffee.`,
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-160, 270, blobColors.color6, 84],
              [-200, -140, blobColors.color1, 0],
              [380, -140, blobColors.color1, 342, 2],
              [350, 280, blobColors.color2, -17, 2],
            ]}
          />
          <Box position="relative">
            <Box position="absolute" left="190px" top="20px" className="item">
              <img
                src="/static/auth/features/mobile_apps_03.png"
                width={(396 / 3) * 2}
                alt="Mobile Apps"
              />
            </Box>

            <Box position="absolute" left="100px" top="-50px" className="item">
              <img
                src="/static/auth/features/mobile_apps_02.png"
                width={(384 / 3) * 2}
                alt="Mobile Apps"
              />
            </Box>

            <Box position="absolute" left="-62px" top="-80px" className="item">
              <img
                src="/static/auth/features/mobile_apps_01.png"
                width={(382 / 3) * 2}
                alt="Mobile Apps"
              />
            </Box>
          </Box>
        </Box>
      )
    },
  },
]

export default featuresSlides
