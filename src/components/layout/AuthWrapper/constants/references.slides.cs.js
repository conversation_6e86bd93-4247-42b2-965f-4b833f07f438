import { BlobsList } from '~/components/layout/AuthWrapper/modules/Blob'
import { Box } from '~/components/misc/Mntr'

const referencesSlidesCs = () => [
  {
    slide: 0,
    color: '#ffbbfc',
    opacityLevel: 0.7,
    name: '<PERSON><PERSON>',
    occupation: 'Vedoucí externí a interní komunikace',
    domain: 'Kaufland',
    description:
      'Naše tříletá zkušenost s Mediaboardem byla zcela pozitivní. Jejich profesionalita, široká škála služeb a prvotřídní čtvrtletní a roční analýzy jsou pro nás nepostradatelné. Mediaboard bych doporučila hlavně kvůli vysoké kvalitě výstupů a spolehlivosti.',
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-130, 160, '#ffd1fd', -74, 2],
              [150, -180, '#ffd1fd', 78, 1],
              [240, -20, '#ffd1fd', 130, 1],
              [-150, 20, '#ffd1fd', 342, 2],
              [160, 160, '#ffd1fd', 0, 1],
            ]}
          />
          <img
            className="item full-width"
            src="/static/auth/references/renata_maierl.png"
            alt="Renata Maierl"
          />
        </Box>
      )
    },
  },
  {
    slide: 1,
    color: '#aed6e3',
    opacityLevel: 0.7,
    name: 'Václav Koukolíček',
    occupation: 'Manažer externí komunikace',
    domain: 'Coca-Cola HBC',
    description:
      'Mediaboard proměnil způsob naší komunikace v Coca-Cola HBC! Je to nepostradatelný nástroj nejen pro monitoring médií s uživatelsky přívětivým rozhraním a hloubkovými analýzami. Jejich výjimečná zákaznická podpora dělá práci s Mediaboardem ještě příjemnější.',
    body: (animated) => {
      return (
        <Box>
          <BlobsList
            animated={animated}
            list={[
              [-140, 190, '#c3e1ea', -74, 2],
              [160, -170, '#c3e1ea', 84, 1],
              [260, -10, '#c3e1ea', 105, 2],
              [-150, 20, '#c3e1ea', 342, 2],
              [160, 160, '#c3e1ea', 0, 1],
            ]}
          />
          <img
            className="item full-width"
            src="/static/auth/references/vaclav_koukolicek.png"
            alt="Václav Koukolíček"
          />
        </Box>
      )
    },
  },
]

export default referencesSlidesCs
