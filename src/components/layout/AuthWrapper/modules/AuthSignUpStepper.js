import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'
import MntrStepper from '~/components/misc/MntrStepper/MntrStepper'

const Wrapper = styled.div`
  margin-bottom: 40px;
  position: relative;
  top: -10px;
  max-width: 420px;
  width: 100%;
  text-align: center;

  @media (max-height: 901px) {
    margin-bottom: 8px;
  }
`

const AuthSignUpStepper = ({ step }) => {
  const steps = [
    {
      label: t`First Step`,
      description: t`Account info`,
    },
    {
      label: t`Second Step`,
      description: t`Company info`,
    },
  ]

  return (
    <Wrapper>
      <MntrStepper active={step} steps={steps} />
    </Wrapper>
  )
}

export default AuthSignUpStepper
