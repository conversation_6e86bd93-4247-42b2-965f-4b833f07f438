import color from 'color'
import { styled } from 'styled-components'

const AuthDividerWrapper = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  svg {
    display: block;
    height: 100%;

    @media (max-width: 1000px) {
      width: 100%;
      transform: translateY(-50%);
    }
  }
`

const VisibleMd = styled.div`
  display: block;

  @media (min-width: 1001px) {
    display: none;
  }
`

const VisibleLg = styled.div`
  display: none;

  @media (min-width: 1001px) {
    display: flex;
  }
`

const Polyline = ({ color, values }) => {
  return <polyline fill={color} points={values} />
}

const AuthDivider = ({ bg = '#1f2f4e', colors, opacityLevel = 1 }) => {
  // Set default colors
  let color1 = '#f5f7f9'
  let color2 = color(bg)
    .lighten(0.2 * opacityLevel)
    .toString()
  let color3 = color(bg)
    .lighten(0.1 * opacityLevel)
    .toString()
  let color4 = bg

  // Override with prop colors if provided
  if (colors && colors.length === 4) {
    ;[color1, color2, color3, color4] = colors
  }

  return (
    <>
      <VisibleMd>
        <AuthDividerWrapper>
          <svg viewBox="0 0 600 100" xmlns="http://www.w3.org/2000/svg">
            <Polyline color={color1} values="0,0 300,50 600,0" />
            <Polyline color={color2} values="0,0 300,50 0,100" />
            <Polyline color={color3} values="600,0 300,50 600,100" />
          </svg>
        </AuthDividerWrapper>
      </VisibleMd>

      <VisibleLg>
        <AuthDividerWrapper>
          <svg viewBox="0 0 100 600" xmlns="http://www.w3.org/2000/svg">
            <Polyline color={color1} values={'0,0 0,0 50,350 20,600 0,600'} />
            <Polyline color={color4} values="100,0 50,350 80,600 100,600" />
            <Polyline color={color2} values="0,0 50,350 100,0" />
            <Polyline color={color3} values="20,600 50,350 80,600 100,600" />
          </svg>
        </AuthDividerWrapper>
      </VisibleLg>
    </>
  )
}

export default AuthDivider
