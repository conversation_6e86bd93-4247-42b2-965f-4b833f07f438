import getRandomInt from '~/components/layout/AuthWrapper/helpers/getRandomInt'

const Blob = ({ top, left, rotate = 0, fill = '#fff', type = 1, animated, transparent }) => {
  return (
    <div className={animated ? `item blob blob${getRandomInt(2)}` : ''}>
      <div
        style={{
          position: 'absolute',
          top: top,
          left: left,
          width: 200,
          height: 200,
          transform: `rotate(${rotate}deg) scale(${type === 1 ? 1 : 0.3})`,
          opacity: transparent ? 0.6 : 1,
        }}
      >
        <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
          {type === 1 && (
            <path
              fill={fill}
              d="M43.2,17C43.1,25.2,21.1,12.8,10.3,-1.5C-0.5,-15.9,-0.3,-32.3,10.7,-26.1C21.7,-19.9,43.4,8.8,43.2,17Z"
              transform="translate(100 100)"
            />
          )}
          {type === 2 && (
            <path
              fill={fill}
              d="M40.1,-5.8C48.4,12.6,49.2,40.7,35,51.9C20.8,63.1,-8.4,57.4,-28.8,42C-49.3,26.6,-61,1.5,-54.8,-14.1C-48.5,-29.7,-24.2,-35.7,-4.2,-34.3C15.9,-33,31.7,-24.2,40.1,-5.8Z"
              transform="translate(100 100)"
            />
          )}
        </svg>
      </div>
    </div>
  )
}

export const BlobsList = ({ list, animated, transparent }) => {
  return (
    <>
      {list.map((item, index) => {
        return (
          <Blob
            animated={animated}
            transparent={transparent}
            key={index}
            left={item[0]}
            top={item[1]}
            fill={item[2]}
            rotate={item[3]}
            type={item[4]}
          />
        )
      })}
    </>
  )
}

export default Blob
