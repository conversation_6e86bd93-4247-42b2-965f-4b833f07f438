import { styled } from 'styled-components'
import { Paper } from '~/components/layout/AuthWrapper/styles/StyledAuthWrapper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Text } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const Title = styled(Text)`
  color: ${({ theme }) => theme.colors.lightGrey};
`

const AuthPaperContainer = ({
  children,
  title,
  actions,
  appStore: {
    account: {
      user: { isLoading },
    },
  },
}) => {
  return (
    <Paper>
      <MntrPaperToolbar
        icon={
          <Icon size={26} color="lightGrey">
            lock_open
          </Icon>
        }
        isLoading={isLoading}
        title={<Title>{title}</Title>}
        actions={actions}
      />
      <Box p={3} color="black">
        {children}
      </Box>
    </Paper>
  )
}

export default observer(AuthPaperContainer)
