import { keyframes } from 'styled-components'

export const upAnimation = keyframes`
 0% { transform: rotate(0deg);}
 50% { transform: rotate(1deg); }
 100% { transform: rotate(0deg); }
`

export const downAnimation = keyframes`
 0% { transform: rotate(0deg); } 
 50% { transform: rotate(-1deg); }
 100% { transform: rotate(0deg); }
`

export const breatheAnimation = keyframes`
 0% { transform: scale(1); opacity: 1}
 30% { transform: scale(1.01); opacity: 1; }
 70% { transform: scale(0.99); opacity: 1; }
 100% { transform: scale(1); opacity: 1; }
`

export const slowIn = keyframes`
 0% { transform: scale(0.8); rotate(0deg); opacity: 0; }
 100% { transform: scale(1); rotate(0deg); opacity: 1; }
`

export const breatheAnimationBlob = keyframes`
 0% { transform: scale(1); opacity: 1}
 50% { transform: scale(0.94); opacity: 0.7; }
 100% { transform: scale(1); opacity: 1; }
`

export const breatheAnimationBlob2 = keyframes`
 0% { transform: scale(1); opacity: 1}
 50% { transform: scale(0.9); opacity: 0.8; }
 100% { transform: scale(1); opacity: 1; }
`
