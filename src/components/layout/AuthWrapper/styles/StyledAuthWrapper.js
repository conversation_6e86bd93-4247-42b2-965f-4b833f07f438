import { css, keyframes, styled } from 'styled-components'
import { Box, Heading, Text } from '~/components/misc/Mntr'
import {
  breatheAnimation,
  breatheAnimationBlob,
  breatheAnimationBlob2,
  downAnimation,
  slowIn,
  upAnimation,
} from './StyledAuthWrapper.keyframes'

const BLOB_FADE_IN = 0.7

export const Container = styled(Box)`
  height: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
`

export const ContainerLeft = styled(Container)`
  flex: 1 1 55%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  height: auto;
  background: ${({ theme }) => theme.colors.background};

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 1000px) {
    background: linear-gradient(
      to bottom,
      transparent 50%,
      ${({ theme }) => theme.authWrapper.primary} 50%
    );
  }

  & .section--left {
    margin-left: auto;
    margin-right: auto;
  }
`

export const ContainerRight = styled(Container)`
  justify-content: flex-start;
  box-shadow: inset 20px 0px 40px 0px rgb(0 0 0 / 6%);
  background: ${({ bg, theme }) => bg || theme.authWrapper.primary};
  position: relative;
`

export const Paper = styled.div`
  background: ${({ theme }) => theme.paper.background};
  border: none;
  border-radius: 10px;
  box-shadow: ${({ theme }) => theme.paper.boxShadow};
`

export const Section = styled.div`
  max-width: ${({ maxwidth }) => maxwidth || 420}px;
  width: 100%;
  margin: 0 170px;
  position: relative;
  z-index: 1;

  @media (min-width: 1400px) {
    margin-left: 170px;
    margin-right: 150px;
    right: 0px;
  }

  @media (min-width: 1700px) {
    margin-left: 200px;
    margin-right: 200px;
    right: 0px;
  }

  @media (min-width: 2000px) {
    margin-left: 300px;
    margin-right: 300px;
    right: 0px;
  }

  @media (max-width: 1399px) {
    margin-left: auto;
    margin-right: auto;
    right: 100px;
  }

  @media (max-width: 1000px) {
    margin-left: auto;
    margin-right: auto;
    right: 0px;
  }
`

export const Pattern = styled.div`
  width: 100%;
  height: 600px;
  margin: 0 250px;
  box-sizing: border-box;
  position: absolute;
  box-shadow: 0px 0px 400px inset ${({ bg }) => bg};
  z-index: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.08'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
`

export const Visible = styled.div`
  display: none;

  ${({ xl }) =>
    xl
      ? css`
          @media (min-width: 1400px) {
            display: flex;
          }
        `
      : ``}

  ${({ md }) =>
    md
      ? css`
          @media (max-width: 1399px) {
            display: flex;
          }
        `
      : ``}
`

export const SlideshowWrapper = styled.div`
  text-align: left;
  position: relative;
  z-index: 2;
  left: 50px;

  & .slideshow {
    transform: scale(0.8);
    opacity: 0 !important;
  }

  & .slideshow.active {
    opacity: 1 !important;
    transition: all 0.15s ease-in-out;
    transform: scale(1);
  }

  & .slideshow.active * .item {
    animation-name: ${breatheAnimation}, ${slowIn};
    animation-duration:
      ${({ animated }) => (animated ? 8 : 0)}s,
      ${({ animated }) => (animated ? BLOB_FADE_IN / 2 : 0)}s;
    animation-iteration-count: infinite, 1;
  }

  & .slideshow.active * .item.anim-up {
    animation-name: ${upAnimation}, ${slowIn};
    animation-duration:
      ${({ animated }) => (animated ? 8 : 0)}s,
      ${({ animated }) => (animated ? BLOB_FADE_IN * 0.6 : 0)}s;
    animation-iteration-count: infinite, 1;
    transform-origin: 100% 50%;
  }

  & .slideshow.active * .item.anim-down {
    animation-name: ${downAnimation}, ${slowIn};
    animation-duration:
      ${({ animated }) => (animated ? 7 : 0)}s,
      ${({ animated }) => (animated ? BLOB_FADE_IN * 0.6 : 0)}s;
    animation-iteration-count: infinite, 1;
    transform-origin: 100% 0%;
  }

  & .slideshow.active * .blob {
    transform: scale(0);
    opacity: 0;
    animation-iteration-count: infinite, 1;
    animation-duration:
      ${({ animated }) => (animated ? 10 : 0)}s, ${({ animated }) => (animated ? BLOB_FADE_IN : 0)}s;
    animation-name: ${breatheAnimation}, ${slowIn};
  }

  & .slideshow.active * .item.blob1 {
    animation-iteration-count: infinite, 1;
    animation-duration:
      ${({ animated }) => (animated ? 16 : 0)}s, ${({ animated }) => (animated ? BLOB_FADE_IN : 0)}s;
    animation-name: ${breatheAnimationBlob}, ${slowIn};
  }

  & .slideshow.active * .item.blob2 {
    animation-iteration-count: infinite, 1;
    animation-duration:
      ${({ animated }) => (animated ? 20 : 0)}s, ${({ animated }) => (animated ? BLOB_FADE_IN : 0)}s;
    animation-name: ${breatheAnimationBlob2}, ${slowIn};
  }

  & img.rounded {
    border-radius: 20px;
    border: 6px solid rgba(255, 255, 255, 0.2);
  }

  & .full-width {
    width: 100%;
  }

  & .slides {
    transform: scale(1);

    @media (max-width: 1900px) {
      transform: scale(0.9);
    }

    @media (max-width: 1600px) {
      transform: scale(0.7);
    }
  }
`

export const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
`

export const PaginationItem = styled.div`
  padding: 10px 2px;
  cursor: pointer;

  & .dot {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    cursor: pointer;
    width: ${({ active }) => (active ? 20 : 6)}px;
    height: 6px;
    background: ${({ theme }) => theme.authWrapper.secondary};
    border-radius: 3px;
    transition: all 0.1s ease-in-out;
  }

  &:hover .dot {
    background: #fff;
  }
`

const pulseAnimation = keyframes`
0 %
  100 % {
  background-position-y: 100 %;
 }
50 % {
  background-position-y: 50 %;
 }
`

export const HeadingGradient = styled(Heading)`
  font-weight: 900;
  background-image: linear-gradient(0deg, ${({ color }) => color}, ${({ gradient }) => gradient});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 100% 200%;
  background-position-y: 100%;
  animation-name: ${pulseAnimation};
  animation-duration: ${({ animated }) => (animated ? 8 : 0)}s;
  animation-iteration-count: infinite;
  font-size: 36px;
  margin-top: 80px;

  @media (max-width: 1600px) {
    font-size: 28px;
  }

  @media (max-height: 901px) {
    margin-top: 40px;
  }
`

export const TextDescription = styled(Text)`
  font-size: 18px;
  @media (max-width: 1600px) {
    font-size: 16px;
  }

  min-height: auto;
  @media (min-height: 900px) {
    min-height: 140px;
  }
`

export const DescriptionWrapper = styled.div`
  min-height: ${({ minheight }) => minheight || '150px'};
  margin-top: ${({ margintop }) => margintop || 200}px;

  @media (max-width: 1600px) {
    margin-top: 30px;
  }

  @media (max-width: 1600px and min-height: 901px) {
    margin-top: 100px;
  }
`

export const MdSideBar = styled.div`
  width: 25%;
  right: 0;
  top: 0;
  bottom: 0;
  position: absolute;
  background: ${({ theme }) => theme.authWrapper.primary};
  transition: all 100ms ease-in-out;

  @media (max-width: 1000px) {
    width: 100%;
    top: 50%;
  }

  @media (max-width: 600px) {
    top: calc(50% - ${({ insetTop }) => insetTop / 2}px);
  }
`
