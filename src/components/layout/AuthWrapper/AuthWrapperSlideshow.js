import { useLingui } from '@lingui/react/macro'
import { useCallback, useEffect, useRef, useState } from 'react'
import { styled } from 'styled-components'
import { Box } from '~/components/misc/Mntr'
import AuthDivider from './modules/AuthDivider'
import {
  ContainerRight,
  HeadingGradient,
  Pagination,
  PaginationItem,
  Section,
  SlideshowWrapper,
  TextDescription,
} from './styles/StyledAuthWrapper'

const AlignCenter = styled(Box)`
  text-align: center;
`

const StylerCornerImage = styled.img`
  position: absolute;
  z-index: 1;
  right: 0;
  opacity: 0.6;
`

const StyledBottomRightCornerImage = styled(StylerCornerImage)`
  bottom: 0;
`

const StyledTopRightCornerImage = styled(StylerCornerImage)`
  top: 0;
`

const AuthWrapperSlideshow = ({ dividerDelta, animated, slides }) => {
  const { i18n } = useLingui()
  const [current, setCurrent] = useState(0)
  const length = slides.length
  const timeout = useRef(null)
  const currentSlideContent = slides[current]

  const nextSlide = useCallback(() => {
    if (timeout.current) {
      clearTimeout(timeout.current)
    }
    setCurrent(current === length - 1 ? 0 : current + 1)
  }, [current, length])

  useEffect(() => {
    if (animated) {
      timeout.current = setTimeout(nextSlide, 8000)
    }

    return function () {
      if (timeout.current) {
        clearTimeout(timeout.current)
      }
    }
  }, [current, length, animated, nextSlide])

  const textColor = '#fff'

  return (
    <ContainerRight>
      <StyledTopRightCornerImage
        src="/static/auth/auth_corner_top_right.png"
        width={512 / 2}
        height={768 / 2}
        alt=""
      />
      <StyledBottomRightCornerImage
        src="/static/auth/auth_corner_bottom_right.png"
        width={512 / 2}
        height={512 / 2}
        alt=""
      />
      <AuthDivider
        colors={['#f5f7f9', '#678fc6', '#23687e', '#1f2f4e']}
        dividerDelta={dividerDelta}
        animated={animated}
      />
      <Section>
        <SlideshowWrapper animated={animated}>
          <Box
            onClick={() => nextSlide()}
            className="slides"
            position="relative"
            height="300px"
            width="300px"
          >
            {slides.map((slide) => {
              return (
                <Box
                  className={slide.slide === current ? 'slideshow active' : 'slideshow'}
                  key={slide.slide.toString()}
                  top="0px"
                  height="300px"
                  width="300px"
                  position="absolute"
                >
                  {slide.body(animated)}
                </Box>
              )
            })}
          </Box>
          <AlignCenter>
            <HeadingGradient
              mb={2}
              fontSize={[5]}
              color="#04EDB5"
              gradient="#1cb7eb"
              animated={animated}
            >
              {i18n._(currentSlideContent.title)}
            </HeadingGradient>
            <TextDescription color={textColor}>
              {i18n._(currentSlideContent.description)}
            </TextDescription>
          </AlignCenter>
          <Pagination>
            {slides.map((slide, index) => {
              return (
                <PaginationItem
                  key={slide.slide}
                  active={index === current}
                  onClick={() => setCurrent(index)}
                >
                  <Box className="dot"></Box>
                </PaginationItem>
              )
            })}
          </Pagination>
        </SlideshowWrapper>
      </Section>
    </ContainerRight>
  )
}

export default AuthWrapperSlideshow
