import { useLingui } from '@lingui/react/macro'
import cookie from 'js-cookie'
import { useRouter } from 'next/router'
import { styled } from 'styled-components'
import Flag from '~/components/Flag/Flag'
import Logo from '~/components/misc/Logo/Logo'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import setLocale from '~/helpers/i18n'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'
import withLanguageMenuItems from '~/helpers/withLanguageMenuItems'
import AuthWrapperReferences from './AuthWrapperReferences'
import AuthWrapperSlideshow from './AuthWrapperSlideshow'
import featuresSlides from './constants/features.slides'
import referencesSlidesCom from './constants/references.slides.com'
import referencesSlidesCs from './constants/references.slides.cs'
import referencesSlidesSk from './constants/references.slides.sk'
import AuthDivider from './modules/AuthDivider'
import AuthPaperContainer from './modules/AuthPaperContainer'
import AuthSignUpStepper from './modules/AuthSignUpStepper'
import { ContainerLeft, MdSideBar, Section, Visible } from './styles/StyledAuthWrapper'

const FlexWrapper = styled(Flex)`
  min-height: 100%;
`

const Wrapper = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
`

const BoxFooter = styled(Box)`
  margin-top: 50px;
  /* Break vertical margin collapse, on purpose, by adding 1px padding, so it
  matches AuthHeader where margin collapse is broken because of flex element in
  between margins. */
  padding-top: 1px;
  margin-bottom: 40px;
  color: ${({ theme }) => theme.colors.black};
`

const StyledAuthHeader = styled(Flex)`
  margin-top: 40px;
  margin-bottom: 50px;
  height: 32px;
`

const LogoWrapper = styled.div`
  margin: 0 auto;
  max-width: 55%;

  a,
  svg {
    display: block;
  }
`

const AuthHeader = ({ backLink: backLinkPathname, redirectTo }) => {
  const { pathname, query } = useRouter()
  const backLink = { pathname: backLinkPathname, query }
  const shouldBackLink = backLinkPathname && backLinkPathname !== pathname

  return (
    <StyledAuthHeader centerY justifyContent="space-between">
      <Box width="40px">
        {shouldBackLink && (
          <MntrButton
            bg="light"
            icon="arrow_back"
            onClick={(e) => {
              e.preventDefault()
              redirectTo(backLink)
            }}
          />
        )}
      </Box>

      <Box flex={1}>
        <LogoWrapper>
          {shouldBackLink ? (
            <Link href={backLink}>
              <Logo />
            </Link>
          ) : (
            <Logo />
          )}
        </LogoWrapper>
      </Box>

      <Box width="40px" />
    </StyledAuthHeader>
  )
}

const AuthWrapper = ({
  isVisibleSlideshow,
  children,
  footer,
  title,
  signUpStep = 0,
  maxWidth = 400,
  backLink,
  appStore: {
    viewport: { insets, isMobile },
    router: { redirectTo },
    appLanguage,
    appSettings: { enableLanguageSelector },
  },
}) => {
  // disable animations for screenshot tests
  const animated = cookie.get('monitora-e2e-animation') !== 'disabled'
  const { i18n } = useLingui()
  const actions = []

  if (enableLanguageSelector) {
    actions.push({
      rounded: true,
      bg: 'light',
      icon: <Flag language={appLanguage} noTooltip />,
      popupPlacement: 'bottom-end',
      popup: (closePopup) => {
        return (
          <MntrMenu
            menuItems={withLanguageMenuItems(setLocale, i18n.locale)}
            closePopup={closePopup}
          />
        )
      },
    })
  }

  let referencesSlides

  switch (appLanguage) {
    case 'cs':
      referencesSlides = referencesSlidesCs()
      break
    case 'sk':
      referencesSlides = referencesSlidesSk()
      break
    default:
      referencesSlides = referencesSlidesCom()
  }

  return (
    <Wrapper>
      <FlexWrapper>
        <ContainerLeft>
          <Section maxwidth={maxWidth} className="section--left">
            <AuthHeader backLink={backLink} isMobile={isMobile} redirectTo={redirectTo} />

            {/* SignUp Stepper */}
            {signUpStep > 0 && <AuthSignUpStepper step={signUpStep} />}

            <AuthPaperContainer title={title} actions={actions}>
              {children}
            </AuthPaperContainer>
            <BoxFooter>{footer}</BoxFooter>
          </Section>
        </ContainerLeft>

        {/* Features slideshow */}
        <Visible xl>
          {isVisibleSlideshow && (
            <AuthWrapperSlideshow animated={animated} slides={featuresSlides} />
          )}
        </Visible>

        {/* References slideshow */}
        <Visible xl>
          {!isVisibleSlideshow && (
            <AuthWrapperReferences animated={animated} slides={referencesSlides} />
          )}
        </Visible>

        {/* Tablet divider */}
        <Visible md>
          <MdSideBar insetTop={insets.top}>
            <AuthDivider animated={animated} opacityLevel={9.5} />
          </MdSideBar>
        </Visible>
      </FlexWrapper>
    </Wrapper>
  )
}

export default observer(AuthWrapper)
