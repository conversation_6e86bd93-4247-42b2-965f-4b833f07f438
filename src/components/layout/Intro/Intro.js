import flatten from 'lodash/flatten'
import { styled } from 'styled-components'
import { Box, Heading, Text } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const IntroBox = styled(Box)`
  text-align: center;
`

const BodyText = styled(Text)`
  .material-symbols-outlined {
    line-height: 0;
    position: relative;
    top: 5px;
  }
`

const Intro = ({
  appStore: {
    viewport: { isTablet },
  },
  children,
  heading,
}) => {
  return (
    <IntroBox my={2} isTablet={isTablet}>
      <Heading as="h1" fontSize={4}>
        {heading}
      </Heading>
      {flatten([].concat(children)).map((bodyItem, index) => {
        return (
          <BodyText my={2} key={`intro-body-text-part-${index}`} color="black">
            {bodyItem}
          </BodyText>
        )
      })}
    </IntroBox>
  )
}

export default observer(Intro)
