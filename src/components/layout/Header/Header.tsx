'use client'

import { ReactNode, useState } from 'react'

import { useWindowSize } from 'react-use'
import { useBreakpoint } from '~/app/lib/use-breakpoint'
import useIsClient from '~/app/lib/use-is-client'
import { UserProps } from '~/app/types/user'
import Logo from '~/components/misc/Logo/Logo'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import { CustomLogo, LogoWrapper, StyledHeaderFlex, Wrapper } from '.'
import MobileNav from './MobileNav'

interface IHeaderProps {
  isImpersonating: boolean
  isLoaded: boolean
  isMobileSearchVisible: boolean
  pathname?: string | null
  setIsMobileSearchVisible?: (isVisible: boolean) => void
  user: UserProps
  workspace: IWorkspaceStore
  // Direct props instead of slot pattern
  logoComponent?: ReactNode
  monitoringNavigationComponent?: (close?: () => void) => ReactNode
  notificationsComponent?: ReactNode
  searchAdminComponent?: ReactNode
  searchInputComponent?: ReactNode
  searchHandlerComponent?: ReactNode
  searchSuggestComponent?: ReactNode
  themeCallbackComponent?: ReactNode
  userMenuComponent?: (closePopup?: () => void) => ReactNode
}

export default function Header({
  isImpersonating,
  isLoaded,
  isMobileSearchVisible,
  pathname,
  setIsMobileSearchVisible,
  user,
  workspace,
  // Direct prop components TODO
  logoComponent,
  monitoringNavigationComponent,
  notificationsComponent,
  searchAdminComponent,
  searchInputComponent,
  searchHandlerComponent,
  searchSuggestComponent,
  themeCallbackComponent,
  userMenuComponent,
}: IHeaderProps) {
  const isClient = useIsClient()
  const [isMobileMenuVisible, setIsMobileMenuVisible] = useState(false)
  const { isMobile, isTablet } = useBreakpoint()
  const { width } = useWindowSize()

  // skip rendering if user is not logged in
  if (!isLoaded) {
    return null
  }

  const isAdmin = pathname?.startsWith('/staff/admin')
  const isVisibleSearchSuggest = pathname === '/'

  if (workspace?.is_expired && !isImpersonating) {
    return false
  }

  return (
    <>
      {isMobileSearchVisible && isVisibleSearchSuggest && searchSuggestComponent}
      {isMobileSearchVisible && isAdmin && searchAdminComponent}
      <MobileNav
        close={() => setIsMobileMenuVisible(false)}
        isImpersonating={isImpersonating}
        isOpen={isMobileMenuVisible}
        monitoringNavigationComponent={monitoringNavigationComponent}
        userEmail={user.email}
        userMenuComponent={userMenuComponent}
        workspace={workspace}
      />
      {user.is_active && (
        <Wrapper isMobile={isMobile}>
          <StyledHeaderFlex pr={4} pl={3}>
            {themeCallbackComponent}

            <Flex centerY justifyContent="flex-start" flexGrow="1">
              <div
                style={{
                  width: isTablet && width > 0 ? 'auto' : 250,
                }}
              >
                {isMobile && isMobileSearchVisible && (
                  <Box ml={-2} mr={1}>
                    <MntrButton
                      bg={'header'}
                      onClick={
                        setIsMobileSearchVisible
                          ? () => {
                              setIsMobileSearchVisible(false)
                            }
                          : undefined
                      }
                      icon="arrow_back"
                    />
                  </Box>
                )}
                {(!isMobileSearchVisible || !isMobile) && (
                  <Box mr={1}>
                    {logoComponent ||
                      (workspace.logo_image_url ? (
                        <CustomLogo url={user.logo_image_url} />
                      ) : (
                        <LogoWrapper>
                          <Logo mono />
                        </LogoWrapper>
                      ))}
                  </Box>
                )}
              </div>
              {searchInputComponent}
            </Flex>

            {!user.is_public_access && (
              <Flex centerY>
                {!isMobileSearchVisible && notificationsComponent}
                {!isTablet && (
                  <Box mr={-1}>
                    {userMenuComponent ? (
                      <MntrButton
                        noPadding
                        icon={isImpersonating ? 'build' : 'account_circle'}
                        bg="header"
                        {...(isClient &&
                          width > 1301 && {
                            label: (
                              <Text fontSize="15px" textTransform="lowercase" color="inherit">
                                {isImpersonating ? workspace?.name : user.email}
                              </Text>
                            ),
                          })}
                        popup={userMenuComponent}
                        popupPlacement="bottom-end"
                        popupWidth={200}
                        zIndex={4999}
                      />
                    ) : null}
                  </Box>
                )}
                {searchHandlerComponent}
                {isTablet && !isMobileSearchVisible && (
                  <MntrButton
                    bg={'header'}
                    onClick={() => {
                      setIsMobileMenuVisible(!isMobileMenuVisible)
                    }}
                    icon={isMobileMenuVisible ? 'close' : 'menu'}
                  />
                )}
              </Flex>
            )}
          </StyledHeaderFlex>
        </Wrapper>
      )}
    </>
  )
}
