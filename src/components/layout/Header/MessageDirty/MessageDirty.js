import { Trans } from '@lingui/react/macro'
import BannerMessage from '~/components/misc/BannerMessage/BannerMessage'
import { Text } from '~/components/misc/Mntr'

const MessageDirty = () => {
  return (
    <BannerMessage
      bg="secondary"
      icon="cloud_queue"
      title={<Trans>Your news feed is being updated</Trans>}
      textComponent={
        <Trans id="message.dirty.description">
          <Text color="inherit">
            The displayed data may not match your current settings because one or more topics have
            been changed.
          </Text>
          <Text color="inherit">Please reload the page in a few minutes.</Text>
        </Trans>
      }
    />
  )
}

export default MessageDirty
