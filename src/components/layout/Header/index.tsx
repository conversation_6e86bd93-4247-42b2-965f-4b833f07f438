import { styled } from 'styled-components'
import { Flex } from '~/components/misc/Mntr'

interface IWrapperProps {
  isMobile: boolean
}

export const Wrapper = styled.div<IWrapperProps>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 54px;
  z-index: ${({ isMobile }) => (isMobile ? 1000 : 4990)};
`

export const StyledHeaderFlex = styled(Flex)`
  background: ${({ theme }) => theme.header.background};
  color: ${({ theme }) => theme.header.color};
  box-shadow: ${({ theme }) => theme.header.boxShadow};
  height: 54px;
`

interface IStyledFormWrapperProps {
  ismobile: boolean
  istablet: boolean
  isqueryfocused: boolean
}

export const StyledFormWrapper = styled.div<IStyledFormWrapperProps>`
  width: ${({ istablet }) => (istablet ? '100%' : '600px')};
  max-width: 600px;
  margin-left: ${({ ismobile }) => (ismobile ? 0 : 10)}px;

  border-radius: 24px;
  height: 36px;
  background: ${({ isqueryfocused, theme }) =>
    isqueryfocused ? theme.header.searchFormActive : theme.header.searchForm};
`

export const LogoWrapper = styled.div`
  display: flex;
  align-items: center;
  width: 120px;
`

interface ICustomLogoProps {
  url: string
}

export const CustomLogo = styled.div<ICustomLogoProps>`
  width: 150px;
  height: 40px;
  background: url(${({ url }) => url});
  background-repeat: no-repeat;
  background-position: left center;
  background-size: contain;
`
