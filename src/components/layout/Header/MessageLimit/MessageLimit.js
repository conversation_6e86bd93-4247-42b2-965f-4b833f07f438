import { Trans } from '@lingui/react/macro'
import BannerMessage from '~/components/misc/BannerMessage/BannerMessage'
import { Text } from '~/components/misc/Mntr'

const MessageLimit = () => {
  return (
    <BannerMessage
      bg="errorLight"
      collapse
      icon="warning"
      text={
        <Text color="inherit">
          <Trans>
            You may not see the latest articles. We recommend that you change your keyword settings
            or limit your watched media in the Topics section.
          </Trans>
        </Text>
      }
      title={<Trans>You have reached the limit on found articles</Trans>}
    />
  )
}

export default MessageLimit
