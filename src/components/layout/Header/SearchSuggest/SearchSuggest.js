import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import { css, styled } from 'styled-components'
import SearchBarTopicSelector from '~/components/forms/dashboard/Search/SearchBarTopicSelector'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'

const Wrapper = styled.div`
  ${(props) =>
    props.isMobile
      ? css`
          position: fixed;
          top: 54px;
          height: 100%;
          left: 0;
          right: 0;
          z-index: 100;
          background: ${({ theme }) => theme.popper.background};
          overflow-y: auto;
        `
      : null};

  & .menu-heading {
    color: ${({ theme }) => theme.popper.heading} !important;
  }
`

const TextWrapper = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

const SearchSuggest = ({
  appStore: {
    searchBar,
    viewport,
    router: { makeRouteWithQuery },
  },
}) => {
  const router = useRouter()
  const { searchData, lastSearchQuery, limitQuery, setIsOpenSearch, setIsMobileSearchVisible } =
    searchBar

  return (
    <Wrapper isMobile={viewport.isMobile} className="popper-menu">
      {lastSearchQuery && viewport.isMobile && (
        <>
          <MntrMenuHeading label={<Trans>Search</Trans>} fontSize={12} />
          <Link href={`${router.pathname}?query=${lastSearchQuery}`}>
            <MenuItem
              onClick={() => {
                setIsOpenSearch(false)
                setIsMobileSearchVisible(false)
              }}
              leftIcon="history"
              label={lastSearchQuery}
              fontSize={12}
            />
          </Link>
          <SearchBarTopicSelector topicTitleLimit={30} />
        </>
      )}
      {searchData?.queries?.length > 0 && (
        <>
          <MntrMenuHeading
            className="menu-heading"
            label={<Trans>Search History</Trans>}
            fontSize={12}
          />
          {searchData?.queries?.map((searchQuery, index) => {
            if (index >= limitQuery) {
              return false
            }

            let redirectRoute = router.pathname
            if (router.pathname === '/author/[authorId]') {
              redirectRoute = '/author/' + router.query.authorId
            }

            if (router.pathname === '/topics') {
              redirectRoute = '/'
            }

            return (
              <Link
                key={index.toString()}
                href={makeRouteWithQuery(redirectRoute, undefined, {
                  query: searchQuery,
                  ...(router.pathname === '/author/[authorId]' ? { authorId: undefined } : {}),
                })}
              >
                <MenuItem
                  onClick={() => {
                    setIsMobileSearchVisible(false)
                    setIsOpenSearch(false)
                  }}
                  leftIcon={'history'}
                  buttonGroup={[
                    {
                      bg: 'default',
                      size: 'small',
                      mr: 1,
                      icon: 'close',
                      onClick: (e) => {
                        e.preventDefault()
                        searchBar.suggestRemove(searchQuery)
                      },
                    },
                  ]}
                  label={<TextWrapper>{searchQuery}</TextWrapper>}
                />
              </Link>
            )
          })}
        </>
      )}
    </Wrapper>
  )
}

export default observer(SearchSuggest)
