import { Trans, useLingui } from '@lingui/react/macro'
import { useEffect, useState } from 'react'
import { useWindowSize } from 'react-use'
import { useBreakpoint } from '~/app/lib/use-breakpoint'
import { UserProps } from '~/app/types/user'
import Flag from '~/components/Flag/Flag'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { routerPush } from '~/helpers/router'
import withLanguageMenuItems from '~/helpers/withLanguageMenuItems'
import withMenuPopup from '~/helpers/withMenuPopup/withMenuPopup'
import withSubmenuModal from '~/helpers/withSubmenuModal'
import { IPermissionsStore } from '~/store/models/account/workspace/permissions/PermissionsStore'
import { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import { IWorkspacesStoreArrItem } from '~/store/models/account/workspaces/WorkspacesStoreArrItem'
import { IAppSettings } from '~/store/models/appSettings/AppSettings'

export interface IUserMenuProps {
  appearanceCookie: string | null
  appearanceCookieSet: (darkTheme: boolean) => void
  appSettings: IAppSettings
  appLanguage: string
  closePopup?: () => void
  isImpersonating: boolean
  permissions: IPermissionsStore
  searchBarReset: () => void
  setLocale: (lang: string) => Promise<void>
  switchWorkspace?: (uuid: string) => void
  unimpersonate?: () => void
  user: UserProps
  workspace: IWorkspaceStore
  workspaces: IWorkspacesStoreArrItem[]
}

const UserMenu = ({
  appearanceCookie,
  appearanceCookieSet,
  appSettings,
  appLanguage,
  closePopup,
  isImpersonating,
  permissions,
  searchBarReset,
  setLocale,
  switchWorkspace,
  unimpersonate,
  user,
  workspace,
  workspaces,
}: IUserMenuProps) => {
  const [darkTheme, setDarkTheme] = useState<boolean | null | undefined>(null)
  const { i18n } = useLingui()
  const { isMobile, isTablet } = useBreakpoint()
  const { width } = useWindowSize()

  const menuItems = []

  useEffect(() => {
    const appearanceCookieBool =
      typeof appearanceCookie === 'boolean'
        ? appearanceCookie
        : appearanceCookie === undefined
          ? undefined
          : appearanceCookie === 'true'

    setDarkTheme(appearanceCookieBool)
  }, [appearanceCookie])

  if (width <= 1301 || isImpersonating) {
    let label = isImpersonating ? workspace?.name : user.email
    if (workspace?.id) label += ` (#${workspace.id})`
    menuItems.push({
      label,
    })
  }

  if (workspaces.length > 1) {
    const getLeftIconColor = (workspace: IWorkspaceStore) => {
      if (workspace.plan === 1) {
        return 'rgb(19, 205, 240)'
      }

      if (workspace.plan === 2) {
        return 'rgb(97, 191, 173)'
      }

      return '#ccc'
    }

    const workspaceMenuItems = workspaces.map((ws: IWorkspacesStoreArrItem) => ({
      uuid: ws.uuid,
      label: ws.name,
      leftIcon: 'account_circle',
      leftIconColor: getLeftIconColor(ws as IWorkspaceStore),
      onClick: () => (switchWorkspace && ws.uuid ? switchWorkspace(ws.uuid) : null),
      selected: ws.uuid === workspace.uuid,
    }))

    menuItems.push(
      {
        leftIcon: 'workspaces',
        label: <Trans>Workspace</Trans>,
        secondaryText: workspace.name,
        ...(isMobile && { placement: 'bottom-start' }),
        ...withMenuPopup({
          closeParentPopup: closePopup,
          label: <Trans>Select workspace</Trans>,
          onChange: (item: { uuid: string }) => {
            switchWorkspace && switchWorkspace(item.uuid)
          },
          items: workspaceMenuItems,
          withSearch: true,
        }),
      },
      {},
    )
  }

  if (appSettings.enableLanguageSelector) {
    const languageLabel = <Trans>Language</Trans>
    const languageItems = withLanguageMenuItems(setLocale, i18n.locale)

    menuItems.push({
      leftIcon: <Flag language={appLanguage} size={24} />,
      label: languageLabel,
      ...(isMobile
        ? // @ts-expect-error TODO refactor withSubmenuModal to TS
          withSubmenuModal({
            items: languageItems,
            modalTitle: languageLabel,
          })
        : { subMenuItems: languageItems }),
    })
  }

  menuItems.push({
    leftIcon: 'dark_mode',
    label: <Trans>Theme</Trans>,
    subMenuItems: [
      {
        leftIcon: 'light_mode',
        label: <Trans>Light</Trans>,
        onClick: () => {
          appearanceCookieSet(false)
        },
        ...(!darkTheme && {
          rightIcon: 'check',
        }),
      },
      {
        leftIcon: 'dark_mode',
        label: <Trans>Dark</Trans>,
        onClick: () => {
          appearanceCookieSet(true)
        },
        ...(darkTheme && {
          rightIcon: 'check',
        }),
      },
      // {
      //   leftIcon: 'computer',
      //   label: <Trans>Default</Trans>,
      //   onClick: () => {
      //     workspace.custom_theme.resetCookie()
      //   },
      //   ...(!isDarkThemeCookie && {
      //     rightIcon: 'check',
      //   }),
      // },
    ],
  })

  if (permissions.user_settings?.can_read || permissions.user_management?.can_read) {
    menuItems.push({
      leftIcon: 'settings',
      href: '/user/settings',
      label: <Trans>Settings</Trans>,
      onClick: () => {
        routerPush('/user/settings')
      },
    })
  }

  if (!isImpersonating) {
    menuItems.push({
      hoverVariant: 'error',
      leftIcon: 'exit_to_app',
      label: <Trans>Logout</Trans>,
      href: '/logout',
      onClick: () => {
        routerPush('/logout')
      },
    })
  }

  // Admin
  if (user.is_salesman || isImpersonating) {
    menuItems.push(
      {},
      {
        label: <Trans>Admin</Trans>,
      },
    )
  }

  // Redirect to workspace settings page
  if (workspace?.id) {
    menuItems.push({
      hoverVariant: 'secondary',
      leftIcon: 'admin_panel_settings',
      label: <Trans>Workspace admin</Trans>,
      href: `/staff/admin/workspaces/${workspace.id}`,
    })
  }

  if ((user.is_salesman || isImpersonating) && !isTablet) {
    menuItems.push({
      hoverVariant: 'secondary',
      leftIcon: 'contact_page',
      label: <Trans>Customers</Trans>,
      href: '/staff/admin/customers',
    })
  }

  if (isImpersonating) {
    menuItems.push({
      hoverVariant: 'secondary',
      leftIcon: 'exit_to_app',
      label: <Trans>Log back in</Trans>,
      onClick: () => {
        searchBarReset()
        unimpersonate && unimpersonate()
      },
    })
  }

  // @ts-expect-error TODO refactor MntrMenu to TS
  return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
}

export default UserMenu
