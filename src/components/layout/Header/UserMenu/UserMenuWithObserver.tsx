import setLocale from '~/helpers/i18n'
import { ObservedFC, observer } from '~/helpers/msts'
import UserMenu from './UserMenu'

interface IUserMenuMstProps {
  closePopup?: () => void
}

const UserMenuWithObserver: ObservedFC<IUserMenuMstProps> = ({
  appStore: { account, appLanguage, searchBar, appSettings },
  closePopup,
}) => {
  return (
    <UserMenu
      appearanceCookie={account.workspace.custom_theme.dark_theme_cookie}
      appearanceCookieSet={account.workspace.custom_theme.setCookie}
      appSettings={appSettings}
      appLanguage={appLanguage}
      closePopup={closePopup ? closePopup : undefined}
      isImpersonating={account.user.isImpersonating}
      permissions={account.workspace.permissions}
      searchBarReset={searchBar.reset}
      setLocale={setLocale}
      switchWorkspace={account.user.switchWorkspace}
      unimpersonate={account.user.unimpersonate}
      user={account.user}
      workspace={account.workspace}
      workspaces={account.workspaces}
    />
  )
}

export default observer(UserMenuWithObserver)
