import { ReactNode } from 'react'
import { styled } from 'styled-components'

import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { HEIGHT_HEADER_PX } from '~/constants'
import cutString from '~/helpers/cutString'
import { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'

const FixedBase = styled.div`
  position: fixed;
  top: ${HEIGHT_HEADER_PX}px;
  right: 0;
  bottom: 0;
`

const Overlay = styled(FixedBase)`
  left: 0;
  z-index: 500;
  background: ${({ theme }) => theme.modal.overlay};
`

const SolidBase = styled(FixedBase)`
  left: auto;
  width: 80%;
  max-width: 300px;
`

const PopupWrapper = styled(SolidBase)`
  z-index: 2100;
  background: ${({ theme }) => theme.colors.background};
  box-shadow: 0 0px 100px -20px rgba(50, 50, 93, 0.35);
`

const ButtonsWrapper = styled(SolidBase)`
  bottom: env(safe-area-inset-bottom);
  z-index: 2100;
  overflow-y: auto;
`

interface IMobileNavProps {
  close: () => void
  isImpersonating: boolean
  isOpen: boolean
  monitoringNavigationComponent?: (close?: () => void) => ReactNode
  userEmail: string
  userMenuComponent?: (closePopup?: () => void) => ReactNode
  workspace: IWorkspaceStore
}

const MobileNav = ({
  close,
  isImpersonating,
  isOpen,
  userEmail,
  userMenuComponent,
  monitoringNavigationComponent,
  workspace,
}: IMobileNavProps) => {
  return (
    <>
      {isOpen && <Overlay onClick={close} />}
      {isOpen && <PopupWrapper />}
      {isOpen && (
        <ButtonsWrapper>
          <Flex
            px={2}
            flexDirection="column"
            justifyContent="space-between"
            style={{ height: '100%', position: 'relative' }}
          >
            <Box pt={2}>
              {monitoringNavigationComponent ? monitoringNavigationComponent(close) : null}
            </Box>
            <Box pb={2}>
              <MntrButton
                bg="secondary"
                fullWidth
                icon={isImpersonating ? 'build' : 'account_circle'}
                label={cutString(isImpersonating ? workspace?.name : userEmail, 20, true)}
                popup={userMenuComponent}
                popupPlacement="bottom-start"
              />
            </Box>
          </Flex>
        </ButtonsWrapper>
      )}
    </>
  )
}

export default MobileNav
