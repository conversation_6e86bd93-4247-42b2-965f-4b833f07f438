import { Trans } from '@lingui/react/macro'
import ErrorLayout from '~/components/layout/ErrorLayout/ErrorLayout'
import { Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

export default function ErrorCustom({ heading, text }) {
  return (
    <ErrorLayout>
      <Heading mb={4} mt={6} lineHeight="1">
        {heading}
      </Heading>
      {text && <Text mb={4}>{text}</Text>}
      <MntrButton bg="tertiary" href="/" label={<Trans>Back to the main page</Trans>} rounded />
    </ErrorLayout>
  )
}
