import { styled } from 'styled-components'

export const AppBadge = styled.img`
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #000;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 0px 10px;
  transition: all 100ms ease-in;

  &:hover {
    border: 2px solid #fff;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 15px;
  }
`

export const AppFeature = styled.div`
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.errorLight};
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 1;
`

export const MainNav = styled.div`
  background: ${({ theme }) => theme.sidebar.background};
  box-shadow: ${({ theme }) => theme.sidebar.boxShadow};
  position: fixed;
  left: 0;
  top: 54px;
  padding-top: 10px;
  bottom: 0;
  width: 48px;
  z-index: 900;

  a div > div {
    display: none;
  }
  a:hover div > div {
    display: inline-block;
  }
  a:hover .badge {
    display: none;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    display: none;
  }
`

export const BottomToolbar = styled.div`
  position: absolute;
  bottom: 10px;
  width: 48px;
`
