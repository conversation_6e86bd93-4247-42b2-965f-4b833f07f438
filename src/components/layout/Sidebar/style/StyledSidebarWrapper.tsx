import { styled } from 'styled-components'

interface IStyledSidebarWrapperProps {
  contentLoading: boolean
}

const StyledSidebarWrapper = styled.div<IStyledSidebarWrapperProps>`
  position: fixed;
  transition: none;
  top: 54px;
  bottom: 0px;
  display: ${(props) => (props.contentLoading ? 'none' : 'block')};
  width: 215px;
  left: 48px;
  overflow-y: auto;
  overflow-x: hidden;
  background: transparent;

  ::-webkit-scrollbar {
    width: 0.6em;
  }

  *::-webkit-scrollbar-track {
    background-color: ${({ theme }) => theme.colors.background};
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background: ${({ theme }) => theme.colors.scrollbar};
    outline: 0px solid slategrey;
  }
`

export default StyledSidebarWrapper
