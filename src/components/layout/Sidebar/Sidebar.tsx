import { PropsWithChildren } from 'react'

import <PERSON>bar<PERSON>ainNav from '~/components/layout/Sidebar/SidebarMainNav'
import StyledSidebarWrapper from '~/components/layout/Sidebar/style/StyledSidebarWrapper'
import { Box } from '~/components/misc/Mntr'
import { ObservedFC, observer } from '~/helpers/msts'

interface ISidebarProps extends PropsWithChildren {
  activeMainNav: string
}

const Sidebar: ObservedFC<ISidebarProps> = ({
  appStore: {
    account,
    topics,
    viewport: { visibleSidebar },
  },
  activeMainNav,
  children,
}) => {
  return (
    <div>
      {visibleSidebar && <SidebarMainNav activeMainNav={activeMainNav} />}
      {visibleSidebar && (
        <StyledSidebarWrapper
          contentLoading={topics.isLoading || !account.isLoaded}
          data-dnd-scrollable-container="sidebar"
        >
          <Box pb={4} pr={0} mt="8px">
            {children}
          </Box>
        </StyledSidebarWrapper>
      )}
    </div>
  )
}

export default observer(Sidebar)
