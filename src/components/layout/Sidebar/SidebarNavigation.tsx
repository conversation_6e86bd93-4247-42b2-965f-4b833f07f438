'use client'

import { useLingui } from '@lingui/react/macro'
import { styled } from 'styled-components'

import { useBreakpoint } from '~/app/lib/use-breakpoint'
import { UserProps } from '~/app/types/user'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton, { MntrButtonProps } from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { IPermissionsStore } from '~/store/models/account/workspace/permissions/PermissionsStore'
import { IAppSettings } from '~/store/models/appSettings/AppSettings'
import { AppBadge, AppFeature, BottomToolbar, MainNav } from './style/StyledMainNav'

const RelativeBox = styled(Box)`
  position: relative;

  &:hover {
    ${AppFeature} {
      display: none;
    }
  }
`

const DownloadAppButton = styled(Flex)`
  justify-content: space-between;
  padding: 0 16px 10px;
  gap: 10px;
`

interface INavButtonProps extends MntrButtonProps, React.PropsWithChildren {
  active?: boolean
  appFeatures?: Record<string, boolean>
  appFeatureId?: string
  key?: string
}

const NavButton = ({
  active,
  appFeatures,
  appFeatureId,
  children,
  key,
  ...rest
}: INavButtonProps) => {
  return (
    <RelativeBox>
      {appFeatures && appFeatureId && appFeatures[appFeatureId] && <AppFeature data-e2e="hidden" />}
      <MntrButton
        isSidebar
        active={active}
        bg="transparentSidebar"
        placement="right"
        key={key}
        {...rest}
      />
      {children}
    </RelativeBox>
  )
}

interface ISidebarProps {
  activeMainNav?: string
  appFeatures: Record<string, boolean>
  appLanguage: string
  appSettings: IAppSettings
  hasIframeEmbedUrl: boolean
  limits: Record<string, number>
  permissions: IPermissionsStore
  user: UserProps
}

export default function SidebarNavigation({
  activeMainNav,
  appFeatures,
  appLanguage,
  appSettings,
  hasIframeEmbedUrl,
  limits,
  permissions,
  user,
}: ISidebarProps) {
  const { t } = useLingui()
  const { isTablet } = useBreakpoint()

  if (isTablet || !user.is_active) {
    return null
  }

  const { appName, contact, roadmapUrl, supportUrls, navigation, mobileAppUrls } = appSettings
  const { showSidebarPromoApps } = navigation

  // render performance optimization
  const activeTVR = activeMainNav === 'tvr'
  const activeRoadmap = activeMainNav === 'roadmap'
  const activeMonitoring = activeMainNav === 'monitoring'
  const activeCustomers = activeMainNav === 'customers'
  const activeMedialist = activeMainNav === 'authors'
  const activeNewsroom = activeMainNav === 'newsroom'
  const activeEmailing = activeMainNav === 'emailing'
  const activeBehavio = activeMainNav === 'behavio'
  const activeIframe = activeMainNav === 'iframe'
  const visibleSupport = supportUrls || contact || mobileAppUrls
  const visibleTvr = permissions.tvr_feed?.can_read || showSidebarPromoApps.tvr
  const visibleMedialist = permissions.authors_database?.can_read || showSidebarPromoApps.medialist
  const visibleNewsroom = permissions.newsroom_blog?.can_read || showSidebarPromoApps.newsroom
  const visibleEmailing = permissions.newsroom_emailing?.can_read || showSidebarPromoApps.emailing
  const visibleBehavio = limits.allow_behavio
  const visibleIframeEmbed = hasIframeEmbedUrl

  return (
    <MainNav>
      <NavButton
        active={activeMonitoring}
        href="/"
        icon="language"
        appFeatureId="monitoring"
        tooltip={t`Monitoring`}
      />
      {visibleTvr && (
        <NavButton
          active={activeTVR}
          href="/crisis-communication"
          icon="campaign"
          appFeatureId="crisisCommunication"
          iconOffsetX={1}
          appFeatures={appFeatures}
          tooltip={t`Crisis communication`}
        />
      )}
      {visibleMedialist && (
        <NavButton
          active={activeMedialist}
          icon="groups"
          appFeatureId="authors"
          tooltip={t`Medialist`}
          href="/authors"
        />
      )}
      {visibleNewsroom && (
        <NavButton
          active={activeNewsroom}
          href="/newsroom"
          icon="newspaper"
          appFeatureId="newsroom"
          tooltip={t`Newsroom`}
        />
      )}
      {visibleEmailing && (
        <NavButton
          active={activeEmailing}
          href="/emailing"
          appFeatureId="emailing"
          appFeatures={appFeatures}
          icon="outgoing_mail"
          iconOffsetX={1}
          tooltip={t`Emailing`}
        />
      )}
      {visibleBehavio && (
        <NavButton
          active={activeBehavio}
          href="/brand-tracking"
          appFeatureId="behavio"
          appFeatures={appFeatures}
          icon="experiment"
          iconOffsetX={1}
          tooltip={t`Brand Tracking`}
        />
      )}
      {visibleIframeEmbed && (
        <NavButton
          active={activeIframe}
          href="/external-analytics"
          appFeatureId="iframe"
          icon="finance"
          iconOffsetX={1}
          tooltip={t`External analytics`}
        />
      )}
      <BottomToolbar>
        {user.is_salesman && (
          <NavButton
            active={activeCustomers}
            href="/staff/admin/customers"
            icon="contact_page"
            tooltip={t`Customers`}
          />
        )}
        {user.is_publisher && (
          <NavButton
            href={`${process.env.NEXT_PUBLIC_BACKEND_URL}/newsstand/`}
            icon="account_balance"
            tooltip={t`Publisher`}
          />
        )}
        {roadmapUrl && (
          <NavButton
            active={activeRoadmap}
            href={roadmapUrl}
            target="_blank"
            icon="map"
            tooltip={t`Roadmap`}
          />
        )}
        {visibleSupport && (
          <NavButton
            icon="help"
            tooltip={t`Support`}
            popupPlacement="bottom-end"
            popup={(closePopup) => {
              const menuItems = supportUrls
                ? [
                    { label: t`Help` },
                    {
                      leftIcon: 'chevron_right',
                      label: t`How to use ${appName}`,
                      href: supportUrls.howToUseUrl,
                      target: '_blank',
                    },
                    { label: t`Media services` },
                    {
                      leftIcon: 'chevron_right',
                      label: t`Media analysis`,
                      href: supportUrls.mediaAnalysisUrl,
                      target: '_blank',
                    },
                    {
                      leftIcon: 'chevron_right',
                      label: t`Management summaries`,
                      href: supportUrls.managementSummariesUrl,
                      target: '_blank',
                    },
                    {
                      leftIcon: 'chevron_right',
                      label: t`AVE and sentiment`,
                      href: supportUrls.aveAndSentimentUrl,
                      target: '_blank',
                    },
                    {
                      leftIcon: 'chevron_right',
                      label: t`Translations`,
                      href: supportUrls.translationsUrl,
                      target: '_blank',
                    },
                    {
                      leftIcon: 'chevron_right',
                      label: t`SMS alerts`,
                      href: supportUrls.smsAlertsUrl,
                      target: '_blank',
                    },
                    {
                      leftIcon: 'chevron_right',
                      label: t`Crisis communication`,
                      href: supportUrls.crisisComUrl,
                      target: '_blank',
                    },
                  ]
                : []

              if (contact) {
                menuItems.push({ label: t`Contact` })

                if (contact.phoneMobile) {
                  menuItems.push({
                    leftIcon: 'phone_iphone',
                    label: contact.phoneMobile,
                    href: `tel:${contact.phoneMobile}`,
                    target: '_blank',
                  })
                }

                if (contact.phoneMain) {
                  menuItems.push({
                    leftIcon: 'phone',
                    label: contact.phoneMain,
                    href: `tel:${contact.phoneMain}`,
                    target: '_blank',
                  })
                }

                if (contact.supportEmail) {
                  menuItems.push({
                    leftIcon: 'mail',
                    label: contact.supportEmail,
                    href: `mailto:${contact.supportEmail}`,
                    target: '_blank',
                  })
                }
              }
              return (
                <Box>
                  {/* @ts-expect-error TODO refactor MntrMenu */}
                  <MntrMenu closePopup={closePopup} menuItems={menuItems} width={300} />
                  {mobileAppUrls && (
                    <Box>
                      <MntrMenuHeading label={t`Mobile Apps`} />
                      <DownloadAppButton>
                        {mobileAppUrls.appStore && (
                          <Box>
                            <a href={mobileAppUrls.appStore} target="_blank">
                              <AppBadge
                                src={`/static/app-store-badge-${appLanguage}.png`}
                                width={120}
                                height={40}
                              />
                            </a>
                          </Box>
                        )}
                        {mobileAppUrls.playStore && (
                          <Box>
                            <a href={mobileAppUrls.playStore} target="_blank">
                              <AppBadge
                                src={`/static/google-play-badge-${appLanguage}.png`}
                                width={120}
                                height={40}
                              />
                            </a>
                          </Box>
                        )}
                      </DownloadAppButton>
                    </Box>
                  )}
                </Box>
              )
            }}
          />
        )}
      </BottomToolbar>
    </MainNav>
  )
}
