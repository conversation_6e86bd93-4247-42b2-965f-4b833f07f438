import SidebarNavigation from '~/components/layout/Sidebar/SidebarNavigation'
import { Observed<PERSON>, observer } from '~/helpers/msts'

interface ISidebarProps {
  activeMainNav?: string
}

const SidebarMainNav: ObservedFC<ISidebarProps> = ({
  appStore: { account, appLanguage, appSettings },
  activeMainNav,
}) => {
  return (
    <SidebarNavigation
      activeMainNav={activeMainNav}
      appFeatures={account.user.frontend_storage.appFeatures}
      appLanguage={appLanguage}
      appSettings={appSettings}
      hasIframeEmbedUrl={!!account.workspace?.iframe_embed_url}
      limits={account.workspace?.limits}
      permissions={account.workspace?.permissions}
      user={account.user}
    />
  )
}
export default observer(SidebarMainNav)
