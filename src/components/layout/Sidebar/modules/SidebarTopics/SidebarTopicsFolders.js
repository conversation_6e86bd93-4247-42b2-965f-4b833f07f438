import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import { css, styled } from 'styled-components'
import FormFilterItems from '~/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems'
import CreateFolderAddTopic from '~/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic'
import { getMenuItems } from '~/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { FILTER_VISIBLE_LIMIT } from '~/constants'
import {
  DND_POPUP_ZINDEX_BUTTON_GROUP,
  DND_POPUP_ZINDEX_FORCE_ADD_TOPIC,
} from '~/constants/zIndexes'
import { observer } from '~/helpers/mst'

const MenuWrapper = styled(Box)`
  ${({ disabled }) => {
    return (
      !disabled &&
      css`
        max-height: ${({ height }) => height * 0.5}px;
        overflow-y: auto;
      `
    )
  }}
`

const SidebarTopicsFolders = ({
  ['data-e2e']: dataE2E,
  appStore: {
    account,
    filter,
    topics,
    topics: { setListFilter, listFilter, filteredFolders, toggleVisibility },
    viewport: { height },
  },
  customRoute,
  disableRedirect,
  feedId,
  getFeedMap,
  type,
  enableTopicActions,
  enableTopicDailyCounter,
  activeSubmenu,
  forceAddToTopicMonitor,
  closePopup = identityFn,
  buttonGroupZIndex = DND_POPUP_ZINDEX_BUTTON_GROUP,
  enableScroll,
  p,
}) => {
  const filterStore = feedId ? getFeedMap(type || 'feed').get(feedId).filter : filter
  const topicMonitors = filterStore.data.topic_monitors || ''
  const activeTopicMonitors = topicMonitors.split(',').map((item) => parseInt(item))
  const sharedRedirectProps = {
    customRoute,
    feedId,
    type,
    disableRedirect,
  }

  if (!account.workspace?.permissions.monitoring_feed.can_read) {
    return null
  }
  return (
    <MenuWrapper data-e2e={dataE2E} disabled={!enableScroll} height={height} p={p}>
      <CreateFolderAddTopic />
      <Flex pb={2} flexDirection="column" gap={2}>
        {topics.list.length > FILTER_VISIBLE_LIMIT && (
          <Box mt={-1}>
            <FormFilterItems
              bgColor="background"
              iconColor="black"
              noPadding
              onChange={(model) => setListFilter(model.filter)}
              placeholder={t`Filter topics`}
              value=""
            />
          </Box>
        )}

        <MntrMenu
          filterStore={filterStore}
          menuItems={getMenuItems({
            account,
            activeSubmenu,
            activeTopicMonitors,
            enableTopicActions,
            enableTopicDailyCounter,
            filteredFolders,
            filterStore,
            sharedRedirectProps,
            forceAddToTopicMonitor,
            closePopup,
            hideTopic: (data) => toggleVisibility(data),
            actionsZIndex: 4999,
          })}
          filterData={filter}
          onDrag={(folderId, topicId, move_to_index) => {
            return topics.moveTopic(folderId, {
              move_to_index,
              topic_monitors_ids: [topicId],
            })
          }}
          onDragFolder={topics.moveFolder}
          enableOutsideClick
          buttonGroupZIndex={buttonGroupZIndex}
          {...(forceAddToTopicMonitor && {
            modalZIndex: DND_POPUP_ZINDEX_FORCE_ADD_TOPIC,
          })}
          canWriteTopics={account.workspace?.permissions.topics.can_write}
          hideFolder={toggleVisibility}
          isFiltered={!!listFilter}
        />

        {!filteredFolders.length && listFilter && (
          <Box p={3} pt={0} color="grey">
            <Trans>No results found</Trans>
          </Box>
        )}
      </Flex>
    </MenuWrapper>
  )
}

export default observer(SidebarTopicsFolders)
