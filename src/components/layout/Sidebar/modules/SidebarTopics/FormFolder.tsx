import { t } from '@lingui/core/macro'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'

interface IFormRenameFolderProps extends IMntrFormProps {
  folderName?: string
}

const FormFolder = ({ folderName, onSubmit }: IFormRenameFolderProps) => {
  const formSchema: IFormSchemaItem[] = [
    {
      name: 'label',
      autoFocus: true,
      autoComplete: 'off',
      initialValue: folderName,
    },
    {
      actions: ({ values }) => {
        return [
          {
            rounded: true,
            type: 'submit',
            bg: 'secondary',
            icon: 'save',
            disabled: !values?.label,
            label: t`Save`,
          },
        ]
      },
    },
  ]
  return <MntrForm contentPadding={3} onSubmit={onSubmit} schema={formSchema} />
}

export default FormFolder
