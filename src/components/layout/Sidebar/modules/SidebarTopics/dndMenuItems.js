import { Trans } from '@lingui/react/macro'
import color from 'color'
import Router from 'next/router'
import { MEDIALIST_ARTICLES_PREFIX } from '~/constants'
import { DND_POPUP_ZINDEX } from '~/constants/zIndexes'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { routerPush } from '~/helpers/router'

const getCommonTopicActions = ({ account, activeSubmenu, item }) => {
  const topicActions = []

  // Divider
  topicActions.push({})

  // Articles
  if (activeSubmenu !== 'articles' && account.workspace?.permissions.monitoring_feed.can_read) {
    topicActions.push({
      label: <Trans>Articles</Trans>,
      leftIcon: 'view_stream',
      href: `/?topic_monitors=${item.id}`,
    })
  }

  // Analytics
  if (activeSubmenu !== 'analytics' && account.workspace?.permissions.analytics.can_read) {
    topicActions.push({
      label: <Trans>Analytics</Trans>,
      leftIcon: 'show_chart',
      href: `/analytics?topic_monitors=${item.id}`,
    })
  }

  // Medialist
  if (activeSubmenu !== 'medialist' && account.workspace?.permissions.authors_database.can_read) {
    topicActions.push({
      label: <Trans>Medialist</Trans>,
      leftIcon: 'person',
      href: `/authors?${MEDIALIST_ARTICLES_PREFIX}topic_monitors=${item.id}`,
    })
  }

  // Reports
  if (activeSubmenu !== 'reports' && account.workspace?.permissions.email_reports.can_read) {
    topicActions.push({
      label: <Trans>Reports</Trans>,
      leftIcon: 'mail',
      href: `/reports?topic_monitors=${item.id}`,
    })
  }

  // Divider
  topicActions.push({})

  // Settings
  if (account.workspace?.permissions.topics.can_read) {
    topicActions.push({
      label: <Trans>Settings</Trans>,
      leftIcon: 'settings',
      href: `/topics?topic_monitors=${item.id}`,
    })
  }

  return topicActions
}

export const getMenuItems = ({
  account,
  activeSubmenu,
  activeTopicMonitors,
  closePopup,
  enableTopicActions,
  enableTopicDailyCounter,
  enableTopicMonitorsCompare,
  filterStore,
  filteredFolders,
  forceAddToTopicMonitor,
  rounded = true,
  sharedRedirectProps,
  topicId,
  withQueryKeysReplace,
  disablePreviewMulti,
  hideTopic,
  actionsZIndex = DND_POPUP_ZINDEX,
}) => {
  return filteredFolders
    .filter((folder) => {
      if (folder.topic_monitors.length === 0 || folder.is_default_folder) {
        return true
      }

      return !folder.topic_monitors.every((topic) => topic.data.is_hidden_for_user)
    })
    .map((folder) => {
      return {
        // TODO: Refactor to avoid this object cloning pattern. When spreading the
        // folder object, we lose the store-bound methods due to object cloning.
        // To preserve the store actions, we need to explicitly reassign the
        // methods below.
        ...folder,
        delete: folder.delete,
        rename: folder.rename,
        toggleIsOpen: folder.toggleIsOpen,
        topic_monitors: folder.topic_monitors
          .filter((item) => !item.data.is_hidden_for_user)
          .map((item) => {
            const selected = activeTopicMonitors.includes(item.id)
            let replaceId = undefined
            if (selected) replaceId = item.id
            // remove current & replace by clicked:
            if (!enableTopicMonitorsCompare && topicId) replaceId = topicId

            const topicItem = {
              ...item,
              folderId: item.folder?.id,
              activeBackground: color(item.data.color).alpha(0.2).toString(),
              bg: 'transparent',
              color: item.data.color,
              data: item.data,
              counter: enableTopicDailyCounter ? item.data.daily_count : null,
              label: item.data.name,
              hoverVariant: 'light',
              image_url: item.data.image_url,
              name: item.data.name,
              actionsZIndex,
              onClick: () => {
                if (forceAddToTopicMonitor) {
                  const { callback, articleItem } = forceAddToTopicMonitor
                  callback(articleItem, item.id)
                  closePopup()
                  return
                }
              },
              // actionsZIndex: DND_POPUP_ZINDEX, // should I?
              redirectProps: {
                ...sharedRedirectProps,
                withQueryKeysReplace: withQueryKeysReplace || enableTopicMonitorsCompare,
                set: { topic_monitors: forceAddToTopicMonitor ? undefined : item.id },
                replace: replaceId,
              },
              rounded,
              selected,
              topicId: item.id,
              actions: enableTopicActions
                ? () => {
                    const topicActions = []

                    // Add To Filters
                    if (!selected) {
                      topicActions.push({
                        label: <Trans>Add to filters</Trans>,
                        leftIcon: 'add',
                        hoverVariant: 'secondary',
                        ...(disablePreviewMulti && {
                          onClick: () => {
                            filterStore.resetFilterMulti()
                            filterStore.addFilterMulti({
                              value: item.id,
                              text: item.data.name,
                            })
                          },
                        }),
                        redirectProps: {
                          ...sharedRedirectProps,
                          withQueryKeysReplace: true,
                          set: { topic_monitors: item.data.id },
                        },
                      })
                    }

                    // Remove From Filters
                    if (selected) {
                      topicActions.push({
                        label: <Trans>Remove from filters</Trans>,
                        leftIcon: 'cancel',
                        hoverVariant: 'error',
                        onClick: () => {
                          filterStore.removeFilterMulti(item.id)
                        },
                        redirectProps: {
                          ...sharedRedirectProps,
                          withQueryKeysReplace: true,
                          replace: item.data.id,
                          set: { topic_monitors: undefined },
                        },
                      })
                    }

                    if (hideTopic) {
                      topicActions.push({
                        label: <Trans>Hide topic</Trans>,
                        leftIcon: 'visibility_off',
                        hoverVariant: 'secondary',
                        modalZIndex: 9999,
                        ...withModalRemove({
                          modalIcon: 'visibility_off',
                          onSubmit: () => {
                            hideTopic([{ id: item.id, is_hidden_for_user: true }])
                            routerPush(Router.pathname)
                          },
                          title: (
                            <>
                              <Trans>Hide topic</Trans>?
                            </>
                          ),
                          message: (
                            <Trans>
                              Topic <b>{item.data.name}</b> will be hidden.
                            </Trans>
                          ),
                          submitLabel: <Trans>Hide</Trans>,
                        }),
                      })
                    }

                    topicActions.push(
                      ...getCommonTopicActions({
                        account,
                        activeSubmenu,
                        item,
                      }),
                    )

                    return topicActions
                  }
                : undefined,
            }
            if (forceAddToTopicMonitor) delete topicItem.redirectProps
            return topicItem
          }),
      }
    })
}

export const getMenuItemsReports = ({
  account,
  activeSubmenu,
  closePopup,
  enableTopicDailyCounter,
  filteredFolders,
  handleSelect,
  handleSelectOne,
  selected: selectedTopics,
}) => {
  const items = filteredFolders.map((folder) => {
    return {
      // TODO: Refactor to avoid this object cloning pattern. When spreading the
      // folder object, we lose the store-bound methods due to object cloning.
      // To preserve the store actions, we need to explicitly reassign the
      // methods below.
      ...folder,
      delete: folder.delete,
      rename: folder.rename,
      toggleIsOpen: folder.toggleIsOpen,
      topic_monitors: folder.topic_monitors.map((item) => {
        const selected = selectedTopics?.includes(item.id)
        const handleClick = () => {
          handleSelectOne(item.id, selected)
          closePopup()
        }

        const topicItem = {
          ...item,
          folderId: item.folder?.id,
          activeBackground: color(item.data.color).alpha(0.2).toString(),
          bg: 'transparent',
          color: item.data.color,
          data: item.data,
          counter: enableTopicDailyCounter ? item.data.daily_count : null,
          label: item.data.name,
          hoverVariant: 'light',
          image_url: item.data.image_url,
          name: item.data.name,
          onClick: handleClick,
          actionsZIndex: DND_POPUP_ZINDEX,
          rounded: true,
          selected,
          topicId: item.id,
          actions: () => {
            const topicActions = []

            topicActions.push({
              label: selected ? <Trans>Remove from report</Trans> : <Trans>Add to report</Trans>,
              leftIcon: selected ? 'cancel' : 'add',
              hoverVariant: selected ? 'error' : 'secondary',
              onClick: handleClick,
            })

            topicActions.push(
              ...getCommonTopicActions({
                account,
                activeSubmenu,
                item,
              }),
            )

            return topicActions
          },
        }

        return topicItem
      }),
    }
  })

  return { items, handleSelect, selected: selectedTopics }
}
