import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import FormFolder from '~/components/layout/Sidebar/modules/SidebarTopics/FormFolder'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import TopicIcon from '~/components/misc/TopicIcon/TopicIcon'
import FormAddTopic from '~/components/topics/Content/TopicsList/Keyword/FormAddTopic'
import withModalToggleVisibility from '~/helpers/modal/withModalToggleVisibility/withModalToggleVisibility'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'

const CreateFolderAddTopic = ({
  appStore: {
    account,
    topics: {
      toggleVisibility,
      folders,
      addNewTopic,
      clearErrors,
      createFolder,
      errorsAddTopic,
      editTopicKeywordLoading,
      generateColor,
    },
    viewport,
  },
}) => {
  const { pathname } = useRouter()

  const topicMenu = [
    {
      label: <Trans>Topics</Trans>,
      buttonGroup: account.workspace?.permissions.topics.can_write
        ? [
            {
              iconBg: 'primary',
              icon: 'more_vert',
              popupPlacement: 'bottom-start',
              popup: (closePopup) => {
                const menuItems = [
                  {
                    hoverVariant: 'secondary',
                    label: <Trans>Add New Topic</Trans>,
                    leftIcon: 'bookmark_add',
                    modal: (closeModal) => {
                      return (
                        <Box p={2}>
                          <FormAddTopic
                            isAdd
                            initialValues={{
                              color: generateColor(),
                              is_traditional_media: true,
                              is_social_media: !account.workspace.is_traditional_media_active,
                            }}
                            isLoading={editTopicKeywordLoading}
                            onSubmit={(model) => {
                              addNewTopic(model, closeModal)
                            }}
                            isTouchDevice={viewport.isTouchDevice}
                            activeCategoryTypes={account.enums.category_type}
                            errors={errorsAddTopic}
                          />
                        </Box>
                      )
                    },
                    onClose: () => {
                      clearErrors()
                    },
                    modalTitle: <Trans>Add New Topic</Trans>,
                  },
                  {
                    bg: 'flat',
                    hoverVariant: 'secondary',
                    leftIcon: 'folder',
                    label: <Trans>Create Folder</Trans>,
                    modalTitle: <Trans>Create Folder</Trans>,
                    modal: (closeModal) => {
                      return (
                        <FormFolder
                          closeModal={closeModal}
                          onSubmit={(values) => {
                            createFolder(values)
                            closeModal()
                          }}
                        />
                      )
                    },
                  },
                ]

                menuItems.push({
                  iconBg: 'primary',
                  leftIcon: 'visibility_off',
                  label: <Trans>Manage hidden topics</Trans>,
                  ...withModalToggleVisibility({
                    closePopup,
                    modalTitle: t`Manage hidden topics`,
                    legend: t`List of topics`,
                    info: t`To hide some topics from the list, uncheck these topics. The user can add hidden topics back to their feed again at any time if necessary.`,
                    groups: folders
                      .filter(({ topic_monitors }) => {
                        return topic_monitors.length > 0
                      })
                      .map(({ topic_monitors, id, name, is_default_folder }) => {
                        return {
                          id: id,
                          is_folder: !is_default_folder,
                          label: name,
                          items: topic_monitors.map((item) => {
                            return {
                              id: item.id,
                              label: (
                                <Flex center gap={1}>
                                  <TopicIcon id={item.id} size={24} />
                                  {item.data.name}
                                </Flex>
                              ),
                              is_hidden_for_user: !item.data.is_hidden_for_user,
                            }
                          }),
                        }
                      }),
                    onSubmit: (model) => {
                      toggleVisibility(model)
                      routerPush(pathname)
                    },
                  }),
                })

                return <MntrMenu closePopup={closePopup} menuItems={menuItems} />
              },
            },
          ]
        : null,
    },
  ]

  return <MntrMenu menuItems={topicMenu} />
}

export default observer(CreateFolderAddTopic)
