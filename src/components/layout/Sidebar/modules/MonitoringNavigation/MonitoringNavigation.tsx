import MonitoringNavigationDumb from '~/app/components/monitoring-navigation'
import { ObservedFC, observer } from '~/helpers/msts'

interface IMonitoringNavigationProps {
  mobileNavigation?: boolean
  onClick?: () => void
}

const MonitoringNavigation: ObservedFC<IMonitoringNavigationProps> = ({
  appStore: {
    appSettings,
    filter,
    account,
    exportList,
    monitoring: { feedMap },
  },
  mobileNavigation,
  onClick,
}) => {
  return (
    <MonitoringNavigationDumb
      appSettings={appSettings}
      exportBasketId={account.export_basket_id}
      exportList={exportList}
      feedMap={feedMap}
      filterUrl={filter.url}
      isImpersonating={account.user.isImpersonating}
      isSalesman={account.user.is_salesman}
      mobileNavigation={mobileNavigation}
      onClick={onClick}
      permissions={account.workspace.permissions}
      workspace={account.workspace}
    />
  )
}

export default observer(MonitoringNavigation)
