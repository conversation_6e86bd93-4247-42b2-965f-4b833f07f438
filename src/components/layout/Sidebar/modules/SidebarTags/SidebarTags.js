import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import color from 'color'
import fuzzy from 'fuzzy'
import { useRouter } from 'next/router'
import { useState } from 'react'
import FormNewTag from '~/components/forms/tags/FormNewTag/FormNewTag'
import FormFilterItems from '~/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { IconWrapper } from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import { FILTER_VISIBLE_LIMIT } from '~/constants'
import withModalRemove from '~/helpers/modal/withModalRemove'
import withModalToggleVisibility from '~/helpers/modal/withModalToggleVisibility/withModalToggleVisibility'
import { observer } from '~/helpers/mst'
import removeAccents from '~/helpers/removeAccents'
import { routerPush } from '~/helpers/router'
import modalEditTags from './modalEditTags'

const SidebarTags = ({
  appStore: {
    filter,
    account,
    monitoring: { tags: monitoringTags },
    viewport: { isTouchDevice },
  },
  customRoute,
  disableRedirect,
  feedId,
  getFeedMap,
  type,
  tags = monitoringTags,
  canEditTags = account.workspace?.permissions.monitoring_feed.can_write ||
    account.workspace?.permissions.archive_feed.can_write,
  filterKey = 'tags',
  label = <Trans>Article Tags</Trans>,
  hideTags = false,
}) => {
  const { pathname } = useRouter()
  const [errors, setErrors] = useState(undefined)
  const [editTagErrors, setEditTagErrors] = useState(undefined)
  const [filterValue, setFilterValue] = useState('')

  const disableDrag = !canEditTags
  const tagsArr = tags.list

  // skip render with read only permissions && empty tags
  if (!tagsArr.length && !canEditTags) {
    return <span />
  }

  const filterStore = feedId ? getFeedMap(type || 'feed').get(feedId).filter : filter
  const filterTags = filterStore.data[filterKey] || ''
  const activeTags = filterTags.split(',').map((item) => parseInt(item))
  const sharedRedirectProps = {
    customRoute,
    feedId,
    type,
    disableRedirect,
  }

  const tagMenu = [
    {
      label,
      buttonGroup: canEditTags
        ? [
            {
              iconBg: 'primary',
              icon: 'more_vert',
              popupPlacement: 'bottom-start',
              onClose: () => setErrors(undefined),
              popup: (closePopup) => {
                const menuItems = [
                  {
                    iconBg: 'primary',
                    leftIcon: 'new_label',
                    label: <Trans>New Tag</Trans>,
                    popupPlacement: 'bottom-start',
                    onClose: () => setErrors(undefined),
                    popup: (closePopup) => {
                      return (
                        <FormNewTag
                          errors={errors}
                          onSubmit={(model) => {
                            tags.create(model).then((res) => {
                              if (!res.valid) {
                                return setErrors({ label: res.errors?.label })
                              }

                              closePopup()
                            })
                          }}
                        />
                      )
                    },
                  },
                ]

                if (hideTags) {
                  menuItems.push({
                    iconBg: 'primary',
                    leftIcon: 'visibility_off',
                    label: <Trans>Manage hidden tags</Trans>,
                    ...withModalToggleVisibility({
                      closePopup,
                      modalTitle: t`Manage hidden tags`,
                      groups: [
                        {
                          items: tags.list.map((item) => {
                            return {
                              id: item.id,
                              label: (
                                <Flex center gap={1}>
                                  <IconWrapper bg={item.color}>
                                    <Icon
                                      color={color(item.color).darken(0.4).toString()}
                                      size={18}
                                    >
                                      label
                                    </Icon>
                                  </IconWrapper>

                                  {item.label}
                                </Flex>
                              ),
                              is_hidden_for_user: !item.is_hidden_for_user,
                            }
                          }),
                        },
                      ],
                      onSubmit: (model) => {
                        monitoringTags.hide(model)
                        routerPush(pathname)
                      },
                    }),
                  })
                }

                return <MntrMenu menuItems={menuItems} />
              },
            },
          ]
        : null,
    },
  ]

  // Without Taqs
  const menuItemsEmptyTag =
    tagsArr.length === 0
      ? []
      : [
          {
            label: <Trans>Without tags</Trans>,
            leftIcon: 'label_off',
            hoverVariant: 'light',
            leftIconBg: 'rgba(0,0,0,.07)',
            leftIconColor: 'black',
            activeBackground: 'rgba(0,0,0,.07)',
            leftIconHoverColor: 'black',
            rounded: true,
            onClick: () => {
              filterStore.withoutTags(
                {
                  value: null,
                  text: <Trans>Without tags</Trans>,
                },
                filterKey,
              )
            },
            bg: 'transparent',
            redirectProps: {
              ...sharedRedirectProps,
              withQueryKeysReplace: false,
              set: { [filterKey]: '' },
            },
          },
        ]

  const menuItems = []

  tagsArr
    .filter((item) => fuzzy.test(removeAccents(filterValue) || '', removeAccents(item.label)))
    .filter((item) => !item.is_hidden_for_user)
    .map((item) => {
      const selected = activeTags.includes(item.id)

      // Tags List
      menuItems.push({
        id: item.id,
        label: item.label,
        leftIcon: 'label',
        leftIconColor: color(item.color).darken(0.4).toString(),
        leftIconHoverColor: color(item.color).darken(0.6).toString(),
        leftIconActiveColor: color(item.color).darken(0.6).toString(),
        leftIconBg: item.color,
        rounded: true,
        selected,
        activeBackground: color(item.color).alpha(0.4).toString(),
        hoverVariant: 'light',
        actions: () => {
          const tagActions = []

          // Add To Filters
          if (!selected) {
            tagActions.push({
              label: <Trans>Add to filters</Trans>,
              leftIcon: 'add',
              hoverVariant: 'secondary',
              onClick: () => {
                filterStore.addFilterMulti(
                  {
                    value: item.id,
                    text: item.label,
                  },
                  filterKey,
                )
              },
              redirectProps: {
                ...sharedRedirectProps,
                withQueryKeysReplace: true,
                replaceKey: filterKey,
                set: { [filterKey]: item.id },
              },
            })
          }

          if (canEditTags && hideTags) {
            tagActions.push({
              label: <Trans>Hide tag</Trans>,
              leftIcon: 'visibility_off',
              hoverVariant: 'secondary',
              ...withModalRemove({
                modalIcon: 'visibility_off',
                onSubmit: () => {
                  monitoringTags.hide([{ id: item.id, is_hidden_for_user: true }])
                  routerPush(pathname)
                },
                title: (
                  <>
                    <Trans>Hide tag</Trans>?
                  </>
                ),
                message: (
                  <Trans>
                    Tag <b>{item.label}</b> will be hidden.
                  </Trans>
                ),
                submitLabel: <Trans>Hide</Trans>,
              }),
            })
          }

          // Remove From Filters
          if (selected) {
            tagActions.push({
              label: <Trans>Remove from filters</Trans>,
              leftIcon: 'cancel',
              hoverVariant: 'error',
              onClick: () => {
                filterStore.removeFilterMulti(item.id)
              },
              redirectProps: {
                ...sharedRedirectProps,
                withQueryKeysReplace: true,
                replace: item.id,
                replaceKey: filterKey,
                set: { [filterKey]: undefined },
              },
            })
          }

          // Divider
          tagActions.push({})

          // Edit Tag
          if (canEditTags) {
            tagActions.push({
              label: <Trans>Edit tag</Trans>,
              leftIcon: 'edit',
              ...modalEditTags({
                initialValues: item,
                isTouchDevice: isTouchDevice,
                errors: editTagErrors,
                onSubmit: (model, cb) => {
                  item.edit(model).then((res) => {
                    if (res.valid) {
                      cb()
                      setEditTagErrors(undefined)
                    } else {
                      setEditTagErrors(res.errors)
                    }
                  })
                },
              }),
            })
          }

          // Delete Tag
          if (canEditTags) {
            tagActions.push({
              label: <Trans>Delete tag</Trans>,
              leftIcon: 'delete',
              hoverVariant: 'error',
              ...withModalRemove({
                onSubmit: () => {
                  item.remove()
                },
                title: (
                  <>
                    <Trans>Delete tag</Trans>?
                  </>
                ),
                message: (
                  <Trans>
                    Tag <b>{item.label}</b> will be removed.
                  </Trans>
                ),
              }),
            })
          }

          return tagActions
        },
        onClick: () => {
          filterStore.resetFilterMulti(filterKey)
          filterStore.addFilterMulti(
            {
              value: item.id,
              text: item.label,
            },
            filterKey,
          )
        },
        bg: 'transparent',
        redirectProps: {
          ...sharedRedirectProps,
          withQueryKeysReplace: false,
          set: { [filterKey]: item.id },
        },
      })
    })

  return (
    <>
      <MntrMenu menuItems={tagMenu} />
      <Box mt={-1}>
        {tagsArr.length > FILTER_VISIBLE_LIMIT && (
          <FormFilterItems
            bgColor="background"
            iconColor="black"
            noPadding
            onChange={(model) => setFilterValue(model.filter)}
            placeholder={t`Filter tags`}
            value=""
          />
        )}
        <MntrMenu menuItems={menuItemsEmptyTag} />
        <Box mt={'-8px'}>
          <MntrMenu
            menuItems={menuItems}
            onDrag={
              disableDrag
                ? null
                : (topicId, source, destination) => tags.moveToIndex(topicId, source, destination)
            }
          />
        </Box>
        {!menuItems.length && filterValue && (
          <Box p={3} pt={0} color="grey">
            <Trans>No results found</Trans>
          </Box>
        )}
      </Box>
    </>
  )
}

export default observer(SidebarTags)
