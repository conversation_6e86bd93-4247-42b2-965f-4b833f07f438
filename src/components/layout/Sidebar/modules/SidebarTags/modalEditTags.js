import { Trans } from '@lingui/react/macro'
import FormEditTag from '~/components/forms/tags/FormEditTag/FormEditTag'
import { Box } from '~/components/misc/Mntr'

const modalEditTags = ({ onSubmit, initialValues, isTouchDevice, errors, onClose }) => {
  return {
    onClose,
    modalTitle: (
      <>
        <Trans>Edit tag</Trans>: {initialValues.label}
      </>
    ),
    modal: (closeModal) => {
      return (
        <Box>
          <FormEditTag
            errors={errors}
            onSubmit={(model) => {
              onSubmit(model, closeModal)
            }}
            initialValues={initialValues}
            closeModal={closeModal}
            isTouchDevice={isTouchDevice}
          />
        </Box>
      )
    },
  }
}

export default modalEditTags
