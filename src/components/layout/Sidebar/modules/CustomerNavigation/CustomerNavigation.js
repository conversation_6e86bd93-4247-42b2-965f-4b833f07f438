import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const CustomerNavigation = ({
  appStore: {
    admin: {
      customer: { selected },
    },
    viewport: { isTablet },
  },
}) => {
  const { pathname } = useRouter()
  const menuItems = []
  const sharedProps = {
    accent: true,
    rounded: !isTablet,
  }

  if (selected) {
    const { customer, workspaces, users, invoices, merged_customers } = selected

    menuItems.push(
      {
        label: t`Workspaces`,
        leftIcon: 'workspaces',
        href: `/staff/admin/customers/${customer.id}/workspaces`,
        selected: pathname.includes('/workspaces'),
        counter: workspaces?.length,
        ...sharedProps,
      },
      {
        label: t`Users`,
        leftIcon: 'group',
        href: `/staff/admin/customers/${customer.id}/users`,
        selected: pathname.includes('/users'),
        counter: users?.length,
        ...sharedProps,
      },
      {
        label: t`Invoices`,
        leftIcon: 'account_balance',
        href: `/staff/admin/customers/${customer.id}/invoices`,
        selected: pathname.endsWith('/invoices'),
        counter: invoices?.length,
        ...sharedProps,
      },
      {
        label: t`Expenses`,
        leftIcon: 'local_atm',
        href: `/staff/admin/customers/${customer.id}/expenses`,
        selected: pathname.endsWith('/expenses'),
        ...sharedProps,
      },
    )

    if (merged_customers?.length > 0) {
      menuItems.push({
        label: t`Merged customers`,
        leftIcon: 'call_merge',
        href: `/staff/admin/customers/${customer.id}/merged-customers`,
        selected: pathname.endsWith('/merged-customers'),
        counter: merged_customers.length,
        ...sharedProps,
      })
    }
  }

  return <MntrMenu menuItems={menuItems} />
}

export default observer(CustomerNavigation)
