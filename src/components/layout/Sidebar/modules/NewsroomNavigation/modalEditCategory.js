import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { Box } from '~/components/misc/Mntr'
import FormCreateCategory from '~/components/newsroom/forms/FormCreateCategory/FormCreateCategory'

const modalEditCategory = ({ onSubmit, initialValues, errors }) => {
  return {
    modalTitle: (
      <>
        <Trans>Edit category</Trans>: {initialValues.name}
      </>
    ),
    modal: (closeModal) => {
      return (
        <Box>
          <FormCreateCategory
            onSubmit={(model) => {
              onSubmit(model, closeModal)
            }}
            initialValues={initialValues}
            closeModal={closeModal}
            errors={errors}
            isEdit
            title={t`Edit category`}
          />
        </Box>
      )
    },
  }
}

export default modalEditCategory
