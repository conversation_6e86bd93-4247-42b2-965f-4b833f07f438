import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import RequestFeatureButton from '~/components/misc/RequestFeatureButton/RequestFeatureButton'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { observer } from '~/helpers/mst'

const StyledLimitReached = styled(Box)`
  border-top: 1px solid ${({ theme }) => theme.popper.divider};
  width: 250px;
`

const BlogsSelector = ({ appStore: { account }, newsroom, isPlain }) => {
  const blogLimit = account.workspace.limits.newsroom_blog_limit
  const blogLimitUsed = account.workspace.limits.newsroom_blog_limit_used

  return (
    <Flex mt="15px" ml={isPlain ? 0 : '11px'}>
      <MntrPaper>
        <MntrButton
          fullWidth
          bg="flat"
          icon="expand_more"
          label={
            !newsroom.isLoaded ? (
              <Trans>Loading...</Trans>
            ) : (
              <Text
                mr="auto"
                textOverflow="ellipsis"
                overflow="hidden"
                whiteSpace="nowrap"
                color="inherit"
              >
                {newsroom.selected?.title}
              </Text>
            )
          }
          popupPlacement="bottom-start"
          popup={(closePopup) => {
            const menuItems = []

            newsroom.results.forEach((blog) => {
              menuItems.push({
                label: blog.title,
                onClick: () => {
                  newsroom.saveBlogSelect(blog.id)
                  pushEvent(events.NEWSROOM_CHANGED)
                },
                href: `/newsroom/${blog.id}`,
              })
            })

            return (
              <div>
                <MntrMenu menuItems={menuItems} closePopup={closePopup} />
                {account.workspace?.permissions.newsroom_blog.can_write &&
                  blogLimit > blogLimitUsed && (
                    <Box px={2} pb={2} pt={1}>
                      <MntrButton
                        fullWidth
                        bg="secondary"
                        icon="add"
                        label={<Trans>Create new Newsroom</Trans>}
                        popupPlacement={'bottom-start'}
                        href={`/newsroom/create`}
                      />
                    </Box>
                  )}

                {account.workspace?.permissions.newsroom_blog.can_write &&
                  blogLimit <= blogLimitUsed && (
                    <StyledLimitReached px={3} pt={2} pb={2} color="black">
                      <Trans>You have reached the limit on the number of Newsrooms.</Trans>
                      <Box mt={3} mb={2} textAlign="center">
                        <RequestFeatureButton
                          id="Newsroom_count_limit"
                          showNotification
                          confirmTitle={t`Increase limit?`}
                          label={<Trans>Increase limit</Trans>}
                        />
                      </Box>
                    </StyledLimitReached>
                  )}
              </div>
            )
          }}
        />
      </MntrPaper>
    </Flex>
  )
}
export default observer(BlogsSelector)
