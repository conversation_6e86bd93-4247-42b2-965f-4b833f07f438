import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import queryString from 'query-string'
import { useState } from 'react'
import { Box, Text } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import FormCreateCategory from '~/components/newsroom/forms/FormCreateCategory/FormCreateCategory'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import BlogsSelector from './BlogsSelector'
import modalEditCategory from './modalEditCategory'

const NewsroomNavigation = ({
  appStore: { account, newsroom, router },
  activePath,
  blogId,
  mobileNavigation,
  urlParams = {},
}) => {
  const [errors, setErrors] = useState(undefined)

  if (!newsroom.selected) {
    return null
  }

  const menuItems = []
  const menuItemsCategories = []
  const menuItemsCategoriesHeading = []

  const sharedProps = {
    accent: true,
    rounded: !mobileNavigation,
  }

  // Newsroom Posts
  menuItems.push({
    label: t`Posts`,
    leftIcon: 'description',
    selected: activePath === 'posts',
    href: `/newsroom/${blogId ? blogId : newsroom.selected.id}`,
    ...sharedProps,
  })

  // Newsroom Settings
  {
    account.workspace?.permissions.newsroom_blog.can_write &&
      menuItems.push({
        label: t`Settings`,
        leftIcon: 'settings',
        selected: activePath === 'settings',
        href: `/newsroom/${blogId ? blogId : newsroom.selected.id}/settings`,
        ...sharedProps,
      })
  }

  // View publishing blog
  menuItems.push({
    label: t`View Newsroom`,
    leftIcon: 'preview',
    href: `https://${newsroom.selected.domain}`,
    target: '_blank',
    ...sharedProps,
  })

  // Newsroom Categories
  menuItemsCategoriesHeading.push({
    label: <Trans>Categories</Trans>,
    buttonGroup: account.workspace?.permissions.newsroom_blog.can_write
      ? [
          {
            icon: 'add',
            tooltip: <Trans>New Category</Trans>,
            popupPlacement: 'bottom-start',
            popup: (closePopup) => {
              return (
                <FormCreateCategory
                  mt={2}
                  errors={errors}
                  onSubmit={(model) => {
                    newsroom.selected.createCategory(model).then((res) => {
                      if (!res.valid) {
                        return setErrors({ name: res.errors?.name })
                      }

                      closePopup()
                    })
                  }}
                />
              )
            },
          },
        ]
      : [],
  })

  newsroom.selected.categories.forEach((item) => {
    menuItemsCategories.push({
      id: item.id,
      label: item.name,
      leftIcon: 'label',
      leftIconColor: item.color || '#AEBBAE',
      ...sharedProps,
      keepButtonGroupVisible: false,
      bg: 'transparent',
      selected: parseInt(item.id) === parseInt(urlParams.category),
      hoverVariant: 'light',
      activeBackground: 'rgba(0,0,0,.07)',
      actions: () => {
        const menuItems = []

        // Edit Category
        menuItems.push({
          label: <Trans>Edit category</Trans>,
          leftIcon: 'edit',
          ...modalEditCategory({
            initialValues: item,
            errors: errors,
            onSubmit: (model, cb) => {
              item.update(model).then((res) => {
                if (res.valid) {
                  cb()
                  setErrors(undefined)
                } else {
                  setErrors(res.errors)
                }
              })
            },
          }),
        })

        // Delete category
        menuItems.push({
          label: <Trans>Delete category</Trans>,
          leftIcon: 'delete',
          hoverVariant: 'error',
          ...withModalRemove({
            onSubmit: () => {
              item.remove()
            },
            title: (
              <>
                <Trans>Delete category</Trans>?
              </>
            ),
            message: (
              <Trans>
                Category <b>{item.name}</b> will be removed.
              </Trans>
            ),
          }),
        })

        return menuItems
      },
      redirectProps: {
        onClick: () => {
          const params = queryString.parse(window.location.search)
          params.category = item.id

          router.redirectTo(`/newsroom/${blogId}/?${queryString.stringify(params)}`)
        },
        customRoute: `/newsroom/${blogId}/`,
        withQueryKeysReplace: false,
        set: { category: item.id },
      },
    })
  })

  return (
    <div>
      {newsroom.selected && (
        <>
          <BlogsSelector newsroom={newsroom} />
          <MntrMenu menuItems={menuItems} />

          <MntrMenu menuItems={menuItemsCategoriesHeading} />
          <Box mt={-1}>
            <MntrMenu
              menuItems={menuItemsCategories}
              onDrag={(id, source, destination) =>
                newsroom.selected.categoryMoveToIndex(id, source, destination)
              }
            />
          </Box>

          {newsroom.selected.categories.length === 0 && (
            <Box px={3}>
              <Text color="mediumGrey">
                <Trans>No categories yet.</Trans>
              </Text>
            </Box>
          )}
        </>
      )}
    </div>
  )
}

export default observer(NewsroomNavigation)
