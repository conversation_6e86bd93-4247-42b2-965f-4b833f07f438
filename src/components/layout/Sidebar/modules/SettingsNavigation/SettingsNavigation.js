import { Trans } from '@lingui/react/macro'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import { scrollToTop } from '~/helpers/scroll'

const scrollTo = (element) => scrollToTop(element, '', 0, 300)

const SettingsNavigation = ({ appStore: { account } }) => {
  const canRead = account.workspace?.permissions.user_settings.can_read
  const canWrite = account.workspace?.permissions.user_settings.can_write
  const canReadUserManagement = account.workspace?.permissions.user_management.can_read

  const menuItems = []
  const sharedProps = {
    accent: true,
    rounded: true,
    leftIcon: 'arrow_right',
    leftIconColor: 'lightGrey',
  }

  menuItems.push({
    label: <Trans>Settings</Trans>,
  })

  if (canRead) {
    menuItems.push({
      label: <Trans>Tariff information</Trans>,
      onClick: () => scrollTo('#tariff'),
      ...sharedProps,
      leftIcon: 'fact_check',
      leftIconColor: null,
    })

    menuItems.push({
      label: <Trans>Limits</Trans>,
      onClick: () => scrollTo('#limits'),
      ...sharedProps,
    })

    menuItems.push({
      label: <Trans>Monitored media</Trans>,
      onClick: () => scrollTo('#monitoring'),
      ...sharedProps,
    })

    if (!account.workspace?.is_global_mediaboard) {
      menuItems.push({
        label: <Trans>Agency media</Trans>,
        onClick: () => scrollTo('#agency-media'),
        ...sharedProps,
      })
    }

    menuItems.push({
      label: <Trans>Application permissions</Trans>,
      onClick: () => scrollTo('#permissions'),
      ...sharedProps,
    })

    menuItems.push({
      offset: true,
    })
  }

  if (canReadUserManagement) {
    menuItems.push({
      label: <Trans>User management</Trans>,
      onClick: () => scrollTo('#user-management'),
      ...sharedProps,
      leftIcon: 'shield_person',
      leftIconColor: null,
    })
  }

  if (canWrite) {
    // Custom Logo
    menuItems.push({
      label: <Trans>Custom logo</Trans>,
      onClick: () => scrollTo('#custom-logo'),
      ...sharedProps,
      leftIcon: 'image',
      leftIconColor: null,
    })

    // Theme
    menuItems.push({
      label: <Trans>Account theme</Trans>,
      onClick: () => scrollTo('#theme'),
      ...sharedProps,
      leftIcon: 'palette',
      leftIconColor: null,
    })
  }

  if (canRead) {
    // App Settings
    menuItems.push({
      label: <Trans>Application settings</Trans>,
      onClick: () => scrollTo('#view'),
      ...sharedProps,
      leftIcon: 'computer',
      leftIconColor: null,
    })

    menuItems.push({
      label: <Trans>In-app currency</Trans>,
      onClick: () => scrollTo('#currency'),
      ...sharedProps,
    })

    menuItems.push({
      offset: true,
    })
  }

  if (canWrite) {
    // Security
    menuItems.push({
      label: <Trans>Password change</Trans>,
      onClick: () => scrollTo('#password'),
      ...sharedProps,
      leftIcon: 'security',
      leftIconColor: null,
    })
  }

  return <MntrMenu menuItems={menuItems} />
}

export default observer(SettingsNavigation)
