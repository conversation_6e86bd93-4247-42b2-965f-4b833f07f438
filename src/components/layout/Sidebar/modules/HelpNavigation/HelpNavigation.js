'use client'

import { Trans } from '@lingui/react/macro'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { scrollToTop } from '~/helpers/scroll'

const scrollTo = (element) => scrollToTop(element, '', 70, 300)
const cateogryIcon = 'help_center'

const HelpNavigation = () => {
  const menuItems = []
  const sharedProps = {
    accent: true,
    rounded: true,
    leftIcon: 'arrow_right',
    leftIconColor: 'lightGrey',
  }

  menuItems.push({
    label: <Trans>Help</Trans>,
  })

  menuItems.push({
    label: <Trans>Word Search</Trans>,
    onClick: () => scrollTo('#rules-search'),
    ...sharedProps,
    leftIcon: cateogryIcon,
    leftIconColor: null,
  })

  menuItems.push({
    label: <Trans>Quick Overview</Trans>,
    onClick: () => scrollTo('#rules-search-overview'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>With inflection</Trans>,
    onClick: () => scrollTo('#rules-search-declension'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>Exact match</Trans>,
    onClick: () => scrollTo('#rules-search-exact'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>Exact match, including letter size</Trans>,
    onClick: () => scrollTo('#rules-search-exact-incl-case'),
    ...sharedProps,
  })

  menuItems.push({ offset: true })

  menuItems.push({
    label: <Trans>Search for phrases</Trans>,
    onClick: () => scrollTo('#rules-phrase'),
    ...sharedProps,
    leftIcon: cateogryIcon,
    leftIconColor: null,
  })

  menuItems.push({
    label: <Trans>Quick Overview</Trans>,
    onClick: () => scrollTo('#rules-phrase-overview'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>With inflection</Trans>,
    onClick: () => scrollTo('#rules-phrase-declension'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>Exact match</Trans>,
    onClick: () => scrollTo('#rules-phrase-exact'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>Exact match with separator</Trans>,
    onClick: () => scrollTo('#rules-phrase-exact-incl-separator'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>Exact match, including letter size</Trans>,
    onClick: () => scrollTo('#rules-phrase-exact-incl-case'),
    ...sharedProps,
  })

  menuItems.push({ offset: true })

  menuItems.push({
    label: <Trans>Words to distance</Trans>,
    onClick: () => scrollTo('#rules-distance'),
    ...sharedProps,
    leftIcon: cateogryIcon,
    leftIconColor: null,
  })

  menuItems.push({
    label: <Trans>Quick Overview</Trans>,
    onClick: () => scrollTo('#rules-distance-overview'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>With inflection</Trans>,
    onClick: () => scrollTo('#rules-distance-declension'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>Exact match</Trans>,
    onClick: () => scrollTo('#rules-distance-exact'),
    ...sharedProps,
  })

  menuItems.push({ offset: true })

  menuItems.push({
    label: <Trans>Search operators</Trans>,
    onClick: () => scrollTo('#rules-operators'),
    ...sharedProps,
    leftIcon: cateogryIcon,
    leftIconColor: null,
  })

  menuItems.push({
    label: <Trans>Quick Overview</Trans>,
    onClick: () => scrollTo('#rules-operators-overview'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>AND</Trans>,
    onClick: () => scrollTo('#rules-operators-and'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>OR</Trans>,
    onClick: () => scrollTo('#rules-operators-or'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>NOT</Trans>,
    onClick: () => scrollTo('#rules-operators-not'),
    ...sharedProps,
  })

  menuItems.push({
    label: <Trans>Brackets</Trans>,
    onClick: () => scrollTo('#rules-operators-parens'),
    ...sharedProps,
  })

  return <MntrMenu menuItems={menuItems} />
}

export default HelpNavigation
