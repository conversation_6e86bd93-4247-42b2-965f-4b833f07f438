import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { Box, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalTvrTopics from '~/helpers/modal/withModalTvrTopics'
import { observer } from '~/helpers/mst'

const TvrNavigation = ({ appStore: { tvr, featureRequest, notification }, onlyTopics = false }) => {
  const onRequestSubmit = () => {
    featureRequest.sendFeatureRequest('TVR_TOPICS_CHANGE')
    notification.add(t`You'll be contacted by our Sales management.`, 'success')
  }

  const menuItems = []

  // Articles
  if (!onlyTopics) {
    menuItems.push({
      label: <Trans>Articles</Trans>,
      leftIcon: 'view_stream',
      selected: true,
      href: '/crisis-communication',
      accent: true,
      rounded: true,
    })

    menuItems.push({ offset: true })
  }

  menuItems.push({
    label: <Trans>Topics</Trans>,
  })

  // TVR topics
  tvr.topicsList.forEach((item) => {
    menuItems.push({
      label: item.name,
      leftIcon: 'label_important',
      leftIconColor: 'primary',
      leftIconHoverColor: 'primary',
      rounded: true,
      accent: true,
      hoverVariant: 'light',
      ...withModalTvrTopics(item, onRequestSubmit),
    })
  })

  return (
    <Box>
      <MntrMenu menuItems={menuItems} />
      {tvr.topicsList.length === 0 && (
        <Box px={3} pb={3}>
          <Text color="mediumGrey" mb={2}>
            <Trans>You have no topics created</Trans>
          </Text>
          <MntrButton
            label={<Trans>Add Topic</Trans>}
            requestId={'TVR_TOPICS_CHANGE'}
            modalTitle={<Trans>Add Topic</Trans>}
          />
        </Box>
      )}
    </Box>
  )
}

export default observer(TvrNavigation)
