import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import ColorPicker from '~/components/misc/ColorPicker/ColorPicker'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import { tagColors } from '~/constants/colors'

interface IModalEditBasket {
  onSubmit: () => void
  initialValues?: Record<string, string>
  isTouchDevice?: boolean
  onClose?: () => void
}

const modalEditBasket = ({ onSubmit, initialValues, isTouchDevice, onClose }: IModalEditBasket) => {
  return {
    onClose,
    modalTitle: (
      <>
        <Trans>Edit list</Trans>: {initialValues?.label}
      </>
    ),
    modal: () => {
      const formSchema: IFormSchemaItem[] = [
        {
          label: t`List name`,
        },
        {
          name: 'label',
          autoFocus: true,
          autoComplete: 'off',
          placeholder: t`List name`,
        },
        {
          customComponent: ({ values, form }) => {
            return (
              <ColorPicker
                color={values.color}
                onColorChange={(color: string) => form?.change('color', color)}
                replaceColorPalette
                // @ts-expect-error: refactor ColorPicker to TS
                colorPalette={tagColors}
                isTouchDevice={isTouchDevice}
              />
            )
          },
        },
        {
          actions: () => {
            return [
              {
                rounded: true,
                icon: 'save',
                bg: 'secondary',
                type: 'submit',
                label: t`Save`,
              },
            ]
          },
        },
      ]
      return (
        <MntrForm
          contentPadding={3}
          onSubmit={onSubmit}
          initialValues={initialValues}
          schema={formSchema}
        />
      )
    },
  }
}

export default modalEditBasket
