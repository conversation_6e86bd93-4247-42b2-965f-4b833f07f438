import MntrAvatar from '~/components/misc/MntrAvatar/MntrAvatar'
import { observer } from '~/helpers/mst'

const BasketIcon = ({ label, bg: background, size = 40, fontSize = size / 2.5 }) => {
  const char = label.charAt(0).toUpperCase()

  return (
    <MntrAvatar
      color={background}
      size={size}
      style={{
        fontSize,
        fontFamily: 'var(--font-family)',
      }}
    >
      {char}
    </MntrAvatar>
  )
}

export default observer(BasketIcon)
