import { t } from '@lingui/core/macro'
import color from 'color'
import { useRouter } from 'next/router'
import { useState } from 'react'
import FormNewBasket from '~/components/forms/baskets/FormNewBasket'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'
import BasketIcon from './BasketIcon'
import modalEditBasket from './modalEditBasket'

const SidebarBaskets = ({
  appStore: {
    authors: { baskets },
    router: { makeRouteWithQuery },
  },
  label = t`Author Lists`,
  canEditBaskets,
  disableDrag,
}) => {
  const router = useRouter()
  const [errors, setErrors] = useState(null)

  if (!baskets?.list.length && !canEditBaskets) {
    return null
  }

  const basketMenu = [
    {
      label,
      buttonGroup: canEditBaskets
        ? [
            {
              icon: 'playlist_add',
              iconBg: 'primary',
              tooltip: t`New list`,
              popupPlacement: 'bottom-start',
              onClose: () => setErrors(undefined),
              popup: (closePopup) => {
                return (
                  <FormNewBasket
                    errors={errors}
                    onSubmit={(model) => {
                      baskets.create(model).then((res) => {
                        if (!res.valid) {
                          return setErrors({ label: res.errors?.label })
                        }

                        closePopup()
                      })
                    }}
                  />
                )
              },
            },
          ]
        : null,
    },
  ]

  const menuItems = []

  baskets.list.forEach((item) => {
    const { id, label, color: itemColor, edit, remove, duplicate, author_count } = item
    const selected = id === baskets.selected?.id

    menuItems.push({
      id,
      label,
      leftIcon: <BasketIcon label={label} bg={color(itemColor).darken(0.3).toString()} />,
      activeBackground: color(itemColor).alpha(0.4).toString(),
      bg: 'transparent',
      rounded: true,
      selected,
      hoverVariant: 'light',
      counter: author_count,
      href: `/authors?listId=${id}`,
      ...(canEditBaskets && {
        buttonGroup: [
          {
            icon: 'more_vert',
            popup: (closePopup, onClose) => {
              const actions = [
                {
                  label: t`Edit list`,
                  leftIcon: 'edit',
                  ...modalEditBasket({
                    initialValues: item,
                    onSubmit: (model) => {
                      return edit(model)
                        .then(() => {
                          closePopup()
                        })
                        .catch((err) => err)
                    },
                  }),
                },
                {
                  label: t`Duplicate`,
                  leftIcon: 'folder_copy',
                  onClick: () => {
                    duplicate(id)
                  },
                },
                {
                  label: t`Delete list`,
                  leftIcon: 'delete',
                  hoverVariant: 'error',
                  ...withModalRemove({
                    onSubmit: () => {
                      if (baskets.selected?.id === item.id) {
                        routerPush('/authors')

                        return setTimeout(() => {
                          remove()
                        }, 0)
                      }

                      remove()
                    },
                    title: t`Delete list?`,
                    message: t`List ${label} will be removed.`,
                  }),
                },
              ]

              return (
                <MntrMenu
                  menuItems={actions}
                  closePopup={() => {
                    closePopup()
                    onClose()
                  }}
                />
              )
            },
            popupPlacement: 'bottom-start',
            size: 'small',
            transformOrigin: '100% 0',
          },
        ],
      }),
    })
  })

  return (
    <>
      <MntrMenu menuItems={basketMenu} />
      <Box mt={-1}>
        <MntrMenu
          menuItems={[
            {
              rounded: true,
              accent: true,
              leftIcon: 'contacts',
              label: t`My authors`,
              href: `/authors?is_own_author=true`,
              selected: router.query.is_own_author,
              hoverVariant: 'light',
              buttonGroup: [
                {
                  icon: 'more_vert',
                  popup: (closePopup, onClose) => {
                    const menuItems = [
                      {
                        label: t`Create author`,
                        leftIcon: 'person_add',
                        onClick: () => {
                          const currRoute = makeRouteWithQuery()

                          routerReplace(currRoute, currRoute, { shallow: true })
                          routerPush(currRoute, '/authors/create', { shallow: true })
                        },
                      },
                    ]

                    return (
                      <MntrMenu
                        menuItems={menuItems}
                        closePopup={() => {
                          closePopup()
                          onClose()
                        }}
                      />
                    )
                  },
                  popupPlacement: 'bottom-start',
                  size: 'small',
                  transformOrigin: '100% 0',
                },
              ],
            },
          ]}
        />
      </Box>
      {menuItems.length > 0 && (
        <Box mt={'-8px'}>
          <MntrMenu
            menuItems={menuItems}
            {...(canEditBaskets && {
              onDrag: disableDrag
                ? null
                : (topicId, source, destination) =>
                    baskets.moveBasketToIndex(topicId, source, destination),
            })}
          />
        </Box>
      )}
    </>
  )
}

export default observer(SidebarBaskets)
