import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const AuthorsNavigation = ({
  appStore: {
    viewport: { isTablet },
  },
  isBasket,
}) => {
  const router = useRouter()
  const menuItems = []
  const sharedProps = {
    accent: true,
    rounded: !isTablet,
  }

  menuItems.push({
    label: t`Authors`,
    leftIcon: 'list',
    href: `/authors`,
    selected: !isBasket && !router.query.is_own_author,
    ...sharedProps,
  })

  return <MntrMenu menuItems={menuItems} />
}

export default observer(AuthorsNavigation)
