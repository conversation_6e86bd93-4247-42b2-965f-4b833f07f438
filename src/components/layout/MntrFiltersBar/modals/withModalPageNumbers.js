import { t } from '@lingui/core/macro'
import FormPageNumbers from '~/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers'

const withModalPageNumbers = ({
  feedId,
  closePopup,
  customRoute,
  initialValues,
  disableRedirect,
}) => {
  return {
    modalWidth: 600,
    isStackedModal: true,
    modalZIndex: 8999,
    modalTitle: t`Filter specific pages`,
    modal: (closeModal) => {
      return (
        <FormPageNumbers
          closePopup={closePopup}
          customRoute={customRoute}
          closeModal={closeModal}
          feedId={feedId}
          initialValues={initialValues}
          disableRedirect={disableRedirect}
        />
      )
    },
  }
}

export default withModalPageNumbers
