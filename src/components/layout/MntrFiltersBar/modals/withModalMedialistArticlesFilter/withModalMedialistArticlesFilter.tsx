import { t } from '@lingui/core/macro'
import { MEDIALIST_ARTICLES_FILTER_FEED } from '~/constants'
import ModalMedialistArticlesFilterContent from './ModalMedialistArticlesFilterContent'
import ModalMedialistArticlesFilterFooter from './ModalMedialistArticlesFilterFooter'

interface IWithModalMedialistArticlesFilterProps {
  feedId?: string
  type?: string
  customRoute?: string
  isStackedModal?: boolean
}

const withModalMedialistArticlesFilter = (params?: IWithModalMedialistArticlesFilterProps) => {
  const { feedId = '', type = '', customRoute = '', isStackedModal = false } = params || {}

  const modalFeedId = MEDIALIST_ARTICLES_FILTER_FEED

  return {
    isStackedModal,
    modalWidth: 1000,
    modalTitle: t`Filter by author's articles`,
    modal: (closeModal: () => void) => {
      return (
        <>
          <ModalMedialistArticlesFilterContent modalFeedId={modalFeedId} closeModal={closeModal} />
          <ModalMedialistArticlesFilterFooter
            modalFeedId={modalFeedId}
            feedId={feedId}
            type={type}
            customRoute={customRoute}
            closeModal={closeModal}
          />
        </>
      )
    },
  }
}

export default withModalMedialistArticlesFilter
