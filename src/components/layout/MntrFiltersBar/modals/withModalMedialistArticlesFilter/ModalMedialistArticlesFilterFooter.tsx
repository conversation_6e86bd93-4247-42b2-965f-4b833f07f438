import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'

import { ButtonGroup, Flex } from '~/components/misc/Mntr'
import { MEDIALIST_ARTICLES_PREFIX } from '~/constants'
import isEmpty from '~/helpers/isEmpty'
import { ObservedFC, observer } from '~/helpers/msts'
import { routerPush } from '~/helpers/router'

interface IModalMedialistArticlesFilterFooterProps {
  closeModal: () => void
  type: string
  customRoute: string
  modalFeedId: string
  feedId: string
}

interface IFilterLabel {
  value: string | { value: string }[]
}

const ModalMedialistArticlesFilterFooter: ObservedFC<IModalMedialistArticlesFilterFooterProps> = ({
  appStore: {
    filter,
    monitoring: { feedMap, getFeedMap },
    router: { makeRouteWithQuery },
    account,
  },
  closeModal,
  type = 'medialist',
  customRoute,
  modalFeedId,
  feedId,
}) => {
  const { pathname } = useRouter()
  const prefix = MEDIALIST_ARTICLES_PREFIX
  const feedItem = feedMap.get(modalFeedId)
  const isVisibleFeed = feedItem.isVisibleFeed
  const filtersWithoutMedialistArticlesFilter = filter.data
    ? Object.keys(filter.data).reduce((obj, key) => {
        if (!key.startsWith(prefix)) {
          return {
            ...obj,
            [key]: filter.data[key],
          }
        }

        return obj
      }, {})
    : {}
  const filterLabels = feedItem.filter.labels

  const buildFiltersFromLabels = (
    filterLabels: { [key: string]: IFilterLabel },
    prefix?: string,
  ) => {
    const filters: { [key: string]: string } = {}

    for (const [key, val] of Object.entries<{ value: string | { value: string }[] }>(
      filterLabels,
    )) {
      const filterKey = prefix ? `${prefix}${key}` : key

      if (Array.isArray(val)) {
        filters[filterKey] = val.map((v) => v.value.toString()).join(',')
      } else {
        filters[filterKey] = val.value.toString()
      }
    }

    return filters
  }

  const setFilters = () => {
    if (!isEmpty(filterLabels)) {
      const medialistArticlesFilters = buildFiltersFromLabels(filterLabels, prefix)

      if (feedId && !pathname.startsWith('/authors')) {
        const feedMapToSet = getFeedMap(type).get(feedId)
        const params = {
          ...filtersWithoutMedialistArticlesFilter,
          ...medialistArticlesFilters,
        }

        const url = `${customRoute || pathname}?${feedMapToSet.filter.queryAddParam(params)}`
        return feedMapToSet.loadUrl(url, type)
      }

      routerPush({
        pathname: customRoute || pathname,
        query: {
          ...filtersWithoutMedialistArticlesFilter,
          ...medialistArticlesFilters,
        },
      })
    }
  }

  const getFeedUrl = () => {
    if (!isEmpty(filterLabels)) {
      const openFeedFilters = buildFiltersFromLabels(filterLabels)

      return makeRouteWithQuery('/', undefined, {
        ...openFeedFilters,
      })
    }
  }

  return (
    <Flex column alignItems="end" p={3} pt={2} bg="background">
      <ButtonGroup
        {...(isVisibleFeed
          ? {
              buttonsLeft: account.workspace?.permissions.monitoring.can_read
                ? [
                    {
                      bg: 'flat',
                      icon: 'view_stream',
                      rounded: true,
                      href: getFeedUrl(),
                      target: '_blank',
                      label: t`Show articles in feed`,
                    },
                  ]
                : [],
              buttons: [
                {
                  bg: 'primary',
                  icon: 'save',
                  rounded: true,
                  onClick: () => {
                    setFilters()
                    closeModal()
                  },
                  label: t`Filter`,
                },
              ],
            }
          : {
              buttons: [
                {
                  bg: 'light',
                  rounded: true,
                  label: <Trans>Back</Trans>,
                  onClick: () => closeModal(),
                },
              ],
            })}
      />
    </Flex>
  )
}

export default observer(ModalMedialistArticlesFilterFooter)
