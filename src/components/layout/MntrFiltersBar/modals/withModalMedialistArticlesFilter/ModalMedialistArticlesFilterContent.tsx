import { t } from '@lingui/core/macro'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Box, Flex, ModalScrollingContent } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import TopicsMultiSelector from '~/components/misc/TopicsMultiSelector/TopicsMultiSelector'
import WidgetFeedSimple from '~/components/widgets/modules/feed/WidgetFeedSimple'
import { MAX_SELECTED_TOPICS } from '~/constants'
import { POPUP_MEDIALIST_ARTICLES_FILTER } from '~/constants/zIndexes'
import {
  getConfigPreviewMedialistArticlesFilter,
  getConfigPreviewMedialistArticlesFilterWithoutPermissionsMonitoringCanRead,
} from '~/helpers/getActiveFiltersConfig'
import { ObservedFC, observer } from '~/helpers/msts'
import MedialistArticlesFilterSearchQuery from './MedialistArticlesFilterSearchQuery'

interface IModalMedialistArticlesFilterContentProps {
  closeModal: () => void
  modalFeedId: string
}

const ModalMedialistArticlesFilterContent: ObservedFC<
  IModalMedialistArticlesFilterContentProps
> = ({
  appStore: {
    monitoring: { feedMap },
    viewport: { height },
    account,
  },
  closeModal,
  modalFeedId,
}) => {
  const zIndex = POPUP_MEDIALIST_ARTICLES_FILTER
  const customRoute = '/authors'
  const feedItem = feedMap.get(modalFeedId)
  const isVisibleFeed = feedItem.isVisibleFeed
  const isLoading = feedItem.loader.isLoading('feed-loading')

  const handleSubmitTopicSelector = (selectedTopics: string[]) => {
    const filters = {
      topic_monitors: selectedTopics,
    }

    feedMap.set(modalFeedId, {
      displayNotifications: false,
      isNewsFeedMedialist: true,
      modalFeedId,
      url: '/news-feed/',
    })

    feedMap.get(modalFeedId).load(filters)
  }

  return (
    <ModalScrollingContent pb={0} bg="background" height={`${height - 170}px`}>
      <Box px={3} width={1}>
        {!isVisibleFeed && !isLoading && (
          <>
            {/* Search query */}
            <MedialistArticlesFilterSearchQuery feedId={modalFeedId} />

            {/* Topic selector */}
            {account.workspace?.permissions.monitoring.can_read && (
              <Box ml={[0, 1]}>
                <TopicsMultiSelector
                  isModal
                  subHeader={t`Select at least one topic`}
                  onSubmit={handleSubmitTopicSelector}
                  maxSelectedLimit={MAX_SELECTED_TOPICS}
                />
              </Box>
            )}
          </>
        )}
        {/* Filters */}
        <Flex flexDirection="column" gap={2} width={1}>
          {isVisibleFeed && (
            <>
              <MedialistArticlesFilterSearchQuery feedId={modalFeedId} />
              <Box mr={['10px', 1]}>
                <MntrFiltersBar
                  customRoute={customRoute}
                  filters={[
                    'Date',
                    'Source',
                    'LanguageMultiselect',
                    'CountryMultiselect',
                    'Author',
                    'Sentiment',
                    'Notes',
                    'ArticleType',
                  ]}
                  feedId={modalFeedId}
                  disableRedirect
                  zIndex={zIndex}
                />
              </Box>
            </>
          )}

          {isVisibleFeed && !isLoading && (
            <>
              {/* Active filters */}
              <MntrActiveFilters
                config={
                  account.workspace?.permissions.monitoring.can_read
                    ? getConfigPreviewMedialistArticlesFilter(zIndex, modalFeedId, customRoute)
                    : getConfigPreviewMedialistArticlesFilterWithoutPermissionsMonitoringCanRead(
                        zIndex,
                        modalFeedId,
                        customRoute,
                      )
                }
              />

              {/* Feed preview */}
              <Box mb={3}>
                <WidgetFeedSimple
                  disabled={!account.workspace?.permissions.monitoring.can_read}
                  feedId={modalFeedId}
                  closeModal={closeModal}
                  isPreview
                />
              </Box>
            </>
          )}
        </Flex>

        {/* Loading spinner */}
        {isLoading && (
          <Flex center m={3}>
            <MntrCircularProgress size={26} />
          </Flex>
        )}
      </Box>
    </ModalScrollingContent>
  )
}

export default observer(ModalMedialistArticlesFilterContent)
