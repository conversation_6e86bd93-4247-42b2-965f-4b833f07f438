import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import { Field, Form } from 'react-final-form'
import { styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const FormWrapper = styled.div`
  background: ${({ theme }) => theme.paper.background};
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: 26px;
  position: relative;
  padding: 5px;
`

const MedialistArticlesFilterSearchQuery = ({
  feedId,
  customRoute,
  appStore: {
    monitoring: { getFeedMap },
  },
}) => {
  const feed = getFeedMap().get(feedId)

  const onSubmit = (model) => {
    const url = model.query
      ? `${customRoute}?${feed.filter.queryAddParam({ query: model.query })}`
      : `${customRoute}?${feed.filter.queryRemoveParam({ query: undefined })}`
    feed.loadUrl(url)
  }

  return (
    <Box mt={2}>
      <FormWrapper>
        <Form
          onSubmit={onSubmit}
          initialValues={{ query: feed.filter.labels?.query?.text }}
          render={({ handleSubmit, pristine, dirty, form }) => {
            return (
              <form onSubmit={handleSubmit}>
                <Flex centerY>
                  <Box mt="5px" mr={1} color="black">
                    <Icon>search</Icon>
                  </Box>
                  <Box width={1}>
                    <Field
                      parse={identityFn}
                      name="query"
                      autoComplete="off"
                      component={MntrTextFieldAdapter}
                      placeholder={t`Search`}
                      transparentBg
                      disableUnderline
                    />
                  </Box>

                  {pristine && feed.filter.labels?.query?.text && (
                    <Box>
                      <MntrButton
                        rounded
                        bg="flat"
                        icon="close"
                        disabled={pristine && dirty}
                        onClick={() => {
                          form.change('query', '')
                          onSubmit({ query: '' })
                        }}
                      />
                    </Box>
                  )}

                  {!pristine && !feed.loader.isLoading('feed-loading') && (
                    <Box>
                      <MntrButton
                        rounded
                        type="submit"
                        bg="flat"
                        icon="search"
                        disabled={pristine && dirty}
                        tooltip={<Trans>Search</Trans>}
                      />
                    </Box>
                  )}
                </Flex>
              </form>
            )
          }}
        />
      </FormWrapper>
    </Box>
  )
}

export default observer(MedialistArticlesFilterSearchQuery)
