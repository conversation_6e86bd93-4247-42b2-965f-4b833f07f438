import get from 'lodash/get'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { filterMultiselectItems } from '~/components/layout/MntrFiltersBar/utils/filterMultiselectItems'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Flex } from '~/components/misc/Mntr'
import MultiSelectContainer from '~/components/misc/MntrMultiSelect/MultiSelectContainer'
import { observer } from '~/helpers/mst'
import MenuFilterToggleAllButtons from './MenuFilterToggleAllButtons'

const MenuFilterMultiselect = ({
  appStore: {
    dashboards: { preview },
    filter,
    monitoring: { feedMap, getFeedMap },
    router: { redirectTo },
  },
  items,
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  filterKey,
  label,
  itemLeftIcon,
  type = 'feed',
  forceOpen,
}) => {
  const { pathname } = useRouter()
  const [filterValueBySearch, setFilterValue] = useState('')

  const feed = getFeedMap(get(preview, 'type') || type).get(feedId)
    ? getFeedMap(get(preview, 'type') || type).get(feedId)
    : feedMap.get(feedId)

  const filterStore = feed ? feed.filter : filter
  const [selectedItems, setSelectedItems] = useState(
    filterStore && filterStore.labels && filterStore.labels[filterKey]
      ? filterStore.labels[filterKey]
      : [],
  )

  const onCheckboxClickHandler = (items) => {
    forceOpen?.()

    const url = `${customRoute ?? pathname}?${filterStore?.replaceFilterMultiArray(
      items,
      filterKey,
    )}`

    return disableRedirect ? feed.loadUrl(url, preview?.subtype) : redirectTo(url)
  }

  return (
    <Flex flexDirection="column">
      <ListScrollWrapper>
        <MultiSelectContainer
          closePopup={closePopup}
          items={filterMultiselectItems(items, filterValueBySearch)}
          selectedItems={selectedItems}
          filterValueBySearch={filterValueBySearch}
          onChange={(model) => setFilterValue(model.filter)}
          itemLeftIcon={itemLeftIcon}
          label={label}
          filterKey={filterKey}
          onCheckboxClickHandler={onCheckboxClickHandler}
          disableRedirect={disableRedirect}
          feedId={feedId}
          customRoute={customRoute}
          forceOpen={forceOpen}
          setSelectedItems={setSelectedItems}
        />
      </ListScrollWrapper>
      <MenuFilterToggleAllButtons
        items={filterMultiselectItems(items, filterValueBySearch)}
        filterStore={filterStore}
        customRoute={customRoute}
        disableRedirect={disableRedirect}
        feed={feed}
        filterKey={filterKey}
        type={type}
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        closePopup={closePopup}
      />
    </Flex>
  )
}

export default observer(MenuFilterMultiselect)
