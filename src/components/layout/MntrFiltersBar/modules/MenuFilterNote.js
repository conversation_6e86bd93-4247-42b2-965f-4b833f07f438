import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import FormNote from '~/components/layout/MntrFiltersBar/forms/FormNote/FormNote'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'

const MenuFilterNote = ({
  closePopup,
  forceOpen,
  customRoute,
  disableRedirect,
  feedId,
  initialValues,
  appStore: {
    filter,
    dashboards,
    monitoring: { getFeedMap, feedMap },
    router: { makeRouteWithQuery },
  },
}) => {
  const { pathname } = useRouter()
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter

  const menuItems = [
    {
      label: <Trans>Additional settings</Trans>,
    },
    {
      label: <Trans>With note</Trans>,
      leftIcon: 'note',
      onClick: () => {
        filterStore.addFilterItem({
          type: 'note',
          value: t`With note`,
          id: '',
        })
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { note: '' },
      },
    },
    {
      label: <Trans>Without note</Trans>,
      leftIcon: 'not_interested',
      onClick: () => {
        filterStore.addFilterItem({
          type: 'note',
          value: t`Without note`,
          id: '%00',
        })
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { note: '%00' },
      },
    },
  ]

  // TODO - optimize
  const handleSubmit = (model) => {
    if (model.note && model.note.length > 0) {
      setTimeout(() => {
        closePopup()
      }, 10)
      filterStore.addFilterItem({
        type: 'note',
        value: model.note,
        id: '',
      })

      if (customRoute) {
        const feed = getFeedMap(dashboards?.preview?.type || 'feed').get(feedId)
        const url = `${customRoute}?${feed.filter.urlWithParam({
          note: model.note,
        })}`

        if (!disableRedirect) {
          routerReplace(makeRouteWithQuery(), url, { shallow: true })
        }
        feed.loadUrl(url)
        return false
      }

      const query = filter.urlWithParam({ note: model.note })

      routerPush(`${pathname}${query ? `?${query}` : ''}`)
    }
  }

  return (
    <Box pt={1}>
      <FormNote
        forceOpen={forceOpen}
        initialValues={initialValues}
        onSubmit={(model) => {
          handleSubmit(model)
        }}
      />
      <MntrMenu menuItems={menuItems} closePopup={closePopup} />
    </Box>
  )
}

export default observer(MenuFilterNote)
