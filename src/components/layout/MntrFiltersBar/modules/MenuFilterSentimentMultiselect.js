import { t } from '@lingui/core/macro'
import Icon from '~/components/misc/Icon/Icon'
import getSentimentIcon from '~/helpers/getSentimentIcon'
import { observer } from '~/helpers/mst'
import MenuFilterMultiselect from './MenuFilterMultiselect'

const MenuFilterSentimentMultiselect = ({
  forceOpen,
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: { account },
}) => {
  const items = account.enums.sentiment
  const filterKey = 'sentiment'

  return (
    <MenuFilterMultiselect
      forceOpen={forceOpen}
      closePopup={closePopup}
      feedId={feedId}
      items={items}
      customRoute={customRoute}
      disableRedirect={disableRedirect}
      filterKey={filterKey}
      label={t`Filter by sentiment`}
      itemLeftIcon={(item) => {
        return (
          <Icon size={22} color={item.color}>
            {getSentimentIcon(item.id)}
          </Icon>
        )
      }}
    />
  )
}

export default observer(MenuFilterSentimentMultiselect)
