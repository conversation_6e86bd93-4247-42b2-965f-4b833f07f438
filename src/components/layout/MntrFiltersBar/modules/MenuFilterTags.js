import { Trans } from '@lingui/react/macro'
import color from 'color'
import fuzzy from 'fuzzy'
import { useState } from 'react'
import FormNewTag from '~/components/forms/tags/FormNewTag/FormNewTag'
import FormFilterItems from '~/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems'
import modalEditTags from '~/components/layout/Sidebar/modules/SidebarTags/modalEditTags'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { FILTER_VISIBLE_LIMIT } from '~/constants'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import removeAccents from '~/helpers/removeAccents'

const MenuFilterTags = ({
  appStore: {
    monitoring: {
      tags: { create, hide },
    },
    viewport: { isTouchDevice, isTablet },
  },
  closePopup,
  tags,
  customRoute,
  disableRedirect,
  feedId,
  filter,
  redirectTo,
  tagsModel,
  selectedId,
  getFeedMap,
  dashboards,
  routerReplace,
  makeRouteWithQuery,
  type,
  canCreateTag = false,
  filterKey = 'tags',
  createTag = create,
}) => {
  const [filterValue, setFilterValue] = useState('')
  const filterStore = feedId ? getFeedMap(type || 'feed').get(feedId).filter : filter
  const [errors, setErrors] = useState(undefined)
  const [editErrors, setEditErrors] = useState(undefined)

  const onAdd = (item) => {
    if (customRoute) {
      const feed = getFeedMap(type || 'feed').get(feedId)

      const url = `${customRoute}?${feed.filter.queryAddParam({ [filterKey]: item.id })}`

      if (!disableRedirect) {
        routerReplace(makeRouteWithQuery(), url, { shallow: true })
      }

      feed.loadUrl(url, dashboards?.preview?.subtype)
      filterStore.addFilterMulti(
        {
          value: item.id,
          text: item.label,
        },
        filterKey,
      )
      return
    }

    redirectTo(tagsModel.urlWithTag(item))
    filterStore.addFilterMulti(
      {
        value: item.id,
        text: item.label,
      },
      filterKey,
    )
  }

  const menuItems = [
    {
      label: <Trans>Tags</Trans>,
    },
  ]

  if (filterStore?.data?.tags !== null) {
    menuItems.push({
      leftIcon: 'label_off',
      label: <Trans>Without tags</Trans>,
      redirectProps: {
        onClick: () => {
          filterStore.withoutTags(
            {
              value: null,
              text: <Trans>Without tags</Trans>,
            },
            filterKey,
          )

          closePopup()
        },
        customRoute,
        feedId,
        disableRedirect,
        set: { [filterKey]: '' },
      },
    })
  }

  tags
    .filter((item) => !item.is_hidden_for_user)
    .map((item) => {
      if (!fuzzy.test(removeAccents(filterValue) || '', removeAccents(item.label))) {
        return false
      }
      menuItems.push({
        leftIcon: 'label',
        leftIconColor: color(item.color).darken(0.4).toString(),
        leftIconBg: item.color,
        label: item.label,
        ...(isTablet && {
          buttonGroup: [
            {
              icon: 'more_vert',
              bg: 'flat',
              mr: 1,
              zIndex: 4999,
              popupPlacement: 'bottom-end',
              transformOrigin: '100% 0',
              popup: (closePopup) => {
                const tagActions = [
                  {
                    label: <Trans>Add to filters</Trans>,
                    leftIcon: 'add',
                    onClick: () => {
                      closePopup()
                      onAdd(item)
                    },
                  },
                ]

                // Divider
                tagActions.push({})

                if (canCreateTag) {
                  tagActions.push({
                    label: <Trans>Hide tag</Trans>,
                    leftIcon: 'visibility_off',
                    hoverVariant: 'secondary',
                    ...withModalRemove({
                      onSubmit: () => {
                        hide([{ id: item.id, is_hidden_for_user: true }])
                      },
                      title: (
                        <>
                          <Trans>Hide tag</Trans>?
                        </>
                      ),
                      message: (
                        <Trans>
                          Tag <b>{item.label}</b> will be hidden.
                        </Trans>
                      ),
                    }),
                  })
                }

                // Edit Tag
                if (canCreateTag) {
                  tagActions.push({
                    label: <Trans>Edit tag</Trans>,
                    leftIcon: 'edit',
                    ...modalEditTags({
                      initialValues: item,
                      isTouchDevice,
                      errors: editErrors,
                      onClose: () => setEditErrors(undefined),
                      onSubmit: (model, cb) => {
                        setEditErrors(undefined)
                        item.edit(model).then((res) => {
                          if (res.valid) {
                            cb()
                          } else {
                            setEditErrors(res.errors)
                          }
                        })
                      },
                    }),
                  })
                }

                // Delete Tag
                if (canCreateTag) {
                  tagActions.push({
                    label: <Trans>Delete tag</Trans>,
                    leftIcon: 'delete',
                    hoverVariant: 'error',
                    ...withModalRemove({
                      onSubmit: () => {
                        item.remove()
                      },
                      title: (
                        <>
                          <Trans>Delete tag</Trans>?
                        </>
                      ),
                      message: (
                        <Trans>
                          Tag <b>{item.label}</b> will be removed.
                        </Trans>
                      ),
                    }),
                  })
                }

                return <MntrMenu menuItems={tagActions} closePopup={closePopup} />
              },
            },
          ],
        }),
        redirectProps: {
          onClick: () => {
            if (!selectedId) {
              filterStore.addFilterMulti(
                {
                  value: item.id,
                  text: item.label,
                },
                filterKey,
              )
            } else {
              filterStore.replaceFilterMulti(
                {
                  value: item.id,
                  text: item.label,
                },
                selectedId,
                filterKey,
              )
            }

            closePopup()
          },
          withQueryKeysReplace: true,
          replaceKey: filterKey,
          replace: selectedId,
          customRoute,
          feedId,
          disableRedirect,
          set: { [filterKey]: item.id },
        },
      })
    })

  return (
    <ListScrollWrapper>
      {tags && tags.length > FILTER_VISIBLE_LIMIT && (
        <Box mr={2}>
          <FormFilterItems value="" onChange={(model) => setFilterValue(model.filter)} />
        </Box>
      )}

      <MntrMenu menuItems={menuItems} closePopup={closePopup} />
      {canCreateTag && (
        <Box mt={-2}>
          <FormNewTag
            title={<Trans>New Tag</Trans>}
            onSubmit={(model) => {
              createTag(model).then((res) => {
                if (!res.valid) {
                  return setErrors({ label: res.errors?.label })
                }

                closePopup?.()
              })
            }}
            errors={errors}
          />
        </Box>
      )}
      {menuItems.length === 1 && filterValue && (
        <Box p={3} pt={0} color="grey">
          <Trans>No results found</Trans>
        </Box>
      )}
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterTags)
