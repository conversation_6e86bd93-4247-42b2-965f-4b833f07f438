import { Trans } from '@lingui/react/macro'
import noopFn from 'lodash/noop'
import FormFilterItems from '~/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems'
import CreateFolderAddTopic from '~/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic'
import { getMenuItems } from '~/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { FILTER_VISIBLE_LIMIT } from '~/constants'
import { DND_POPUP_ZINDEX_NESTED_MODAL } from '~/constants/zIndexes'
import { observer } from '~/helpers/mst'

const MenuFilterTopicMonitors = ({
  activeSubmenu,
  appStore: {
    account,
    topics: { list, listFilter, filteredFolders, setListFilter, toggleVisibility },
  },
  enableTopicActions = true,
  enableTopicDailyCounter = false,
  enableTopicMonitorsCompare = false,
  enableTopicMonitorsAdd = false,
  customRoute,
  disableRedirect,
  feedId,
  filter,
  getFeedMap,
  type,
  closePopup,
  topicId,
  disablePreviewMulti,
  isPreview,
  withQueryKeysReplace,
  buttonGroupZIndex,
}) => {
  const filterStore = feedId ? getFeedMap(type || 'feed').get(feedId).filter : filter
  const topicMonitors = filterStore.data?.topic_monitors || ''
  const activeTopicMonitors = topicMonitors.split(',').map((item) => parseInt(item))
  const sharedRedirectProps = {
    customRoute,
    feedId,
    type,
    disableRedirect,
  }

  return (
    <ListScrollWrapper>
      <Box>
        <CreateFolderAddTopic />
        {list.length > FILTER_VISIBLE_LIMIT && (
          <Box pb={2}>
            <FormFilterItems value="" onChange={(model) => setListFilter(model.filter)} noPadding />
          </Box>
        )}
        <MntrMenu
          filterStore={filterStore}
          menuItems={getMenuItems({
            account,
            activeSubmenu,
            activeTopicMonitors,
            enableTopicActions,
            enableTopicDailyCounter,
            filteredFolders,
            filterStore,
            sharedRedirectProps,
            enableTopicMonitorsCompare,
            enableTopicMonitorsAdd,
            closePopup,
            topicId,
            withQueryKeysReplace,
            rounded: false,
            disablePreviewMulti,
          })}
          filterData={filter}
          onDrag={noopFn}
          buttonGroupZIndex={buttonGroupZIndex}
          modalZIndex={DND_POPUP_ZINDEX_NESTED_MODAL}
          disablePreviewMulti={disablePreviewMulti}
          isPreview={isPreview}
          canWriteTopics={account.workspace?.permissions.topics.can_write}
          hideFolder={toggleVisibility}
          isFiltered={!!listFilter}
        />
        {!filteredFolders.length && listFilter && (
          <Box p={3} pt={0} color="grey">
            <Trans>No results found</Trans>
          </Box>
        )}
      </Box>
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterTopicMonitors)
