import { Trans } from '@lingui/react/macro'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrSlider from '~/components/misc/MntrSlider/MntrSlider'
import { ObservedFC, observer } from '~/helpers/msts'

import get from 'lodash/get'
import { useRouter } from 'next/router'
import {
  deselectAll as resetFilter,
  selectAll as setFilter,
} from '~/components/layout/MntrFiltersBar/utils/MenuFilterUtils'
import { PRIME_FILTER_PREFIX } from '~/constants'
import { routerPush } from '~/helpers/router'
import { ILabelItem } from '~/store/models/monitoring/FeedMapItem/types/IFilterInterface'

interface IMenuFilterPrimeProps {
  closePopup: () => void
  customRoute?: string
  disableRedirect?: boolean
  feedId?: string
  type?: string
  rank?: number[]
  forceOpen?: () => void
}

interface ISliderChangeProps {
  val: number[]
  minValue: number
  maxValue: number
  filterKey: string
}

const MenuFilterPrime: ObservedFC<IMenuFilterPrimeProps> = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  type = 'feed',
  appStore: {
    dashboards: { preview },
    filter,
    monitoring: { getFeedMap, feedMap },
    router: { redirectTo },
  },
  forceOpen,
}) => {
  const feed = getFeedMap(get(preview, 'type') || type).get(feedId)
    ? getFeedMap(get(preview, 'type') || type).get(feedId)
    : feedMap.get(feedId)
  const filterStore = feed ? feed.filter : filter

  const relevanceList = [
    { text: <Trans>Relevant</Trans>, icon: 'my_location', id: 1 },
    { text: <Trans>Irrelevant</Trans>, icon: 'hide_source', id: 0 },
  ]
  const primeTotalRange = filterStore?.labels?.['prime_total_range']?.map(
    (item: ILabelItem) => item.value,
  ) || [-100, 100]
  const primeVisibilityRange = filterStore?.labels?.['prime_visibility_range']?.map(
    (item: ILabelItem) => item.value,
  ) || [1, 10]

  const { asPath } = useRouter()
  const isActive = filterStore?.labels
    ? Object.keys(filterStore.labels).some((key) => key.startsWith(PRIME_FILTER_PREFIX))
    : false

  const onSliderChangeHandler = ({ val, minValue, maxValue, filterKey }: ISliderChangeProps) => {
    forceOpen?.()

    const [min, max] = val

    if (min === minValue && max === maxValue) {
      return resetFilter(
        filterKey,
        disableRedirect ? undefined : redirectTo,
        feed,
        preview,
        customRoute,
      )
    }

    setFilter(
      val.map((i) => {
        return {
          value: i,
          text: i,
        }
      }),
      val,
      filterKey,
      filterStore,
      disableRedirect ? undefined : redirectTo,
      feed,
      preview,
      customRoute,
    )
  }

  const menuItems = [
    {
      label: <Trans>Filter by absolute score</Trans>,
    },
    {
      customComponent: () => {
        return (
          <Box px={4} mb={5}>
            <MntrSlider
              range
              allowCross={false}
              dots
              min={-100}
              max={100}
              step={50}
              marks={{
                '-100': <Box>-100</Box>,
                '-50': <Box>-50</Box>,
                0: <Box>0</Box>,
                50: <Box>50</Box>,
                100: <Box>100</Box>,
              }}
              //@ts-expect-error: wtf
              onChangeComplete={(val: number[]) => {
                onSliderChangeHandler({
                  val,
                  minValue: -100,
                  maxValue: 100,
                  filterKey: 'prime_total_range',
                })
              }}
              defaultValue={primeTotalRange}
            />
          </Box>
        )
      },
    },

    {
      label: <Trans>Filter by relevance</Trans>,
    },
  ]

  menuItems.push({
    label: <Trans>All</Trans>,
    //@ts-expect-error: wtf
    leftIcon: 'description',
    rightIcon: !filter.data.prime_relevance ? 'radio_button_checked' : 'radio_button_unchecked',
    redirectProps: {
      onClick: () => {
        forceOpen?.()
      },
      customRoute,
      disableRedirect,
      feedId,
      set: { prime_relevance: undefined },
    },
  })

  relevanceList.map(({ text, id, icon }) => {
    const isSelected = Number(filter.data.prime_relevance) === id

    menuItems.push({
      label: text,
      //@ts-expect-error: wtf
      leftIcon: icon,
      rightIcon: isSelected ? 'radio_button_checked' : 'radio_button_unchecked',
      redirectProps: {
        onClick: () => {
          forceOpen?.()
          filterStore.addFilterItem({
            type: 'prime_relevance',
            value: text,
            id,
          })
        },
        customRoute,
        disableRedirect,
        feedId,
        set: { prime_relevance: id },
      },
    })
  })

  menuItems.push(
    { label: <Trans>Filter by reach</Trans> },
    {
      customComponent: () => {
        return (
          <Box px={4} mb={6}>
            <MntrSlider
              range
              allowCross={false}
              dots
              min={1}
              max={10}
              step={1}
              marks={{
                1: (
                  <Flex flexDirection="column">
                    <Box>1</Box>
                    <Box>
                      (<Trans>worst</Trans>)
                    </Box>
                  </Flex>
                ),
                2: <Box>2</Box>,
                3: <Box>3</Box>,
                4: <Box>4</Box>,
                5: <Box>5</Box>,
                6: <Box>6</Box>,
                7: <Box>7</Box>,
                8: <Box>8</Box>,
                9: <Box>9</Box>,
                10: (
                  <Flex flexDirection="column">
                    <Box>10</Box>
                    <Box>
                      (<Trans>best</Trans>)
                    </Box>
                  </Flex>
                ),
              }}
              //@ts-expect-error: wtf
              onChangeComplete={(val: number[]) => {
                onSliderChangeHandler({
                  val,
                  minValue: 1,
                  maxValue: 10,
                  filterKey: 'prime_visibility_range',
                })
              }}
              defaultValue={primeVisibilityRange}
            />
          </Box>
        )
      },
    },
  )

  return (
    <Flex flexDirection="column">
      <Box px={1}>
        {/* @ts-expect-error: menu */}
        {menuItems.length > 0 && <MntrMenu menuItems={menuItems} closePopup={closePopup} />}
      </Box>
      {isActive && (
        <Flex
          justifyContent="space-between"
          fontSize={0}
          width={1}
          p={2}
          px={'15px'}
          bg="multiselectFooter"
        >
          <Flex gap={2}>
            <MntrButton
              icon="remove_done"
              iconBg="transparent"
              iconColor="currentColor"
              isChip
              onClick={() => {
                const query = filterStore.urlWithParam({
                  prime_relevance: undefined,
                  prime_total_range: undefined,
                  prime_visibility_range: undefined,
                })

                routerPush(`${asPath.split('?')[0]}${query ? `?${query}` : ''}`)
                closePopup?.()
              }}
              label={<Trans>Cancel choice</Trans>}
            />
          </Flex>
        </Flex>
      )}
    </Flex>
  )
}

export default observer(MenuFilterPrime)
