import { t } from '@lingui/core/macro'
import { observer } from '~/helpers/mst'
import MenuFilterMultiselect from './MenuFilterMultiselect'

const MenuFilterContactInformationMultiselect = ({
  closePopup,
  forceOpen,
  appStore: {
    account: {
      enums: {
        authors: { contact_information },
      },
    },
  },
  customRoute,
  disableRedirect,
  feedId,
  type,
}) => {
  const items = contact_information
  const filterKey = 'contact_information'

  return (
    <MenuFilterMultiselect
      forceOpen={forceOpen}
      closePopup={closePopup}
      feedId={feedId}
      items={items}
      customRoute={customRoute}
      disableRedirect={disableRedirect}
      filterKey={filterKey}
      label={t`Filter by contact`}
      type={type}
    />
  )
}

export default observer(MenuFilterContactInformationMultiselect)
