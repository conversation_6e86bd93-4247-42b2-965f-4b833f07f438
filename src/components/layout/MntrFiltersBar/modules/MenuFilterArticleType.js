import { t } from '@lingui/core/macro'
import Icon from '~/components/misc/Icon/Icon'
import { observer } from '~/helpers/mst'
import MenuFilterMultiselect from './MenuFilterMultiselect'

const MenuFilterArticleType = ({
  forceOpen,
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: { account },
}) => {
  const items = account.enums.articleTypeIsActiveIsFeedFilter
  const filterKey = 'article_type'

  return (
    <MenuFilterMultiselect
      forceOpen={forceOpen}
      closePopup={closePopup}
      feedId={feedId}
      items={items}
      customRoute={customRoute}
      disableRedirect={disableRedirect}
      filterKey={filterKey}
      label={t`Filter by article type`}
      itemLeftIcon={() => {
        return <Icon size={22}>{'article'}</Icon>
      }}
    />
  )
}

export default observer(MenuFilterArticleType)
