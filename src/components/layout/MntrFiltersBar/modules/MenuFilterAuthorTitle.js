import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import fuzzy from 'fuzzy'
import debounce from 'lodash/debounce'
import { useState } from 'react'
import FormFilterAuthorTitles from '~/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import removeAccents from '~/helpers/removeAccents'
import { routerReplace } from '~/helpers/router'

const MenuFilterAuthorTitle = ({
  closePopup,
  appStore: {
    authors: { getAuthorTitles, authorTitles },
    filter,
    monitoring,
    router: { makeRouteWithQuery },
  },
  customRoute,
  disableRedirect,
  feedId,
  type,
}) => {
  const [filterValue, setFilterValue] = useState('')
  const feedMap = monitoring.getFeedMap(type)
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter

  const onSearch = ({ filter }) => {
    setFilterValue(filter)
    getAuthorTitles(filter)
  }

  const onSubmit = ({ filter }) => {
    const currRoute = makeRouteWithQuery(
      customRoute,
      undefined,
      { title_query: filter },
      {
        resetPage: true,
      },
    )

    filterStore.addFilterItem({
      type: 'title_query',
      value: filter,
      id: filter,
    })

    closePopup()
    routerReplace(currRoute)
  }

  const menuItems = []

  authorTitles?.map((value) => {
    if (!fuzzy.test(removeAccents(filterValue) || '', removeAccents(value))) {
      return false
    }
    const isSelected = filterStore.labels.title_query?.value === value

    menuItems.push({
      label: value,
      leftIcon: isSelected ? 'radio_button_checked' : 'radio_button_unchecked',
      redirectProps: {
        onClick: () => {
          filterStore.addFilterItem({
            type: 'title_query',
            value,
            id: value,
          })
        },
        customRoute,
        disableRedirect,
        feedId,
        set: { title_query: value },
      },
    })
  })

  return (
    <ListScrollWrapper>
      <Box ml={3} mr={1}>
        <FormFilterAuthorTitles
          autoFocus
          disableIcon
          value=""
          placeholder={t`Search job position`}
          onChange={debounce(onSearch, 500)}
          onSubmit={onSubmit}
        />
      </Box>

      <MntrMenu menuItems={menuItems} closePopup={closePopup} />
      {menuItems.length === 0 && filterValue && (
        <Box p={3} pt={0} color="grey">
          <Trans>No results found</Trans>
        </Box>
      )}
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterAuthorTitle)
