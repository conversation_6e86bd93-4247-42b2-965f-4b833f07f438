import { Trans } from '@lingui/react/macro'
import {
  endOfMonth,
  endOfWeek,
  endOfYear,
  startOfMonth,
  startOfWeek,
  startOfYear,
  subDays,
  subMonths,
  subWeeks,
  subYears,
} from 'date-fns'
import FormFilterDate from '~/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { WEEK_STARTS_ON } from '~/constants'
import formatDate from '~/helpers/date/format'
import isEmpty from '~/helpers/isEmpty'
import { observer } from '~/helpers/mst'
import { routerReplace } from '~/helpers/router'

const generateLeftSide = (customRoute, disableRedirect, feedId) => {
  const date = new Date()

  return [
    {
      label: <Trans>Without limit</Trans>,
      leftIcon: 'calendar_today',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { lower_date: undefined, upper_date: undefined },
      },
    },
    {
      label: <Trans>Today</Trans>,
      leftIcon: 'local_fire_department',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(date, 'yyyy-MM-dd'),
          upper_date: formatDate(date, 'yyyy-MM-dd'),
        },
      },
    },
    {
      label: <Trans>This week</Trans>,
      leftIcon: 'date_range',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(startOfWeek(date, { weekStartsOn: WEEK_STARTS_ON }), 'yyyy-MM-dd'),
          upper_date: formatDate(date, 'yyyy-MM-dd'),
        },
      },
    },
    {
      label: <Trans>This month</Trans>,
      leftIcon: 'date_range',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(startOfMonth(date), 'yyyy-MM-dd'),
          upper_date: formatDate(date, 'yyyy-MM-dd'),
        },
      },
    },
    {
      label: <Trans>This year</Trans>,
      leftIcon: 'date_range',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(startOfYear(date), 'yyyy-MM-dd'),
          upper_date: formatDate(date, 'yyyy-MM-dd'),
        },
      },
    },
  ]
}

const generateRightSide = (customRoute, disableRedirect, feedId) => {
  const date = new Date()

  return [
    {
      label: (
        <>
          <Trans>Today</Trans> + <Trans>Yesterday</Trans>
        </>
      ),
      leftIcon: 'local_fire_department',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(subDays(date, 1), 'yyyy-MM-dd'),
          upper_date: formatDate(date, 'yyyy-MM-dd'),
        },
      },
    },
    {
      label: <Trans>Yesterday</Trans>,
      leftIcon: 'local_fire_department',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(subDays(date, 1), 'yyyy-MM-dd'),
          upper_date: formatDate(subDays(date, 1), 'yyyy-MM-dd'),
        },
      },
    },
    {
      label: <Trans>Last week</Trans>,
      leftIcon: 'date_range',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(
            startOfWeek(subWeeks(date, 1), { weekStartsOn: WEEK_STARTS_ON }),
            'yyyy-MM-dd',
          ),
          upper_date: formatDate(
            endOfWeek(subWeeks(date, 1), { weekStartsOn: WEEK_STARTS_ON }),
            'yyyy-MM-dd',
          ),
        },
      },
    },
    {
      label: <Trans>Last month</Trans>,
      leftIcon: 'date_range',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(startOfMonth(subMonths(date, 1)), 'yyyy-MM-dd'),
          upper_date: formatDate(endOfMonth(subMonths(date, 1)), 'yyyy-MM-dd'),
        },
      },
    },
    {
      label: <Trans>Last year</Trans>,
      leftIcon: 'date_range',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: {
          lower_date: formatDate(startOfYear(subYears(date, 1)), 'yyyy-MM-dd'),
          upper_date: formatDate(endOfYear(subYears(date, 1)), 'yyyy-MM-dd'),
        },
      },
    },
  ]
}

const parseDate = (inputDate) => {
  const arr = inputDate.split('.')
  return `${arr[2]}-${arr[1]}-${arr[0]}`
}

const MenuFilterDate = ({
  closePopup,
  forceOpen,
  customRoute,
  disableRedirect,
  feedId,
  initialValues,
  appStore: {
    router: { makeRouteWithQuery, redirectTo },
    dashboards,
    monitoring: { getFeedMap },
    viewport: { isMobile },
  },
  disableTimePicker = false,
}) => {
  const menuItemsLeftSide = generateLeftSide(customRoute, disableRedirect, feedId)
  const menuItemsRightSide = generateRightSide(customRoute, disableRedirect, feedId)

  const handleSubmit = (model) => {
    closePopup()
    const params = {}
    const startDate = model.lower_date
    const endDate = model.upper_date

    if (startDate) {
      params.lower_date = parseDate(startDate)
    }
    if (endDate) {
      params.upper_date = parseDate(endDate)
    }

    if (!startDate) {
      params.lower_date = params.upper_date
    }

    if (!endDate) {
      params.upper_date = params.lower_date
    }

    params.upper_time = model.upper_time || undefined
    params.lower_time = model.lower_time || undefined

    if (!isEmpty(params) && !customRoute && !disableRedirect) {
      redirectTo(makeRouteWithQuery(undefined, undefined, params, { resetPage: true }))
    }

    if (customRoute) {
      const feed = getFeedMap(dashboards.preview?.type || 'feed').get(feedId)
      const query = feed.filter.queryAddParam(params)
      const as = `${customRoute}${query ? `?${query}` : ''}`

      if (!disableRedirect) {
        routerReplace(makeRouteWithQuery(), as, { shallow: true })
      }
      feed.loadUrl(as)
    }
  }
  return (
    <Box pt={1} maxWidth="500px">
      <MntrMenuHeading label={<Trans>Filter by date</Trans>} />
      <Flex flexWrap="nowrap">
        <Box width={[1 / 2]} mt={-1}>
          <MntrMenu menuItems={menuItemsLeftSide} closePopup={closePopup} />
        </Box>
        <Box width={[1 / 2]} mt={-1}>
          <MntrMenu menuItems={menuItemsRightSide} closePopup={closePopup} />
        </Box>
      </Flex>
      <Box>
        <FormFilterDate
          forceOpen={forceOpen}
          onSubmit={handleSubmit}
          initialValues={initialValues}
          disableTimePicker={disableTimePicker}
          isMobile={isMobile}
        />
      </Box>
    </Box>
  )
}

export default observer(MenuFilterDate)
