import { Trans } from '@lingui/react/macro'
import noopFn from 'lodash/noop'
import FormFilterItems from '~/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems'
import { getMenuItemsReports } from '~/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import { FILTER_VISIBLE_LIMIT } from '~/constants'
import { observer } from '~/helpers/mst'

const MenuFilterTopicMonitorsReports = ({
  activeSubmenu,
  appStore: {
    account,
    filter,
    topics: { list, listFilter, filteredFolders, setListFilter },
  },
  closePopup,
  enableTopicDailyCounter = false,
  handleSelectAll,
  handleSelectOne,
  handleSelect,
  selected,
}) => {
  return (
    <ListScrollWrapper>
      <Box p="2">
        {list.length > FILTER_VISIBLE_LIMIT && (
          <Box pt={1}>
            <FormFilterItems value="" onChange={(model) => setListFilter(model.filter)} noPadding />
          </Box>
        )}

        <Box my={2}>
          <MntrMenuItem
            bg="transparent"
            onClick={handleSelectAll}
            hoverVariant="light"
            label={<Trans>All topics</Trans>}
            mb={1}
            leftIcon="add"
            rounded
          />
        </Box>

        <MntrMenu
          filterStore={filter}
          menuItems={getMenuItemsReports({
            account,
            activeSubmenu,
            enableTopicDailyCounter,
            filteredFolders,
            closePopup,
            handleSelect,
            handleSelectOne,
            selected,
          })}
          filterData={filter}
          onDrag={noopFn}
        />
        {!filteredFolders.length && listFilter && (
          <Box p={3} pt={0} color="grey">
            <Trans>No results found</Trans>
          </Box>
        )}
      </Box>
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterTopicMonitorsReports)
