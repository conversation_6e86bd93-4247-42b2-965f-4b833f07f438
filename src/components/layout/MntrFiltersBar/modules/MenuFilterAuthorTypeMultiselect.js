import { t } from '@lingui/core/macro'
import { observer } from '~/helpers/mst'
import MenuFilterMultiselect from './MenuFilterMultiselect'

const MenuFilterAuthorTypeMultiselect = ({
  closePopup,
  forceOpen,
  appStore: {
    account: {
      enums: {
        authors: { author_type },
      },
    },
  },
  customRoute,
  disableRedirect,
  feedId,
  type,
}) => {
  const items = author_type.filter(({ is_medialist_filter }) => is_medialist_filter)
  const filterKey = 'author_type'

  return (
    <MenuFilterMultiselect
      forceOpen={forceOpen}
      closePopup={closePopup}
      feedId={feedId}
      items={items}
      customRoute={customRoute}
      disableRedirect={disableRedirect}
      filterKey={filterKey}
      label={t`Filter by author type`}
      type={type}
    />
  )
}

export default observer(MenuFilterAuthorTypeMultiselect)
