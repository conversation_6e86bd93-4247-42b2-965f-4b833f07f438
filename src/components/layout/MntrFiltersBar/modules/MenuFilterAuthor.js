import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import Flag from '~/components/Flag/Flag'
import FormFilterAuthor from '~/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor'
import AuthorInfoDetail from '~/components/medialist/content/AuthorInfoDetail'
import AuthorPhoto from '~/components/medialist/content/FeedMedialist/AuthorPhoto/AuthorPhoto'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'

const Country = styled(Box)`
  position: absolute;
  bottom: 14px;
  left: 28px;
`

const MenuFilterAuthor = ({
  closePopup,
  forceOpen,
  appStore: {
    account: { enums },
    authors,
    filter,
    monitoring: { feedMap },
    router: { makeRouteWithQuery },
    viewport,
  },
  customRoute,
  disableRedirect,
  feedId,
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter
  const menuItems = []

  authors.autocompleteData.map((item) => {
    const country = enums.media_country.find((country) => country.id === item.country)

    menuItems.push({
      label: item.name,
      secondaryText: <AuthorInfoDetail author={item} />,
      leftIcon: (
        <>
          <AuthorPhoto size={26} authorType={item.author_type} image={item.photo_url.small} />
          {item.country && (
            <Country>
              <Flag country={country.code} size={11} />
            </Country>
          )}
        </>
      ),
      buttonGroup: [
        {
          tooltip: <Trans>Open</Trans>,
          icon: 'open_in_browser',
          href: `/author/${item.id}`,
          size: 'small',
          onClick: () => {
            closePopup()

            const currRoute = makeRouteWithQuery()

            routerReplace(currRoute, currRoute, { shallow: true })
            routerPush(currRoute, `/author/${item.id}`, { shallow: true })
          },
        },
      ],
      onClick: () => {
        filterStore.addFilterItem({
          type: 'author',
          value: item.name,
          id: item.id,
        })
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { author: item.id },
      },
    })
  })

  return (
    <ListScrollWrapper delta={150}>
      <Box pt={1} style={viewport.isMobile ? { maxWidth: 300 } : { width: 'auto' }}>
        <FormFilterAuthor
          forceOpen={forceOpen}
          onSubmit={(model) => authors.loadAutocomplete(model.author)}
        />
        {menuItems.length > 0 && (
          <Box mt={-2}>
            <MntrMenu menuItems={menuItems} closePopup={closePopup} />
          </Box>
        )}
      </Box>
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterAuthor)
