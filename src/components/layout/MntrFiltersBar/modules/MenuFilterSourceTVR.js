import { Trans } from '@lingui/react/macro'
import MediaIcon from '~/components/misc/MediaIcon/MediaIcon'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const MenuFilterSourceTVR = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    account: {
      enums: { crisis_communication },
    },
    filter,

    monitoring: { feedMap },
  },
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter
  const mediaTypes = crisis_communication.media_types
  const menuItems = [
    {
      label: <Trans>Filter by media</Trans>,
    },
  ]

  mediaTypes.map((item) => {
    menuItems.push({
      label: item.text,
      leftIcon: (
        <MediaIcon
          avatarStyle={{
            position: 'absolute',
            left: 0,
            top: 0,
            width: 26,
            height: 26,
          }}
          color={item.color}
          size={26}
          id={item.id}
          type={'tvr'}
        />
      ),
      onClick: () => {
        filterStore.addFilterItem({
          type: 'tvr_category_type',
          value: item.text,
          id: item.id,
        })
      },
      leftIconColor: item.color,
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { tvr_category_type: item.id },
      },
    })
  })
  return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
}

export default observer(MenuFilterSourceTVR)
