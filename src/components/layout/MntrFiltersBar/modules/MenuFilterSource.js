import { Trans } from '@lingui/react/macro'
import color from 'color'
import debounce from 'lodash/debounce'
import get from 'lodash/get'
import { styled } from 'styled-components'
import FormSearchSources from '~/components/layout/MntrFiltersBar/forms/FormSearchSources/FormSearchSources'
import withModalPageNumbers from '~/components/layout/MntrFiltersBar/modals/withModalPageNumbers'
import StyledSubTree from '~/components/layout/MntrFiltersBar/style/StyledSubTree'
import StyledTree from '~/components/layout/MntrFiltersBar/style/StyledTree'
import FilterNewsSourceIcon from '~/components/layout/MntrFiltersBar/utils/FilterNewsSourceIcon/FilterNewsSourceIcon'
import CategoryTypeIcon from '~/components/misc/CategoryTypeIcon/CategoryTypeIcon'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuRedirectItem from '~/components/misc/MntrMenu/modules/MntrMenuRedirectItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import categoryTypes from '~/constants/categoryTypes'
import withModalMegalist from '~/helpers/modal/withModalMegalist'
import { observer } from '~/helpers/mst'

const NewsSourceFilterBar = styled.div`
  display: inline-block;
  margin-top: 4px;
  margin-left: 37px;
`

const MenuFilterSource = ({
  closePopup,
  forceOpen,
  customRoute,
  feedId,
  disableRedirect,
  isDashboard,
  appStore: {
    account,
    megalist,
    dataSource,
    filter,
    monitoring,
    monitoring: { activeCategoryTypes, promoCategoryTypes },
  },
  type,
  isPreview,
}) => {
  const feedMap = monitoring.getFeedMap(type)
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter

  // if is visible, tree is moved to second level
  const isVisibleRootLineInFilterSource = () => {
    return account.enums.categoryTypeIsActive.filter((item) => item.parent_id === null).length > 0
  }

  const menuItems = []

  if (!account.workspace.is_global_mediaboard) {
    menuItems.push({
      label: <Trans>Filter sources</Trans>,
    })

    if (account.workspace?.limits.allow_mainstream_media) {
      menuItems.push({
        leftIcon: 'star',
        label: <Trans>Mainstream sources</Trans>,
        redirectProps: {
          customRoute,
          disableRedirect,
          feedId,
          onClick: () => {
            filterStore.addFilterItem({
              type: 'megalist_hash',
              value: 'mainstream',
            })
            closePopup()
          },
          set: { megalist_hash: 'mainstream' },
        },
      })
    }

    menuItems.push({
      label: <Trans>Custom selection</Trans>,
      leftIcon: 'sort',
      offset: true,
      beforeOpen: () => {
        if (typeof forceOpen === 'function') {
          forceOpen()
        }
        // closePopup()

        megalist.openFromHash()
      },
      modalZIndex: 7000,
      ...withModalMegalist({
        modalTitle: <Trans>Select sources</Trans>,
        isStackedModal: isPreview,
        showAllCategoryTypes: true,
      }),
    })
  }

  const loadSource = (object) => {
    dataSource.load('source', object.source)
  }

  const hasSubtype = activeCategoryTypes.find(
    (item) => item.is_active && item.id === categoryTypes.CATEGORY_TYPE_SOCIAL_MEDIA,
  )

  return (
    <ListScrollWrapper delta={isDashboard ? 300 : 200}>
      <FormSearchSources forceOpen={forceOpen} onSubmit={debounce(loadSource, 500)} />
      <Box mt={-2}>
        <MntrMenuHeading label={<Trans>Filter by media</Trans>} />
      </Box>
      {activeCategoryTypes.map((item) => {
        const buttonGroup = () => [
          {
            label: <Trans>Filter specific pages</Trans>,
            leftIcon: 'chrome_reader_mode',
            ...withModalPageNumbers({
              feedId,
              closePopup,
              customRoute,
              disableRedirect,
            }),
          },
        ]

        return (
          <div key={item.id.toString()}>
            <StyledTree
              subtype={hasSubtype && item.parent_id !== null}
              isLast={item.id === categoryTypes.CATEGORY_TYPE_AGENCY_MEDIA}
            >
              <div className="border"></div>
              <MntrMenuRedirectItem
                label={item.text}
                actionsZIndex={6000}
                actions={item.id === categoryTypes.CATEGORY_TYPE_OFFLINE_MEDIA ? buttonGroup : null}
                customRoute={customRoute}
                disableRedirect={disableRedirect}
                feedId={feedId}
                onClick={() => {
                  filterStore.addFilterItem({
                    type: 'category_type',
                    value: item.text,
                    id: item.id,
                  })
                  closePopup()
                }}
                closePopup={closePopup}
                set={{ category_type: item.id }}
                hoverColor={color(item.color).alpha(0.2).toString()}
                leftIcon={<CategoryTypeIcon categoryType={item} />}
              />
            </StyledTree>

            {get(dataSource, 'data.category_types', []).map((category) => {
              if (category.category_type.id === item.id) {
                const sources = get(category, 'news_sources') || []
                const length = sources.length
                return get(category, 'news_sources').map((newsSource, index) => {
                  return (
                    <StyledSubTree
                      color={get(category, 'category_type.color')}
                      key={newsSource.id.toString()}
                      subtype={true}
                      mainLine={isVisibleRootLineInFilterSource}
                      marginLeft={
                        !isVisibleRootLineInFilterSource ||
                        item.id === categoryTypes.CATEGORY_TYPE_SOCIAL_MEDIA
                          ? 26
                          : 52
                      }
                      isLast={index === length - 1}
                      className="list-media-subtype"
                    >
                      {item.id !== categoryTypes.CATEGORY_TYPE_SOCIAL_MEDIA && (
                        <div className="line"></div>
                      )}
                      <div className="border"></div>
                      <MntrMenuRedirectItem
                        closePopup={closePopup}
                        label={newsSource.name}
                        onClick={() => {
                          filterStore.addFilterItem({
                            type: 'news_source',
                            value: newsSource.name,
                            id: newsSource.id,
                          })
                          closePopup()
                        }}
                        secondaryText={get(newsSource, 'category.name')}
                        customRoute={customRoute}
                        disableRedirect={disableRedirect}
                        feedId={feedId}
                        hoverColor={color(item.color).alpha(0.2).toString()}
                        set={{ news_source: newsSource.id }}
                      />
                    </StyledSubTree>
                  )
                })
              }
            })}

            {item.filter_news_source && (
              <NewsSourceFilterBar>
                {item.filter_news_source.map((newsSource) => {
                  return (
                    <span key={newsSource.id}>
                      <FilterNewsSourceIcon
                        icon={newsSource.icon}
                        customRoute={customRoute}
                        disableRedirect={disableRedirect}
                        feedId={feedId}
                        set={{ news_source: newsSource.id }}
                        onClick={() => {
                          filterStore.addFilterItem({
                            type: 'news_source',
                            value: newsSource.text,
                            id: newsSource.id,
                          })
                          closePopup()
                        }}
                      />
                    </span>
                  )
                })}
              </NewsSourceFilterBar>
            )}
          </div>
        )
      })}

      {promoCategoryTypes.map((item) => {
        return (
          <div key={item.id.toString()}>
            <StyledTree>
              <div className="border"></div>
              <div className="request-label">
                <MntrButton
                  label={<Trans id="featureRequest.Request">Request</Trans>}
                  requestId={`CATEGORY-TYPE__${item.text}`}
                />
              </div>
              <MntrMenuItem
                disabled
                label={item.text}
                key={item.id && item.id.toString()}
                hoverColor={color(item.color).alpha(0.2).toString()}
                leftIcon={<CategoryTypeIcon categoryType={item} />}
              />
            </StyledTree>
          </div>
        )
      })}

      {get(dataSource, 'data.publishers') && get(dataSource, 'data.publishers').length > 0 && (
        <MntrMenuHeading label={<Trans>Publisher</Trans>} />
      )}

      {get(dataSource, 'data.publishers') &&
        get(dataSource, 'data.publishers').map((publisher) => {
          return (
            <MntrMenuRedirectItem
              key={publisher.id.toString()}
              label={publisher.name}
              onClick={() => {
                filterStore.addFilterItem({
                  type: 'publisher',
                  value: publisher.name,
                  id: publisher.id,
                })
                closePopup()
              }}
              customRoute={customRoute}
              disableRedirect={disableRedirect}
              closePopup={closePopup}
              feedId={feedId}
              set={{ publisher: publisher.id }}
              leftIcon={'bookmark_border'}
            />
          )
        })}
      <MntrMenu menuItems={menuItems} closePopup={closePopup} />
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterSource)
