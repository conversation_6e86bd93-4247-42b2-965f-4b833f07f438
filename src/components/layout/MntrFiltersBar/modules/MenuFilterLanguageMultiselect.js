import { t } from '@lingui/core/macro'
import Flag from '~/components/Flag/Flag'
import { observer } from '~/helpers/mst'
import MenuFilterMultiselect from './MenuFilterMultiselect'

const MenuFilterLanguageMultiselect = ({
  forceOpen,
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: { account },
}) => {
  const items = account.enums.mediaLanguageIsActive
  const filterKey = 'language'

  return (
    <MenuFilterMultiselect
      forceOpen={forceOpen}
      closePopup={closePopup}
      feedId={feedId}
      items={items}
      customRoute={customRoute}
      disableRedirect={disableRedirect}
      filterKey={filterKey}
      label={t`Filter by language`}
      itemLeftIcon={(item) => {
        return <Flag language={item.id} size={20} />
      }}
    />
  )
}

export default observer(MenuFilterLanguageMultiselect)
