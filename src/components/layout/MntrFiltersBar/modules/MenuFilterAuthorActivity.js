import { t } from '@lingui/core/macro'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const MenuFilterAuthorActivity = ({
  closePopup,
  // forceOpen,
  appStore: {
    account: {
      enums: {
        authors: { author_activity },
      },
    },
    filter,
    monitoring,
    viewport,
  },
  customRoute,
  disableRedirect,
  feedId,
  type,
}) => {
  const menuItems = [
    {
      label: t`Filter by activity`,
    },
  ]
  const feedMap = monitoring.getFeedMap(type)
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter

  author_activity.map(({ id, text }) => {
    const isSelected = filterStore.labels?.author_activity?.value === id

    menuItems.push({
      label: text,
      leftIcon: isSelected ? 'radio_button_checked' : 'radio_button_unchecked',
      redirectProps: {
        onClick: () => {
          filterStore.addFilterItem({
            type: 'author_activity',
            value: text,
            id: id,
          })
        },
        customRoute,
        disableRedirect,
        feedId,
        set: { author_activity: id },
      },
    })
  })

  return (
    <ListScrollWrapper delta={150}>
      <Box maxWidth={viewport.isMobile ? 300 : null}>
        {menuItems.length > 0 && (
          <Box>
            <MntrMenu menuItems={menuItems} closePopup={closePopup} />
          </Box>
        )}
      </Box>
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterAuthorActivity)
