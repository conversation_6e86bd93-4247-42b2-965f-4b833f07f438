import { Trans } from '@lingui/react/macro'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const MenuFilterLanguageTVR = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    account: {
      enums: { crisis_communication },
    },
    filter,
    monitoring: { feedMap },
  },
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter
  const languages = crisis_communication.languages || []

  const menuItems = []

  menuItems.push({
    label: <Trans>Filter by language</Trans>,
  })

  languages.map((item) => {
    menuItems.push({
      label: item.text,
      flag: item.id,
      onClick: () => {
        filterStore.addFilterItem({
          type: 'tvr_language',
          value: item.text,
          id: item.id,
        })
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { tvr_language: item.id },
      },
    })
  })

  return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
}

export default observer(MenuFilterLanguageTVR)
