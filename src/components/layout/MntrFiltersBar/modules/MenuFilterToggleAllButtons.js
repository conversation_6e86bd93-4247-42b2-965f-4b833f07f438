import { Trans } from '@lingui/react/macro'
import { deselectAll, selectAll } from '~/components/layout/MntrFiltersBar/utils/MenuFilterUtils'
import { Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const MenuFilterToggleAllButtons = ({
  appStore: {
    dashboards: { preview },
    router: { redirectTo },
  },
  items,
  customRoute,
  disableRedirect,
  feed,
  filterKey,
  setSelectedItems,
  filterStore,
}) => {
  return (
    <Flex
      justifyContent="space-between"
      fontSize={0}
      width={1}
      px={3}
      py={2}
      bg="multiselectFooter"
    >
      <Flex gap={2}>
        <MntrButton
          icon="done_all"
          iconBg="transparent"
          iconColor="currentColor"
          isChip
          onClick={() => {
            const itemsSelectAll = items.flatMap(({ text, id, subItems }) => {
              return subItems
                ? subItems.map((item) => {
                    return { value: item.id, text: item.text }
                  })
                : { value: id, text }
            })

            setSelectedItems(itemsSelectAll)

            const itemsSelectAllIds = itemsSelectAll.map(({ value }) => value)

            selectAll(
              itemsSelectAll,
              itemsSelectAllIds,
              filterKey,
              filterStore,
              disableRedirect ? undefined : redirectTo,
              feed,
              preview,
              customRoute,
            )
          }}
          label={<Trans>Select all</Trans>}
        />
        <MntrButton
          icon="remove_done"
          iconBg="transparent"
          iconColor="currentColor"
          isChip
          onClick={() => {
            setSelectedItems([])
            deselectAll(
              filterKey,
              disableRedirect ? undefined : redirectTo,
              feed,
              preview,
              customRoute,
            )
          }}
          label={<Trans>Deselect all</Trans>}
        />
      </Flex>
    </Flex>
  )
}

export default observer(MenuFilterToggleAllButtons)
