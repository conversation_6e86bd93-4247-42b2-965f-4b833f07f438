import { Trans } from '@lingui/react/macro'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const MenuFilterChangeType = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    account: { enums },
    filter,
    monitoring: { feedMap },
    viewport,
  },
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter
  const changeTypes = enums.workspaces_changelog.change_type

  const menuItems = [
    {
      label: <Trans>Change Type</Trans>,
    },
  ]

  changeTypes.map(({ text, id }) => {
    const isSelected = Number(filter.data.change_type) === id

    menuItems.push({
      label: text,
      leftIcon: isSelected ? 'radio_button_checked' : 'radio_button_unchecked',
      onClick: () => {
        filterStore.addFilterItem({
          type: 'change_type',
          value: text,
          id,
        })
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { change_type: id },
      },
    })
  })

  return (
    <ListScrollWrapper delta={150}>
      <Box style={viewport.isMobile ? { maxWidth: 300 } : {}}>
        {menuItems.length > 0 && <MntrMenu menuItems={menuItems} closePopup={closePopup} />}
      </Box>
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterChangeType)
