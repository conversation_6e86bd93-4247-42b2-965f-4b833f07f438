import { Trans } from '@lingui/react/macro'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const MenuFilterNewsroomCategory = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    filter,
    newsroom,
    monitoring: { feedMap },
    viewport,
  },
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter
  const categoriesList = newsroom.selected.categories

  const menuItems = [
    {
      label: <Trans>Category</Trans>,
    },
  ]

  categoriesList.map(({ name, id }) => {
    menuItems.push({
      label: name,
      onClick: () => {
        filterStore.addFilterItem({
          type: 'category',
          value: name,
          id,
        })
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { category: id },
      },
    })
  })

  return (
    <ListScrollWrapper delta={150}>
      <Box style={viewport.isMobile ? { maxWidth: 300 } : {}}>
        {menuItems.length > 0 && <MntrMenu menuItems={menuItems} closePopup={closePopup} />}
      </Box>
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterNewsroomCategory)
