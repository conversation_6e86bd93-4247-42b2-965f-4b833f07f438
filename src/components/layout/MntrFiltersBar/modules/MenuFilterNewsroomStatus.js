import { Trans } from '@lingui/react/macro'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const MenuFilterNewsroomStatus = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    account: { enums },
    filter,
    monitoring: { feedMap },
    viewport,
  },
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter
  const statusList = enums.publishing.blog_post_status

  const menuItems = [
    {
      label: <Trans>Status</Trans>,
    },
  ]

  statusList.map(({ text, id }) => {
    const isSelected = Number(filter.data.status) === id

    menuItems.push({
      label: text,
      leftIcon: isSelected ? 'radio_button_checked' : 'radio_button_unchecked',
      onClick: () => {
        filterStore.addFilterItem({
          type: 'status',
          value: text,
          id,
        })
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { status: id },
      },
    })
  })

  return (
    <ListScrollWrapper delta={150}>
      <Box style={viewport.isMobile ? { maxWidth: 300 } : {}}>
        {menuItems.length > 0 && <MntrMenu menuItems={menuItems} closePopup={closePopup} />}
      </Box>
    </ListScrollWrapper>
  )
}

export default observer(MenuFilterNewsroomStatus)
