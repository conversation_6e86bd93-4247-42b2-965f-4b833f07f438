import { t } from '@lingui/core/macro'
import Flag from '~/components/Flag/Flag'
import groupCountriesByContinent from '~/helpers/groupCountriesByContinent'
import { observer } from '~/helpers/mst'
import MenuFilterMultiselect from './MenuFilterMultiselect'

const MenuFilterCountryMultiselect = ({
  forceOpen,
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    account: { enums },
  },
  filterKey = 'news_source_country',
}) => {
  const items = groupCountriesByContinent(enums.mediaCountryIsActive, enums.continent)

  return (
    <MenuFilterMultiselect
      forceOpen={forceOpen}
      closePopup={closePopup}
      feedId={feedId}
      items={items}
      customRoute={customRoute}
      disableRedirect={disableRedirect}
      filterKey={filterKey}
      label={t`Filter by country`}
      itemLeftIcon={(item) => {
        return item.code ? <Flag country={item.code} size={20} /> : null
      }}
    />
  )
}

export default observer(MenuFilterCountryMultiselect)
