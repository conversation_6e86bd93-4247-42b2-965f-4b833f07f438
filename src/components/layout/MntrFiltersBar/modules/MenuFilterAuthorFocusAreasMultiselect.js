import { t } from '@lingui/core/macro'
import { observer } from '~/helpers/mst'
import MenuFilterMultiselect from './MenuFilterMultiselect'

const MenuFilterAuthorFocusAreasMultiselect = ({
  closePopup,
  forceOpen,
  appStore: {
    account: {
      enums: {
        authors: { author_focus_areas },
      },
    },
  },
  customRoute,
  disableRedirect,
  feedId,
  type,
}) => {
  const items = author_focus_areas
  const filterKey = 'author_focus_areas'

  return (
    <MenuFilterMultiselect
      forceOpen={forceOpen}
      closePopup={closePopup}
      feedId={feedId}
      items={items}
      customRoute={customRoute}
      disableRedirect={disableRedirect}
      filterKey={filterKey}
      label={t`Filter by focus area`}
      type={type}
    />
  )
}

export default observer(MenuFilterAuthorFocusAreasMultiselect)
