import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'

const MenuFilterOrderBy = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    account,
    filter,
    monitoring: { feedMap },
    router: { makeRouteWithQuery },
  },
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter
  const { asPath } = useRouter()
  const menuItems = [
    {
      label: <Trans>Sort</Trans>,
    },
  ]

  account.enums.feed.order_by.forEach((item) => {
    menuItems.push({
      label: item.text,
      leftIcon: 'sort',
      onClick: () => {
        filterStore.addFilterItem({
          type: 'order_by',
          value: item.text,
          id: item.id,
        })

        if (customRoute) {
          const feed = feedMap.get(feedId)
          const url = `${customRoute}?${feed.filter.urlWithParam({
            order_by: item.id,
          })}`

          if (!disableRedirect) {
            routerReplace(makeRouteWithQuery(), url, { shallow: true })
          }
          feed.loadUrl(url)
          return false
        }

        const query = filter.urlWithParam({ order_by: item.id })
        routerPush(`${asPath.split('?')[0]}${query ? `?${query}` : ''}`)
      },
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { order_by: item.id },
      },
    })
  })

  return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
}

export default observer(MenuFilterOrderBy)
