import { Trans } from '@lingui/react/macro'
import fuzzy from 'fuzzy'
import Form<PERSON>hannelSearch from '~/components/layout/MntrFiltersBar/forms/FormChannelSearch/FormChannelSearch'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import makeUrl from '~/helpers/makeurl'
import { observer } from '~/helpers/mst'

const MenuFilterChannelsTVR = ({
  closePopup,
  customRoute,
  disableRedirect,
  feedId,
  appStore: {
    tvr,
    account: {
      enums: { crisis_communication },
    },
  },
}) => {
  const channels = crisis_communication.channels || []
  const channelsFiltered = channels.filter((item) =>
    fuzzy.test(tvr.filterChannels.toLowerCase(), makeUrl(item.name.toLowerCase())),
  )
  const channelsTV = channelsFiltered.filter((item) => item.medium_type === 2)
  const channelsRadio = channelsFiltered.filter((item) => item.medium_type === 1)
  const isEmpty = channelsFiltered.length === 0

  const menuItems = []

  // Empty
  if (isEmpty) {
    menuItems.push({
      label: <Trans>No results found</Trans>,
    })
  }

  // TV
  if (channelsTV.length > 0) {
    menuItems.push({
      label: <Trans>TV</Trans>,
    })
  }

  channelsTV.map((item) => {
    menuItems.push({
      label: item.name,
      img: item.logo,
      leftIcon: 'tv',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { tvr_channel: item.id },
      },
    })
  })

  // RADIO
  if (channelsRadio.length > 0) {
    menuItems.push({
      label: <Trans>Radio</Trans>,
    })
  }

  channelsRadio.map((item) => {
    menuItems.push({
      label: item.name,
      img: item.logo,
      leftIcon: 'radio',
      redirectProps: {
        customRoute,
        disableRedirect,
        feedId,
        set: { tvr_channel: item.id },
      },
    })
  })
  return (
    <Box>
      <FormChannelSearch
        value={tvr.filterChannels}
        onChange={(model) => tvr.setChannelFilter(model.filter)}
      />
      <MntrMenu menuItems={menuItems} closePopup={closePopup} />
    </Box>
  )
}

export default observer(MenuFilterChannelsTVR)
