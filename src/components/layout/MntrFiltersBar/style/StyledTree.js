import { css, styled } from 'styled-components'

const StyledTree = styled.div`
  position: relative;

  & .hidden {
    display: none !important;
  }

  & .request-label {
    display: inline-block;
    position: absolute;
    right: 12px;
    top: 4px;
    z-index: 1;
  }

  & .request-close {
    position: absolute;
    top: 4px !important;
    right: 4px;
    z-index: 10;
    cursor: pointer;
  }

  &:hover .hidden {
    display: block !important;
  }
  ${(props) =>
    props.subtype
      ? css`
          margin-left: 26px;

          & .border {
            position: absolute;
            left: 0;
            top: 7px;
            width: 8px;
            height: 8px;
            border-left: 1px solid ${(props) => (props.color ? props.color : '#bdbdbd')};
            border-bottom: 1px solid ${(props) => (props.color ? props.color : '#bdbdbd')};
            border-radius: 0 0 0 8px;
          }

          &:before {
            content: '';
            position: absolute;
            width: 1px;
            height: ${props.isLast ? 10 : 32}px;
            background: #bdbdbd;
            left: 0px;
            top: -3px;
          }

          &:after {
            content: '';
            position: absolute;
            width: 6px;
            height: 1px;
            background: #bdbdbd;
            left: 8px;
            top: 15px;
          }
        `
      : null};
`

export default StyledTree
