import { css, styled } from 'styled-components'

const StyledSubTree = styled.div`
  position: relative;
  ${(props) =>
    props.subtype
      ? css`
          margin-left: ${props.marginLeft || 52}px;
          padding-left: 11px;

          ${Boolean(props.mainLine) &&
          css`
            & .line {
              position: absolute;
              width: 1px;
              height: auto;
              left: -26px;
              top: -3px;
              bottom: 0;
              background: #ccc;
            }
          `}

          & .border {
            position: absolute;
            left: 0;
            top: 7px;
            width: 8px;
            height: 8px;
            border-left: 1px solid ${(props) => (props.color ? props.color : '#ccc')};
            border-bottom: 1px solid ${(props) => (props.color ? props.color : '#ccc')};
            border-radius: 0 0 0 8px;
          }

          &:before {
            content: '';
            position: absolute;
            width: 1px;
            height: ${props.isLast ? '13px' : 'auto'};
            background: ${(props) => (props.color ? props.color : '#ccc')};
            left: 0px;
            top: -3px;
            bottom: 0px;
          }

          &:after {
            content: '';
            position: absolute;
            width: 10px;
            height: 1px;
            background: ${(props) => (props.color ? props.color : '#ccc')};
            left: 9px;
            top: 15px;
          }
        `
      : null};
`

export default StyledSubTree
