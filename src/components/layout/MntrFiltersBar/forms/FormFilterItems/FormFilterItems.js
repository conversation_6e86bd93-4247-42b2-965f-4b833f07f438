import { t } from '@lingui/core/macro'
import { Field, Form, FormSpy } from 'react-final-form'
import { css, styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'

const StickyFilter = styled(Flex)`
  position: sticky;
  top: 0;
  z-index: 6;
  background-color: ${({ theme, bgColor }) =>
    bgColor ? theme.colors[bgColor] : theme.popper.background};
`

const LeftCol = styled(Flex)`
  padding-left: 16px;
  color: ${({ theme, iconColor }) => {
    if (iconColor && theme.colors[iconColor]) {
      return theme.colors[iconColor]
    }
    return theme.popper.menuItem
  }};

  ${({ iconPadding }) =>
    iconPadding &&
    css`
      padding: ${iconPadding} !important;
    `}
`
const FormFilterItems = ({
  value,
  onChange,
  autoFocus = false,
  bgColor,
  iconColor,
  disableIcon = false,
  placeholder = t`Filter`,
  iconPadding,
  noPadding,
}) => {
  return (
    <StickyFilter flexWrap="nowrap" {...(!noPadding && { pt: 2 })} center bgColor={bgColor} py={1}>
      {!disableIcon && (
        <LeftCol iconPadding={iconPadding} px={2} iconColor={iconColor} center>
          <Icon>search</Icon>
        </LeftCol>
      )}

      <Box width={1} mr={2}>
        <Form
          onSubmit={onChange}
          initialValues={{ filter: value }}
          render={({ handleSubmit }) => {
            return (
              <>
                <FormSpy onChange={handleSubmit} />
                <Field
                  name="filter"
                  fullWidth
                  autoFocus={autoFocus}
                  autoComplete="off"
                  placeholder={placeholder}
                  component={MntrTextFieldAdapter}
                />
              </>
            )
          }}
        ></Form>
      </Box>
    </StickyFilter>
  )
}

export default FormFilterItems
