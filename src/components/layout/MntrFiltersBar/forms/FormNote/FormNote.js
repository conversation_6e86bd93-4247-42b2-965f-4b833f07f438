import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { Field, Form, FormSpy } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

const FormNote = ({ onSubmit, forceOpen, initialValues }) => {
  return (
    <Form
      onSubmit={onSubmit}
      initialValues={initialValues}
      render={({ handleSubmit }) => {
        return (
          <form onSubmit={handleSubmit}>
            <FormSpy
              onChange={(spy) => {
                if (spy.values?.note && typeof forceOpen === 'function') {
                  forceOpen()
                }
              }}
            />
            <Flex flexWrap="nowrap" p={3} pt={0} center>
              <Box width={1}>
                <Box pr={2}>
                  <Field
                    name="note"
                    autoFocus
                    autoComplete="off"
                    placeholder={t`Search in Notes`}
                    component={MntrTextFieldAdapter}
                    label={<MntrMenuHeading label={<Trans>Search</Trans>} noPadding />}
                  />
                </Box>
              </Box>
              <Box mt={4}>
                <MntrButton mt={2} type="submit" icon="search" bg="primary" />
              </Box>
            </Flex>
          </form>
        )
      }}
    />
  )
}

export default FormNote
