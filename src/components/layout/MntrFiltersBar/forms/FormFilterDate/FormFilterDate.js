import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import { useState } from 'react'
import { Field, Form, FormSpy } from 'react-final-form'
import MntrDatepickerAdapter from '~/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'

const FormFilterDate = ({
  onSubmit,
  forceOpen,
  initialValues,
  disableTimePicker,
  isMobile,
  enableLowerDate,
  enableUpperDate,
  lowerDateEnabled,
  upperDateEnabled,
}) => {
  return (
    <Form
      onSubmit={onSubmit}
      initialValues={initialValues}
      subscription={{}}
      render={({ handleSubmit, form: { change } }) => {
        return (
          <form onSubmit={handleSubmit}>
            <FormSpy
              onChange={(state) => {
                const lowerDate = state.values.lower_date
                const upperDate = state.values.upper_date

                enableLowerDate(!!lowerDate)
                enableUpperDate(!!upperDate)

                if (!lowerDate) {
                  change('lower_time', undefined)
                }

                if (!upperDate) {
                  change('upper_time', undefined)
                }
              }}
            />
            <MntrMenuHeading label={<Trans>Custom</Trans>} />

            <Flex p={3} pt={0} centerY>
              <Flex width={1} mr={isMobile ? 2 : 1} flexDirection={isMobile ? 'column' : 'row'}>
                <Box pr={!isMobile && 1} width={!isMobile && !disableTimePicker && [1 / 2]}>
                  <Field
                    name="lower_date"
                    label={<Trans>From</Trans>}
                    onFocus={forceOpen}
                    component={MntrDatepickerAdapter}
                    onSubmit={(day) => change('lower_date', day)}
                  />
                </Box>
                {!disableTimePicker && (
                  <Box width={!isMobile && [1 / 2]} mt={'16px'}>
                    <Field
                      parse={identityFn}
                      name="lower_time"
                      type="time"
                      component={MntrTextFieldAdapter}
                      disabled={!lowerDateEnabled}
                      placeholder={t`Time`}
                    />
                  </Box>
                )}
              </Flex>

              <Flex
                width={1}
                mr={isMobile ? 2 : 1}
                ml={!isMobile && 1}
                flexDirection={isMobile ? 'column' : 'row'}
              >
                <Box pr={!isMobile && 1} width={!isMobile && !disableTimePicker && [1 / 2]}>
                  <Field
                    name="upper_date"
                    label={<Trans>To</Trans>}
                    onFocus={forceOpen}
                    component={MntrDatepickerAdapter}
                    onSubmit={(day) => change('upper_date', day)}
                  />
                </Box>
                {!disableTimePicker && (
                  <Box width={!isMobile && [1 / 2]} mt={'16px'}>
                    <Field
                      parse={identityFn}
                      name="upper_time"
                      type="time"
                      component={MntrTextFieldAdapter}
                      disabled={!upperDateEnabled}
                      placeholder={t`Time`}
                    />
                  </Box>
                )}
              </Flex>
              <Box mt={3}>
                <MntrButton type="submit" icon="search" bg="primary" />
              </Box>
            </Flex>
          </form>
        )
      }}
    />
  )
}

const FormDate = observer(FormFilterDate)

export default function FormFilterDateWithHooks(props) {
  const [lowerDateEnabled, enableLowerDate] = useState(false)
  const [upperDateEnabled, enableUpperDate] = useState(false)

  return (
    <FormDate
      {...props}
      lowerDateEnabled={lowerDateEnabled}
      enableLowerDate={enableLowerDate}
      upperDateEnabled={upperDateEnabled}
      enableUpperDate={enableUpperDate}
    />
  )
}
