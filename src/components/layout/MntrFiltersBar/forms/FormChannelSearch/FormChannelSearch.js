import { t } from '@lingui/core/macro'
import { Field, Form, FormSpy } from 'react-final-form'
import { styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'

const LeftCol = styled(Box)`
  padding-top: 20px;
  padding-left: 16px;
  color: ${({ theme }) => theme.popper.menuItem};
`
const FormChannelSearch = ({ value, onChange }) => {
  return (
    <Flex flexWrap="nowrap">
      <LeftCol px={2}>
        <Icon>search</Icon>
      </LeftCol>
      <Box width={1} mr={2} mt={2}>
        <Form
          onSubmit={onChange}
          initialValues={{ filter: value }}
          render={({ handleSubmit }) => {
            return (
              <>
                <FormSpy onChange={handleSubmit} />
                <Field
                  name="filter"
                  fullWidth
                  autoComplete="off"
                  placeholder={t`Filter`}
                  component={MntrTextFieldAdapter}
                />
              </>
            )
          }}
        ></Form>
      </Box>
    </Flex>
  )
}

export default FormChannelSearch
