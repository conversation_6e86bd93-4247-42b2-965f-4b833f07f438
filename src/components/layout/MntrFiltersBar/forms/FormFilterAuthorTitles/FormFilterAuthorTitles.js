import { t } from '@lingui/core/macro'
import { Field, Form, FormSpy } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

const FormFilterAuthorTitles = ({
  value,
  onChange,
  onSubmit,
  autoFocus = false,
  placeholder = t`Filter`,
}) => {
  return (
    <Flex flexWrap="nowrap">
      <Box width={1} mr={2} mt={1}>
        <Form
          onSubmit={onSubmit}
          initialValues={{ filter: value }}
          render={({ handleSubmit }) => {
            return (
              <form onSubmit={handleSubmit}>
                <FormSpy
                  onChange={(model) => {
                    onChange(model.values)
                  }}
                />

                <Flex center>
                  <Field
                    label={<MntrMenuHeading label={t`Filter by job position`} noPadding />}
                    name="filter"
                    fullWidth
                    autoFocus={autoFocus}
                    autoComplete="off"
                    placeholder={placeholder}
                    component={MntrTextFieldAdapter}
                    transparentBg
                  />
                  <Box mt={5}>
                    <MntrButton type="submit" icon="search" bg="flat" />
                  </Box>
                </Flex>
              </form>
            )
          }}
        ></Form>
      </Box>
    </Flex>
  )
}

export default FormFilterAuthorTitles
