import { t } from '@lingui/core/macro'
import { Box } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'

interface IFormAuthorProps extends IMntrFormProps {
  forceOpen: () => void
  title: string
}

const FormAuthor = ({ onSubmit, initialValues, forceOpen, title }: IFormAuthorProps) => {
  const schema: IFormSchemaItem[] = [
    {
      label: title || t`Filter by author`,
    },
    {
      name: 'author',
      adapter: 'text',
      placeholder: t`Author's name`,
      autoFocus: true,
      autoComplete: 'off',
    },
  ]

  return (
    <Box p={3} pt={0}>
      <MntrForm
        formGap={1}
        onSubmit={onSubmit}
        initialValues={initialValues}
        schema={schema}
        autosubmit
        visibleSubmitButton={false}
        onChange={(spy) => {
          if (spy.values.author && typeof forceOpen === 'function') {
            forceOpen()
          }
        }}
      />
    </Box>
  )
}

export default FormAuthor
