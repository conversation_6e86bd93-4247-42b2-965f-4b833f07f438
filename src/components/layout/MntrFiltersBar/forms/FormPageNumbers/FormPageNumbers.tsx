import { t } from '@lingui/core/macro'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import { ObservedFC, observer } from '~/helpers/msts'
import { routerReplace } from '~/helpers/router'

interface IFormPageNumbersProps extends IMntrFormProps {
  closeModal?: () => void
  closePopup?: () => void
  customRoute?: string
  feedId?: string
  disableRedirect?: boolean
}

const FormPageNumbers: ObservedFC<IFormPageNumbersProps> = ({
  appStore: {
    filter,
    dashboards,
    monitoring: { getFeedMap, feedMap },
    router: { makeRouteWithQuery },
  },
  initialValues,
  closeModal,
  closePopup,
  customRoute,
  feedId,
  disableRedirect,
}) => {
  const filterStore = feedId && feedMap.get(feedId) ? feedMap.get(feedId).filter : filter

  const onSubmit: IMntrFormProps['onSubmit'] = (values) => {
    // trim empty values
    const pageNumbers =
      values && values.page_numbers
        ? values.page_numbers
            .split(',')
            .filter((pageNumber: string) => pageNumber !== '')
            .join(',')
        : undefined

    const currRoute = makeRouteWithQuery(
      customRoute,
      undefined,
      {
        page_numbers: pageNumbers,
      },
      {
        resetPage: true,
      },
    )

    filterStore.addFilterItem({
      type: 'page_numbers',
      value: pageNumbers,
      id: pageNumbers,
    })

    if (typeof closeModal === 'function') closeModal()
    if (typeof closePopup === 'function') closePopup()

    if (customRoute) {
      const feed = getFeedMap(dashboards.preview.type || 'feed').get(feedId)
      const url = `${customRoute}?${feed.filter.queryAddParam({ page_numbers: pageNumbers })}`
      if (!disableRedirect) {
        routerReplace(makeRouteWithQuery(), url, { shallow: true })
      }
      feed.loadUrl(url)
      return
    }

    routerReplace(currRoute)
  }

  const formSchema: IFormSchemaItem[] = [
    {
      label: t`Select pages`,
    },
    {
      name: 'page_numbers',
      autoFocus: true,
      autoComplete: 'off',
      placeholder: t`For example, "1,4-6" will filter out 1,4,5,6`,
    },
    {
      actions: () => [
        {
          label: t`Cancel`,
          onClick: closeModal,
          rounded: true,
        },
        {
          label: t`Save`,
          bg: 'secondary',
          type: 'submit',
          rounded: true,
        },
      ],
    },
  ]

  return (
    <MntrForm
      contentPadding={3}
      formGap={1}
      onSubmit={onSubmit}
      initialValues={initialValues}
      schema={formSchema}
      validate={(values) => {
        interface IErrorsProps {
          page_numbers?: string
        }

        const errors: IErrorsProps = {}
        const arrItemRegex = new RegExp(/^\s*\d{1,3}\s*(?:-\s*\d{1,3}\s*)?$/)
        const arr = values.page_numbers?.split(',') || []

        arr.map((item: string) => {
          if (item && !arrItemRegex.test(item)) {
            errors.page_numbers = t`Invalid page number`
          }
        })

        return errors
      }}
    />
  )
}

export default observer(FormPageNumbers)
