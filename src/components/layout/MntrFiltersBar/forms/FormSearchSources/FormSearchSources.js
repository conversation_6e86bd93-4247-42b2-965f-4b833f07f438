import { t } from '@lingui/core/macro'
import { Field, Form, FormSpy } from 'react-final-form'
import { styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'

const StyledForm = styled.form`
  color: red;
`

const FormSearchSources = ({ onSubmit, forceOpen }) => {
  return (
    <Form
      onSubmit={onSubmit}
      render={({ handleSubmit }) => {
        return (
          <StyledForm onSubmit={handleSubmit}>
            <FormSpy
              onChange={(spy) => {
                handleSubmit()
                if (spy.values?.source && typeof forceOpen === 'function') {
                  forceOpen()
                }
              }}
            />
            <Flex flexWrap="nowrap" p={3} pt={0} mt={3}>
              <Box width={1}>
                <Box>
                  <Field
                    name="source"
                    autoComplete="off"
                    autoFocus
                    placeholder={t`Search source or publisher`}
                    component={MntrTextFieldAdapter}
                  />
                </Box>
              </Box>
            </Flex>
          </StyledForm>
        )
      }}
    />
  )
}

export default FormSearchSources
