import get from 'lodash/get'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'
import { Link, routerReplace } from '~/helpers/router'

const FilterNewsSourceIcon = ({
  appStore: {
    monitoring: { getFeedMap },
    dashboards: { preview },
    router: { makeRouteWithQuery },
  },
  customRoute,
  disableRedirect,
  feedId,
  set,
  closePopup,
  withQueryKeysReplace,
  replace,
  replaceKey,
  onClick,
  replaceFromTopic,
  type,
  icon,
}) => {
  return (
    <>
      {disableRedirect && (
        <MntrButton
          onClick={() => {
            if (typeof onClick === 'function') {
              onClick()
            }
            if (typeof closePopup === 'function') {
              closePopup()
            }
            const feed = getFeedMap(get(preview, 'type') || 'feed').get(feedId)
            const url = `${customRoute}?${feed.filter.queryAddParam(set, replace)}`

            feed.loadUrl(url, preview.subtype)
          }}
          bg="flatPrimary"
          icon={<img src={`/static/social/${icon}.png`} alt={icon} width={26} height={26} />}
          mr={1}
        />
      )}

      {!disableRedirect && customRoute && (
        <Link
          href={makeRouteWithQuery(customRoute, undefined, set, {
            ...(withQueryKeysReplace
              ? {
                  multi: [
                    {
                      key: replaceKey || 'topic_monitors',
                      replace: replace + '',
                    },
                  ],
                }
              : {}),
            resetPage: true,
          })}
        >
          <span
            onClick={(event) => {
              if (event.ctrlKey || event.metaKey) {
                return false
              }

              if (typeof onClick === 'function') {
                event.preventDefault()
                onClick()
              }

              const feed = type
                ? getFeedMap(type).get(feedId)
                : getFeedMap(get(preview, 'type') || 'feed').get(feedId)
              const url = `${customRoute}?${feed.filter.queryAddParam(set, replaceFromTopic)}`

              routerReplace(makeRouteWithQuery(), url, { shallow: true })
              feed.loadUrl(url)
            }}
          >
            <MntrButton
              onClick={(event) => {
                if (event.ctrlKey || event.metaKey) {
                  return false
                }

                if (onClick) {
                  onClick()
                }
              }}
              bg="flatPrimary"
              icon={<img src={`/static/social/${icon}.png`} alt={icon} width={26} height={26} />}
              mr={1}
            />
          </span>
        </Link>
      )}

      {!disableRedirect && !customRoute && (
        <Link
          href={makeRouteWithQuery(customRoute, undefined, set, {
            ...(withQueryKeysReplace
              ? {
                  multi: [
                    {
                      key: replaceKey || 'topic_monitors',
                      replace: replace + '',
                    },
                  ],
                }
              : {}),
            resetPage: true,
          })}
        >
          <MntrButton
            onClick={(event) => {
              if (event.ctrlKey || event.metaKey) {
                return false
              }

              if (onClick) {
                onClick()
              }
            }}
            bg="flatPrimary"
            icon={<img src={`/static/social/${icon}.png`} alt={icon} width={26} height={26} />}
            mr={1}
          />
        </Link>
      )}
    </>
  )
}

export default observer(FilterNewsSourceIcon)
