import removeAccents from '~/helpers/removeAccents'

export const filterMultiselectItems = (items, filterValueBySearch) => {
  const normalizedFilterValue = removeAccents(filterValueBySearch || '')
  const isMatch = (text) => removeAccents(text).includes(normalizedFilterValue)
  const filterSubItems = (subItems) => subItems.filter((subItem) => isMatch(subItem.text))

  return items
    .filter((item) =>
      item.subItems?.length > 0 ? filterSubItems(item.subItems).length > 0 : isMatch(item.text),
    )
    .map((item) => ({
      ...item,
      subItems: item.subItems?.length > 0 ? filterSubItems(item.subItems) : undefined,
    }))
}
