export const selectAll = (
  allItems,
  selectedItems,
  filterName,
  filterStore,
  redirectTo,
  feed,
  preview,
  customRoute,
) => {
  if (feed) {
    const url = `${customRoute}?${feed.filter.queryReplaceParam(
      { [filterName]: [] },
      {
        [filterName]: selectedItems,
      },
    )}`

    feed.loadUrl(url, preview.subtype)
  }

  if (redirectTo) {
    const currentUrl = new URL(window.location.href)
    const searchParams = currentUrl.searchParams

    searchParams.set(filterName, selectedItems.join(','))
    currentUrl.search = searchParams.toString()

    filterStore?.replaceFilterMultiArray(allItems, filterName)
    redirectTo(currentUrl.href)
  }
}

export const deselectAll = (filterName, redirectTo, feed, preview, customRoute) => {
  if (feed) {
    const url = `${customRoute}?${feed.filter.queryReplaceParam(
      { [filterName]: [] },
      { [filterName]: [] },
    )}`

    feed.loadUrl(url, preview.subtype)
  }

  if (redirectTo) {
    const currentUrl = new URL(window.location.href)
    const searchParams = currentUrl.searchParams

    searchParams.delete(filterName)
    currentUrl.search = searchParams.toString()

    redirectTo(currentUrl.href)
  }
}
