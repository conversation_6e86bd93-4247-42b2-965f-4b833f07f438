import type { Meta, StoryObj } from '@storybook/react'
import MntrFiltersBar from './MntrFiltersBar'

const meta = {
  title: 'MntrFiltersBar',
  component: MntrFiltersBar,
} satisfies Meta<typeof MntrFiltersBar>

export default meta

type Story = StoryObj<typeof meta>

const FILTERS = [
  'MedialistArticles',
  'AuthorType',
  'AuthorFocusAreas',
  'ContactInformation',
  'AuthorTitle',
  'Date',
  'Source',
  'SourceTVR',
  'ChannelsTVR',
  'LanguageMultiselect',
  'LanguageTVR',
  'Country',
  'ChangeType',
  'NewsroomStatus',
  'CountryMultiselect',
  'Author',
  'Sentiment',
  'Notes',
  'ArticleType',
]

const FILTERS_FEED = [
  'Date',
  'Source',
  'LanguageMultiselect',
  'CountryMultiselect',
  'Author',
  'Sentiment',
  'Notes',
  'ArticleType',
]

const FILTERS_MEDIALIST = [
  'MedialistArticles',
  'AuthorTitle',
  'AuthorType',
  'AuthorFocusAreas',
  'AuthorActivity',
  'Source',
  'ContactInformation',
  'Country',
]

export const MainFeed: Story = {
  args: {
    filters: FILTERS_FEED,
  },
  argTypes: {
    filters: {
      options: FILTERS,
      control: 'check',
    },
  },
}

export const Medialist: Story = {
  args: {
    filters: FILTERS_MEDIALIST,
  },
  argTypes: {
    filters: {
      options: FILTERS,
      control: 'check',
    },
  },
}
