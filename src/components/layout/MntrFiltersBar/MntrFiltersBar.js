import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'
import withModalMedialistArticlesFilter from './modals/withModalMedialistArticlesFilter/withModalMedialistArticlesFilter'
import MenuFilterArticleType from './modules/MenuFilterArticleType'
import MenuFilterAuthor from './modules/MenuFilterAuthor'
import MenuFilterAuthorFocusAreasMultiselect from './modules/MenuFilterAuthorFocusAreasMultiselect'
import MenuFilterAuthorTitle from './modules/MenuFilterAuthorTitle'
import MenuFilterAuthorTypeMultiselect from './modules/MenuFilterAuthorTypeMultiselect'
import MenuFilterChangeType from './modules/MenuFilterChangeType'
import MenuFilterChannelsTVR from './modules/MenuFilterChannelsTVR'
import MenuFilterContactInformationMultiselect from './modules/MenuFilterContactInformationMultiselect'
import MenuFilterCountryMultiselect from './modules/MenuFilterCountryMultiselect'
import MenuFilterDate from './modules/MenuFilterDate'
import MenuFilterLanguageMultiselect from './modules/MenuFilterLanguageMultiselect'
import MenuFilterLanguageTVR from './modules/MenuFilterLanguageTVR'
import MenuFilterNewsroomStatus from './modules/MenuFilterNewsroomStatus'
import MenuFilterNote from './modules/MenuFilterNote'
import MenuFilterOrderBy from './modules/MenuFilterOrderBy'
import MenuFilterPrime from './modules/MenuFilterPrime'
import MenuFilterSentimentMultiselect from './modules/MenuFilterSentimentMultiselect'
import MenuFilterSource from './modules/MenuFilterSource'
import MenuFilterSourceTVR from './modules/MenuFilterSourceTVR'

const StyledFiltersBar = styled.div`
  background: ${({ theme }) => theme.paper.background};
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  padding: ${({ isTablet }) => (isTablet ? '3px' : 0)};
  width: 100%;
  box-sizing: border-box;
`

const getPopupWidth = (button, viewport) => {
  if (button.popupMobileFullWidth && viewport.isMobile) {
    return viewport.width - 48
  }

  return viewport.isMobile ? Math.min(button.popupWidth, 300) : button.popupWidth
}

const MntrFiltersBar = ({
  appStore: { viewport, appLanguage, authors, tvr, account },
  isDashboard,
  customRoute,
  disableRedirect,
  feedId,
  filters,
  withSort,
  backButton,
  zIndex,
  disableTimePicker,
  type,
  isPreview,
}) => {
  const buttonGroup = []

  const sharedProps = {
    customRoute,
    disableRedirect,
    feedId,
    type,
  }

  // Back Button
  if (backButton) {
    buttonGroup.push({
      icon: 'keyboard_backspace',
      label: backButton.text,
      href: backButton.href,
    })
  }

  // Date
  if (filters.indexOf('Date') > -1) {
    buttonGroup.push({
      icon: 'date_range',
      label: viewport.isTablet ? null : <Trans>Date</Trans>,
      popupWidth: 500,
      popupMobileFullWidth: true,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterDate
            disableTimePicker={disableTimePicker}
            closePopup={closePopup}
            forceOpen={forceOpen}
            language={appLanguage}
            initialValues={{
              lower_date: null,
              upper_date: null,
              lower_time: null,
              upper_time: null,
            }}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Articles
  if (filters.indexOf('MedialistArticles') > -1) {
    buttonGroup.push({
      icon: 'view_stream',
      label: viewport.isTablet ? null : <Trans>Articles</Trans>,
      beforeOpen: () => {
        authors.loadArticlesFilterFeed(feedId)
      },
      ...withModalMedialistArticlesFilter(feedId, type, customRoute, isPreview),
      zIndex,
    })
  }

  // Author type
  if (filters.indexOf('AuthorType') > -1) {
    buttonGroup.push({
      icon: 'person',
      label: viewport.isTablet ? null : <Trans>Author type</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterAuthorTypeMultiselect
            closePopup={closePopup}
            forceOpen={forceOpen}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Author focus areas
  if (filters.indexOf('AuthorFocusAreas') > -1) {
    buttonGroup.push({
      icon: 'adjust',
      label: viewport.isTablet ? null : <Trans>Focus area</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterAuthorFocusAreasMultiselect
            closePopup={closePopup}
            forceOpen={forceOpen}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Author contacts
  if (filters.indexOf('ContactInformation') > -1) {
    buttonGroup.push({
      icon: 'contact_phone',
      label: viewport.isTablet ? null : <Trans>Contact</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterContactInformationMultiselect
            closePopup={closePopup}
            forceOpen={forceOpen}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Author title
  if (filters.indexOf('AuthorTitle') > -1) {
    buttonGroup.push({
      icon: 'title',
      label: viewport.isTablet ? null : <Trans>Job position</Trans>,
      popupWidth: 280,
      popup: (closePopup, forceOpen) => (
        <MenuFilterAuthorTitle closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }

  // Author activity
  /*if (filters.indexOf('AuthorActivity') > -1) {
    buttonGroup.push({
      icon: 'border_color',
      label: viewport.isTablet ? null : <Trans>Activity</Trans>,
      popupWidth: 280,
      popup: (closePopup, forceOpen) => (
        <MenuFilterAuthorActivity closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }*/

  // Source
  if (filters.indexOf('Source') > -1) {
    buttonGroup.push({
      icon: 'input',
      label: viewport.isTablet ? null : <Trans>Source</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterSource
            closePopup={closePopup}
            forceOpen={forceOpen}
            {...sharedProps}
            isDashboard={isDashboard}
            isPreview={isPreview}
          />
        )
      },
      zIndex,
    })
  }

  // Source TVR
  if (filters.indexOf('SourceTVR') > -1) {
    buttonGroup.push({
      icon: 'input',
      label: viewport.isTablet ? null : <Trans>Source</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterSourceTVR closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
        )
      },
      zIndex,
    })
  }

  // Source TVR
  if (filters.indexOf('ChannelsTVR') > -1) {
    buttonGroup.push({
      icon: 'format_list_bulleted',
      label: viewport.isTablet ? null : <Trans>Channels</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <ListScrollWrapper>
            <MenuFilterChannelsTVR closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
          </ListScrollWrapper>
        )
      },
      zIndex,
    })
  }

  // Author
  if (filters.indexOf('Author') > -1) {
    buttonGroup.push({
      icon: 'person_search',
      label: viewport.isTablet ? null : <Trans>Author</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => (
        <MenuFilterAuthor closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }

  // Country
  if (filters.indexOf('Country') > -1) {
    buttonGroup.push({
      icon: 'language',
      label: viewport.isTablet ? null : <Trans>Country</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterCountryMultiselect
            closePopup={closePopup}
            forceOpen={forceOpen}
            filterKey={'country'}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Country with multiselect
  if (filters.indexOf('CountryMultiselect') > -1) {
    buttonGroup.push({
      icon: 'language',
      label: viewport.isTablet ? null : <Trans>Country</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterCountryMultiselect
            closePopup={closePopup}
            forceOpen={forceOpen}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Language with multiselect
  if (filters.indexOf('LanguageMultiselect') > -1) {
    buttonGroup.push({
      icon: 'translate',
      label: viewport.isTablet ? null : <Trans>Language</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterLanguageMultiselect
            closePopup={closePopup}
            forceOpen={forceOpen}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Language
  if (filters.indexOf('LanguageTVR') > -1) {
    buttonGroup.push({
      icon: 'language',
      label: viewport.isTablet ? null : <Trans>Language</Trans>,
      popupWidth: 230,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterLanguageTVR
            closePopup={closePopup}
            forceOpen={forceOpen}
            tvr={tvr}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Sentiment
  if (filters.indexOf('Sentiment') > -1) {
    buttonGroup.push({
      icon: 'mood',
      label: viewport.isTablet ? null : <Trans>Sentiment</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => {
        return (
          <MenuFilterSentimentMultiselect
            closePopup={closePopup}
            forceOpen={forceOpen}
            {...sharedProps}
          />
        )
      },
      zIndex,
    })
  }

  // Notes
  if (filters.indexOf('Notes') > -1) {
    buttonGroup.push({
      icon: 'edit_note',
      label: viewport.width < 1100 ? null : <Trans>Notes</Trans>,
      popupWidth: 280,
      popup: (closePopup, forceOpen) => (
        <MenuFilterNote closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }

  // Article Type
  if (filters.indexOf('ArticleType') > -1 && account.enums.isVisibleArticleTypeInFilter) {
    buttonGroup.push({
      icon: 'comment',
      label: viewport.width < 1240 ? null : <Trans>Article Type</Trans>,
      popupWidth: 375,
      popup: (closePopup, forceOpen) => (
        <MenuFilterArticleType closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }

  // Change type
  if (filters.indexOf('ChangeType') > -1) {
    buttonGroup.push({
      icon: 'edit_note',
      label: viewport.isTablet ? null : <Trans>Change Type</Trans>,
      popupWidth: 280,
      popup: (closePopup, forceOpen) => (
        <MenuFilterChangeType closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }

  // Change type
  if (filters.indexOf('NewsroomStatus') > -1) {
    buttonGroup.push({
      icon: 'checklist',
      label: viewport.isTablet ? null : <Trans>Status</Trans>,
      popupWidth: 280,
      popup: (closePopup, forceOpen) => (
        <MenuFilterNewsroomStatus closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }

  // PRIMe
  if (filters.indexOf('Prime') > -1) {
    buttonGroup.push({
      icon: 'target',
      label: viewport.isTablet ? null : <Trans>PRIMe</Trans>,
      popupWidth: 320,
      popup: (closePopup, forceOpen) => (
        <MenuFilterPrime closePopup={closePopup} forceOpen={forceOpen} {...sharedProps} />
      ),
      zIndex,
    })
  }

  return (
    <StyledFiltersBar isTablet={viewport.isTablet}>
      <Flex flexWrap="nowrap">
        <Box width={1}>
          {buttonGroup.map((button) => {
            if (!button.popup) {
              return <MntrButton key={button.icon} bg="transparent" {...button} />
            }
            return (
              <MntrButton
                key={button.icon}
                icon={button.icon}
                hoverable
                bg="flat"
                popupWidth={getPopupWidth(button, viewport)}
                popup={button.popup}
                label={button.label}
                zIndex={zIndex}
              />
            )
          })}
        </Box>
        {withSort && (
          <Box>
            <MntrButton
              icon={'sort'}
              hoverable
              bg="flat"
              popup={(closePopup, forceOpen) => {
                return (
                  <MenuFilterOrderBy
                    closePopup={closePopup}
                    forceOpen={forceOpen}
                    customRoute={customRoute}
                    disableRedirect={disableRedirect}
                    feedId={feedId}
                  />
                )
              }}
              label={viewport.isTablet ? null : <Trans>Sort</Trans>}
              zIndex={zIndex}
            />
          </Box>
        )}
      </Flex>
    </StyledFiltersBar>
  )
}

export default observer(MntrFiltersBar)
