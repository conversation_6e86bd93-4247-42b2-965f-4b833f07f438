import { cloneElement, ReactElement, ReactNode } from 'react'
import { css, styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'

export interface IToolbarAction {
  icon: string
  iconColor?: string
  bg?: string
  tooltip?: string
  onClick?: () => void
}

export interface IMntrPaperToolbarProps {
  actions?: IToolbarAction[]
  bg?: string
  flat?: boolean
  icon?: string | ReactNode
  isLoading?: boolean
  onClick?: () => void
  rightTitle?: ReactNode
  title?: ReactNode
  uppercase?: boolean
}

const StyledPaperToolbar = styled(Flex)<{ bg?: string; flat?: boolean }>`
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  background: ${({ theme, bg = 'primary', flat }) => {
    const key = bg as keyof typeof theme.colors
    return flat ? theme.colors[key] : theme.buttons[bg as keyof typeof theme.buttons]
  }};
  color: ${({ theme, bg = 'primary' }) => {
    const colorKey = `${bg}Color` as keyof typeof theme.buttons
    const textColor = theme.buttons[colorKey] as keyof typeof theme.colors
    return theme.colors[textColor] ?? theme.colors.black
  }};
  height: 54px;
  min-height: 54px;
  font-size: 18px;
  line-height: 26px;
  overflow: hidden;
  width: 100%;

  ${({ bg, flat }) =>
    bg !== 'flat' &&
    !flat &&
    css`
      box-shadow: inset 0 0 13px 1px rgba(0, 0, 0, 0.03);
    `}

  ${({ onClick }) =>
    onClick &&
    css`
      cursor: pointer;
    `}
`

const StyledTitle = styled(Box)<{ uppercase?: boolean }>`
  text-overflow: ellipsis;
  white-space: nowrap;
  transform: translateZ(0);

  ${({ uppercase }) =>
    uppercase &&
    css`
      text-transform: uppercase;
      font-size: 75%;
      letter-spacing: 0.6px;
    `}
`

const StyledTitleWrapper = styled(Flex)`
  &,
  ${StyledTitle} {
    overflow: hidden;
  }
`

const MntrPaperToolbar = ({
  actions = [],
  bg = 'flat',
  flat,
  icon,
  isLoading,
  onClick,
  rightTitle,
  title,
  uppercase,
}: IMntrPaperToolbarProps) => {
  return (
    <StyledPaperToolbar bg={bg} flat={flat} onClick={onClick}>
      <StyledTitleWrapper centerY px={1}>
        {icon && (
          <Flex px={1} centerY>
            {isLoading ? (
              <MntrCircularProgress size={26} />
            ) : typeof icon === 'string' ? (
              <Icon size={26}>{icon}</Icon>
            ) : (
              cloneElement(icon as ReactElement<{ size?: number }>, { size: 26 })
            )}
          </Flex>
        )}
        <StyledTitle px={1} fontSize={[1, 3]} uppercase={uppercase}>
          {title}
        </StyledTitle>
      </StyledTitleWrapper>
      <Flex px={1}>
        {rightTitle && <Box px={1}>{rightTitle}</Box>}
        {!icon && isLoading ? (
          <Box px={1}>
            <Flex width="32px" centerX>
              <MntrCircularProgress size={22} />
            </Flex>
          </Box>
        ) : (
          actions.map((button, index) => {
            return (
              <Box px={1} key={index}>
                <MntrButton bg="toolbar" {...button} />
              </Box>
            )
          })
        )}
      </Flex>
    </StyledPaperToolbar>
  )
}

export default MntrPaperToolbar
