import type { Meta, StoryObj } from '@storybook/react'
import noopFn from 'lodash/noop'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { Table, TableBody, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import { grp as grpHint } from '~/components/OurChart/hints'
import withModalHelp from '~/helpers/modal/withModalHelp'

const meta = {
  title: 'MntrPaper',
  component: MntrPaper,
} satisfies Meta<typeof MntrPaper>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'content',
    p: 6,
  },
}

export const WithToolbar: Story = {
  render() {
    const menuItems = [
      {
        leftIcon: 'mood',
        bg: 'flat',
        label: 'Zapnout sentiment',
        hoverVariant: 'secondary',
        onClick: noopFn,
      },
      {
        label: 'Typ grafu',
        leftIcon: 'bar_chart',
        hoverVariant: 'secondary',
        subMenuItems: [
          {
            label: 'Typ grafu',
          },
          {
            label: 'Sloupcový',
            leftIcon: 'bar_chart',
            hoverVariant: 'secondary',
            onClick: noopFn,
          },
          {
            label: 'Čárový',
            leftIcon: 'show_chart',
            hoverVariant: 'secondary',
            onClick: noopFn,
          },
          {
            label: 'Plošný',
            leftIcon: 'landscape',
            hoverVariant: 'secondary',
            onClick: noopFn,
          },
        ],
      },
      {},
      {
        label: 'Celá obrazovka',
        leftIcon: 'fullscreen',
        onClick: noopFn,
      },
      {
        label: 'Tisk',
        leftIcon: 'print',
        onClick: noopFn,
      },
      {
        label: 'Uložit',
        leftIcon: 'download',
        onClick: noopFn,
      },
      {},
      {
        label: 'Nápověda',
        leftIcon: 'help',
        hoverVariant: 'tertiary',
        ...withModalHelp({
          title: 'Mediální dopad (GRP)',
          message: grpHint(),
        }),
      },
    ]

    const buttonGroup = [
      {
        icon: 'more_vert',
        bg: 'flat',
        popup: (closePopup: () => void) => {
          // @ts-expect-error TODO refactor MntrMenu to tsx
          return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
        },
      },
    ]

    return (
      <>
        <MntrPaper mb={2}>
          <MntrPaperToolbar title="Počet článků" actions={buttonGroup} />
          content
          <Table>
            <TableBody>
              <Tr>
                <Th>Online</Th>
                <Th>Tisk</Th>
                <Th>Televize</Th>
                <Th>Rozhlas</Th>
                <Th dark>Celkem</Th>
              </Tr>
              <Tr>
                <Td>2 články</Td>
                <Td>3 články</Td>
                <Td>4 články</Td>
                <Td>5 články</Td>
                <Td dark>14 článků</Td>
              </Tr>
            </TableBody>
          </Table>
        </MntrPaper>

        <MntrPaper>
          <MntrPaperToolbar title={'Mediální dopad (GRP)'} actions={buttonGroup} />
          content
        </MntrPaper>
      </>
    )
  },
}
