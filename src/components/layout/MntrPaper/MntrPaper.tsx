import { PropsWithChildren } from 'react'
import { css, styled } from 'styled-components'
import { Box, IStyleProps } from '~/components/misc/Mntr/index'
import parseStyles from '~/components/misc/Mntr/parseStyles'

interface IPaperProps extends IStyleProps {
  safeAreaInsets?: ('top' | 'right' | 'bottom' | 'left')[]
  isSticky?: boolean
}

export const Paper = styled(Box)<IPaperProps>`
  flex-shrink: 0;
  width: 100%;
  border: 1px solid ${({ bg, theme }) => (bg ? 'transparent' : theme.paper.border)};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  overflow: hidden;
  background: ${({ bg, theme }) => (bg ? 'inherit' : theme.paper.background)};

  ${({ safeAreaInsets = [] }) => {
    return safeAreaInsets.map((edge) => {
      return css`
        padding-${edge}: env(safe-area-inset-${edge});
      `
    })
  }}

  ${({ isSticky }) => {
    return (
      isSticky &&
      css`
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        border-top-color: transparent;
      `
    )
  }}

  ${parseStyles}
`

interface MntrPaperProps extends PropsWithChildren, IPaperProps {
  id?: string
}

const MntrPaper = ({
  children,
  isSticky = false,
  id,
  height = '100%',
  ...rest
}: MntrPaperProps) => {
  return (
    <Paper isSticky={isSticky} id={id} height={height} {...rest}>
      {children}
    </Paper>
  )
}

export default MntrPaper
