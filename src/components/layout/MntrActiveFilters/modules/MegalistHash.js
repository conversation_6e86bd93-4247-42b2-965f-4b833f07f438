import get from 'lodash/get'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalMegalist from '~/helpers/modal/withModalMegalist'
import { observer } from '~/helpers/mst'

const MegalistHash = ({
  onDelete,
  feedId,
  getFeedMap,
  dashboards,
  megalist,
  customRoute,
  text,
}) => {
  return (
    <MntrButton
      label={text}
      popupWidth={320}
      icon="input"
      iconBg="#7f5ffb"
      onDelete={onDelete}
      beforeOpen={() => {
        let hash

        if (customRoute) {
          const feed = getFeedMap(get(dashboards, 'preview.type') || 'feed').get(feedId)
          hash = get(feed, 'filter.labels.megalist_hash.value')
        }

        megalist.openFromHash(hash)
      }}
      {...withModalMegalist({ modalTitle: text, showAllCategoryTypes: true })}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(MegalistHash)
