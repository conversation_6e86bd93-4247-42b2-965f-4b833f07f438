import { t } from '@lingui/core/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterNewsroomStatus from '~/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomStatus'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const NewsroomStatus = ({
  value,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  zIndex,
  appStore: { account },
}) => {
  const statusList = account.enums.publishing.blog_post_status.toJSON()
  const status = statusList.find((item) => item.id === parseInt(value))

  return (
    <MntrButton
      label={status?.text || t`Status`}
      popupWidth={320}
      icon="checklist"
      iconBg={status?.color}
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterNewsroomStatus
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(NewsroomStatus)
