import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterLanguageMultiselect from '~/components/layout/MntrFiltersBar/modules/MenuFilterLanguageMultiselect'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const Language = ({
  type,
  onDelete,
  feedId,
  value,
  customRoute,
  disableRedirect,
  zIndex,
  list,
  appStore: { account },
}) => {
  const buttonProps = {}

  // if is only one language in list, then use it's flag
  if (list.length === 1) {
    const mediaLanguageObj = account.enums.media_language.find((item) => item.id === list[0].value)
    if (mediaLanguageObj) {
      buttonProps.flagLanguage = mediaLanguageObj.id
    }
  } else {
    // otherwise use globe icon
    buttonProps.icon = 'translate'
  }
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterLanguageMultiselect
            type={type}
            disableCountry
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
      {...buttonProps}
    />
  )
}

export default observer(Language)
