import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterOrderBy from '~/components/layout/MntrFiltersBar/modules/MenuFilterOrderBy'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const OrderBy = ({ type, onDelete, feedId, customRoute, disableRedirect, value, zIndex }) => {
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      icon="sort"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterOrderBy
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(OrderBy)
