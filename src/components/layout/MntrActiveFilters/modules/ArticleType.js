import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterArticleType from '~/components/layout/MntrFiltersBar/modules/MenuFilterArticleType'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const ArticleType = ({ type, onDelete, feedId, customRoute, disableRedirect, value, zIndex }) => {
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      icon="article"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterArticleType
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(ArticleType)
