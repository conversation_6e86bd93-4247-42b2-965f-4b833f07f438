import { t } from '@lingui/core/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const Paywalled = ({ onDelete }) => {
  const subject = { id: 'is_paywalled', text: t`Behind paywall`, color: '#13CDF0', icon: 'lock' }

  return (
    <MntrButton
      disabled
      label={subject.text}
      icon={subject.icon}
      iconColor="white"
      iconBg={subject.color}
      onDelete={onDelete}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default Paywalled
