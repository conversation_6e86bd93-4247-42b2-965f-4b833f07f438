import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterAuthor from '~/components/layout/MntrFiltersBar/modules/MenuFilterAuthor'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const Author = ({ filter, type, onDelete, feedId, customRoute, disableRedirect, zIndex }) => {
  return (
    <MntrButton
      label={filter.labels?.author?.text}
      popupWidth={320}
      icon="person"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterAuthor
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Author)
