import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterLanguageTVR from '~/components/layout/MntrFiltersBar/modules/MenuFilterLanguageTVR'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const LanguageTVR = ({
  flagId,
  tvr,
  type,
  onDelete,
  feedId,
  value,
  customRoute,
  disableRedirect,
  zIndex,
}) => {
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      flagLanguage={flagId}
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterLanguageTVR
            type={type}
            tvr={tvr}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(LanguageTVR)
