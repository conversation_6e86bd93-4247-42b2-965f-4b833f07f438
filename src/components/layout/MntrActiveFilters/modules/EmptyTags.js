import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterTags from '~/components/layout/MntrFiltersBar/modules/MenuFilterTags'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const EmptyTags = ({
  type,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  tags,
  filter,
  redirectTo,
  getFeedMap,
  feedMap,
  zIndex,
  permissions,
  canCreateTag = permissions.archive_feed.can_write || permissions.monitoring_feed.can_write,
  filterKey = 'tags',
  createTag,
  label = <Trans>Article Tags</Trans>,
}) => {
  return (
    <MntrButton
      label={label}
      popupWidth={320}
      icon="label"
      iconBg="primary"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterTags
            canCreateTag={canCreateTag}
            type={type}
            feedMap={feedMap}
            tags={tags.list}
            tagsModel={tags}
            closePopup={closePopup}
            filter={filter}
            forceOpen={forceOpen}
            customRoute={customRoute}
            redirectTo={redirectTo}
            disableRedirect={disableRedirect}
            feedId={feedId}
            getFeedMap={getFeedMap}
            filterKey={filterKey}
            createTag={createTag}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(EmptyTags)
