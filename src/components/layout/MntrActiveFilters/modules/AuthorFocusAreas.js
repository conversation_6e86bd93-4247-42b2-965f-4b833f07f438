import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterAuthorFocusAreasMultiselect from '~/components/layout/MntrFiltersBar/modules/MenuFilterAuthorFocusAreasMultiselect'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const AuthorFocusAreas = ({
  type,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  zIndex,
  value,
}) => {
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      icon="adjust"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterAuthorFocusAreasMultiselect
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(AuthorFocusAreas)
