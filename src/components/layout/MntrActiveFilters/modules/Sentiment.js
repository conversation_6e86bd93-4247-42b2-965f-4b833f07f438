import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterSentimentMultiselect from '~/components/layout/MntrFiltersBar/modules/MenuFilterSentimentMultiselect'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import getSentimentIcon from '~/helpers/getSentimentIcon'
import { observer } from '~/helpers/mst'

const Sentiment = ({
  type,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  value,
  zIndex,
  list,
  appStore: { account },
}) => {
  let iconBg = null
  let sentimentId = null

  // if is only one sentiment in list, then use it's color and icon
  if (list.length === 1) {
    const sentimentObj = account.enums.sentiment.find((item) => item.id === parseInt(list[0].value))
    if (sentimentObj) {
      iconBg = sentimentObj.color
      sentimentId = sentimentObj.id
    }
  }
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      icon={getSentimentIcon(sentimentId)}
      iconBg={iconBg}
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterSentimentMultiselect
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Sentiment)
