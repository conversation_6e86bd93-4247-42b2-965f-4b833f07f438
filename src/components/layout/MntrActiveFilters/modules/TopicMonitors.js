import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterTopicMonitors from '~/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors'
import { Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'

const TopicMonitors = ({
  appStore: {
    topics,
    monitoring: { getFeedMap },
    router: { makeRouteWithQuery },
  },
  item,
  value,
  feedId,
  type,
  filter,
  customRoute,
  disableRedirect,
  pathname,
  routerReplace,
  zIndex,
  enableTopicMonitorsCompare,
  enableTopicMonitorsAdd,
  disablePreviewMulti,
  isPreview,
  withQuery<PERSON>eysReplace,
  buttonGroupZIndex,
}) => {
  return (
    <Flex gap={1} flexWrap="wrap">
      {value.map((itemTopic) => {
        const topicItem = topics.list.find(
          (entry) => parseInt(entry.id) === parseInt(itemTopic.value),
        ) || {
          data: {},
        }

        const onDelete = () => {
          filter.removeFilterMulti(itemTopic.value)

          if (customRoute) {
            const feed = getFeedMap(type).get(feedId)

            const url = `${customRoute}?${feed.filter.queryRemoveParam({
              [item]: itemTopic.value,
            })}`
            if (!disableRedirect) {
              routerReplace(makeRouteWithQuery(), url, { shallow: true })
            }
            feed.loadUrl(url)
            return false
          }

          const query = filter.urlWithParam({
            [item]: topics.removeTopicFromFilter(itemTopic.value),
          })

          routerPush(`${pathname}${query ? `?${query}` : ''}`)
        }

        const ignoreTopicMonitors = value.map((item) => parseInt(item.value))
        if (!topicItem.data.name) return null

        return (
          <MntrButton
            key={topicItem.id}
            label={cutString(topicItem.data.name, 20)}
            topicId={topicItem.id}
            popupWidth={320}
            icon="edit"
            disabled={topics.list.length === ignoreTopicMonitors.length}
            disablePreviewMulti={disablePreviewMulti}
            onDelete={onDelete}
            popup={(closePopup) => {
              return (
                <MenuFilterTopicMonitors
                  type={type}
                  closePopup={closePopup}
                  customRoute={customRoute}
                  disableRedirect={disableRedirect}
                  feedId={feedId}
                  filter={filter}
                  getFeedMap={getFeedMap}
                  enableTopicMonitorsCompare={enableTopicMonitorsCompare}
                  enableTopicMonitorsAdd={enableTopicMonitorsAdd}
                  topicId={itemTopic.value}
                  disablePreviewMulti={disablePreviewMulti}
                  isPreview={isPreview}
                  withQueryKeysReplace={withQueryKeysReplace}
                  buttonGroupZIndex={buttonGroupZIndex}
                />
              )
            }}
            zIndex={zIndex}
            {...activeFilterButtonSharedProps}
          />
        )
      })}
    </Flex>
  )
}

export default observer(TopicMonitors)
