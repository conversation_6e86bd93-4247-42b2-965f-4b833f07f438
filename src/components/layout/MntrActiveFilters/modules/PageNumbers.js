import { t } from '@lingui/core/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import withModalPageNumbers from '~/components/layout/MntrFiltersBar/modals/withModalPageNumbers'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import categoryTypes from '~/constants/categoryTypes'
import { observer } from '~/helpers/mst'

const PageNumbers = ({
  account,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  value,
  zIndex,
}) => {
  const iconBg = account.enums.categoryTypeIsActive.find(
    (item) => item.id === categoryTypes.CATEGORY_TYPE_OFFLINE_MEDIA,
  ).color
  return (
    <MntrButton
      label={`${t`Pages`}: ${value}`}
      popupWidth={320}
      icon="chrome_reader_mode"
      iconBg={iconBg}
      onDelete={onDelete}
      {...withModalPageNumbers({
        feedId,
        customRoute,
        disableRedirect,
        initialValues: {
          page_numbers: value,
        },
      })}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(PageNumbers)
