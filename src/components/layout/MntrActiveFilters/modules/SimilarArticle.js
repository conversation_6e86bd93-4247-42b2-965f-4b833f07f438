import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'

const SimilarArticle = ({ onDelete, value }) => {
  return (
    <MntrButton
      label={
        <>
          <Trans>Similar to</Trans>: {cutString(value, 15)}
        </>
      }
      popupWidth={320}
      icon="compare"
      iconBg="#0e7b90"
      disabled
      onDelete={onDelete}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(SimilarArticle)
