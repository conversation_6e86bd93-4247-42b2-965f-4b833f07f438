import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterSource from '~/components/layout/MntrFiltersBar/modules/MenuFilterSource'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const Source = ({
  account,
  type,
  value,
  customRoute,
  disableRedirect,
  feedId,
  onDelete,
  zIndex,
  isPreview,
}) => {
  const categoryTypes = account.enums.categoryTypeIsFeedFilter
  const subject = categoryTypes.find((entry) => parseInt(entry.id) === parseInt(value)) || {}

  return (
    <MntrButton
      label={subject.text}
      popupWidth={320}
      icon={subject.icon}
      iconBg={subject.color}
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterSource
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            isPreview={isPreview}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Source)
