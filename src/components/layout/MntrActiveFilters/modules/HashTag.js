import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'

const HashTag = ({ onDelete, value }) => {
  return (
    <MntrButton
      label={cutString(value, 20, true)}
      popupWidth={320}
      icon="tag"
      iconBg="social"
      disabled
      onDelete={onDelete}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(HashTag)
