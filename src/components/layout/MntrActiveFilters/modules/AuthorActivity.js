import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterAuthorActivity from '~/components/layout/MntrFiltersBar/modules/MenuFilterAuthorActivity'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const AuthorActivity = ({
  filter,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  zIndex,
  type,
}) => {
  return (
    <MntrButton
      label={filter.labels?.author_activity?.text}
      popupWidth={320}
      icon="border_color"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterAuthorActivity
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            type={type}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(AuthorActivity)
