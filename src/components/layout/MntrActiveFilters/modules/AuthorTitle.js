import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterAuthorTitle from '~/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const AuthorTitle = ({ filter, onDelete, feedId, customRoute, disableRedirect, zIndex, type }) => {
  return (
    <MntrButton
      label={filter.labels?.title_query?.text}
      popupWidth={320}
      icon="title"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterAuthorTitle
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            type={type}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(AuthorTitle)
