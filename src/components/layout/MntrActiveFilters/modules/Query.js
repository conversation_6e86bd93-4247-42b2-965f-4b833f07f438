import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const Query = ({
  appStore: {
    searchBar: { setIsMobileSearchVisible },
  },
  onDelete,
  value,
}) => {
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      icon="search"
      onDelete={onDelete}
      onClick={() => {
        setIsMobileSearchVisible(true)
        setTimeout(() => {
          const input = document.querySelector('.search-input input')

          if (input) {
            input.focus()
          }
        }, 50)
      }}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Query)
