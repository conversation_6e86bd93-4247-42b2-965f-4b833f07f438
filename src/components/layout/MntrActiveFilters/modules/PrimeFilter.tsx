import { t } from '@lingui/core/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterPrime from '~/components/layout/MntrFiltersBar/modules/MenuFilterPrime'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

interface IPrimeFilterProps {
  onDelete: () => void
  customRoute?: string
  disableRedirect?: boolean
  feedId?: string
  type: string
  forceOpen?: () => void
  zIndex?: number
}

const PrimeFilter = ({
  onDelete,
  feedId = 'feed',
  customRoute,
  disableRedirect,
  zIndex,
}: IPrimeFilterProps) => {
  return (
    <MntrButton
      label={t`PRIMe`}
      popupWidth={320}
      icon="target"
      onDelete={onDelete}
      // @ts-expect-error MntrButton refactor
      popup={(closePopup: () => void, forceOpen: () => void) => {
        return (
          <MenuFilterPrime
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default PrimeFilter
