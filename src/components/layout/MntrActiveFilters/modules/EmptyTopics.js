import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterTopicMonitors from '~/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const EmptyTopics = ({
  type,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  filter,
  getFeedMap,
  zIndex,
  buttonGroupZIndex,
  isPreview,
}) => {
  return (
    <MntrButton
      label={<Trans>Topics</Trans>}
      popupWidth={320}
      icon="book"
      iconBg="primary"
      onDelete={onDelete}
      popup={(closePopup) => {
        return (
          <MenuFilterTopicMonitors
            activeSubmenu="articles"
            type={type}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            filter={filter}
            getFeedMap={getFeedMap}
            buttonGroupZIndex={buttonGroupZIndex}
            isPreview={isPreview}
            closePopup={closePopup}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(EmptyTopics)
