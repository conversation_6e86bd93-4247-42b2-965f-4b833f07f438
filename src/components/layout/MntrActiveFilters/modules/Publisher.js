import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterSource from '~/components/layout/MntrFiltersBar/modules/MenuFilterSource'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'

const Publisher = ({ type, onDelete, feedId, customRoute, disableRedirect, value, zIndex }) => {
  return (
    <MntrButton
      label={
        <>
          <Trans>Publisher</Trans> {cutString(value, 15, true)}
        </>
      }
      popupWidth={320}
      icon="bookmark_border"
      iconBg="#0e7b90"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterSource
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Publisher)
