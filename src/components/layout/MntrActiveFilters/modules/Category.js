import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterSource from '~/components/layout/MntrFiltersBar/modules/MenuFilterSource'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'

const Category = ({ type, onDelete, feedId, customRoute, disableRedirect, value, zIndex }) => {
  return (
    <MntrButton
      label={cutString(value, 20, true)}
      popupWidth={320}
      icon="input"
      iconBg="#fb9b2a"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterSource
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Category)
