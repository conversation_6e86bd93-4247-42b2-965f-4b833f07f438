import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterChannelsTVR from '~/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const Channel = ({
  appStore: {
    account: {
      enums: { crisis_communication },
    },
  },
  type,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  value,
  zIndex,
}) => {
  const channels = crisis_communication.channels || []

  const subject = channels.find((entry) => parseInt(entry.id) === parseInt(value)) || {}

  return (
    <MntrButton
      label={subject.name}
      popupWidth={320}
      icon="input"
      image={subject.logo}
      iconBg="#fb9b2a"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <ListScrollWrapper>
            <MenuFilterChannelsTVR
              type={type}
              closePopup={closePopup}
              forceOpen={forceOpen}
              customRoute={customRoute}
              disableRedirect={disableRedirect}
              feedId={feedId}
            />
          </ListScrollWrapper>
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Channel)
