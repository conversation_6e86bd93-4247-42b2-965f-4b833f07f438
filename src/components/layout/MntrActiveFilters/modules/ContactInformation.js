import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterContactInformationMultiselect from '~/components/layout/MntrFiltersBar/modules/MenuFilterContactInformationMultiselect'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const ContactInformation = ({
  type,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  zIndex,
  value,
}) => {
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      icon="contact_phone"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterContactInformationMultiselect
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(ContactInformation)
