import { t } from '@lingui/core/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import withModalMedialistArticlesFilter from '~/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/withModalMedialistArticlesFilter'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const MedialistArticles = ({
  appStore: { authors },
  onDelete,
  zIndex,
  feedId,
  type,
  customRoute,
  isPreview,
}) => {
  return (
    <MntrButton
      label={t`Articles`}
      popupWidth={320}
      icon="view_stream"
      onDelete={onDelete}
      zIndex={zIndex}
      beforeOpen={() => {
        authors.loadArticlesFilterFeed(feedId)
      }}
      {...withModalMedialistArticlesFilter(feedId, type, customRoute, isPreview)}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(MedialistArticles)
