import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterChangeType from '~/components/layout/MntrFiltersBar/modules/MenuFilterChangeType'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const ChangeType = ({ filter, onDelete, feedId, customRoute, disableRedirect, zIndex }) => {
  return (
    <MntrButton
      label={filter.changeType?.text}
      popupWidth={320}
      icon="edit_note"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterChangeType
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(ChangeType)
