import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterTopicMonitors from '~/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { MAX_SELECTED_TOPICS } from '~/constants'
import { observer } from '~/helpers/mst'

const AddTopic = ({
  buttonGroupZIndex,
  type,
  feedId,
  customRoute,
  disableRedirect,
  topics,
  filter,
  getFeedMap,
  value,
  label,
  icon,
  zIndex,
  enableTopicMonitorsCompare,
  isPreview,
}) => {
  if (!value) {
    return <span />
  }
  const ignoreTopicMonitors = value.map((item) => item.value)

  if (
    topics.list.length === ignoreTopicMonitors.length ||
    ignoreTopicMonitors.length >= MAX_SELECTED_TOPICS
  ) {
    return <span />
  }

  return (
    <MntrButton
      label={label}
      popupWidth={320}
      iconBg="#bfb5d6"
      icon={icon}
      popup={(closePopup) => {
        return (
          <MenuFilterTopicMonitors
            buttonGroupZIndex={buttonGroupZIndex}
            type={type}
            closePopup={closePopup}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            filter={filter}
            getFeedMap={getFeedMap}
            enableTopicMonitorsCompare={enableTopicMonitorsCompare}
            isPreview={isPreview}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(AddTopic)
