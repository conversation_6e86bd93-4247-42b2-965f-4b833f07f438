import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterSourceTVR from '~/components/layout/MntrFiltersBar/modules/MenuFilterSourceTVR'
import MediaIcon from '~/components/misc/MediaIcon/MediaIcon'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const SourceTVR = ({
  appStore: {
    account: {
      enums: { crisis_communication },
    },
  },
  type,
  value,
  customRoute,
  disableRedirect,
  feedId,
  onDelete,
  zIndex,
}) => {
  const mediaTypes = crisis_communication.media_types

  if (!mediaTypes) {
    return <span />
  }
  const subject = mediaTypes.find((entry) => parseInt(entry.id) === parseInt(value)) || {}

  return (
    <MntrButton
      label={subject.text}
      popupWidth={320}
      icon={
        <MediaIcon
          avatarStyle={{
            position: 'absolute',
            left: 0,
            top: 0,
            width: 26,
            height: 26,
          }}
          color={subject.color}
          size={26}
          id={subject.id}
          type={'tvr'}
        />
      }
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterSourceTVR
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(SourceTVR)
