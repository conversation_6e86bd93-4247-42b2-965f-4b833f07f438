import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterAuthorTypeMultiselect from '~/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTypeMultiselect'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const AuthorType = ({ type, onDelete, feedId, customRoute, disableRedirect, zIndex, value }) => {
  return (
    <MntrButton
      label={value}
      popupWidth={320}
      icon="person"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterAuthorTypeMultiselect
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(AuthorType)
