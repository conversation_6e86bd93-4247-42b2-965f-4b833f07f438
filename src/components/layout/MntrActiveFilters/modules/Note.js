import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterNote from '~/components/layout/MntrFiltersBar/modules/MenuFilterNote'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'

const Note = ({ type, onDelete, feedId, customRoute, disableRedirect, value, zIndex }) => {
  const label = () => {
    if (value === '') {
      return <Trans>With note</Trans>
    }

    if (value === '%00') {
      return <Trans>Without note</Trans>
    }

    return cutString(value, 30, true)
  }

  return (
    <MntrButton
      label={label()}
      popupWidth={320}
      icon="edit_note"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterNote
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            initialValues={{ note: value !== '%00' ? value : '' }}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Note)
