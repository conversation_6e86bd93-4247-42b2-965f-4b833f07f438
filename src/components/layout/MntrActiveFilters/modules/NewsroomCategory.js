import { t } from '@lingui/core/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterNewsroomCategory from '~/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomCategory'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const NewsroomCategory = ({
  value,
  onDelete,
  feedId,
  customRoute,
  disableRedirect,
  zIndex,
  appStore: { newsroom },
}) => {
  const categoriesList = newsroom.selected.categories
  const category = categoriesList.find((item) => item.id === parseInt(value))

  return (
    <MntrButton
      label={category?.name || t`Category`}
      popupWidth={320}
      icon="label"
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterNewsroomCategory
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(NewsroomCategory)
