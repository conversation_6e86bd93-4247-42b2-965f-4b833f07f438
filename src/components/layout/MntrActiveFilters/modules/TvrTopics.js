import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import TvrNavigation from '~/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const TvrTopics = () => {
  return (
    <MntrButton
      label={<Trans>Topics</Trans>}
      popupWidth={320}
      icon="book"
      iconBg="primary"
      popup={() => <TvrNavigation onlyTopics />}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(TvrTopics)
