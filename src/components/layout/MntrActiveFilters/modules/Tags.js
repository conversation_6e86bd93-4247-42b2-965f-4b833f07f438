import { Trans } from '@lingui/react/macro'
import color from 'color'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterTags from '~/components/layout/MntrFiltersBar/modules/MenuFilterTags'
import { Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'
import { routerReplace } from '~/helpers/router'

const Tags = ({
  appStore: {
    filter,
    dashboards,
    monitoring: { getFeedMap, feedMap },
    router: { makeRouteWithQuery },
    account: {
      workspace: { permissions },
    },
  },
  value,
  feedId,
  type,
  tags,
  customRoute,
  redirectTo,
  disableRedirect,
  zIndex,
  filterKey = 'tags',
  canCreateTag = permissions.archive_feed.can_write || permissions.monitoring_feed.can_write,
  createTag,
  onDelete,
}) => {
  return (
    <Flex gap={1}>
      {value.map((itemTag) => {
        const tag = tags.list.find((entry) => parseInt(entry.id) === parseInt(itemTag.value)) || {
          id: 0,
        }

        const ignoreTags = value.map((item) => item.value)

        return (
          <MntrButton
            key={tag.id}
            label={tag.label ? cutString(tag.label, 20) : <Trans>Without tags</Trans>}
            popupWidth={320}
            icon={tag.label ? 'label' : 'label_off'}
            iconColor={tag.label ? color(tag.color).darken(0.5).toString() : 'black'}
            iconColorHover={'#fff'}
            iconBg={tag.label ? tag.color : 'chipTagBg'}
            disabled={tags.list.length === ignoreTags.length}
            onDelete={() => onDelete(tag, itemTag, filterKey, tags)}
            popup={(closePopup, forceOpen) => {
              return (
                <MenuFilterTags
                  type={type}
                  feedMap={feedMap}
                  tags={tags.list.filter((item) => ignoreTags.indexOf(item.id) === -1)}
                  tagsModel={tags}
                  closePopup={closePopup}
                  forceOpen={forceOpen}
                  customRoute={customRoute}
                  disableRedirect={disableRedirect}
                  feedId={feedId}
                  selectedId={tag.id}
                  redirectTo={redirectTo}
                  filter={filter}
                  getFeedMap={getFeedMap}
                  dashboards={dashboards}
                  makeRouteWithQuery={makeRouteWithQuery}
                  routerReplace={routerReplace}
                  canCreateTag={canCreateTag}
                  filterKey={filterKey}
                  createTag={createTag}
                />
              )
            }}
            zIndex={zIndex}
            {...activeFilterButtonSharedProps}
          />
        )
      })}
    </Flex>
  )
}

export default observer(Tags)
