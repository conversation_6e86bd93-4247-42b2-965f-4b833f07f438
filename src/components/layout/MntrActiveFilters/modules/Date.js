import { useRouter } from 'next/router'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterDate from '~/components/layout/MntrFiltersBar/modules/MenuFilterDate'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const Date = ({
  filter,
  type,
  feedId,
  customRoute,
  disableRedirect,
  getFeedMap,
  makeRouteWithQuery,
  routerReplace,
  routerPush,
  initialValues,
  zIndex,
  disableTimePicker,
}) => {
  const {
    query: { lower_date, upper_date },
  } = useRouter()
  const isDefaultDate = !lower_date && !upper_date

  const onDelete = () => {
    if (customRoute) {
      const feed = getFeedMap(type).get(feedId)
      const url = `${customRoute}?${feed.filter.queryRemoveParam({
        lower_date: undefined,
        upper_date: undefined,
        lower_time: undefined,
        upper_time: undefined,
      })}`
      if (!disableRedirect) {
        routerReplace(makeRouteWithQuery(), url, { shallow: true })
      }
      feed.loadUrl(url)
      return false
    }

    routerPush(
      makeRouteWithQuery(
        undefined,
        ['lower_date', 'upper_date', 'lower_time', 'upper_time'],
        undefined,
        { inverse: true },
      ),
    )
  }
  if (!filter.dateType.visible) {
    return <span />
  }
  return (
    <MntrButton
      label={filter.dateType.title}
      popupWidth={320}
      icon="date_range"
      onDelete={!isDefaultDate ? onDelete : null}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterDate
            type={type}
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            initialValues={initialValues}
            disableTimePicker={disableTimePicker}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(Date)
