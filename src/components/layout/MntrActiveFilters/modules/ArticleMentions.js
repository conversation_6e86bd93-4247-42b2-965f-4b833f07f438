import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'

const ArticleMentions = ({ onDelete, value }) => {
  return (
    <MntrButton
      label={
        <>
          <Trans>Article mentions</Trans>: {cutString(value, 15)}
        </>
      }
      popupWidth={320}
      icon="comment"
      iconBg="social"
      iconOffsetX={-1}
      iconOffsetY={1}
      disabled
      onDelete={onDelete}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(ArticleMentions)
