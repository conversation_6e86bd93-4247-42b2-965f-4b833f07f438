import { Trans } from '@lingui/react/macro'
import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import cutString from '~/helpers/cutString'
import { observer } from '~/helpers/mst'

const SimilarArticle = ({ onDelete, value }) => {
  return (
    <MntrButton
      label={
        <>
          <Trans>Discussion thread</Trans>: {cutString(value, 15)}
        </>
      }
      popupWidth={320}
      icon="article"
      iconBg="social"
      disabled
      onDelete={onDelete}
      {...activeFilterButtonSharedProps}
    />
  )
}

export default observer(SimilarArticle)
