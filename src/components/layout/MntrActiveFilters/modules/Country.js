import activeFilterButtonSharedProps from '~/components/layout/MntrActiveFilters/constants/activeFilterButtonSharedProps'
import MenuFilterCountryMultiselect from '~/components/layout/MntrFiltersBar/modules/MenuFilterCountryMultiselect'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const Country = ({
  type,
  onDelete,
  feedId,
  value,
  customRoute,
  disableRedirect,
  zIndex,
  list,
  appStore: { account },
  filterKey,
}) => {
  const buttonProps = {}

  // if is only one country in list, then use it's flag
  if (list.length === 1) {
    const mediaCountryObj = account.enums.media_country.find(
      (item) => item.id === parseInt(list[0].value),
    )
    if (mediaCountryObj) {
      buttonProps.flagCountry = mediaCountryObj.code
    }
  } else {
    // otherwise use globe icon
    buttonProps.icon = 'language'
  }

  return (
    <MntrButton
      label={value}
      popupWidth={320}
      onDelete={onDelete}
      popup={(closePopup, forceOpen) => {
        return (
          <MenuFilterCountryMultiselect
            type={type}
            disableLanguage
            closePopup={closePopup}
            forceOpen={forceOpen}
            customRoute={customRoute}
            disableRedirect={disableRedirect}
            feedId={feedId}
            filterKey={filterKey}
          />
        )
      }}
      zIndex={zIndex}
      {...activeFilterButtonSharedProps}
      {...buttonProps}
    />
  )
}

export default observer(Country)
