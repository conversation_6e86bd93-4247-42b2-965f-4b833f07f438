import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import React from 'react'
import Paywalled from '~/components/layout/MntrActiveFilters/modules/Paywalled'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { MEDIALIST_ARTICLES_PREFIX, PRIME_FILTER_PREFIX } from '~/constants'
import { availableFilters } from '~/constants/availableFilters'
import { getMultiText } from '~/helpers/getMultiText'
import isEmpty from '~/helpers/isEmpty'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'
import AddTopic from './modules/AddTopic'
import ArticleMentions from './modules/ArticleMentions'
import ArticleType from './modules/ArticleType'
import Author from './modules/Author'
import AuthorActivity from './modules/AuthorActivity'
import AuthorFocusAreas from './modules/AuthorFocusAreas'
import AuthorTitle from './modules/AuthorTitle'
import AuthorType from './modules/AuthorType'
import Category from './modules/Category'
import ChangeType from './modules/ChangeType'
import Channel from './modules/Channel'
import ContactInformation from './modules/ContactInformation'
import Country from './modules/Country'
import Date from './modules/Date'
import DiscussionThread from './modules/DiscussionThread'
import EmptyTags from './modules/EmptyTags'
import EmptyTopics from './modules/EmptyTopics'
import HashTag from './modules/HashTag'
import Language from './modules/Language'
import LanguageTVR from './modules/LanguageTVR'
import MedialistArticles from './modules/MedialistArticles'
import MegalistHash from './modules/MegalistHash'
import NewsSource from './modules/NewsSource'
import NewsroomCategory from './modules/NewsroomCategory'
import NewsroomStatus from './modules/NewsroomStatus'
import Note from './modules/Note'
import OrderBy from './modules/OrderBy'
import PageNumbers from './modules/PageNumbers'
import PrimeFilter from './modules/PrimeFilter'
import Publisher from './modules/Publisher'
import Query from './modules/Query'
import Sentiment from './modules/Sentiment'
import SimilarArticle from './modules/SimilarArticle'
import SocialNetworkProfile from './modules/SocialNetworkProfile'
import Source from './modules/Source'
import SourceTVR from './modules/SourceTVR'
import Tags from './modules/Tags'
import TopicMonitors from './modules/TopicMonitors'
import TvrTopics from './modules/TvrTopics'

const formatDate = (input) => {
  const arr = input.split('-')
  return `${arr[2]}.${arr[1]}.${arr[0]}`
}

const handleDeleteWithPrefix = ({
  prefix,
  filter,
  customRoute,
  feedId,
  type,
  getFeedMap,
  pathname,
  routerPush,
}) => {
  return () => {
    const filterData = filter.labels
    const filters = Object.keys(filterData).reduce((obj, key) => {
      if (!key.startsWith(prefix)) {
        return {
          ...obj,
          [key]: Array.isArray(filterData[key])
            ? filterData[key].map((v) => v.value).join(',')
            : filterData[key].value,
        }
      }
      return obj
    }, {})

    if (customRoute) {
      const feed = getFeedMap(type).get(feedId)
      const url = `${customRoute}?${feed.filter.urlWithParam(filters)}`
      return feed.loadUrl(url)
    }

    routerPush({
      pathname,
      query: filters,
    })
  }
}

const MntrActiveFilters = ({
  appStore: {
    account,
    account: { workspace },
    authors,
    dashboards,
    filter: appStoreFilter,
    megalist,
    monitoring: { isVisibleActiveFilters, feedMap, getFeedMap, tags },
    router: { makeRouteWithQuery, redirectTo },
    topics,
    tvr,
    viewport,
  },
  config: {
    disableActiveFilterAuthor,
    allowFilters,
    allowAuthorTags,
    customRoute,
    disableFilters,
    disablePreviewMulti,
    feedId,
    isPreview,
    allowNewsroomCategory,
    allowNewsroomStatus,
    disableRedirect,
    disableReset,
    withTags,
    disableTimePicker,
    withTopics,
    enableTopicMonitorsAdd,
    enableTopicMonitorsCompare,
    tvrTopics,
    type,
    visibleEmptyAuthorTags,
    visibleEmptyTags,
    visibleEmptyTopics,
    withQueryKeysReplace,
    zIndex,
    buttonGroupZIndex,
  },
}) => {
  const { asPath, pathname, query } = useRouter()

  const isVisibleFilter = (selectedFilterType, filterType) => {
    let useFilters = availableFilters

    if (allowFilters.length > 0) {
      useFilters = allowFilters
    }

    if (disableFilters.length > 0) {
      useFilters = []
      availableFilters.map((item) => {
        if (!disableFilters.includes(item)) {
          useFilters.push(item)
        }
      })
    }

    return selectedFilterType === filterType && useFilters.includes(filterType)
  }

  if (!isVisibleActiveFilters) {
    return <span />
  }

  let filter = feedMap.get(feedId) ? feedMap.get(feedId).filter : appStoreFilter
  let isEmptyFilter = isEmpty(filter.data)
  let isEmptyTopics =
    filter.data && (isEmpty(filter.data.topic_monitors) || isEmpty(filter.labels?.topic_monitors))
  let isEmptyTags = filter.data && (isEmpty(filter.data.tags) || isEmpty(filter.labels?.tags))
  const hasAuthorTags = !!filter.labels?.author_tags?.length > 0

  if (Object.keys(filter.data || {}).length === 1 && filter.data.query && viewport.isMobile) {
    isEmptyFilter = false
  }

  if (Object.keys(filter.data || {}).length === 1 && filter.data.query && !viewport.isMobile) {
    isEmptyFilter = true
  }

  if (Object.keys(filter.data || {}).length === 1 && filter.data.topic_monitors && !withTopics) {
    isEmptyFilter = true
  }

  let map

  if (feedId) {
    map = getFeedMap(type)
    filter = map.get(feedId) && map.get(feedId).filter
    const topicMonitors = filter?.labels?.topic_monitors || []
    isEmptyTopics = topicMonitors.length === 0
    isEmptyTags = !filter?.labels?.tags || !filter.data?.tags
    isEmptyFilter = !filter || (filter && filter.labels === undefined)
  }

  const isVisibleGroupOfFilters = (groupPrefix) => {
    const filters = availableFilters.filter((item) => item.startsWith(groupPrefix))

    return filters.some((item) => filter.labels?.[item])
  }

  const isVisibleMedialistArticlesFilter = () => isVisibleGroupOfFilters(MEDIALIST_ARTICLES_PREFIX)
  const isVisiblePrimeFilter = () => isVisibleGroupOfFilters(PRIME_FILTER_PREFIX)

  if (viewport.width > 1024 && isEmptyFilter) {
    return null
  }

  const isVisibleEmptyAuthorTags =
    (!hasAuthorTags && allowAuthorTags && viewport.width < 1025) ||
    (!hasAuthorTags && visibleEmptyAuthorTags)

  const sharedActiveItemProps = {
    customRoute,
    disableRedirect,
    feedId,
    zIndex,
  }

  return (
    <div className="active-filters">
      <Box
        mx={!viewport.isTablet ? 1 : 0}
        display="block"
        flexGrow="1"
        flexWrap="wrap"
        position="relative"
        ml="2px"
      >
        <Box
          className="ios-scroll"
          display="block"
          flexGrow="1"
          width={viewport.width < 1025 ? '100%' : 'auto'}
          overflow={viewport.isMobile ? 'auto' : 'visible'}
        >
          <Flex gap={1} flexWrap={['nowrap', 'wrap']} pl={1} pb={[1, 0]}>
            {!disableReset && !disableRedirect && !customRoute && !isEmptyFilter ? (
              <Box onClick={() => filter.reset()}>
                <MntrButton
                  href={asPath.split('?')[0]}
                  icon="close"
                  bg="error"
                  size="small"
                  tooltip={<Trans>Reset filters</Trans>}
                />
              </Box>
            ) : null}

            {!isEmpty(filter.labels) && !disableReset && customRoute && disableRedirect && (
              <MntrButton
                onClick={(e) => {
                  e.preventDefault()
                  const feed = map.get(feedId)
                  feed.load()
                  filter.reset()
                  feed.resetFilters()
                }}
                icon="close"
                bg="error"
                size="small"
                tooltip={<Trans>Reset filters</Trans>}
              />
            )}

            {(!isEmptyTopics || !isEmptyTags) &&
              !disableReset &&
              !disableRedirect &&
              customRoute && (
                <MntrButton
                  onClick={(e) => {
                    e.preventDefault()
                    const feed = map.get(feedId)
                    filter.reset()
                    routerReplace(makeRouteWithQuery(), customRoute, { shallow: true })
                    feed.resetFilters()
                  }}
                  icon="close"
                  bg="error"
                  size="small"
                  tooltip={<Trans>Reset filters</Trans>}
                />
              )}

            {isVisiblePrimeFilter() && (
              <PrimeFilter
                filter={filter}
                onDelete={handleDeleteWithPrefix({
                  prefix: PRIME_FILTER_PREFIX,
                  filter,
                  customRoute,
                  feedId,
                  type,
                  getFeedMap,
                  pathname,
                  routerPush,
                })}
                type={type}
                {...sharedActiveItemProps}
              />
            )}
            {isVisibleMedialistArticlesFilter() && (
              <MedialistArticles
                isPreview={isPreview}
                feedId={feedId}
                filter={filter}
                customRoute={customRoute}
                onDelete={handleDeleteWithPrefix({
                  prefix: MEDIALIST_ARTICLES_PREFIX,
                  filter,
                  customRoute,
                  feedId,
                  type,
                  getFeedMap,
                  pathname,
                  routerPush,
                })}
                type={type}
              />
            )}

            {((!isEmptyFilter && !disableRedirect) ||
              (!isEmpty(filter.labels) && disableRedirect)) &&
              availableFilters.map((item, index) => {
                const onDelete = () => {
                  filter.remove(item)

                  if (customRoute) {
                    const feed = getFeedMap(type).get(feedId)
                    const url = `${customRoute}?${feed.filter.queryRemoveParam({
                      [item]: undefined,
                    })}`
                    if (!disableRedirect) {
                      routerReplace(makeRouteWithQuery(), url, { shallow: true })
                    }
                    feed.loadUrl(url)
                    return false
                  }

                  const query = filter.urlWithParam({ [item]: undefined })

                  routerPush(`${asPath.split('?')[0]}${query ? `?${query}` : ''}`)
                }

                const onDeleteTag = (tag, itemTag, filterKey, tags) => {
                  filter.removeFilterMulti(itemTag.value, filterKey)

                  if (customRoute) {
                    const feed = getFeedMap(type).get(feedId)

                    let url = tags.urlWithoutTag(tag, customRoute)
                    if (isPreview) {
                      url = `${customRoute}?${feed.filter.queryRemoveParam({
                        [item]: undefined,
                      })}`
                    } else {
                      if (!disableRedirect) {
                        routerReplace(makeRouteWithQuery(), url, { shallow: true })
                      }
                    }
                    feed.loadUrl(url)
                    return false
                  }

                  routerPush(tags.urlWithoutTag(tag))
                }

                const value = filter.labels && filter.labels[item]?.value

                return (
                  <React.Fragment key={index + item.toString}>
                    {isVisibleFilter(item, 'query') && value && viewport.isMobile && (
                      <Query
                        onDelete={onDelete}
                        value={value}
                        routerPush={routerPush}
                        filter={filter}
                      />
                    )}
                    {isVisibleFilter(item, 'category_type') && value && (
                      <Source
                        account={account}
                        item={item}
                        onDelete={onDelete}
                        value={value}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'is_paywalled') && value && (
                      <Paywalled item={item} onDelete={onDelete} {...sharedActiveItemProps} />
                    )}
                    {isVisibleFilter(item, 'page_numbers') && value && (
                      <PageNumbers
                        account={account}
                        item={item}
                        onDelete={onDelete}
                        value={value}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'tvr_category_type') && value && (
                      <SourceTVR
                        item={item}
                        onDelete={onDelete}
                        value={value}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'news_source_category') && value && (
                      <Category
                        item={item}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        sharedActiveItemProps
                      />
                    )}
                    {isVisibleFilter(item, 'hashtag') && value && (
                      <HashTag
                        item={item}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        sharedActiveItemProps
                      />
                    )}
                    {isVisibleFilter(item, 'social_network_profile') && value && (
                      <SocialNetworkProfile
                        item={item}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        sharedActiveItemProps
                      />
                    )}
                    {isVisibleFilter(item, 'similar_article') && value && (
                      <SimilarArticle
                        item={item}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'article_mentions') && value && (
                      <ArticleMentions
                        item={item}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'discussion_thread') && value && (
                      <DiscussionThread
                        item={item}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'order_by') && value && (
                      <OrderBy
                        item={item}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'megalist_hash') &&
                      value &&
                      !workspace.is_global_mediaboard && (
                        <MegalistHash
                          item={item}
                          onDelete={onDelete}
                          getFeedMap={getFeedMap}
                          dashboards={dashboards}
                          megalist={megalist}
                          text={filter.labels[item]?.text}
                          type={type}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'topic_monitors') &&
                      filter.labels &&
                      filter.labels[item] &&
                      withTopics && (
                        <TopicMonitors
                          item={item}
                          value={filter.labels[item]}
                          type={type}
                          pathname={pathname}
                          filter={filter}
                          query={query}
                          withQueryKeysReplace={withQueryKeysReplace}
                          routerReplace={routerReplace}
                          buttonGroupZIndex={buttonGroupZIndex}
                          disablePreviewMulti={disablePreviewMulti}
                          isPreview={isPreview}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'topic_monitors') &&
                      !filter.labels?.topic_monitors &&
                      filter.data &&
                      filter.data[item] &&
                      withTopics && (
                        <TopicMonitors
                          item={item}
                          value={filter.data[item].split(',').map((item) => {
                            return { value: item }
                          })}
                          type={type}
                          pathname={pathname}
                          filter={filter}
                          query={query}
                          enableTopicMonitorsCompare={enableTopicMonitorsCompare}
                          enableTopicMonitorsAdd={enableTopicMonitorsAdd}
                          disablePreviewMulti={disablePreviewMulti}
                          isPreview={isPreview}
                          withQueryKeysReplace={withQueryKeysReplace}
                          buttonGroupZIndex={buttonGroupZIndex}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'tags') && filter.labels && filter.labels[item] && (
                      <Tags
                        tags={tags}
                        item={item}
                        onDelete={onDeleteTag}
                        value={filter.labels[item]}
                        type={type}
                        redirectTo={redirectTo}
                        filter={filter}
                        query={query}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'author_tags') &&
                      filter.labels &&
                      filter.labels[item] && (
                        <Tags
                          tags={authors.tags}
                          item={item}
                          onDelete={onDeleteTag}
                          value={filter.labels[item]}
                          type={type}
                          redirectTo={redirectTo}
                          filter={filter}
                          query={query}
                          filterKey="author_tags"
                          canCreateTag={workspace?.permissions.authors_database.can_write}
                          createTag={authors.tags.create}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'lower_date') &&
                      filter.data &&
                      filter.data?.lower_date && (
                        <Date
                          disableTimePicker={disableTimePicker}
                          item={item}
                          filter={filter}
                          getFeedMap={getFeedMap}
                          makeRouteWithQuery={makeRouteWithQuery}
                          routerReplace={routerReplace}
                          routerPush={routerPush}
                          value={value}
                          type={type}
                          initialValues={{
                            lower_date: formatDate(filter.data?.lower_date),
                            upper_date: filter.data?.upper_date
                              ? formatDate(filter.data?.upper_date)
                              : formatDate(filter.data?.lower_date),
                            lower_time: filter.data?.lower_time,
                            upper_time: filter.data?.upper_time,
                          }}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'lower_date') &&
                      filter.data &&
                      !filter.data?.lower_date &&
                      filter.labels &&
                      filter.labels?.lower_date && (
                        <Date
                          disableTimePicker={disableTimePicker}
                          item={item}
                          filter={filter}
                          getFeedMap={getFeedMap}
                          makeRouteWithQuery={makeRouteWithQuery}
                          routerReplace={routerReplace}
                          routerPush={routerPush}
                          value={value}
                          type={type}
                          initialValues={{}}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'author') && value && !disableActiveFilterAuthor && (
                      <Author
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={value}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'status') && filter.data?.status && (
                      <NewsroomStatus
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={filter.data.status}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'category') && filter.data?.category && (
                      <NewsroomCategory
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={filter.data.category}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'news_source_country') &&
                      filter.labels?.news_source_country && (
                        <Country
                          item={item}
                          filter={filter}
                          onDelete={onDelete}
                          value={getMultiText(filter.labels.news_source_country)}
                          list={filter.labels.news_source_country}
                          type={type}
                          {...sharedActiveItemProps}
                        />
                      )}

                    {isVisibleFilter(item, 'language') && filter.labels?.language && (
                      <Language
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={getMultiText(filter.labels.language)}
                        list={filter.labels.language}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'tvr_language') && value && (
                      <LanguageTVR
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        flagId={filter.labels[item]?.value}
                        type={type}
                        tvr={tvr}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'country') && filter.labels?.country && (
                      <Country
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={getMultiText(filter.labels.country)}
                        list={filter.labels.country}
                        filterKey="country"
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'sentiment') && filter.labels?.sentiment && (
                      <Sentiment
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={getMultiText(filter.labels.sentiment)}
                        list={filter.labels.sentiment}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'note') && value !== undefined && (
                      <Note
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={filter.labels && filter.labels[item]?.text}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'article_type') && filter.labels?.article_type && (
                      <ArticleType
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={getMultiText(filter.labels.article_type)}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'publisher') && value && (
                      <Publisher
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'news_source') && value && (
                      <NewsSource
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={filter.labels[item]?.text}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'tvr_channel') && filter.data?.tvr_channel && (
                      <Channel
                        item={item}
                        filter={filter}
                        value={filter.data?.tvr_channel}
                        onDelete={onDelete}
                        type={type}
                        tvr={tvr}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'tvr_channel') &&
                      filter.labels &&
                      filter.labels?.tvr_channel &&
                      !filter.data?.tvr_channel && (
                        <Channel
                          item={item}
                          filter={filter}
                          value={filter.labels?.tvr_channel.value}
                          onDelete={onDelete}
                          type={type}
                          tvr={tvr}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'author_type') && filter.labels?.author_type && (
                      <AuthorType
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        value={getMultiText(filter.labels.author_type)}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'author_focus_areas') &&
                      filter.labels?.author_focus_areas && (
                        <AuthorFocusAreas
                          item={item}
                          filter={filter}
                          onDelete={onDelete}
                          value={getMultiText(filter.labels.author_focus_areas)}
                          type={type}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'contact_information') &&
                      filter.labels?.contact_information && (
                        <ContactInformation
                          item={item}
                          filter={filter}
                          onDelete={onDelete}
                          value={getMultiText(filter.labels.contact_information)}
                          type={type}
                          {...sharedActiveItemProps}
                        />
                      )}
                    {isVisibleFilter(item, 'author_activity') && filter.labels?.author_activity && (
                      <AuthorActivity
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'title_query') && filter.labels?.title_query && (
                      <AuthorTitle
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                    {isVisibleFilter(item, 'change_type') && filter.data?.change_type && (
                      <ChangeType
                        item={item}
                        filter={filter}
                        onDelete={onDelete}
                        type={type}
                        {...sharedActiveItemProps}
                      />
                    )}
                  </React.Fragment>
                )
              })}

            {tvrTopics && viewport.isTablet && <TvrTopics />}

            {((withTopics &&
              isEmptyTopics &&
              (viewport.width < 1025 || isPreview || disableActiveFilterAuthor)) ||
              (isEmptyTopics && !isEmptyFilter && visibleEmptyTopics) ||
              (isEmptyTopics && type !== 'tvr' && viewport.width < 1025 && withTopics)) && (
              <EmptyTopics
                disableRedirect={disableRedirect}
                filter={filter}
                customRoute={customRoute}
                feedId={feedId}
                type={type}
                getFeedMap={getFeedMap}
                zIndex={zIndex}
                buttonGroupZIndex={buttonGroupZIndex}
                isPreview={isPreview}
              />
            )}

            {((withTags &&
              isEmptyTags &&
              (viewport.width < 1025 || isPreview || disableActiveFilterAuthor)) ||
              (isEmptyTags && !isEmptyFilter && visibleEmptyTags) ||
              (isEmptyTags && type !== 'tvr' && viewport.width < 1025 && withTags)) && (
              <EmptyTags
                permissions={workspace?.permissions}
                disableRedirect={disableRedirect}
                filter={filter}
                tags={tags}
                customRoute={customRoute}
                redirectTo={redirectTo}
                feedId={feedId}
                type={type}
                getFeedMap={getFeedMap}
                feedMap={feedMap}
                zIndex={zIndex}
              />
            )}

            {isVisibleEmptyAuthorTags && (
              <EmptyTags
                permissions={workspace?.permissions}
                disableRedirect={disableRedirect}
                filter={filter}
                tags={authors.tags}
                customRoute={customRoute}
                redirectTo={redirectTo}
                feedId={feedId}
                type={type}
                getFeedMap={getFeedMap}
                feedMap={feedMap}
                zIndex={zIndex}
                filterKey="author_tags"
                canCreateTag={workspace?.permissions.authors_database.can_write}
                createTag={authors.tags.create}
                label={<Trans>Author Tags</Trans>}
              />
            )}

            {!filter.data?.status && allowNewsroomStatus && (
              <NewsroomStatus filter={filter} value={filter.data.status} type={type} />
            )}

            {!filter.data?.category && allowNewsroomCategory && (
              <NewsroomCategory filter={filter} value={filter.data.category} type={type} />
            )}

            {(enableTopicMonitorsAdd || enableTopicMonitorsCompare) && (
              <AddTopic
                buttonGroupZIndex={buttonGroupZIndex}
                disableRedirect={disableRedirect}
                enableTopicMonitorsCompare={enableTopicMonitorsCompare}
                label={
                  enableTopicMonitorsCompare ? (
                    <Trans>Compare Topic</Trans>
                  ) : (
                    <Trans>Add Topic</Trans>
                  )
                }
                icon={enableTopicMonitorsCompare ? 'compare_arrows' : 'add'}
                filter={filter}
                topics={topics}
                customRoute={customRoute}
                feedId={feedId}
                type={type}
                getFeedMap={getFeedMap}
                value={filter.labels?.topic_monitors}
                zIndex={zIndex}
                isPreview={isPreview}
              />
            )}
          </Flex>
        </Box>
      </Box>
    </div>
  )
}

export default observer(MntrActiveFilters)
