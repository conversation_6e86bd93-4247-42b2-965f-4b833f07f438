import { INotificationItem } from '~/store/models/NotificationStore'
import NotificationItem from './NotificationItem'

interface INotificationsProps {
  list: INotificationItem[]
  pushErrorToTracking?: (message: string) => void
}

const Notifications = ({ list, pushErrorToTracking }: INotificationsProps) => {
  return list.map((item, index) => {
    return (
      <NotificationItem
        isOpen={index === 0}
        item={item}
        key={`snackbar-notification-${item.id}-${index}`}
        pushErrorToTracking={pushErrorToTracking}
      />
    )
  })
}

export default Notifications
