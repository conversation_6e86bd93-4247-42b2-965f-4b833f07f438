import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { getSnapshot, ObservedFC, observer } from '~/helpers/msts'
import { INotificationItem } from '~/store/models/NotificationStore'
import Notifications from './Notifications'

const NotificationsWithObserver: ObservedFC = ({ appStore: { notification } }) => {
  const { list } = notification

  const enhancedList = list.map((item: INotificationItem) => ({
    ...getSnapshot(item),
    remove: () => item.remove(),
    setVisible: (visible: boolean) => item.setVisible(visible),
  }))

  return (
    <Notifications
      list={enhancedList}
      pushErrorToTracking={(msg: string) => {
        pushEvent(events.ERROR_MESSAGE_SHOWN, { error_text: msg })
      }}
    />
  )
}

export default observer(NotificationsWithObserver)
