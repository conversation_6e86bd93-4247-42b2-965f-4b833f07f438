'use client'

import { useEffect, useState } from 'react'
import { styled } from 'styled-components'
import { Paper } from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, IStyleProps } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrSnackbar from '~/components/misc/MntrSnackbar/MntrSnackbar'
import { INotificationItem } from '~/store/models/NotificationStore'

interface INotificationPaperProps extends IStyleProps {
  bg: string
}

const isValidSnackbarBg = (bg: string): bg is 'error' | 'info' | 'success' => {
  return ['error', 'info', 'success'].includes(bg)
}

const NotificationPaper = styled(Paper)<INotificationPaperProps>`
  background: ${({ theme, bg }) => {
    if (isValidSnackbarBg(bg)) {
      return theme.snackbar[bg]
    }
    return theme.snackbar.info
  }};
  color: ${({ theme }) => theme.snackbar.color};
  box-shadow: ${({ theme }) => theme.snackbar.boxShadow};
`
interface INotificationItemProps {
  actions?: []
  anchorOrigin?: {
    vertical: 'top'
    horizontal: 'center'
  }
  icon?: string
  isOpen: boolean
  item: INotificationItem
  pushErrorToTracking?: (message: string) => void | null
  width?: number
}

const NotificationItem = ({
  actions = [],
  anchorOrigin = { vertical: 'top', horizontal: 'center' },
  icon,
  isOpen = true,
  item: { id, autoHideDuration, message, type, remove, setVisible },
  pushErrorToTracking,
  width = 320,
}: INotificationItemProps) => {
  const [open, setOpen] = useState(isOpen)

  useEffect(() => {
    setOpen(isOpen)
    if (type === 'error' && pushErrorToTracking) pushErrorToTracking(message)
  }, [id, isOpen, message, type])

  const handleClose = (_event: React.SyntheticEvent | Event, reason: string) => {
    if (reason === 'clickaway') {
      return
    }

    setOpen(false)
    // Mark as not visible immediately so new notifications can be added
    if (setVisible) setVisible(false)

    setTimeout(function () {
      if (remove) remove()
    }, 150)
  }

  return (
    <MntrSnackbar
      anchorOrigin={anchorOrigin}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      open={open}
    >
      <div data-e2e="notification">
        <NotificationPaper bg={type}>
          <Flex p={1} justifyContent="space-between" width={`${width}px`}>
            <Flex>
              {icon && (
                <Flex m={1}>
                  <Icon>{icon}</Icon>
                </Flex>
              )}
              <Box m={1}>{message}</Box>
            </Flex>
            <Box m="1px">
              {actions &&
                (actions.length ? (
                  actions.map(({ onClick, ...action }, index) => {
                    return (
                      <MntrButton
                        key={`notification-item-action-${index}`}
                        bg="flat"
                        icon="close"
                        iconColor="currentColor"
                        {...action}
                        onClick={() => {
                          onClick(handleClose)
                        }}
                      />
                    )
                  })
                ) : (
                  <MntrButton
                    bg="flat"
                    icon="close"
                    iconColor="currentColor"
                    onClick={handleClose}
                  />
                ))}
            </Box>
          </Flex>
        </NotificationPaper>
      </div>
    </MntrSnackbar>
  )
}

export default NotificationItem
