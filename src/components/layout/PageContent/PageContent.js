import { styled } from 'styled-components'

export const PageContentOffset = styled.div`
  margin-left: 0;

  @media (min-width: ${({ theme }) => theme.breakpoints[1]}) {
    margin-left: 48px;
  }
`

const PageContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.space[2]}px;
  transition: none;
  opacity: ${(props) => (props.contentLoading ? 0 : 1)};
  position: relative;
  margin-left: ${({ visibleSidebarPrimary, visibleSidebar }) => {
    if (visibleSidebar) {
      return 254
    } else if (visibleSidebarPrimary) {
      return 48
    }

    return 0
  }}px;
  width: auto;
  top: 0px;
`

export default PageContent
