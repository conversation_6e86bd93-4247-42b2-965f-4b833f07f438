import type { Edge } from '@atlaskit/pragmatic-drag-and-drop-hitbox/types'
import { autoUpdate, useFloating } from '@floating-ui/react'
import { PropsWithChildren } from 'react'
import { css, styled } from 'styled-components'
import ConditionalWrapper from '~/components/misc/Mntr/ConditionalWrapper'

type Orientation = 'horizontal' | 'vertical'

const edgeToOrientationMap: Record<Edge, Orientation> = {
  top: 'horizontal',
  bottom: 'horizontal',
  left: 'vertical',
  right: 'vertical',
}

const strokeSize = 2
const terminalSize = 8
const offsetToAlignTerminalWithLine = (strokeSize - terminalSize) / 2
export const indicatorColor = '#155dfc' // --color-blue-600

/**
 * This is a styled-components port of a Tailwind port[^1] of
 * `@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/box`.
 *
 * If we ever implement a DnD tree, we should revisit the React package[^2], as
 * it supports trees and features like indentation that we've implemented on our
 * own here.
 *
 * [^1]:
 * https://github.com/alexreardon/pdnd-react-tailwind/blob/main/src/drop-indicator.tsx
 * [^2]:
 * https://atlassian.design/components/pragmatic-drag-and-drop/optional-packages/react-drop-indicator
 */
const DropIndicatorContainer = styled.div<{ edge: Edge; gap: string; indent: string }>`
  --indicator-color: ${indicatorColor};

  position: absolute;
  z-index: 99; /* Above sticky actions bar */
  background-color: var(--indicator-color);
  pointer-events: none;

  --line-thickness: ${strokeSize}px;
  --line-offset: ${(props) => `calc(-0.5 * (${props.gap} + ${strokeSize}px))`};
  --terminal-size: ${terminalSize}px;
  --terminal-radius: ${terminalSize / 2}px;
  --negative-terminal-size: -${terminalSize}px;
  --offset-terminal: ${offsetToAlignTerminalWithLine}px;

  ${(props) => {
    const orientation = edgeToOrientationMap[props.edge]
    return orientation === 'horizontal'
      ? css`
          height: var(--line-thickness);
          right: ${props.indent};
          left: calc(var(--terminal-radius) + ${props.indent});
          &::before {
            left: var(--negative-terminal-size);
          }
        `
      : css`
          width: var(--line-thickness);
          top: var(--terminal-radius);
          bottom: 0;
          &::before {
            top: var(--negative-terminal-size);
          }
        `
  }}

  ${(props) => {
    switch (props.edge) {
      case 'top':
        return css`
          top: var(--line-offset);
          &::before {
            top: var(--offset-terminal);
          }
        `
      case 'right':
        return css`
          right: var(--line-offset);
          &::before {
            right: var(--offset-terminal);
          }
        `
      case 'bottom':
        return css`
          bottom: var(--line-offset);
          &::before {
            bottom: var(--offset-terminal);
          }
        `
      case 'left':
        return css`
          left: var(--line-offset);
          &::before {
            left: var(--offset-terminal);
          }
        `
      default:
        return ''
    }
  }}

  &::before {
    content: '';
    box-sizing: border-box;
    position: absolute;
    border: var(--line-thickness) solid var(--indicator-color);
    width: var(--terminal-size);
    height: var(--terminal-size);
    border-radius: 50%;
  }
`

const FloatingDropIndicatorWrapper = ({
  children,
  edge,
  referenceElement,
}: PropsWithChildren<{
  edge: Edge
  referenceElement: HTMLElement
}>) => {
  const { refs, floatingStyles } = useFloating({
    elements: {
      reference: referenceElement,
    },
    placement: edge,
    strategy: 'fixed',
    whileElementsMounted: autoUpdate,
  })

  const referenceElementRect = referenceElement.getBoundingClientRect()
  const orientationToSizeMap = { horizontal: 'width', vertical: 'height' }
  const sizeProp = orientationToSizeMap[edgeToOrientationMap[edge]] as 'width' | 'height'

  return (
    <div
      ref={refs.setFloating}
      style={{
        [sizeProp]: referenceElementRect[sizeProp],
        ...floatingStyles,
      }}
    >
      {children}
    </div>
  )
}

export function DropIndicator({
  edge,
  gap,
  indent,
  referenceElement,
}: {
  edge: Edge
  gap: string
  indent?: string
  referenceElement?: HTMLElement | null
}) {
  // When a reference element is passed, we handle positioning of the indicator
  // with Floating UI. It's well suited to "break" the floating element out of a
  // clipping ancestor.
  // - https://floating-ui.com/docs/useFloating#strategy
  //
  // Otherwise, we fall back to the simpler absolute positioning implemented
  // within DropIndicatorContainer.
  return (
    <ConditionalWrapper
      condition={!!referenceElement}
      wrapper={(children) => {
        return (
          <FloatingDropIndicatorWrapper edge={edge} referenceElement={referenceElement!}>
            {children}
          </FloatingDropIndicatorWrapper>
        )
      }}
    >
      <DropIndicatorContainer edge={edge} gap={gap} indent={indent || '0px'} />
    </ConditionalWrapper>
  )
}
