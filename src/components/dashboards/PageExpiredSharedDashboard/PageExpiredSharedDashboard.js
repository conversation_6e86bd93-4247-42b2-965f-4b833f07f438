import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, Text } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const Wrapper = styled(Flex)`
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 150px;
`

const Decription = styled(Flex)`
  margin-top: 35px;
  flex-direction: column;
  font-size: 16px;
  text-align: center;
`

const PageExpiredSharedDashboard = ({
  appStore: {
    dashboards: { sharedDashboard },
  },
}) => {
  return (
    <>
      <Wrapper color="black">
        <Icon size="80" color="primary">
          error
        </Icon>
        <Text my={2} fontSize="24px">
          {sharedDashboard.name}
        </Text>
        <Decription>
          <Text fontWeight="bold">
            <Trans>Access to this dashboard has expired.</Trans>
          </Text>
          <Text>
            <Trans>For renewal, contact account admin.</Trans>
          </Text>
        </Decription>
      </Wrapper>
    </>
  )
}

export default observer(PageExpiredSharedDashboard)
