import { styled } from 'styled-components'
import Inspector from '~/components/monitoring/Inspector/Inspector'
import { observer } from '~/helpers/mst'

const Wrapper = styled.div`
  position: fixed;
  top: ${(props) => (props.isMobile ? 0 : 54)}px;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ theme }) => theme.paper.background};
  z-index: ${(props) => (props.isMobile ? 1701 : 1000)};
`

const DashboardInspectorWrapper = ({ appStore: { viewport }, isDashboard = true, feedId }) => {
  return (
    <Wrapper isMobile={viewport.isMobile}>
      <Inspector isDashboard={isDashboard} feedId={feedId} />
    </Wrapper>
  )
}

export default observer(DashboardInspectorWrapper)
