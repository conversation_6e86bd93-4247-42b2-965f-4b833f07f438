import DashboardInspectorWrapper from '~/components/dashboards/DashboardInspectorWrapper/DashboardInspectorWrapper'
import { Flex, Heading, Text } from '~/components/misc/Mntr'
import parseDate from '~/helpers/date/parse'
import { getTitleWithDateFromTo } from '~/helpers/getTitleWithDateFromTo'
import { observer } from '~/helpers/mst'
import AddModuleDrawer from './DashboardsGrid/AddModuleDrawer/AddModuleDrawer'
import DashboardsGrid from './DashboardsGrid/DashboardsGrid'

const SharedDashboardContent = ({
  appStore: {
    monitoring,
    dashboards: { dragLock, sharedDashboard, isLoading },
    viewport,
    megalist,
  },
}) => {
  if (!sharedDashboard) return null

  const subtitle = getTitleWithDateFromTo(
    parseDate(sharedDashboard.filter.lower_date, 'yyyy-MM-dd'),
    parseDate(sharedDashboard.filter.upper_date, 'yyyy-MM-dd'),
  )

  return (
    <>
      {!isLoading && (
        <div>
          <Flex flexDirection="column" color="black" mt={3} mb={1} centerY>
            <Heading as="h1" fontSize={4} textAlign="center">
              {sharedDashboard.name}
            </Heading>
            <Text color="lightGrey">{subtitle}</Text>
          </Flex>
          <>
            {sharedDashboard.data && (
              <DashboardsGrid
                disabledWidgets
                id={sharedDashboard.data && sharedDashboard.data.id}
                layout={sharedDashboard.data.layout}
                selected={sharedDashboard.data}
                items={sharedDashboard.data.items && sharedDashboard.data.items.length}
                onLayoutChange={(layout) => sharedDashboard.data.setLayout(layout)}
                viewport={viewport}
                dragLock={dragLock}
              />
            )}

            {!megalist.isOpen && <AddModuleDrawer />}
            {monitoring.activeFeedMapItem && <DashboardInspectorWrapper />}
          </>{' '}
        </div>
      )}
    </>
  )
}

export default observer(SharedDashboardContent)
