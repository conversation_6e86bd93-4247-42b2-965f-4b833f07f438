import { Trans } from '@lingui/react/macro'
import AddWidgetButton from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetButton/AddWidgetButton'
import { Box, Grid } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

const EmptyDashboard = ({ onSelect }) => {
  return (
    <Box p={5}>
      <Box mb={1} mt={3} mx={1}>
        <MntrMenuHeading label={<Trans>Monitoring</Trans>} />
      </Box>
      <Grid gap={3} gridTemplateColumns="repeat(auto-fill, minmax(250px, 1fr))">
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('analytics')}
            label={<Trans>Analytics</Trans>}
            icon="show_chart"
          />
        </Box>
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('stats')}
            label={<Trans>Statistics</Trans>}
            icon="show_chart"
          />
        </Box>
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('feed')}
            label={<Trans>Articles</Trans>}
            icon="reorder"
          />
        </Box>
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('tvr')}
            label={<Trans>Crisis communication</Trans>}
            icon="campaign"
          />
        </Box>
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('medialist')}
            label={<Trans>Medialist</Trans>}
            icon="people"
          />
        </Box>
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('social_engagement')}
            label={<Trans>Social Engagement</Trans>}
            icon="share"
          />
        </Box>
      </Grid>
      <Box mb={1} mt={3} mx={1}>
        <MntrMenuHeading label={<Trans>Other</Trans>} />
      </Box>
      <Grid gap={3} gridTemplateColumns="repeat(auto-fill, minmax(250px, 1fr))">
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('note')}
            label={<Trans>Note</Trans>}
            icon="note"
          />
        </Box>
        <Box>
          <AddWidgetButton
            onClick={() => onSelect('image')}
            label={<Trans>Image</Trans>}
            icon="image"
          />
        </Box>
      </Grid>
    </Box>
  )
}

export default EmptyDashboard
