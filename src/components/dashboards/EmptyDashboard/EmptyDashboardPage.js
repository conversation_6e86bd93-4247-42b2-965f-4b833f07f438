import { Trans } from '@lingui/react/macro'
import AddWidgetButton from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetButton/AddWidgetButton'
import { Box, Grid } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

const EmptyDashboardPage = ({ onSelect }) => {
  return (
    <Box pb={7}>
      <Box mb={1} mt={3} mx={1}>
        <MntrMenuHeading label={<Trans>Monitoring</Trans>} />
      </Box>
      <Grid gap={3} gridTemplateColumns="repeat(auto-fill, minmax(300px, 1fr))">
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('analytics')}
            label={<Trans>Analytics</Trans>}
            icon="show_chart"
          />
        </Box>
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('stats')}
            label={<Trans>Statistics</Trans>}
            icon="show_chart"
          />
        </Box>
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('feed')}
            label={<Trans>Articles</Trans>}
            icon="reorder"
          />
        </Box>
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('tvr')}
            label={<Trans>Crisis communication</Trans>}
            icon="campaign"
          />
        </Box>
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('medialist')}
            label={<Trans>Medialist</Trans>}
            icon="people"
          />
        </Box>
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('social_engagement')}
            label={<Trans>Social Engagement</Trans>}
            icon="share"
          />
        </Box>
      </Grid>
      <Box mb={1} mt={3} mx={1}>
        <MntrMenuHeading label={<Trans>Other</Trans>} />
      </Box>
      <Grid gap={3} gridTemplateColumns="repeat(auto-fill, minmax(300px, 1fr))">
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('note')}
            label={<Trans>Note</Trans>}
            icon="note"
          />
        </Box>
        <Box>
          <AddWidgetButton
            beforeOpen={() => onSelect('image')}
            label={<Trans>Image</Trans>}
            icon="image"
          />
        </Box>
      </Grid>
    </Box>
  )
}

export default EmptyDashboardPage
