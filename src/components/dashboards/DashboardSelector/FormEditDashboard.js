import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import identityFn from 'lodash/identity'
import { Field, Form } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const FormEditDashboard = ({ onSubmit, initialValues, errors }) => {
  return (
    <Box p={1}>
      <Form
        onSubmit={onSubmit}
        initialValues={initialValues}
        render={({ handleSubmit }) => {
          return (
            <form onSubmit={handleSubmit}>
              <Flex flexDirection="column">
                <Box>
                  <Field
                    autoFocus
                    name="name"
                    parse={identityFn}
                    component={MntrTextFieldAdapter}
                    label={<Trans>Label</Trans>}
                    errorText={get(errors, 'name[0]')}
                  />
                </Box>
                <Box textAlign="right" mt="10px">
                  <MntrButton
                    rounded
                    bg="primary"
                    type="submit"
                    label={<Trans>Save</Trans>}
                    icon="save"
                    touch
                  />
                </Box>
              </Flex>
            </form>
          )
        }}
      />
    </Box>
  )
}

export default observer(FormEditDashboard)
