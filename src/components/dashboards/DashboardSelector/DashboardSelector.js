import { Trans } from '@lingui/react/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalCreateSharedDashboard from '~/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard'
import { observer } from '~/helpers/mst'
import DashboardSelectorPopup from './DashboardSelectorPopup'

const DashboardSelector = ({ viewport: { isMobile }, dashboards, account, notification }) => {
  return (
    <Flex ml="10px">
      <MntrPaper>
        <Flex>
          <MntrButton
            fullWidth
            bg="flat"
            icon="expand_more"
            label={
              dashboards.isLoadingCreateNew || !dashboards.selected ? (
                <Trans>Loading...</Trans>
              ) : (
                <Text
                  mr="auto"
                  textOverflow="ellipsis"
                  overflow="hidden"
                  whiteSpace="nowrap"
                  color="inherit"
                >
                  {dashboards.selected.name}
                </Text>
              )
            }
            popupPlacement="bottom-start"
            popup={(closePopup) => {
              return (
                <DashboardSelectorPopup
                  dashboards={dashboards}
                  account={account}
                  closePopup={closePopup}
                />
              )
            }}
          />
          {isMobile && (
            <Box mr={10}>
              <MntrButton
                icon="more_vert"
                bg="flat"
                popupPlacement="bottom-end"
                popup={(closePopup) => {
                  return (
                    <MntrMenu
                      menuItems={[
                        {
                          disabled: dashboards.selected.isLoading,
                          leftIcon: 'share',
                          beforeOpen: () => {
                            const data = {
                              serialized_dashboard: {
                                ...dashboards.selected,
                                is_read_only: true,
                                name: dashboards.selected.name,
                              },
                              serialized_widgets_data: '',
                            }
                            dashboards.createSharedDashboard(data)
                          },
                          label: <Trans>Share</Trans>,
                          ...withModalCreateSharedDashboard({
                            addNotification: notification?.add,
                            resetShared: dashboards.resetShared,
                            sharedDashboardKey: dashboards.sharedDashboardKey,
                          }),
                        },
                      ]}
                      closePopup={closePopup}
                    />
                  )
                }}
              />
            </Box>
          )}
        </Flex>
      </MntrPaper>
    </Flex>
  )
}

export default observer(DashboardSelector)
