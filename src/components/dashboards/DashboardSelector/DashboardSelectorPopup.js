import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import RequestFeatureButton from '~/components/misc/RequestFeatureButton/RequestFeatureButton'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import CreateDashboard from './CreateDashboard'
import FormEditDashboard from './FormEditDashboard'

const StyledLimitReached = styled(Box)`
  border-top: 1px solid ${({ theme }) => theme.popper.divider};
  width: 250px;
`

const DashboardSelectorPopup = ({
  dashboards,
  dashboards: { errors, clearErrors },
  account,
  closePopup,
}) => {
  const menuItems = []
  const dashboardsLimit = account.workspace.limits.dashboards_limit
  const dashboardsUsed = dashboards.list.length

  dashboards.list.map((item) => {
    const actions = [
      {
        leftIcon: 'edit',
        label: <Trans>Edit</Trans>,
        beforeOpen: () => dashboards.setEditItem(item.id),
        onClose: () => clearErrors(),
        modalTitle: <Trans>Edit dashboard</Trans>,
        modal: (closeModal) => {
          return (
            <Box m={1}>
              <FormEditDashboard
                onSubmit={(values) => dashboards.updateName(item.id, values, closeModal)}
                initialValues={{ name: item.name }}
                errors={errors}
              />
            </Box>
          )
        },
      },
      {
        hoverVariant: 'error',
        leftIcon: 'delete',
        label: <Trans>Delete</Trans>,
        ...withModalRemove({
          onSubmit: () => dashboards.removeItem(item.id),
          message: <Trans>Dashboard will be removed.</Trans>,
        }),
      },
    ]

    menuItems.push({
      href: {
        pathname: '/dashboard',
        query: {
          dashboardId: item.id,
        },
      },
      label: (
        <Text width={[8 / 10]} color="inherit">
          {item.name}
        </Text>
      ),
      buttonGroup: [
        {
          icon: 'more_vert',
          size: 'small',
          zIndex: 5000,
          popupPlacement: 'bottom-start',
          popup: (closePopup) => <MntrMenu menuItems={actions} closePopup={closePopup} />,
        },
      ],
      onClick: () => {
        pushEvent(events.DASHBOARD_CHANGED)
      },
    })
  })

  return (
    <ListScrollWrapper>
      <Flex flexDirection="column">
        <MntrMenu menuItems={menuItems} closePopup={closePopup} width={250} />
        {dashboardsLimit > dashboardsUsed && (
          <CreateDashboard
            fullWidth
            dashboards={dashboards}
            highlighted
            onCreate={() => {
              closePopup()
            }}
          />
        )}
        {dashboardsLimit <= dashboardsUsed && (
          <StyledLimitReached px={3} pt={2} color="black">
            <Trans>You have reached the limit on the number of dashboards.</Trans>
            <Box mt={3} mb={2} textAlign="center">
              <RequestFeatureButton
                id="Dashboards_count_limit"
                showNotification
                confirmTitle={t`Increase limit?`}
                label={<Trans>Increase limit</Trans>}
              />
            </Box>
          </StyledLimitReached>
        )}
      </Flex>
    </ListScrollWrapper>
  )
}

export default observer(DashboardSelectorPopup)
