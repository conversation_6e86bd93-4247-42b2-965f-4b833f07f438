import { Trans } from '@lingui/react/macro'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'
import FormCreateDashboard from './FormCreateDashboard'

const CreateDashboard = ({
  dashboards,
  dashboards: { errorsAdd, clearErrors },
  highlighted,
  onCreate,
  fullWidth = false,
}) => {
  return (
    <Box ml="11px" mr={highlighted ? '11px' : '0px'} pb={1}>
      <MntrButton
        fullWidth={fullWidth}
        bg="secondary"
        icon="add"
        label={<Trans>Add Dashboard</Trans>}
        popupPlacement={'bottom-start'}
        onClose={() => clearErrors()}
        popup={() => {
          return (
            <Box py={2} pr={1}>
              <FormCreateDashboard
                errors={errorsAdd}
                onSubmit={(values) => {
                  dashboards.create(values.name, onCreate)
                }}
              />
            </Box>
          )
        }}
      />
    </Box>
  )
}

export default observer(CreateDashboard)
