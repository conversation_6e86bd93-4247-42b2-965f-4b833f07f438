import { t } from '@lingui/core/macro'
import get from 'lodash/get'
import identityFn from 'lodash/identity'
import { Field, Form } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const FormCreateDashboard = ({ onSubmit, initialValues, edit, errors }) => {
  return (
    <Box>
      <Form
        onSubmit={onSubmit}
        initialValues={initialValues}
        render={({ handleSubmit }) => {
          return (
            <form onSubmit={handleSubmit}>
              <Flex alignItems={'center'}>
                <Box pl={3} width={[8 / 10]}>
                  <Field
                    autoFocus
                    name="name"
                    parse={identityFn}
                    component={MntrTextFieldAdapter}
                    placeholder={t({ id: 'name.nazev', message: 'Name' })}
                    errorText={get(errors, 'name[0]')}
                  />
                </Box>
                <Box ml={2}>
                  <MntrButton bg="primary" type="submit" icon={edit ? 'edit' : 'add'} />
                </Box>
              </Flex>
            </form>
          )
        }}
      />
    </Box>
  )
}

export default FormCreateDashboard
