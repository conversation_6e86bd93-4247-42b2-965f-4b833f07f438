import { Trans, useLingui } from '@lingui/react/macro'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import dataTypes from '~/constants/analytics'
import events from '~/constants/gtm'
import stats from '~/constants/stats'
import { pushEvent } from '~/helpers/gtm'
import withModalAddWidget from '~/helpers/modal/withModalAddWidget'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import withSubmenuModal from '~/helpers/withSubmenuModal'

const DashboardsGridMenu = ({
  id,
  isReadOnly,
  widget,
  removeItem,
  copyToDashboard,
  moveToDashboard,
  duplicateItem,
  appStore: {
    dashboards,
    viewport: { isMobile },
  },
}) => {
  const { i18n, t } = useLingui()

  const title = () => {
    let title = widget.title

    // In order to maintain backward compatibility for previously created widgets that do not have setup a title
    if (
      widget.blueprint &&
      widget.blueprint.subtype &&
      widget.blueprint.type === 'analytics' &&
      (widget.title === t`Analytics` || widget.title === '')
    ) {
      title = i18n._(dataTypes[widget.blueprint.subtype].title.primary)
    }
    if (
      widget.blueprint &&
      widget.blueprint.subtype &&
      widget.blueprint.type === 'stats' &&
      (widget.title === t`Statistics` || widget.title === '')
    ) {
      title = i18n._(
        (stats[widget.blueprint.subtype.split('::')[0]] || stats.summary).title.primary,
      )
    }

    return title
  }

  const menuItems = []

  if (widget.blueprint.type !== 'html') {
    menuItems.push({
      leftIcon: 'edit',
      label: <Trans>Edit widget</Trans>,
      beforeOpen: () => {
        dashboards.openAdd(widget.blueprint, id)
        dashboards.lockDrag()
      },
      onClose: () => {
        dashboards.unlockDrag()
      },
      ...withModalAddWidget(!!dashboards.widgetId),
    })
  }

  // Duplicate Item in same dashboard
  menuItems.push({
    leftIcon: 'content_copy',
    label: <Trans>Duplicate widget</Trans>,
    onClick: () => duplicateItem(),
  })

  // Move or Copy to different dashboard
  if (dashboards.list.length > 1) {
    const submenuItems = (handleClick) =>
      dashboards.list
        .filter((dashboard) => dashboard.id !== dashboards.selected.id)
        .map((item) => {
          return {
            label: item.name,
            leftIcon: 'dashboard',
            onClick: () => {
              handleClick(item.id)
            },
          }
        })
    const createDashboardSubmenuItemsAction = (icon, label, action) => {
      const submenuItemsList = submenuItems((id) => action(id))
      return {
        leftIcon: icon,
        label,
        ...(isMobile
          ? withSubmenuModal({
              modalIcon: icon,
              modalTitle: label,
              items: submenuItemsList,
            })
          : {
              subMenuItems: submenuItemsList,
            }),
      }
    }

    menuItems.push(
      createDashboardSubmenuItemsAction(
        'fork_left',
        <Trans>Copy to Dashboard</Trans>,
        copyToDashboard,
      ),
    )
    menuItems.push(
      createDashboardSubmenuItemsAction(
        'turn_left',
        <Trans>Move to Dashboard</Trans>,
        moveToDashboard,
      ),
    )
  }

  // Divider
  menuItems.push({})

  // Remove Item
  menuItems.push({
    leftIcon: 'delete',
    hoverVariant: 'error',
    label: <Trans>Remove widget</Trans>,
    ...withModalRemove({
      onSubmit: () => {
        pushEvent(events.DASHBOARD_WIDGET_REMOVED)
        removeItem()
      },
      message: <Trans>Widget will be removed</Trans>,
    }),
  })

  const buttonGroup = isReadOnly
    ? []
    : [
        {
          icon: 'more_vert',
          bg: 'flat',
          captureIgnore: true,
          popupPlacement: 'bottom-end',
          popup: (closePopup) => <MntrMenu menuItems={menuItems} closePopup={closePopup} />,
        },
      ]

  return <MntrPaperToolbar title={title()} actions={buttonGroup} />
}

export default observer(DashboardsGridMenu)
