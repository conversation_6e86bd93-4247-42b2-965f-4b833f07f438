import map from 'lodash/map'
import noopFn from 'lodash/noop'
import RGL, { WidthProvider as widthProvider } from 'react-grid-layout'
import { styled } from 'styled-components'
import { observer } from '~/helpers/mst'
import DashboardsGridItem from './DashboardsGridItem'
import DashboardsGridMenu from './DashboardsGridMenu'

const WidgetBox = styled.div`
  background: ${({ theme }) => theme.paper.background};
  border: 1px solid ${({ theme }) => theme.paper.border};
`

const ReactGridLayout = widthProvider(RGL)

const GridWrapper = styled.div`
  margin-top: -12px;

  .disable-resize {
    .react-resizable-handle {
      display: none;
    }
  }
`

const DashboardsGrid = ({
  layout,
  onLayoutChange,
  selected,
  viewport,
  dragLock,
  disabledWidgets,
}) => {
  const onRemoveItem = (i) => {
    selected.removeItem(i)
  }

  const createElement = (el) => {
    const i = el.add ? '+' : el.i
    const widget = selected.widgets.find((item) => item.id.toString() === el.i.toString())

    return (
      <WidgetBox
        id={`widget-${i}`}
        key={i}
        data-grid={viewport.width < 768 ? { ...el, w: 12 } : el}
        style={{ position: 'relative', borderRadius: 5 }}
        className="widget capture-single"
      >
        <DashboardsGridMenu
          id={el.i}
          isReadOnly={selected.is_read_only}
          widget={widget}
          removeItem={() => {
            onRemoveItem(el)
          }}
          duplicateItem={() => {
            selected.duplicateItem(el)
          }}
          moveToDashboard={(dashboardId) => {
            selected.moveToDashboard(el, dashboardId)
          }}
          copyToDashboard={(dashboardId) => {
            selected.copyToDashboard(el, dashboardId)
          }}
        />
        <div className="content">
          <DashboardsGridItem
            disabled={disabledWidgets}
            selected={selected}
            el={el}
            w={el.w}
            h={el.h}
            item={widget}
            i={el.i}
          />
        </div>
      </WidgetBox>
    )
  }

  return (
    <GridWrapper
      className={[selected.is_read_only && 'disable-resize', 'capture-bundle']
        .filter(Boolean)
        .join(' ')}
    >
      <ReactGridLayout
        isResizable={!selected.is_read_only}
        isDraggable={!(viewport.width < 768 || dragLock) && !selected.is_read_only}
        layout={viewport.width < 768 ? [] : [...layout]}
        onLayoutChange={viewport.width < 768 ? noopFn : onLayoutChange}
      >
        {map(layout, (el) => createElement(el))}
      </ReactGridLayout>
    </GridWrapper>
  )
}

export default observer(DashboardsGrid)
