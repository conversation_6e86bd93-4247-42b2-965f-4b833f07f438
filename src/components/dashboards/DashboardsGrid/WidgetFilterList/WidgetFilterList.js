import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { MEDIALIST_ARTICLES_PREFIX, PRIME_FILTER_PREFIX } from '~/constants'
import categoryTypes from '~/constants/categoryTypes'
import cutString from '~/helpers/cutString'
import { getMultiText } from '~/helpers/getMultiText'
import { observer } from '~/helpers/mst'
import defaultTheme from '~/styles/theme/defaultTheme'
import WidgetFilterChannel from './WidgetFilterChannel'
import WidgetFilterCountry from './WidgetFilterCountry'
import WidgetFilterLanguage from './WidgetFilterLanguage'
import WidgetFilterMedia from './WidgetFilterMedia'
import WidgetFilterSentiment from './WidgetFilterSentiment'
import WidgetFilterTags from './WidgetFilterTags'

const WidgetFilterList = ({
  id,
  type,
  appStore: {
    account,
    authors: { tags: authorTags },
    monitoring: { getFeedMap, tags },
  },
}) => {
  const feedMapItem = getFeedMap(type).get(`dashboards-${id}`)
  const labels = get(feedMapItem, 'filter.labels') || {}
  const isVisibleMedialistArticlesFilter = (() => {
    return Object.keys(labels).some((key) => key.startsWith(MEDIALIST_ARTICLES_PREFIX))
  })()
  const isVisiblePrimeFilter = (() => {
    return Object.keys(labels).some((key) => key.startsWith(PRIME_FILTER_PREFIX))
  })()

  return (
    <Flex ml="13px" mr="22px" overflowX="auto" flexDirection="row" flexWrap="nowrap">
      {isVisiblePrimeFilter && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={t`PRIMe`}
            icon="target"
          />
        </Box>
      )}
      {isVisibleMedialistArticlesFilter && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={t`Articles`}
            icon="view_stream"
          />
        </Box>
      )}

      {labels.topic_monitors &&
        labels.topic_monitors.map((item, key) => {
          return (
            <MntrButton
              mr={1}
              mb={1}
              key={key}
              disabled
              isChip
              topicId={item.value}
              label={item.text}
            />
          )
        })}

      {labels.category_type && (
        <Box mr={1} mb={1}>
          <WidgetFilterMedia
            type={type}
            id={labels.category_type.value}
            label={labels.category_type.text}
          />
        </Box>
      )}

      {labels.tvr_channel && (
        <Box mr={1} mb={1}>
          <WidgetFilterChannel
            channels={account.enums.crisis_communication.channels}
            channelId={labels.tvr_channel.value}
            label={labels.tvr_channel.text}
          />
        </Box>
      )}

      {labels.author && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="person"
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            label={labels.author.text}
          />
        </Box>
      )}

      {labels.page_numbers && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="chrome_reader_mode"
            iconBg={
              account.enums.categoryTypeIsActive.find(
                (item) => item.id === categoryTypes.CATEGORY_TYPE_OFFLINE_MEDIA,
              ).color
            }
            label={`${t`Pages`}: ${labels.page_numbers.text}`}
          />
        </Box>
      )}

      {labels.order_by && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="sort"
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            label={labels.order_by.text}
          />
        </Box>
      )}

      {labels.query && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="search"
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            label={cutString(labels.query.text, 30, true)}
          />
        </Box>
      )}

      {labels.news_source && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="input"
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            label={cutString(labels.news_source.text, 30, true)}
          />
        </Box>
      )}

      {labels.publisher && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="person"
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            label={cutString(labels.publisher.text, 30, true)}
          />
        </Box>
      )}

      {labels.megalist_hash && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="input"
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            label={<Trans>Selected sources</Trans>}
          />
        </Box>
      )}

      {labels.article_type && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            isChip
            icon="comment"
            iconBg={defaultTheme.buttons.primary}
            label={labels.article_type.text}
          />
        </Box>
      )}

      {labels.news_source_country && (
        <Box mr={1} mb={1}>
          <WidgetFilterCountry
            list={labels.news_source_country}
            label={getMultiText(labels.news_source_country)}
          />
        </Box>
      )}

      {labels.language && (
        <Box mr={1} mb={1}>
          <WidgetFilterLanguage list={labels.language} label={getMultiText(labels.language)} />
        </Box>
      )}

      {labels.sentiment && (
        <Box mr={1} mb={1}>
          <WidgetFilterSentiment list={labels.sentiment} label={getMultiText(labels.sentiment)} />
        </Box>
      )}

      {labels.tags && (
        <Box mr={1} mb={1}>
          <WidgetFilterTags tags={labels.tags} tagsList={tags.list} />
        </Box>
      )}

      {labels.author_tags && (
        <Box mr={1} mb={1}>
          <WidgetFilterTags tags={labels.author_tags} tagsList={authorTags.list} />
        </Box>
      )}

      {labels.author_type && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={getMultiText(labels.author_type)}
            icon="person"
          />
        </Box>
      )}

      {labels.author_focus_areas && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={getMultiText(labels.author_focus_areas)}
            icon="adjust"
          />
        </Box>
      )}

      {labels.contact_information && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={getMultiText(labels.contact_information)}
            icon="contact_phone"
          />
        </Box>
      )}

      {labels.title_query && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={labels.title_query.text}
            icon="title"
          />
        </Box>
      )}

      {labels.author_activity && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={labels.author_activity.text}
            icon="border_color"
          />
        </Box>
      )}

      {labels.country && (
        <Box mr={1} mb={1}>
          <MntrButton
            disabled
            iconBg={defaultTheme.buttons.primary}
            iconColor={defaultTheme.buttons.primaryColor}
            isChip
            label={getMultiText(labels.country)}
            icon="language"
          />
        </Box>
      )}
    </Flex>
  )
}

export default observer(WidgetFilterList)
