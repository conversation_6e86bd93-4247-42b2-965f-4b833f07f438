import MntrAvatar from '~/components/misc/MntrAvatar/MntrAvatar'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const WidgetFilterChannel = ({ channels, channelId, label }) => {
  const channel = channels.find((entry) => channelId === entry.id)

  if (!channel) {
    return <span />
  }

  return (
    <MntrButton
      disabled
      isChip
      label={label}
      icon={<MntrAvatar src={channel.logo} size={26} shadow />}
    />
  )
}

export default observer(WidgetFilterChannel)
