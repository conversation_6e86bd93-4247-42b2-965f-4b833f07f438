import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const WidgetFilterCountry = ({ label, list, appStore: { account } }) => {
  const buttonProps = {}

  // if is only one country in list, then use it's flag
  if (list.length === 1) {
    const mediacountryObj = account.enums.media_country.find((item) => item.id === list[0].value)
    if (mediacountryObj) {
      buttonProps.flagCountry = mediacountryObj.code
    }
  } else {
    // otherwise use globe icon
    buttonProps.icon = 'language'
  }

  return (
    <MntrButton disabled isChip iconBg="primary" iconColor="white" label={label} {...buttonProps} />
  )
}

export default observer(WidgetFilterCountry)
