import CategoryTypeIcon from '~/components/misc/CategoryTypeIcon/CategoryTypeIcon'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const WidgetFilterMedia = ({ id, label, type, appStore: { account } }) => {
  const category_type =
    type === 'tvr'
      ? account.enums.crisis_communication.media_types
      : account.enums.categoryTypeIsFeedFilter

  const subject = category_type.find((entry) => parseInt(entry.id) === parseInt(id)) || {}

  return (
    <MntrButton disabled isChip label={label} icon={<CategoryTypeIcon categoryType={subject} />} />
  )
}

export default observer(WidgetFilterMedia)
