import { Trans } from '@lingui/react/macro'
import color from 'color'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const WidgetFilterTags = ({ tags, tagsList }) => {
  return (
    <>
      {tags.map((tag, key) => {
        const subject = tagsList.find((item) => item.id === tag.value) || {}

        return (
          <MntrButton
            key={key}
            disabled
            isChip
            icon={subject.label ? 'label' : 'label_off'}
            iconColor={subject.label ? color(subject.color).darken(0.5).toString() : 'black'}
            iconBg={subject.label ? subject.color : 'chipTagBg'}
            label={subject.label || <Trans>Without tags</Trans>}
          />
        )
      })}
    </>
  )
}

export default WidgetFilterTags
