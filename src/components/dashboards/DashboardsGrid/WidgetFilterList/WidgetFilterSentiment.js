import MntrButton from '~/components/misc/MntrButton/MntrButton'
import getSentimentIcon from '~/helpers/getSentimentIcon'
import { observer } from '~/helpers/mst'

const WidgetFilterSentiment = ({ label, list, appStore: { account } }) => {
  let iconBg = 'primary'
  let sentimentId = null

  // if is only one sentiment in list, then use it's color and icon
  if (list.length === 1) {
    const sentimentObj = account.enums.sentiment.find((item) => item.id === list[0].value)
    if (sentimentObj) {
      iconBg = sentimentObj.color
      sentimentId = sentimentObj.id
    }
  }

  return (
    <MntrButton
      disabled
      icon={getSentimentIcon(sentimentId)}
      iconBg={iconBg}
      iconColor="white"
      isChip
      label={label}
    />
  )
}

export default observer(WidgetFilterSentiment)
