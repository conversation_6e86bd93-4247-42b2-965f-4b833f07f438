import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const WidgetFilterLanguage = ({ label, list, appStore: { account } }) => {
  const buttonProps = {}

  // if is only one language in list, then use it's flag
  if (list.length === 1) {
    const mediaLanguageObj = account.enums.media_language.find((item) => item.id === list[0].value)
    if (mediaLanguageObj) {
      buttonProps.flagLanguage = mediaLanguageObj.id
    }
  } else {
    // otherwise use globe icon
    buttonProps.icon = 'translate'
  }
  return (
    <MntrButton disabled isChip iconBg="primary" iconColor="white" label={label} {...buttonProps} />
  )
}

export default observer(WidgetFilterLanguage)
