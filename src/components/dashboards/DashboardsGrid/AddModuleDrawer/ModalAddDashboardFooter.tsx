import { Trans } from '@lingui/react/macro'
import { ButtonGroup, Flex } from '~/components/misc/Mntr'
import { IButtonProps } from '~/components/misc/Mntr/ButtonGroup'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { ObservedFC, observer } from '~/helpers/msts'

interface IModalAddDashboardFooterProps {
  closeModal: () => void
}

const ModalAddDashboardFooter: ObservedFC<IModalAddDashboardFooterProps> = ({
  appStore: { dashboards },
  closeModal,
}) => {
  const isEmpty = dashboards.preview.type === 'empty'
  const isEdit = dashboards.widgetId

  const getActions = () => {
    const actions: (IButtonProps | (() => void))[] = []
    const saveButton = {
      bg: 'secondary',
      icon: 'save',
      rounded: true,
      onClick: () => {
        if (isEdit) {
          pushEvent(events.DASHBOARD_WIDGET_EDITED)
          dashboards.editWidget(isEdit)
        } else {
          pushEvent(events.DASHBOARD_WIDGET_ADDED)
          dashboards.submitWidget()
        }
        closeModal()
      },
      label: isEdit ? <Trans>Save changes</Trans> : <Trans>Add to Dashboard</Trans>,
    }

    if (dashboards.preview.type === 'note' && !isEmpty) {
      actions.push(saveButton)
    }

    if (dashboards.preview.type !== 'note' && !isEmpty && dashboards.isVisibleFooter) {
      actions.push(saveButton)
    }

    return actions
  }

  return (
    <Flex column alignItems="end" p={3} pt={2}>
      <ButtonGroup buttons={getActions()} />
    </Flex>
  )
}

export default observer(ModalAddDashboardFooter)
