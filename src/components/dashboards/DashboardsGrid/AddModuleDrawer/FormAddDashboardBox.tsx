import { FormProps } from 'react-final-form'

import EmptyDashboard from '~/components/dashboards/EmptyDashboard/EmptyDashboard'
import { ModalScrollingContent } from '~/components/misc/Mntr'
import { ObservedFC, observer } from '~/helpers/msts'
import PreviewBox from './PreviewBox/PreviewBox'

interface IFormAddDashboardBoxProps {
  closeModal?: () => void
}

const FormAddDashboardBox: ObservedFC<IFormAddDashboardBoxProps> = ({
  appStore: {
    dashboards,
    viewport: { height },
  },
  closeModal,
}) => {
  const isEmpty = dashboards.preview.type === 'empty'
  return (
    <ModalScrollingContent minHeight={height - 340}>
      {isEmpty && (
        <EmptyDashboard
          onSelect={(values: FormProps['FormValues']) => dashboards.preview.setType(values)}
        />
      )}
      {!isEmpty && <PreviewBox closeModal={closeModal} />}
    </ModalScrollingContent>
  )
}

export default observer(FormAddDashboardBox)
