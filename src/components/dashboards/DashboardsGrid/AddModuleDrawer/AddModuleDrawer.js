import { useEffect } from 'react'
import { styled } from 'styled-components'
import { observer } from '~/helpers/mst'
import FormAddDashboardBox from './FormAddDashboardBox'

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(251, 252, 253, 0.7);
  z-index: 2000;
`

const WrapperContent = styled.div`
  margin: 36px auto;
  box-shadow:
    rgba(0, 0, 0, 0.2) 0px 14px 45px,
    rgba(0, 0, 0, 0.18) 0px 10px 18px;
  border-radius: 3px;
  overflow: hidden;
  position: fixed;
  z-index: 2020;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 1024px;
  width: 100%;
  background: rgb(245, 247, 249);
`

const AddModuleDrawer = ({ appStore: { dashboards } }) => {
  useEffect(() => {
    document.onkeyup = (e) => {
      if ((e.key === 'Escape' || e.key === 'q') && dashboards.isOpenAdd) {
        dashboards.closeAdd()
      }
    }

    return () => {
      document.onkeyup = null
    }
  }, [dashboards])

  return (
    <div className="widget-drawer">
      {dashboards.isOpenDrawer && <Overlay onClick={() => dashboards.closeAdd()}></Overlay>}
      {dashboards.isOpenDrawer && (
        <WrapperContent>
          <FormAddDashboardBox />
        </WrapperContent>
      )}
    </div>
  )
}

export default observer(AddModuleDrawer)
