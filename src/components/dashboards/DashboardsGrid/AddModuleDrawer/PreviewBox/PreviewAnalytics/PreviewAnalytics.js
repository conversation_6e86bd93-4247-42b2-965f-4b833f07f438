import { Trans } from '@lingui/react/macro'
import FormQuery from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery'
import FormSubtype from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype'
import FormWidgetTitle from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Box, Flex } from '~/components/misc/Mntr'
import RequestFeatureButton from '~/components/misc/RequestFeatureButton/RequestFeatureButton'
import TopicsMultiSelector from '~/components/misc/TopicsMultiSelector/TopicsMultiSelector'
import WidgetAnalytics from '~/components/widgets/modules/analytics/WidgetAnalytics'
import { MAX_SELECTED_TOPICS } from '~/constants'
import { getConfigPreviewAnalytics } from '~/helpers/getActiveFiltersConfig'
import isEmpty from '~/helpers/isEmpty'
import { observer } from '~/helpers/mst'

const PreviewAnalytics = ({
  appStore: {
    monitoring: { analyticsMap },
    account,
    viewport: { isMobile },
  },
  zIndex,
}) => {
  const { shouldSelectTopic, load, loader, filter } = analyticsMap.get('dashboard-preview')
  const handleSubmitTopicSelector = (model) => {
    load({ topic_monitors: model })
  }

  if (loader.isLoading('dashboard-preview') && isEmpty(filter.labels)) {
    return <span />
  }

  if (!account.workspace?.permissions.analytics.can_read) {
    return (
      <div style={{ textAlign: 'center', marginTop: 40 }}>
        <Trans>You don't have permission to view</Trans>: <Trans>Analytics</Trans>
        <div style={{ marginTop: 4 }}>
          <RequestFeatureButton id="Analytics" showNotification />
        </div>
      </div>
    )
  }

  if (shouldSelectTopic) {
    return (
      <Box px={3}>
        <FormQuery feedId="dashboard-preview" type="analytics" />
        <Box mx={1}>
          <TopicsMultiSelector
            isModal
            subHeader={<Trans>Select at least one topic</Trans>}
            maxSelectedLimit={MAX_SELECTED_TOPICS}
            onSubmit={handleSubmitTopicSelector}
          />
        </Box>
      </Box>
    )
  }

  return (
    <>
      <Flex flexDirection="column" gap={2} px={3}>
        <FormQuery feedId="dashboard-preview" type="analytics" />
        <MntrFiltersBar
          filters={[
            'Date',
            'Source',
            'LanguageMultiselect',
            'CountryMultiselect',
            'Author',
            'Sentiment',
            'Notes',
            'ArticleType',
          ]}
          feedId="dashboard-preview"
          customRoute="/"
          type="analytics"
          disableRedirect
          zIndex={zIndex}
        />
        <MntrActiveFilters config={getConfigPreviewAnalytics(zIndex)} />
        <Flex flexDirection={isMobile ? 'column' : 'row'} gap={2} my={1}>
          <Box flex={1}>
            <FormSubtype type="analytics" />
          </Box>
          <Box flex={1}>
            <FormWidgetTitle />
          </Box>
        </Flex>
      </Flex>
      <Box mx={3}>
        <WidgetAnalytics isPreview chartId="dashboard-preview" zIndex={zIndex} />
      </Box>
    </>
  )
}

export default observer(PreviewAnalytics)
