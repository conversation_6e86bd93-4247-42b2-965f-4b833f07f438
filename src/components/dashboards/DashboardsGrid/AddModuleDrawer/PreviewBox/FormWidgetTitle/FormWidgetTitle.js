import { t } from '@lingui/core/macro'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const FormWidgetTitle = ({ appStore: { dashboards }, placeholder }) => {
  return (
    <Box>
      <MntrTextFieldAdapter
        meta={{}}
        name="title"
        onChange={(el) => dashboards.preview.setTitle(el.currentTarget.value)}
        value={dashboards.preview.title}
        id={dashboards.preview.title}
        fullWidth
        placeholder={placeholder}
        label={t`Title`}
      />
    </Box>
  )
}

export default observer(FormWidgetTitle)
