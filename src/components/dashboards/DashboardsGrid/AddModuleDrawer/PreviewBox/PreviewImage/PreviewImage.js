import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import Dropzone from 'react-dropzone'
import { Field, Form } from 'react-final-form'
import { styled } from 'styled-components'
import FormWidgetTitle from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import { ACCEPTED_FILE_TYPES } from '~/constants'
import { observer } from '~/helpers/mst'

const StyledFlex = styled(Flex)`
  & .dropzone-dashboard-image {
    background: ${({ theme }) => theme.paper.background};
    color: ${({ theme }) => theme.colors.black};
  }
`
const PreviewImage = ({
  appStore: {
    dashboards,
    monitoring: { imagesMap },
  },
}) => {
  const item = imagesMap.get('dashboard-preview')

  return (
    <StyledFlex flexDirection="column" gap={3} mx={3}>
      <FormWidgetTitle item={item} placeholder={t`Image`} />

      <Box position="relative">
        <Form
          onSubmit={identityFn}
          initialValues={{
            image: item.url,
          }}
          render={({ handleSubmit, form: { change }, values }) => {
            return (
              <form onSubmit={handleSubmit}>
                <Flex flexWrap="wrap">
                  <Box width={1}>
                    <Dropzone
                      accept={ACCEPTED_FILE_TYPES}
                      onDrop={(files) => {
                        const reader = new FileReader()
                        reader.readAsDataURL(files[0])
                        reader.addEventListener('load', (res) => {
                          change('image', res.target.result)
                          dashboards.preview.setData(res.target.result)
                        })
                      }}
                    >
                      {({ getRootProps, getInputProps }) => {
                        return (
                          <div {...getRootProps({ className: 'dropzone-dashboard-image' })}>
                            <input {...getInputProps()} />
                            {values.image ? (
                              <img src={values.image} alt="preview" style={{ width: '100%' }} />
                            ) : (
                              <Trans>Drag 'n' drop image or click to select files</Trans>
                            )}
                          </div>
                        )
                      }}
                    </Dropzone>
                    <div style={{ display: 'none' }}>
                      <Field name="image" component={MntrTextFieldAdapter} />
                    </div>
                  </Box>
                </Flex>
              </form>
            )
          }}
        />
      </Box>
    </StyledFlex>
  )
}

export default observer(PreviewImage)
