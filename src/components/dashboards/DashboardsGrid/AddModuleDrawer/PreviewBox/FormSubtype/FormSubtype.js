import { Trans, useLingui } from '@lingui/react/macro'
import get from 'lodash/get'
import identityFn from 'lodash/identity'
import { Field, Form, FormSpy } from 'react-final-form'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import dataTypes from '~/constants/analytics'
import { observer } from '~/helpers/mst'

const FormSubtype = ({ type, appStore: { dashboards, account } }) => {
  const { i18n, t } = useLingui()
  const items = []

  const dataTypesItems = get(dashboards, 'preview.chart.isMultiTopic')
    ? Object.keys(dataTypes).filter((dataType) => {
        return (
          dataTypes[dataType].analyticsDashboardWidget !== false &&
          dataTypes[dataType].isMultiTopic &&
          dataTypes[dataType].dataSets.every((dataSet) => {
            return account.enums.analytics.multi_topic_charts.includes(dataSet)
          })
        )
      })
    : Object.keys(dataTypes).filter((dataType) => {
        return (
          dataTypes[dataType].analyticsDashboardWidget !== false &&
          dataTypes[dataType].isSingleTopic &&
          dataTypes[dataType].dataSets.every((dataSet) => {
            return account.enums.analytics.single_topic_charts.includes(dataSet)
          })
        )
      })

  const setPostfixTitle = (dataType) => {
    const postfixMessage = dataTypes[dataType].title.postfix

    return postfixMessage ? '(' + i18n._(postfixMessage) + ')' : ``
  }

  const setPreviewTitle = (dataType) => {
    const primaryMessages = dataTypes[dataType].title.primary
    const messagesArray = Array.isArray(primaryMessages) ? primaryMessages : [primaryMessages]

    return `${messagesArray
      .map((message) => (message.id ? i18n._(message) : message))
      .join(' ')} ${setPostfixTitle(dataType)}`
  }

  dataTypesItems.map((dataType) => {
    items.push({
      label: `${i18n._(dataTypes[dataType].title.prefix)} - ${setPreviewTitle(dataType)}`,
      value: dataType,
    })
  })

  if (dashboards.preview.title === t`Analytics`) {
    dashboards.preview.setTitle(setPreviewTitle(dashboards.preview.subtype))
  }

  return (
    <div>
      <Flex flexWrap="nowrap">
        <Box width={1}>
          {type === 'analytics' && (
            <Form
              onSubmit={identityFn}
              initialValues={{ subtype: dashboards.preview.subtype }}
              render={({ handleSubmit }) => {
                return (
                  <form onSubmit={handleSubmit}>
                    <FormSpy
                      onChange={(state) => {
                        if (state.values.subtype !== dashboards.preview.subtype) {
                          dashboards.preview.setSubtype(state.values.subtype)
                          dashboards.preview.setTitle(setPreviewTitle(dashboards.preview.subtype))
                        }
                      }}
                    />
                    <Field
                      name="subtype"
                      label={<Trans>Select type</Trans>}
                      component={MntrSelectAdapter}
                      items={items}
                    />
                  </form>
                )
              }}
            />
          )}
        </Box>
      </Flex>
    </div>
  )
}

export default observer(FormSubtype)
