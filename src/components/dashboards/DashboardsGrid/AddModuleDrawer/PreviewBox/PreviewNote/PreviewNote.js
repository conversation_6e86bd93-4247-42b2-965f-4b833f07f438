import identityFn from 'lodash/identity'
import { Field, Form, FormSpy } from 'react-final-form'
import FormWidgetTitle from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle'
import MntrCmsEditorAdapter from '~/components/forms/adapters/MntrCmsEditorAdapter/MntrCmsEditorAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const PreviewNote = ({
  appStore: {
    dashboards,
    monitoring: { notesMap },
  },
}) => {
  const isEditForm = dashboards.widgetId
  const feedItem = notesMap.get('dashboard-preview')

  return (
    <Flex flexDirection="column" gap={3} mx={3}>
      <FormWidgetTitle item={feedItem} />

      <Box className="widget-drawer">
        <Form
          onSubmit={identityFn}
          initialValues={{ note: isEditForm ? feedItem.data.data : '<p></p>' }}
          render={({ handleSubmit }) => {
            return (
              <form onSubmit={handleSubmit}>
                <FormSpy onChange={(state) => dashboards.preview.setData(state.values?.note)} />
                <Field
                  name="note"
                  isVisibleMenuBar
                  isVisibleBubbleMenu={false}
                  transparent={false}
                  className="field-dashboard-name"
                  autofocus
                  component={MntrCmsEditorAdapter}
                />
              </form>
            )
          }}
        />
      </Box>
    </Flex>
  )
}

export default observer(PreviewNote)
