import FormQuery from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Flex } from '~/components/misc/Mntr'
import CrisisCommunicationPromoBox from '~/components/tvr/Content/TvrPromoBox/TvrPromoBox'
import WidgetTvr from '~/components/widgets/modules/tvr/WidgetTvr'
import { getConfigPreviewTvr } from '~/helpers/getActiveFiltersConfig'
import { observer } from '~/helpers/mst'

const PreviewTvr = ({ appStore: { account }, zIndex, closeModal }) => {
  const hasTVRAccess = account.workspace?.permissions.tvr_feed.can_read

  return (
    <Flex flexDirection="column" gap={2} px={3}>
      {!hasTVRAccess && <CrisisCommunicationPromoBox />}
      {hasTVRAccess && (
        <>
          <FormQuery feedId="dashboard-preview" type="tvr" />
          <MntrFiltersBar
            filters={['SourceTVR', 'LanguageTVR', 'ChannelsTVR']}
            feedId="dashboard-preview"
            customRoute="/dashboard"
            type="tvr"
            disableRedirect
            zIndex={zIndex}
          />
          <MntrActiveFilters config={getConfigPreviewTvr(zIndex)} />
          <WidgetTvr feedId="dashboard-preview" isPreview closeModal={closeModal} />
        </>
      )}
    </Flex>
  )
}

export default observer(PreviewTvr)
