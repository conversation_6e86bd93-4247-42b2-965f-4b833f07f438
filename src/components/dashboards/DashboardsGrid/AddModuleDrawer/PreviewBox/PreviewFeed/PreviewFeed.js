import FormQuery from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Flex } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import WidgetFeedSimple from '~/components/widgets/modules/feed/WidgetFeedSimple'
import { getConfigPreviewFeed } from '~/helpers/getActiveFiltersConfig'
import { observer } from '~/helpers/mst'

const PreviewFeed = ({
  appStore: {
    account,
    dashboards,
    monitoring: { feedMap },
    topics,
  },
  zIndex,
  closeModal,
}) => {
  const feedId = 'dashboard-preview'
  const feedItem = feedMap.get(feedId)
  const isLoading = feedItem.loader.isLoading('feed-loading')

  const filters = [
    'Source',
    'LanguageMultiselect',
    'CountryMultiselect',
    'Author',
    'Sentiment',
    'Notes',
    'ArticleType',
  ]

  if (feedItem.filter.data?.topic_monitors?.trim()) {
    const topicIds = feedItem.filter.data?.topic_monitors
      ?.split(',')
      .map((id) => Number(id.trim()))
      .filter(Boolean)

    const hasAnyPrimeScore = topics.list.some(
      (item) => topicIds.includes(item.id) && item.data.has_prime_score,
    )

    if (hasAnyPrimeScore && account.enums.prime_score.prime_score_definitions?.length > 0) {
      filters.push('Prime')
    }
  }

  return (
    <Flex flexDirection="column" gap={2} px={3}>
      <FormQuery feedId={feedId} />
      <MntrFiltersBar
        filters={filters}
        feedId={feedId}
        customRoute="/dashboard"
        disableRedirect
        withSort
        zIndex={zIndex}
        isDashboard
        isPreview
      />
      {!isLoading && (
        <>
          <MntrActiveFilters config={getConfigPreviewFeed(zIndex)} />
          <WidgetFeedSimple
            feedId={feedId}
            layout={dashboards.preview.subtype}
            closeModal={closeModal}
            isPreview
            openInspector
            withTitle
          />
        </>
      )}
      {isLoading && (
        <Flex alignItems="center" justifyContent="center" m={3}>
          <MntrCircularProgress size={26} />
        </Flex>
      )}
    </Flex>
  )
}

export default observer(PreviewFeed)
