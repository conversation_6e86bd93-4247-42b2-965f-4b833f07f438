import { Box } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'
import PreviewAnalytics from './PreviewAnalytics/PreviewAnalytics'
import PreviewFeed from './PreviewFeed/PreviewFeed'
import PreviewImage from './PreviewImage/PreviewImage'
import PreviewMedialist from './PreviewMedialist/PreviewMedialist'
import PreviewNote from './PreviewNote/PreviewNote'
import PreviewSocialEngagement from './PreviewSocialEngagement/PreviewSocialEngagement'
import PreviewStats from './PreviewStats/PreviewStats'
import PreviewTvr from './PreviewTvr/PreviewTvr'

const PreviewBox = ({
  appStore: {
    dashboards: { preview },
  },
  zIndex = 5995,
  closeModal,
}) => {
  return (
    <Box pt={3}>
      {preview.type === 'feed' && <PreviewFeed zIndex={zIndex} closeModal={closeModal} />}
      {preview.type === 'analytics' && <PreviewAnalytics zIndex={zIndex} />}
      {preview.type === 'stats' && <PreviewStats zIndex={zIndex} />}
      {preview.type === 'tvr' && <PreviewTvr zIndex={zIndex} closeModal={closeModal} />}
      {preview.type === 'medialist' && <PreviewMedialist zIndex={zIndex} closeModal={closeModal} />}
      {preview.type === 'social_engagement' && (
        <PreviewSocialEngagement zIndex={zIndex} closeModal={closeModal} />
      )}
      {preview.type === 'note' && <PreviewNote />}
      {preview.type === 'image' && <PreviewImage />}
    </Box>
  )
}

export default observer(PreviewBox)
