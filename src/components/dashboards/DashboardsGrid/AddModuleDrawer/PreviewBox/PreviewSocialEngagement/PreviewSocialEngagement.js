import { Trans } from '@lingui/react/macro'
import FormQuery from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Box, Flex } from '~/components/misc/Mntr'
import TopicsMultiSelector from '~/components/misc/TopicsMultiSelector/TopicsMultiSelector'
import WidgetSocialEngagement from '~/components/widgets/modules/socialEngagement/WidgetSocialEngagement'
import { getConfigPreviewDisabledMulti } from '~/helpers/getActiveFiltersConfig'
import { observer } from '~/helpers/mst'

const PreviewSocialEngagement = ({
  appStore: {
    dashboards,
    monitoring: { socialEngagementMap },
  },
  zIndex,
  closeModal,
}) => {
  const { shouldSelectTopic, load, loader } = socialEngagementMap.get('dashboard-preview')

  const handleSubmitTopicSelector = (model) => {
    load({ topic_monitors: model })
  }

  if (shouldSelectTopic && !loader.isLoading('feed-loading')) {
    return (
      <Box px={3}>
        <FormQuery feedId="dashboard-preview" type="social_engagement" />
        <Box mx={1}>
          <TopicsMultiSelector
            isModal
            subHeader={<Trans>Select at least one topic</Trans>}
            maxSelectedLimit={1}
            onSubmit={handleSubmitTopicSelector}
          />
        </Box>
      </Box>
    )
  }

  return (
    <Flex flexDirection="column" gap={2} px={3}>
      <FormQuery feedId="dashboard-preview" type="social_engagement" />
      <MntrFiltersBar
        filters={[
          'Source',
          'LanguageMultiselect',
          'CountryMultiselect',
          'Author',
          'Sentiment',
          'Notes',
          'ArticleType',
        ]}
        feedId="dashboard-preview"
        customRoute="/dashboard"
        type="social_engagement"
        disableRedirect
        zIndex={zIndex}
      />
      <MntrActiveFilters config={getConfigPreviewDisabledMulti(zIndex, 'social_engagement')} />
      <WidgetSocialEngagement
        type="social_engagement"
        feedId="dashboard-preview"
        layout={dashboards.preview.subtype}
        isPreview
        closeModal={closeModal}
      />
    </Flex>
  )
}

export default observer(PreviewSocialEngagement)
