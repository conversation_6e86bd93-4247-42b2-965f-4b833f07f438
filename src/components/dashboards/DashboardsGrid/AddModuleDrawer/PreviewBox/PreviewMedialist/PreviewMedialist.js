import FormQuery from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery'
import FormWidgetTitle from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Box, Flex } from '~/components/misc/Mntr'
import WidgetMedialist from '~/components/widgets/modules/medialist/WidgetMedialist'
import { getConfigPreviewMedialist } from '~/helpers/getActiveFiltersConfig'
import { observer } from '~/helpers/mst'

const PreviewMedialist = ({ appStore: { dashboards }, zIndex, closeModal }) => {
  return (
    <Flex flexDirection="column" gap={2} px={3}>
      <Box>
        <FormWidgetTitle />
      </Box>
      <FormQuery feedId="dashboard-preview" type="medialist" />
      <Box>
        <MntrFiltersBar
          filters={[
            'MedialistArticles',
            'AuthorTitle',
            'AuthorType',
            'AuthorFocusAreas',
            'AuthorActivity',
            'Source',
            'ContactInformation',
            'Country',
          ]}
          type="medialist"
          feedId="dashboard-preview"
          customRoute="/dashboard"
          disableRedirect
          zIndex={zIndex}
          isPreview
        />
      </Box>{' '}
      <MntrActiveFilters config={getConfigPreviewMedialist(zIndex)} />
      <WidgetMedialist
        type="medialist"
        feedId="dashboard-preview"
        layout={dashboards.preview.subtype}
        isPreview
        closeModal={closeModal}
      />
    </Flex>
  )
}

export default observer(PreviewMedialist)
