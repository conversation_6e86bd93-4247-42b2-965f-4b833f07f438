import { Trans, useLingui } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import { Field, Form, FormSpy } from 'react-final-form'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import stats from '~/constants/stats'
import { observer } from '~/helpers/mst'

const FormStatsSubtype = ({ appStore: { dashboards, account } }) => {
  const { i18n, t } = useLingui()
  const availableStats = account.enums.analytics.stats

  const items = [
    {
      label: i18n._(stats.summary.title.primary),
      value: 'summary',
    },
    {
      label: `${i18n._(stats.summary.title.primary)} - ${t`Traditional Media`}`,
      value: 'summary-traditional_media',
    },
    {
      label: `${i18n._(
        stats.summary.title.primary,
      )} - ${t`Traditional Media w/o percentage change`}`,
      value: 'summary-traditional_media_no_percentages',
    },
    {
      label: `${i18n._(stats.summary.title.primary)} - ${t`Social Media`}`,
      value: 'summary-social_media',
    },
    {
      label: `${t`PRIMe`}`,
      value: 'prime_total_value',
    },
    ...availableStats
      .filter((type) => stats[type])
      .map((type) => {
        return {
          label: i18n._(stats[type].title.primary),
          value: type,
        }
      }),
    ...availableStats
      .filter((type) => stats[type])
      .map((type) => {
        return {
          label: `${i18n._(stats[type].title.primary)} ${t`by source`}`,
          value: `${type}::source`,
        }
      }),
  ]

  if (dashboards.preview.title === t`Statistics`) {
    dashboards.preview.setTitle(
      i18n._((stats[dashboards.preview.subtype.split('::')[0]] || stats.summary).title.primary),
    )
  }

  return (
    <div>
      <Flex flexWrap="nowrap">
        <Box width={1}>
          <Form
            onSubmit={identityFn}
            initialValues={{ subtype: dashboards.preview.subtype }}
            render={({ handleSubmit }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <FormSpy
                    onChange={(state) => {
                      if (state.values.subtype !== dashboards.preview.subtype) {
                        dashboards.preview.setSubtype(state.values.subtype)
                        dashboards.preview.setTitle(
                          i18n._(
                            (stats[dashboards.preview.subtype.split('::')[0]] || stats.summary)
                              .title.primary,
                          ),
                        )
                      }
                    }}
                  />
                  <Field
                    name="subtype"
                    label={<Trans>Select type</Trans>}
                    component={MntrSelectAdapter}
                    items={items}
                  />
                </form>
              )
            }}
          />
        </Box>
      </Flex>
    </div>
  )
}

export default observer(FormStatsSubtype)
