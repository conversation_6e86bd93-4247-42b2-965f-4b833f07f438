import { Trans } from '@lingui/react/macro'
import FormQuery from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery'
import FormWidgetTitle from '~/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import { Box, Flex } from '~/components/misc/Mntr'
import RequestFeatureButton from '~/components/misc/RequestFeatureButton/RequestFeatureButton'
import TopicsMultiSelector from '~/components/misc/TopicsMultiSelector/TopicsMultiSelector'
import WidgetStats from '~/components/widgets/modules/stats/WidgetStats'
import { getConfigPreviewDisabledMulti } from '~/helpers/getActiveFiltersConfig'
import isEmpty from '~/helpers/isEmpty'
import { observer } from '~/helpers/mst'
import FormStatsSubtype from './FormStatsSubtype'

const PreviewStats = ({
  appStore: {
    dashboards,
    monitoring: { statsMap },
    account,
    viewport: { isMobile },
  },
  zIndex,
}) => {
  const { shouldSelectTopic, load, loader, filter } = statsMap.get('dashboard-preview')

  const handleSubmitTopicSelector = (model) => {
    load({ topic_monitors: model })
  }

  if (loader.isLoading('dashboard-preview') && isEmpty(filter.labels)) {
    return <span />
  }

  if (!account.workspace?.permissions.analytics.can_read) {
    return (
      <div style={{ textAlign: 'center', marginTop: 40 }}>
        <Trans>You don't have permission to view</Trans>: <Trans>Analytics</Trans>
        <div style={{ marginTop: 4 }}>
          <RequestFeatureButton id="Analytics" showNotification />
        </div>
      </div>
    )
  }

  if (shouldSelectTopic) {
    return (
      <Flex flexDirection="column" gap={2} px={3}>
        <FormQuery feedId="dashboard-preview" type="analytics" />
        <Box mx={1}>
          <TopicsMultiSelector
            isModal
            subHeader={<Trans>Select at least one topic</Trans>}
            maxSelectedLimit={1}
            onSubmit={handleSubmitTopicSelector}
          />
        </Box>
      </Flex>
    )
  }

  return (
    <Flex flexDirection="column" gap={3} mx={3}>
      <FormQuery feedId="dashboard-preview" type="stats" />
      <MntrFiltersBar
        filters={[
          'Source',
          'LanguageMultiselect',
          'CountryMultiselect',
          'Author',
          'Sentiment',
          'Notes',
          'ArticleType',
        ]}
        feedId="dashboard-preview"
        customRoute="/dashboard"
        type="stats"
        disableRedirect
        zIndex={zIndex}
      />
      <MntrActiveFilters config={getConfigPreviewDisabledMulti(zIndex, 'stats')} />
      <Flex flexDirection={isMobile ? 'column' : 'row'} gap={2}>
        <Box flex={1}>
          <FormStatsSubtype type="stats" />
        </Box>
        <Box flex={1}>
          <FormWidgetTitle />
        </Box>
      </Flex>
      <WidgetStats feedId="dashboard-preview" layout={dashboards.preview.subtype} isPreview />
    </Flex>
  )
}

export default observer(PreviewStats)
