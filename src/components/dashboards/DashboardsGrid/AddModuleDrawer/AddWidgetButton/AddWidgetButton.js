import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalAddWidget from '~/helpers/modal/withModalAddWidget'
import { observer } from '~/helpers/mst'

const Wrapper = styled.div`
  height: 100px;
  text-transform: uppercase;
  text-align: right;
  font-weight: 800;
  border-radius: 12px;
  background: ${({ theme }) => theme.paper.background};
  box-shadow: 8px 8px 12px ${({ theme }) => theme.paper.background};
  border: 1px solid ${({ theme }) => theme.paper.border};
  color: ${({ theme }) => theme.colors.black};
  position: relative;
  cursor: pointer;
  width: auto;
  &:hover {
    border: 1px solid ${({ theme }) => theme.paper.borderSelected};
    background: ${({ theme }) => theme.paper.backgroundSelected};
    color: ${({ theme }) => theme.paper.borderSelected};
    & * {
      fill: ${({ theme }) => theme.paper.borderSelected};
    }

    & > div > i {
      color: ${({ theme }) => theme.paper.borderSelected} !important;
    }
  }
`

const WrapperTitle = styled.div`
  position: absolute;
  right: 20px;
  bottom: 20px;
`

const WrapperIcon = styled.div`
  position: absolute;
  left: 10px;
  top: 10px;
`

const AddWidgetButton = ({ appStore: { dashboards }, icon, label, onClick, beforeOpen }) => {
  return (
    <>
      {typeof beforeOpen === 'function' && dashboards && (
        <MntrButton
          customElement={
            <Wrapper>
              <WrapperIcon>
                <Icon size={60} color="black">
                  {icon}
                </Icon>
              </WrapperIcon>
              <WrapperTitle>{label}</WrapperTitle>
            </Wrapper>
          }
          beforeOpen={beforeOpen}
          {...withModalAddWidget(!!dashboards.widgetId)}
        />
      )}
      {typeof onClick === 'function' && (
        <Wrapper onClick={onClick}>
          <WrapperIcon>
            <Icon size={60} color="black">
              {icon}
            </Icon>
          </WrapperIcon>
          <WrapperTitle>{label}</WrapperTitle>
        </Wrapper>
      )}
    </>
  )
}

export default observer(AddWidgetButton)
