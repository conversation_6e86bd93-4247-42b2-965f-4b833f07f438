import { Trans } from '@lingui/react/macro'
import Icon from '~/components/misc/Icon/Icon'
import { Flex } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const AddWidgetHeader = ({
  appStore: {
    dashboards: {
      preview: { type, setType },
    },
  },
  isEdit,
}) => {
  let title
  switch (type) {
    case 'feed':
      title = <Trans>Articles</Trans>
      break
    case 'analytics':
      title = <Trans>Analytics</Trans>
      break
    case 'tvr':
      title = <Trans>Crisis communication</Trans>
      break
    case 'stats':
      title = <Trans>Statistics</Trans>
      break
    case 'medialist':
      title = <Trans>Medialist</Trans>
      break
    case 'social_engagement':
      title = <Trans>Social Engagement</Trans>
      break
    case 'note':
      title = <Trans>Note</Trans>
      break
    case 'image':
      title = <Trans>Image</Trans>
      break
    default:
      title = <Trans>Select category</Trans>
      break
  }

  const renderTitle =
    type === 'empty' ? (
      <div style={{ display: 'inline-block', position: 'relative', marginTop: 1 }}>
        <Trans>Select category</Trans>
      </div>
    ) : (
      <Flex centerY>
        <span style={{ cursor: 'pointer' }} onClick={() => setType('empty')}>
          <span style={{ textDecoration: 'underline' }}>
            <Trans>Select category</Trans>
          </span>
        </span>{' '}
        <Flex centerY>
          <Icon>arrow_right</Icon> {title}
        </Flex>
      </Flex>
    )

  return <>{isEdit ? <Trans>Edit widget</Trans> : renderTitle}</>
}

export default observer(AddWidgetHeader)
