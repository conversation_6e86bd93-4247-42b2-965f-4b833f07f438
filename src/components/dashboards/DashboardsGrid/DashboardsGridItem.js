import Widget from '~/components/widgets/Widget'
import isEmpty from '~/helpers/isEmpty'
import { observer } from '~/helpers/mst'
import WidgetFilterList from './WidgetFilterList/WidgetFilterList'

// validate if user has read permission for specific widget type
const hasReadPermission = (type, account) => {
  const typePermissionMap = {
    analytics: account.workspace?.permissions.analytics.can_read,
    stats: account.workspace?.permissions.analytics.can_read,
    medialist: account.workspace?.permissions.authors_database.can_read,
  }

  return typePermissionMap[type] ?? true
}

const DashboardsGridItem = ({ item, w, h, disabled, appStore: { account } }) => {
  let hasFilters =
    !isEmpty(item.blueprint.filter) && hasReadPermission(item.blueprint.type, account)

  if (
    hasFilters &&
    Object.keys(item.blueprint.filter).length === 1 &&
    item.blueprint.filter.custom
  ) {
    hasFilters = false
  }

  // disable scroll for specific widgets
  const isSpecificType = ['image', 'stats', 'analytics'].includes(item.blueprint.type)

  return (
    <div>
      {hasFilters && (
        <WidgetFilterList type={item.blueprint.type} id={item.id} filter={item.blueprint.filter} />
      )}
      <div
        style={{
          whiteSpace: 'pre',
          overflowY:
            isSpecificType || !hasReadPermission(item.blueprint.type, account)
              ? 'hidden'
              : 'scroll',
          position: 'absolute',
          top: hasFilters ? 100 : 54,
          bottom: 15,
          left: 0,
          right: 0,
          height: 'auto',
          ...(item.blueprint.type === 'image' ||
          (item.blueprint.type === 'stats' &&
            item.blueprint.subtype.split('::').reverse()[0] !== 'source')
            ? {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }
            : {}),
        }}
      >
        <Widget item={item} blueprint={item.blueprint} w={w} h={h} disabled={disabled} />
      </div>
    </div>
  )
}

export default observer(DashboardsGridItem)
