import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import { styled } from 'styled-components'
import Content from '~/components/Content/Content'
import DashboardInspectorWrapper from '~/components/dashboards/DashboardInspectorWrapper/DashboardInspectorWrapper'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import Capture from '~/components/misc/Capture/Capture'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { getConfigDashboardContent } from '~/helpers/getActiveFiltersConfig'
import withModalAddWidget from '~/helpers/modal/withModalAddWidget'
import withModalCreateSharedDashboard from '~/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard'
import { observer } from '~/helpers/mst'
import CreateDashboard from './DashboardSelector/CreateDashboard'
import DashboardSelector from './DashboardSelector/DashboardSelector'
import AddModuleDrawer from './DashboardsGrid/AddModuleDrawer/AddModuleDrawer'
import DashboardsGrid from './DashboardsGrid/DashboardsGrid'
import EmptyDashboardPage from './EmptyDashboard/EmptyDashboardPage'

const LeftCol = styled.div`
  display: block;
  width: 218px;
`

const RightCol = styled.div`
  display: block;
  flex-grow: 1;
  margin: 0px 0px 0px 12px;
`

const renderActiveFilters = (selectedDashboard, router, offset) => {
  return (
    <>
      {selectedDashboard && selectedDashboard.widgets && selectedDashboard.widgets.length > 0 && (
        <RightCol>
          <div style={offset ? { position: 'relative' } : {}}>
            <MntrActiveFilters config={getConfigDashboardContent()} />
          </div>
        </RightCol>
      )}
    </>
  )
}

const DashboardContent = ({
  appStore: {
    monitoring,
    dashboards,
    dashboards: {
      isLoading,
      list: dashboardsList,
      selected: selectedDashboard,
      createSharedDashboard,
      openAdd,
      dragLock,
      resetShared,
      sharedDashboardKey,
    },
    viewport,
    viewport: { isMobile, isTabletBig },
    account,
    megalist,
    notification,
  },
}) => {
  const router = useRouter()
  const actions = selectedDashboard
    ? [
        {
          disabled: selectedDashboard.isLoading,
          icon: 'share',
          beforeOpen: () => {
            createSharedDashboard()
          },
          ...(isTabletBig && { tooltip: t`Share` }),
          ...(!isTabletBig && { label: t`Share` }),
          ...withModalCreateSharedDashboard({
            addNotification: notification?.add,
            resetShared: resetShared,
            sharedDashboardKey: sharedDashboardKey,
          }),
        },
        {
          bg: 'secondary',
          icon: 'add',
          beforeOpen: () => {
            openAdd({ type: 'empty' })
          },
          ...(isTabletBig && { tooltip: t`Add Widget` }),
          ...(!isTabletBig && { label: t`Add Widget` }),
          ...withModalAddWidget(false),
        },
      ]
    : []

  return (
    <>
      {account.user.isActiveUser && account.isLoaded && (
        <Content>
          <>
            {isMobile && (
              <>
                <Flex style={{ marginRight: 10 }} flexDirection="column" gap={2}>
                  {dashboardsList && dashboardsList.length > 0 && (
                    <DashboardSelector
                      account={account}
                      dashboards={dashboards}
                      viewport={viewport}
                    />
                  )}
                  {!isLoading && dashboardsList && dashboardsList.length === 0 && (
                    <Box>
                      <CreateDashboard dashboards={dashboards} fullWidth />
                    </Box>
                  )}
                  {renderActiveFilters(selectedDashboard, router)}
                </Flex>
              </>
            )}
            {!isMobile && (
              <Flex centerY>
                <LeftCol>
                  {dashboardsList && dashboardsList.length > 0 && (
                    <DashboardSelector
                      account={account}
                      dashboards={dashboards}
                      viewport={viewport}
                    />
                  )}
                  {!isLoading && dashboardsList && dashboardsList.length === 0 && (
                    <Box>
                      <CreateDashboard dashboards={dashboards} />
                    </Box>
                  )}
                </LeftCol>
                {renderActiveFilters(selectedDashboard, router, true)}

                {selectedDashboard &&
                  !selectedDashboard.is_read_only &&
                  selectedDashboard.widgets &&
                  selectedDashboard.widgets.length > 0 && (
                    <Flex flexGrow="0" gap={2} mx={2}>
                      <Capture
                        disabled={selectedDashboard.isLoading}
                        exportFilenameLabel="dashboard"
                        imageFilenamePrefix="widget"
                      />
                      {actions.map((button, key) => {
                        return <MntrButton key={key} {...button} />
                      })}
                    </Flex>
                  )}
              </Flex>
            )}
            {selectedDashboard && (
              <DashboardsGrid
                id={selectedDashboard && selectedDashboard.id}
                layout={selectedDashboard.layout}
                selected={selectedDashboard}
                onLayoutChange={(layout) => selectedDashboard.setLayout(layout)}
                dragLock={dragLock}
                viewport={viewport}
              />
            )}
            {selectedDashboard &&
              selectedDashboard.widgets &&
              selectedDashboard.widgets.length === 0 && (
                <Box px={3}>
                  <EmptyDashboardPage onSelect={(model) => openAdd({ type: model })} />
                </Box>
              )}
            {!megalist.isOpen && <AddModuleDrawer />}
            {monitoring.activeFeedMapItem && <DashboardInspectorWrapper />}
          </>{' '}
        </Content>
      )}
    </>
  )
}

export default observer(DashboardContent)
