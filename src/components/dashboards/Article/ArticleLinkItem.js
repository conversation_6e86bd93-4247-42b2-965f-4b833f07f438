import color from 'color'
import get from 'lodash/get'
import { css, styled } from 'styled-components'
import categoryTypes from '~/constants/categoryTypes'
import formatDate from '~/helpers/date/format'
import { observer } from '~/helpers/mst'
import { Link, routerPush, routerReplace } from '~/helpers/router'

const StyledLink = styled(Link)`
  padding: 7px;
  word-break: break-word;
  background: ${({ isActive, theme }) => (isActive ? '#e4e4e4' : theme.paper.background)};
  width: 100%;
  white-space: normal;
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: 3px;
  text-decoration: none;
  transition: all 100ms ease-in;
  border-left: 2px solid ${({ categoryColor }) => categoryColor};

  ${({ theme, categoryColor }) => {
    const r = parseInt(categoryColor.substring(1, 3), 16)
    const g = parseInt(categoryColor.substring(3, 5), 16)
    const b = parseInt(categoryColor.substring(5, 7), 16)

    return css`
      box-shadow: inset ${theme.paper.boxShadowInset} rgba(${r} ${g} ${b} / 5%);
    `
  }}

  &:hover {
    cursor: pointer;
    background: ${(props) =>
      props.isActive
        ? '#e4e4e4'
        : color(props.categoryColor).alpha(0.02).toString() || 'rgb(240, 240, 240)'};
  }

  & * {
    text-decoration: none;
  }

  ${({ disabled }) => {
    return (
      disabled &&
      css`
        pointer-events: none;
      `
    )
  }}
`

const Title = styled.h2`
  font-weight: 500;
  font-size: 14px;
  margin: 0;
  padding: 0;
  text-decoration: none;
  line-height: 1.22;
  margin-bottom: 4px;
  color: ${({ theme }) => theme.colors.black};
`

const Published = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.mediumGrey};
`

const ArticleLinkItem = ({
  appStore: {
    monitoring: { setActiveFeed, feedMap },
    router: { makeRouteWithQuery },
  },
  item,
  feedId,
  disabled,
  closeModal,
  openInspector,
}) => {
  const feedItem = feedMap.get(feedId)
  const query = feedItem.filter.labels?.query?.value

  const as = {
    pathname: `/article/${item.article_id}/${item.token}`,
    query: {
      ...(item.topic_monitor ? { topic_monitor: item.topic_monitor.id } : {}),
      ...(query ? { query } : {}),
    },
  }

  const onClick = (e) => {
    if (e.ctrlKey || e.metaKey) {
      return true
    }
    e.preventDefault()
    setActiveFeed(feedId)
    feedItem.openArticle()

    const currRoute = makeRouteWithQuery()

    routerReplace(currRoute, currRoute, { shallow: true })
    routerPush(currRoute, as, { shallow: true })

    closeModal?.()
  }

  const format =
    get(item, 'news_source.category.category_type.id') === categoryTypes.CATEGORY_TYPE_OFFLINE_MEDIA
      ? 'd. M. yyyy'
      : 'd. M. yyyy HH:mm'

  return (
    <StyledLink
      naked
      disabled={disabled}
      categoryColor={get(item, 'news_source.category.category_type.color')}
      href={as}
      {...(openInspector && { onClick })}
      {...(!openInspector && { target: '_blank' })} // no inspector opening, so open in new tab
    >
      <Title>{item.title}</Title>
      <Published>
        {formatDate(item?.published, format)}, {item?.news_source.name}
      </Published>
    </StyledLink>
  )
}

export default observer(ArticleLinkItem)
