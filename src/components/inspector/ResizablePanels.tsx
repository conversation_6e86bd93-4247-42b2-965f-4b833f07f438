import { ReactElement } from 'react'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import { styled } from 'styled-components'
import { observer } from '~/helpers/msts'

const minWidth = 500

const StyledResizeHandle = styled(PanelResizeHandle)`
  box-sizing: border-box;
  margin: 0 -5px;
  width: 11px;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  background: black;
  background-clip: padding-box;
  opacity: 0.2;
  cursor: col-resize;

  &:hover {
    border-color: rgba(0, 0, 0, 0.5);
    transition: all 2s ease;
  }
`

const ResizablePanels = observer<{ left: ReactElement; right: ReactElement }>(
  ({ appStore: { viewport }, left, right }) => {
    return (
      <PanelGroup autoSaveId="inspector" direction="horizontal">
        <Panel
          defaultSize={60}
          minSize={(100 * minWidth) / viewport.width}
          maxSize={(100 * (viewport.width - minWidth)) / viewport.width}
        >
          {left}
        </Panel>
        <StyledResizeHandle />
        <Panel defaultSize={40}>{right}</Panel>
      </PanelGroup>
    )
  },
)

export default ResizablePanels
