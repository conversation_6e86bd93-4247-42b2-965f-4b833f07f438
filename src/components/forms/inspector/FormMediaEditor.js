import { t } from '@lingui/core/macro'
import { useEffect } from 'react'
import { Field, Form, FormSpy } from 'react-final-form'
import { css, styled } from 'styled-components'
import { Box, Flex } from '~/components/misc/Mntr'

import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'
import { parseValueToSeconds } from '~/helpers/parseDurationToSeconds'

const validateTimeFormat = (value) => {
  // Define a regular expression that matches the desired format 00:00:00
  const regex = /^([0-1]\d|2[0-3]):([0-5]\d):([0-5]\d)$/

  // Test the input value against the regular expression
  return regex.test(value)
}

const ControlsWrapper = styled.div`
  ${({ isVideo }) =>
    isVideo
      ? css`
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(17rem, 1fr));
        `
      : css`
          display: flex;
        `}

  align-items: start;
  justify-content: space-between;
  gap: 1rem;

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    ${({ isVideo }) =>
      isVideo
        ? css`
            grid-template-columns: repeat(auto-fit, minmax(16rem, 1fr));
          `
        : css`
            flex-direction: column;
          `}
  }
`
const StyledFlex = styled(Flex)`
  gap: 1rem;
`

const FormMediaEditor = ({
  onChange,
  initialValues,
  children,
  isVideo = true,
  durationTime,
  mediaEditor,
  isTvr = false,
}) => {
  useEffect(() => {
    mediaEditor.setStartTime(0)
    mediaEditor.setEndTime(durationTime)

    return () => {
      mediaEditor.setStartTime(0)
      mediaEditor.setEndTime(0)
    }
  }, [mediaEditor, durationTime])
  return (
    <Form
      onSubmit={(e) => e.preventDefault()}
      initialValues={initialValues}
      validate={(values) => {
        const { startTime, endTime } = values
        const errors = {}
        if (!validateTimeFormat(startTime)) {
          errors.startTime = t`Invalid time format. Enter hh:mm:ss`
        }
        if (!validateTimeFormat(endTime)) {
          errors.endTime = t`Invalid time format. Enter hh:mm:ss`
        }
        if (parseValueToSeconds(startTime) > parseValueToSeconds(endTime)) {
          errors.startTime = t`Start time must be lower than end time`
        }
        if (parseValueToSeconds(startTime) > durationTime) {
          errors.startTime = t`Time must not exceed media length`
        }
        if (parseValueToSeconds(endTime) > durationTime) {
          errors.endTime = t`Time must not exceed media length`
        }
        return errors
      }}
      render={({ handleSubmit, submitting, valid }) => {
        return (
          <form onSubmit={handleSubmit} style={{ width: '100%' }}>
            <ControlsWrapper isVideo={isVideo}>
              <FormSpy
                onChange={(spy) => {
                  // skip if values are not changed
                  if (!spy.dirty) {
                    return null
                  }

                  const { startTime, endTime } = spy.values

                  if (spy.valid) {
                    const start = parseValueToSeconds(startTime)
                    const end = parseValueToSeconds(endTime)
                    onChange([start, end])
                    mediaEditor.setStartTime(start)
                    mediaEditor.setEndTime(end)
                  }
                }}
              />
              <Box pt={4}>
                <StyledFlex flexWrap="nowrap" alignItems="start">
                  <Box width={[1, '5rem']}>
                    <Field label={t`Cut from`} component={MntrTextFieldAdapter} name="startTime" />
                  </Box>
                  <Box width={[1, '5rem']}>
                    <Field label={t`Cut to`} component={MntrTextFieldAdapter} name="endTime" />
                  </Box>
                  <Box width={[1, '5rem']}>
                    <Field
                      label={t`Clip duration`}
                      component={MntrTextFieldAdapter}
                      name="cutLength"
                      disableUnderline
                      readOnly
                      style={{ background: 'transparent' }}
                    />
                  </Box>
                </StyledFlex>
              </Box>

              <StyledFlex mt={[1, '2.85rem']}>
                {children}
                <Box textAlign={'end'} pl={isVideo ? 1 : 0} flexShrink={0}>
                  <MntrButton
                    onClick={() => mediaEditor.downloadTrimmed(isVideo, isTvr)}
                    placement="top-center"
                    label={t`Download clip`}
                    bg="secondary"
                    disabled={submitting || !valid}
                  />
                </Box>
              </StyledFlex>
            </ControlsWrapper>
          </form>
        )
      }}
    />
  )
}

export default observer(FormMediaEditor)
