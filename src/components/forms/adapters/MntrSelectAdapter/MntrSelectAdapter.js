import find from 'lodash/find'
import { useState } from 'react'
import { styled } from 'styled-components'
import { StyledErrorMessage } from '~/components/forms/adapters/shared/StyledInput'
import StyledInputLabel from '~/components/forms/adapters/shared/StyledInputLabel'
import Icon from '~/components/misc/Icon/Icon'
import InfoTooltip from '~/components/misc/InfoTooltip/InfoTooltip'
import { Box, Flex, FormControl } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { StyledSelect } from '~/components/misc/MntrButton/style/StyledSelectButton'
import withMenuPopup from '~/helpers/withMenuPopup/withMenuPopup'

const StyledIconWrapper = styled(Box)`
  top: calc(50% - 11px);
  right: 0;
  position: absolute;
  pointer-events: none;
`

const StyledLabel = styled.div`
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  min-height: 23px;
  padding-top: 7px;
`

const MntrSelectAdapter = ({
  color,
  displayEmpty,
  errorText,
  icon,
  iconColor = 'lightGrey',
  input,
  inputCustom: InputCustom,
  inputProps,
  items,
  label,
  menuProps,
  meta,
  name,
  onChange,
  placeholderColor,
  shrinkLabel,
  tooltip,
  transparentBg = false,
  fontSize = 16,
  withSearch = false,
  ...rest
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const native = 'ontouchstart' in window
  const errorLabel = meta.error || meta.submitError || errorText
  const error = !!errorLabel || meta.submitFailed
  const selectedValue = inputProps?.value || input.value

  const renderValue = (value) => {
    return find(items, ['value', value])?.label || 'ㅤ'
  }

  return (
    <FormControl error={error} fullWidth>
      <Flex width={1}>
        {icon && (
          <Flex
            style={{
              position: 'absolute',
              top: -5,
            }}
          >
            <Icon color={iconColor}>{icon}</Icon>
          </Flex>
        )}
        <Flex flexDirection="column" width={1} {...(icon && { ml: 5 })}>
          {label && (
            <StyledInputLabel
              error={error}
              id={`${name ?? input.name}-label`}
              icon={icon}
              iconColor={iconColor}
              label={label}
              shrink={shrinkLabel}
            />
          )}
          <Flex>
            {native ? (
              <StyledSelect
                {...input}
                {...inputProps}
                {...rest}
                onChange={(e) => {
                  if (onChange) {
                    return onChange(e.target.value)
                  }
                  input?.onChange(e.target.value)
                }}
              >
                {items.map((item, index) => {
                  return (
                    <option key={index} disabled={item.disabled} value={item.value}>
                      {item.label}
                    </option>
                  )
                })}
              </StyledSelect>
            ) : (
              <Box width={1} position="relative">
                <MntrButton
                  {...input}
                  {...inputProps}
                  {...rest}
                  transparentBg={transparentBg}
                  fontSize={fontSize}
                  isSelectButton
                  label={
                    <StyledLabel data-e2e="replace_content">
                      {renderValue(selectedValue)}
                    </StyledLabel>
                  }
                  displayEmpty={displayEmpty}
                  fullWidth
                  native={native}
                  icon={icon ? 1 : 0}
                  labelId={`${name ?? input.name}-label`}
                  onOpen={() => setIsOpen(true)}
                  onClose={() => setIsOpen(false)}
                  endIconElement={
                    <StyledIconWrapper>
                      <Icon>{isOpen ? 'expand_less' : 'expand_more'}</Icon>
                    </StyledIconWrapper>
                  }
                  popupPlacement="bottom-start"
                  {...withMenuPopup({
                    onChange: (item) => {
                      if (onChange) {
                        return onChange(item.value)
                      }
                      input?.onChange(item.value)
                    },
                    items: items.map((item) => {
                      return { ...item, selected: selectedValue === item.value }
                    }),
                    withSearch,
                  })}
                  zIndex={9000}
                  color={color}
                  placeholderColor={placeholderColor}
                  tooltip={null}
                />

                {/* Info Tooltip -> right side help icon */}
                {tooltip && (
                  <Box position="absolute" top="8px" right="24px">
                    <InfoTooltip tooltip={tooltip} size={26} />
                  </Box>
                )}
              </Box>
            )}
          </Flex>
        </Flex>
      </Flex>

      {error && errorLabel && (
        <StyledErrorMessage className="error-message">{errorLabel}</StyledErrorMessage>
      )}
    </FormControl>
  )
}

export default MntrSelectAdapter
