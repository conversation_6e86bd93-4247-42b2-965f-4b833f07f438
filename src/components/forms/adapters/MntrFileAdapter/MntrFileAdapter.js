import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Text } from '~/components/misc/Mntr'

const StyledBox = styled(Box)`
  width: 100%;
  padding: 2px;
  position: relative;

  & .dropzone {
    width: 100%;
    height: ${({ height }) => height}px;
    background: ${({ theme }) => theme.paper.background};
    color: ${({ theme }) => theme.colors.black};
    justify-content: center;
    align-items: center;
    border-radius: 5px !important;
    border: 3px dashed ${({ theme }) => theme.paper.border};

    & span {
      font-size: 15px;
    }
  }
`
const StyledPlaceholder = styled(Box)`
  font-size: 15px;
  display: flex;
  align-items: center;
  & i {
    margin-right: 6px;
    margin-left: 6px;
  }
`

const Hidden = styled(Box)`
  display: none;
`

const MntrFileAdapter = ({
  input,
  name,
  renderLabel,
  placeholderIcon,
  placeholder,
  placeholderExtensions,
  placeholderResolution,
  height = 200,
  ...rest
}) => {
  return (
    <StyledBox height={height}>
      {renderLabel && (
        <label htmlFor={`${name ?? input.name}-input`}>
          <i className="dropzone">
            <StyledPlaceholder mb={2}>
              <Icon>{placeholderIcon || 'file_upload'}</Icon>
              {placeholder !== null && (placeholder || <Trans>Select Image</Trans>)}
            </StyledPlaceholder>

            {placeholderExtensions && (
              <Box mt={1}>
                <Text color="mediumGrey">[{placeholderExtensions.toUpperCase()}]</Text>
              </Box>
            )}

            {placeholderResolution && (
              <Box mt={0}>
                <Text color="mediumGrey">
                  <Trans>Recommended resolution</Trans>: {placeholderResolution}
                </Text>
              </Box>
            )}
          </i>
        </label>
      )}

      <Hidden>
        <input
          {...input}
          {...rest}
          id={`${name ?? input.name}-input`}
          name={name ?? input.name}
          type="file"
        />
      </Hidden>
    </StyledBox>
  )
}

export default MntrFileAdapter
