import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { endOfYear, format, isValid, parse, startOfDay, startOfYear } from 'date-fns'
import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { useEffect, useState } from 'react'
import { DayPicker } from 'react-day-picker'
import { styled } from 'styled-components'

import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { StyledErrorMessage } from '~/components/forms/adapters/shared/StyledInput'
import Icon from '~/components/misc/Icon/Icon'
import { Box } from '~/components/misc/Mntr'
import Popper from '~/components/misc/Mntr/Popper'
import { WEEK_STARTS_ON } from '~/constants'
import { getWeekDays } from '~/helpers/date/localizedStrings'
import { dateFnsLocaleData } from '~/helpers/i18n'
import { observer } from '~/helpers/mst'

export const DATE_FORMAT = 'dd.MM.yyyy'

const today = startOfDay(new Date())

const PopperContent = styled.div`
  background: ${({ theme }) => theme.popper.background};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  box-shadow: ${({ theme }) => theme.popper.boxShadow};
`

const validateDateBounds = (date, minDate, maxDate) => {
  const defaultMinDate = startOfYear(new Date(1990, 0))
  const effectiveMinDate = minDate || defaultMinDate
  const effectiveMaxDate = maxDate || endOfYear(today)

  if (effectiveMinDate && isValid(effectiveMinDate) && date < startOfDay(effectiveMinDate)) {
    return t`Date cannot be earlier than ${format(effectiveMinDate, DATE_FORMAT)}`
  }
  if (effectiveMaxDate && isValid(effectiveMaxDate) && date > startOfDay(effectiveMaxDate)) {
    return t`Date cannot be later than ${format(effectiveMaxDate, DATE_FORMAT)}`
  }
  return null
}

const parseDateString = (value, DATE_FORMAT, today) => {
  const fullDatePattern = /^\d{1,2}\.\d{1,2}\.\d{4}$/

  if (!fullDatePattern.test(value)) {
    return { isFullDate: false, parsedDate: null }
  }

  const parsedDate = parse(value, DATE_FORMAT, today)

  return {
    isFullDate: isValid(parsedDate),
    parsedDate,
  }
}

const MntrDatepickerAdapter = ({
  appStore: { account },
  input,
  inputProps,
  label,
  meta,
  minDate,
  maxDate,
  onSubmit,
  onFocus,
  zIndex = 4900,
  ...rest
}) => {
  const { name, value, placeholder } = input
  const native = 'ontouchstart' in window
  const [selectedDate, setSelectedDate] = useState(null)
  const [textInputValue, setTextInputValue] = useState(value || '')
  const [month, setMonth] = useState(null)
  const [visibleErrors, setVisibleErrors] = useState(false)
  const [validationError, setValidationError] = useState(null)
  const [isDropdownVisible, setIsDropdownVisible] = useState(false)

  const errorLabel = validationError || meta.error || meta.submitError
  const error = !!errorLabel && visibleErrors

  const invalidDateFormatText = t`Invalid date format. Expected format is ${format(
    startOfYear(today),
    DATE_FORMAT,
  )}`

  useEffect(() => {
    const newTimeoutId = setTimeout(() => {
      const parsedDate = value ? (isValid(value) ? value : parse(value, DATE_FORMAT, today)) : null

      setSelectedDate(parsedDate)
      setMonth(parsedDate)
      setTextInputValue(value || '')
    }, 1000)

    return () => {
      clearTimeout(newTimeoutId)
    }
  }, [value])

  const handleDayPickerSelect = (date) => {
    if (date) {
      const validationError = validateDateBounds(date, minDate, maxDate)

      if (!validationError) {
        const formattedDate = format(date, DATE_FORMAT)
        setSelectedDate(date)
        setTextInputValue(formattedDate)
        onSubmit(formattedDate)
        setValidationError(null)
      } else {
        setValidationError(validationError)
        setVisibleErrors(true)
      }
    }
  }

  const handleManualInput = (e) => {
    const inputValue = e.target.value
    setTextInputValue(inputValue)
    setValidationError(null)

    const { isFullDate, parsedDate } = parseDateString(inputValue, DATE_FORMAT, today)
    if (!inputValue || inputValue === '' || inputValue === null || inputValue === undefined) {
      setSelectedDate(null)
      setValidationError(null)
      return onSubmit(null)
    } else if (isFullDate) {
      if (isValid(parsedDate)) {
        const validationError = validateDateBounds(parsedDate, minDate, maxDate)

        if (!validationError) {
          setSelectedDate(parsedDate)
          setMonth(parsedDate)
          setValidationError(null)
          return onSubmit(format(parsedDate, DATE_FORMAT))
        } else {
          setValidationError(validationError)
          setVisibleErrors(true)
        }
      } else {
        setValidationError(invalidDateFormatText)
        setVisibleErrors(true)
      }
    }
  }

  const handleKeyDown = (e) => {
    if (e.key !== 'Enter') return
    const inputValue = e.target.value

    if (!inputValue || inputValue === '' || inputValue === null || inputValue === undefined) {
      setSelectedDate(null)
      setValidationError(null)
      return onSubmit(null)
    }

    const { isFullDate, parsedDate } = parseDateString(inputValue, DATE_FORMAT, today)

    if (!isFullDate || !isValid(parsedDate)) {
      setValidationError(invalidDateFormatText)
      setVisibleErrors(true)
      return e.preventDefault()
    }

    const error = validateDateBounds(parsedDate, minDate, maxDate)
    if (error) {
      setValidationError(error)
      setVisibleErrors(true)
      return e.preventDefault()
    }

    setSelectedDate(parsedDate)
    setValidationError(null)
    onSubmit(format(parsedDate, DATE_FORMAT))
  }

  const handleBlur = () => {
    const { isFullDate, parsedDate } = parseDateString(textInputValue, DATE_FORMAT, today)

    if (isFullDate) {
      if (isValid(parsedDate)) {
        const validationError = validateDateBounds(parsedDate, minDate, maxDate)

        if (!validationError) {
          setSelectedDate(parsedDate)
          onSubmit(format(parsedDate, DATE_FORMAT))
          setValidationError(null)
        } else {
          setValidationError(validationError)
          setVisibleErrors(true)
        }
      } else {
        setValidationError(invalidDateFormatText)
        setVisibleErrors(true)
      }
    } else {
      if (textInputValue) setValidationError(invalidDateFormatText)
      setVisibleErrors(true)
    }
  }

  const getNativeValue = (val) => {
    if (!val) return ''
    try {
      const parsedDate = parse(val, 'dd.MM.yyyy', new Date())
      if (isValid(parsedDate)) {
        return format(parsedDate, 'yyyy-MM-dd')
      }
      return ''
    } catch {
      return ''
    }
  }

  return (
    <Box position="relative">
      {native ? (
        <MntrTextFieldAdapter
          {...input}
          {...inputProps}
          {...rest}
          fullWidth
          type="date"
          meta={meta}
          label={label}
          onChange={(e) => {
            const val = e.target.value
            if (!val) {
              onSubmit(null)
              return
            }
            const date = new Date(val)
            if (isValid(date)) {
              onSubmit(format(date, 'dd.MM.yyyy'))
            }
          }}
          value={getNativeValue(value)}
          onFocus={() => {
            onFocus && onFocus()
            setVisibleErrors(false)
          }}
        />
      ) : (
        <PopupState variant="popover" popupId="datepicker-popup">
          {(popupState) => {
            return (
              <>
                <MntrTextFieldAdapter
                  input={{
                    ...input,
                  }}
                  {...inputProps}
                  {...rest}
                  fullWidth
                  autoComplete="off"
                  label={label}
                  name={name}
                  meta={meta}
                  value={textInputValue}
                  format={DATE_FORMAT}
                  placeholder={placeholder || t`Date`}
                  onFocus={() => {
                    setIsDropdownVisible(true)
                    onFocus && onFocus()
                    setVisibleErrors(false)
                  }}
                  onChange={handleManualInput}
                  onKeyDown={handleKeyDown}
                  onBlur={handleBlur}
                  {...bindTrigger(popupState)}
                  errorText={validationError}
                />
                {isDropdownVisible && popupState.isOpen ? (
                  <Popper
                    disablePortal={false}
                    {...bindPopover(popupState)}
                    transition
                    placement="bottom-start"
                    style={{ zIndex: zIndex }}
                  >
                    <PopperContent className="popper-menu">
                      <DayPicker
                        mode="single"
                        month={month}
                        onMonthChange={(month) => setMonth(month)}
                        selected={selectedDate}
                        onSelect={(date) => {
                          handleDayPickerSelect(date)
                          popupState.close()
                        }}
                        captionLayout="dropdown"
                        modifiers={{ disabled: { before: minDate, after: maxDate } }}
                        firstDayOfWeek={WEEK_STARTS_ON}
                        locale={dateFnsLocaleData[account.user.app_language]}
                        weekdaysShort={getWeekDays(true)}
                        startMonth={minDate ? startOfDay(minDate) : startOfYear(new Date(1990, 0))}
                        endMonth={maxDate}
                        components={{
                          Chevron: ({ orientation, ...props }) => {
                            const iconConfig = {
                              left: { name: 'arrow_left', size: 30 },
                              right: { name: 'arrow_right', size: 30 },
                              up: { name: 'keyboard_arrow_up', size: 20 },
                              down: { name: 'keyboard_arrow_down', size: 20 },
                            }

                            const { name, size } = iconConfig[orientation] || iconConfig.down

                            return (
                              <Icon {...props} size={size}>
                                {name}
                              </Icon>
                            )
                          },
                        }}
                        footer={
                          <button
                            onClick={(e) => {
                              e.preventDefault()
                              handleDayPickerSelect(today)
                              popupState.close()
                            }}
                            className="today-button"
                          >
                            <Trans>Today</Trans>
                          </button>
                        }
                      />
                    </PopperContent>
                  </Popper>
                ) : null}
              </>
            )
          }}
        </PopupState>
      )}
      {error ? (
        <StyledErrorMessage className="error-message">{errorLabel}</StyledErrorMessage>
      ) : null}
    </Box>
  )
}

export default observer(MntrDatepickerAdapter)
