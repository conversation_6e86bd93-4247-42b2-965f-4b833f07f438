import type { <PERSON>a, StoryObj } from '@storybook/react'
import { FormApi } from 'final-form'
import { Field, FormProps } from 'react-final-form'
import { Box } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import MntrDatepickerAdapter from './MntrDatepickerAdapter'

const meta = {
  title: 'Datepicker',
  component: MntrDatepickerAdapter,
  render({ ...args }) {
    const formSchema: IFormSchemaItem[] = [
      {
        customComponent: ({ form }) => {
          return (
            <Field
              label="Date"
              name="date"
              component={MntrDatepickerAdapter}
              onSubmit={(day: string) => {
                form?.change('date', day)
              }}
              {...args}
            />
          )
        },
      },
    ]

    const onSubmit = (values: FormProps['FormValues'], form: FormApi) => {
      console.log('## submit', values, form)
    }

    const onChange = (form: FormApi) => {
      console.log('## change', form)
    }

    const onCancel = () => {
      console.log('## cancel')
    }

    return (
      <Box style={{ width: '100%', maxWidth: '800px', margin: '50px auto' }} height="600px" p={3}>
        <h1>Datepicker Example</h1>
        <MntrForm schema={formSchema} onSubmit={onSubmit} onChange={onChange} onCancel={onCancel} />
      </Box>
    )
  },
} satisfies Meta<typeof MntrDatepickerAdapter>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}
