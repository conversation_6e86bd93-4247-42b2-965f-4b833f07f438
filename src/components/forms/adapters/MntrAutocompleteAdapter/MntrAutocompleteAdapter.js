import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'
import { styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { FormControl } from '~/components/misc/Mntr'
import Popper from '~/components/misc/Mntr/Popper'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import HandleEscapeKey from '~/helpers/hooks/modules/HandleEscapeKey'

const PopperContent = styled.div`
  background: ${({ theme }) => theme.popper.background};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  box-shadow: ${({ theme }) => theme.popper.boxShadow};
  min-width: ${({ popupWidth }) => popupWidth || 160}px;

  & .menu,
  & .menu-heading {
    color: ${({ theme }) => theme.popper.heading};
  }

  & .form-label {
    color: ${({ theme }) => theme.popper.formLabel};
  }

  & textarea,
  & input {
    color: ${({ theme }) => theme.popper.formLabel};
    &::placeholder {
      color: ${({ theme }) => theme.popper.formLabel};
      opacity: 0.8;
    }
  }
`

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: ${({ zIndex }) => zIndex};
`

const OverlayPortal = ({ close, isOpen, zIndex }) =>
  isOpen && <Overlay onClick={close} zIndex={zIndex}></Overlay>

const MntrAutocompleteAdapter = ({
  displayEmpty,
  icon,
  input,
  inputProps,
  inputCustom: InputCustom,
  options,
  label,
  meta,
  menuProps,
  name,
  errorText,
  shrinkLabel,
  zIndex = 3001,
  renderValue = (value) => {
    return value || ''
  },
  renderOption = (value) => {
    return value
  },
  onChange,
  onInputChange,
  emptyLabel = '-----',
  ...rest
}) => {
  const portalContainer = document.getElementById('portal-overlay')
  const [menuItems, setMenuItems] = useState([])

  useEffect(() => {
    if (options.length > 0) {
      const suggestions = options.map((value) => {
        return {
          hoverVariant: 'light',
          label: renderOption(value),
          onClick: () => {
            input.onChange(value)
          },
        }
      })

      setMenuItems(suggestions)
    } else setMenuItems([{ label: renderOption() || emptyLabel }])
  }, [input, options, onChange, renderOption, emptyLabel])

  return (
    <PopupState variant="popover" popupId="combobox">
      {(popupState) => {
        let props = {}

        if (popupState.isOpen && menuItems.length > 0) {
          props.bg = 'primary'
        }

        return (
          <>
            {createPortal(
              <OverlayPortal
                close={() => {
                  popupState.close()
                }}
                isOpen={popupState.isOpen && menuItems.length > 0}
                zIndex={zIndex}
              />,
              portalContainer,
            )}
            {popupState.isOpen && menuItems.length > 0 && (
              <HandleEscapeKey onCancel={popupState.close} />
            )}

            <FormControl fullWidth>
              <MntrTextFieldAdapter
                input={input}
                {...input}
                {...inputProps}
                {...rest}
                {...bindTrigger(popupState)}
                label={label}
                autoComplete="off"
                meta={meta}
                onChange={(el) => {
                  const value = el.currentTarget.value

                  onInputChange?.(value)
                  input.onChange(value)
                }}
                value={renderValue(input.value)}
              />
            </FormControl>
            {popupState.isOpen && menuItems.length > 0 && (
              <Popper
                {...bindPopover(popupState)}
                transition
                placement="bottom-start"
                style={{ zIndex }}
                transformOrigin={'100% 0'}
              >
                <PopperContent className="popper-menu">
                  <MntrMenu
                    closePopup={() => {
                      popupState.close()
                    }}
                    menuItems={menuItems}
                    popupPlacement="bottom-start"
                  />
                </PopperContent>
              </Popper>
            )}
          </>
        )
      }}
    </PopupState>
  )
}

export default MntrAutocompleteAdapter
