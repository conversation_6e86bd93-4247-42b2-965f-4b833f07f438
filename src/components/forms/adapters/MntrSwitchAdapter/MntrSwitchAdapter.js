import { css, styled } from 'styled-components'
import { Flex, FormControlLabel } from '~/components/misc/Mntr'

const StyledInput = styled.input`
  height: 0;
  width: 0;
  visibility: hidden;
`

export const StyledSwitch = styled.div`
  cursor: pointer;
  width: 40px;
  height: 20px;
  background: ${({ theme }) => theme.switch.inactive.track};
  display: block;
  border-radius: 100px;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: ${({ theme }) => theme.switch.active.thumb};
    border-radius: 90px;
    transition: 0.3s;
  }

  &:active:after {
    width: 20px;
  }

  ${({ isChecked }) =>
    isChecked &&
    css`
      background: ${({ theme }) => theme.switch.active.track};

      &:after {
        left: calc(100% - 2px);
        transform: translateX(-100%);
      }
    `}

  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.4;
    `}
`

const StyledLabel = styled.div`
  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.4;
    `}
`

const MntrSwitchAdapter = ({ input, disabled, label, name, meta, errorText, bg, ...rest }) => {
  return (
    <FormControlLabel
      control={
        <Flex mr={2}>
          <StyledInput
            type="checkbox"
            {...input}
            {...rest}
            disabled={disabled}
            checked={input.value}
            name={name ?? input.name}
          />
          <StyledSwitch isChecked={input.value} disabled={disabled} />
        </Flex>
      }
      label={<StyledLabel disabled={disabled}>{label}</StyledLabel>}
    />
  )
}

export default MntrSwitchAdapter
