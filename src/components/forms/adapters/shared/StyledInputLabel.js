import { css, styled } from 'styled-components'

const StyledLabel = styled.span`
  font-family: ${({ theme }) => theme.fontFamily};
  color: ${({ theme }) => theme.colors.lightGrey};
  line-height: 1;
`

const StyledLabelWrapper = styled.label`
  color: ${({ theme }) => theme.label.primary} !important;
  font-size: 12px;
  margin-bottom: 4px;
  line-height: 1 !important;

  ${({ shrink }) =>
    shrink &&
    css`
      transform: none;
    `}
`

const StyledInputLabel = ({ icon, label, shrink = true, error, labelStyle, ...rest }) => {
  return (
    <StyledLabelWrapper {...rest} style={labelStyle} error={error} shrink={shrink}>
      <StyledLabel className="form-label">{label}</StyledLabel>
    </StyledLabelWrapper>
  )
}

export default StyledInputLabel
