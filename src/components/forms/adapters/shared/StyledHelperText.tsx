import { PropsWithChildren } from 'react'
import { styled } from 'styled-components'

type HelperTextProps = {
  error?: boolean
}

const HelperText = styled.p<HelperTextProps>`
  margin: 0;
  font-size: 0.75rem;
  margin-top: 3px;
  font-weight: 400;
  line-height: 1.66;
  color: ${({ error, theme }) => (error ? theme.colors.error : theme.colors.lightGrey)};

  a {
    color: ${({ error, theme }) => (error ? theme.colors.error : theme.colors.lightGrey)};
  }
`

const StyledHelperText = ({ error = true, children }: PropsWithChildren<HelperTextProps>) => {
  return <HelperText error={error}>{children}</HelperText>
}

export default StyledHelperText
