import TextareaAutosize from 'react-textarea-autosize'
import { css, styled } from 'styled-components'

export const baseInputStyle = css`
  box-sizing: border-box;
  line-height: 1;
  font-family: ${({ theme }) => theme.fontFamily};
  font-size: ${({ fontSize }) => (fontSize ? fontSize : '16')}px;
  color: ${({ color, theme }) => (color ? color : theme.colors.default)};
  padding: ${({ theme }) => theme.space[1]}px;
  border: none;
  border-radius: 5px 5px 0px 0px;
  background-color: ${({ theme }) => theme.colors.extraLightGrey};
  transition: all 0.15s ease-in-out;
  width: 100%;

  ${({ transparentBg }) =>
    transparentBg &&
    css`
      background-color: transparent;
    `}

  &:before {
    border-bottom-color: ${({ theme }) => theme.form.underline} !important;
  }

  &:disabled {
    color: ${({ theme, disabledColor }) =>
      disabledColor ? disabledColor : theme.colors.disabled} !important;
  }

  &:focus {
    border-bottom-color: ${({ theme }) => theme.colors.primary} !important;
  }

  &::placeholder {
    color: #adadad;
    opacity: 0.9;
  }

  ${({ placeholderColor }) =>
    placeholderColor &&
    css`
      &::placeholder {
        color: ${placeholderColor} !important;
        opacity: 1 !important;
      }
    `}

  ${({ textAlign }) =>
    textAlign &&
    css`
      text-align: ${textAlign};
    `}

  ${({ resize }) =>
    resize &&
    css`
      resize: ${resize};
    `}

  ${({ disableUnderline }) =>
    !disableUnderline
      ? css`
          border-bottom: 1px solid
            ${({ theme, borderBottomColor }) =>
              borderBottomColor ? borderBottomColor : theme.form.underline};
          &:before {
            border-bottom-color: ${({ theme }) => theme.textfield.primary};
          }
          &:hover:before {
            border-bottom-color: ${({ theme }) => theme.colors.primary};
          }
          &:after {
            border-bottom-color: ${({ theme }) => theme.colors.primary};
          }
          &:disabled:before {
            border-bottom-color: ${({ theme }) => theme.colors.mediumGrey};
          }
        `
      : css`
          border: none;
        `}

  &[type="time"] {
    &::-webkit-calendar-picker-indicator {
      filter: ${({ theme }) => theme.timepicker.indicator};
    }
  }
`

export const StyledInput = styled.input`
  ${baseInputStyle}
`

export const StyledInputTransparent = styled.input`
  ${baseInputStyle}
  background-color: transparent;
  border-bottom: none;
  margin-left: 8px;
`

export const StyledTextareaFixedHeight = styled.textarea`
  ${baseInputStyle}
  padding-top: 3px;
  padding-bottom: 3px;
  min-height: ${({ minHeight }) => (minHeight ? minHeight : '21px')};
  line-height: 1.2;
  resize: none;
`

export const StyledErrorMessage = styled.div`
  background: ${({ theme }) => theme.colors.error};
  color: #fff;
  font-size: 13px;
  padding: 4px 6px;
  display: inline-block;
  position: absolute;
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  line-height: 1.2;
  max-width: 350px;
  bottom: 4px;
  left: 4px;
  transform: translateY(100%);
  z-index: 2;

  &&::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 15px;
    margin-left: -10px;
    border: 6px solid transparent;
    border-bottom-color: ${({ theme }) => theme.colors.error};
  }
`

export const StyledTextArea = styled(TextareaAutosize)`
  ${baseInputStyle}
  min-height: 21px;
  line-height: 1.2;
  padding: 6px;
`

export default StyledInput
