import type { Meta, StoryObj } from '@storybook/react'
import StyledHelperText from './StyledHelperText'

const meta = {
  title: 'StyledHelperText',
  component: StyledHelperText,
} satisfies Meta<typeof StyledHelperText>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Default Helper Text',
    error: false,
  },
}

export const Error: Story = {
  args: {
    children: 'Error Helper Text',
  },
}

export const ErrorLink: Story = {
  args: {
    children: (
      <>
        Error Helper Text <a href="#">here</a>
      </>
    ),
  },
}

export const AnotherVariant: Story = {
  args: {
    children: 'Another Variant Helper Text',
    error: false,
  },
}

export const AnotherLinkVariant: Story = {
  args: {
    children: (
      <>
        Another Variant Helper Text <a href="#">here</a>
      </>
    ),
    error: false,
  },
}
