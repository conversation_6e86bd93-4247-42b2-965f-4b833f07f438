import { forwardRef } from 'react'
import { StyledErrorMessage } from '~/components/forms/adapters/shared/StyledInput'
import StyledInputLabel from '~/components/forms/adapters/shared/StyledInputLabel'
import { Box, FormControl } from '~/components/misc/Mntr'
import MntrEditor from '~/components/misc/MntrEditor/MntrEditor'

const MntrCmsEditorAdapter = forwardRef(
  (
    {
      allowActions,
      errorText,
      extraMenuBarItems,
      icon,
      input,
      label,
      meta,
      onChange,
      disabled,
      isPreview,
      setIsVisibleStickyFooterBar,
      onInputFocus,
      ...rest
    },
    ref,
  ) => {
    const metaErrorText = meta.error || meta.submitError
    const error = !!metaErrorText

    const handleChange = (value) => {
      input.onChange(value)
    }
    return (
      <FormControl error={error} {...input} fullWidth>
        <StyledInputLabel error={error} htmlFor={`${input.name}-rich`} icon={icon} label={label} />
        <Box position="relative" mb={2}>
          <MntrEditor
            ref={ref}
            onUpdateValue={(value) => {
              if (typeof onChange === 'function') {
                onChange(value)
              }
            }}
            onInputFocus={onInputFocus}
            setIsVisibleStickyFooterBar={setIsVisibleStickyFooterBar}
            onChange={handleChange}
            onSubmit={handleChange}
            id={`${input.name}-rich`}
            hasLabel={Boolean(label)}
            extraMenuBarItems={extraMenuBarItems}
            allowActions={allowActions}
            disabled={disabled}
            isPreview={isPreview}
            {...input}
            {...rest}
          />

          {/* Error Tooltip Message */}
          {error && (
            <StyledErrorMessage className="error-message">
              {errorText || metaErrorText}
            </StyledErrorMessage>
          )}
        </Box>
      </FormControl>
    )
  },
)

export default MntrCmsEditorAdapter
