import { useEffect, useState } from 'react'

import StyledHelperText from '~/components/forms/adapters/shared/StyledHelperText'
import { StyledErrorMessage, StyledInput } from '~/components/forms/adapters/shared/StyledInput'
import StyledInputLabel from '~/components/forms/adapters/shared/StyledInputLabel'
import Icon from '~/components/misc/Icon/Icon'
import InfoTooltip from '~/components/misc/InfoTooltip/InfoTooltip'
import { Box, Flex, FormControl, Text } from '~/components/misc/Mntr'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import useAutofocus from '~/helpers/hooks/useAutofocus'
import MntrTextAreaAdapter from './MntrTextAreaAdapter'

const MntrTextFieldAdapter = ({
  autoFocus,
  color,
  description,
  disableUnderline,
  errorText,
  helpText,
  helperText,
  icon,
  iconColor = 'lightGrey',
  input = {},
  label,
  labelStyle,
  maxRows,
  meta,
  minRows,
  multiline,
  name,
  placeholderColor,
  resize = 'none',
  rows,
  tooltip,
  transparentBg = false,
  onChange = input.onChange,
  ...rest
}) => {
  const inputRef = useAutofocus()
  const [showError, setShowError] = useState(true)
  const errorLabel = meta.error || meta.submitError || errorText
  const error = (!!errorLabel && !input.value) || meta.submitFailed

  useEffect(() => {
    setShowError(error)
  }, [error])

  const handleChange = (event) => {
    onChange(event) // Call the original form's onChange handler
    setShowError(false) // Hide error message on change
  }

  return (
    <FormControl error={error} fullWidth>
      <Flex width={1} gap={1}>
        {icon && (
          <Flex mr="2px" style={{ alignItems: label ? 'baseline' : 'center' }}>
            <Box mt={label ? '-5px' : 0}>
              <Icon color={iconColor}>{icon}</Icon>
            </Box>
          </Flex>
        )}
        <Flex flexDirection="column" width={1}>
          {/* Field Label */}
          {label && (
            <StyledInputLabel
              error={error}
              htmlFor={`${name ?? input.name}-input`}
              icon={icon}
              iconColor={iconColor}
              label={label}
              labelStyle={labelStyle}
            />
          )}

          <Box position="relative">
            {/* Field Input */}
            {multiline ? (
              <MntrTextAreaAdapter
                {...rest}
                ref={autoFocus ? inputRef : null}
                input={input}
                color={color}
                id={`${name ?? input.name}-input`}
                name={name ?? input.name}
                fullWidth
                icon={icon ? 1 : 0}
                disableUnderline={disableUnderline}
                placeholderColor={placeholderColor}
                resize={resize}
                transparentBg={transparentBg}
                minRows={minRows}
                maxRows={maxRows}
                onChange={handleChange} // Bind the handleChange
              />
            ) : (
              <StyledInput
                {...input}
                {...rest}
                ref={autoFocus ? inputRef : null}
                color={color}
                id={`${name ?? input.name}-input`}
                name={name ?? input.name}
                fullWidth
                icon={icon ? 1 : 0}
                disableUnderline={disableUnderline}
                placeholderColor={placeholderColor}
                resize={resize}
                transparentBg={transparentBg}
                onChange={handleChange} // Bind the handleChange
                onFocus={() => {
                  setShowError(false)

                  if (rest.onFocus) {
                    rest.onFocus()
                  }
                }}
              />
            )}

            {/* Error Tooltip Message */}
            {error && errorLabel && showError && (
              <StyledErrorMessage className="error-message">{errorLabel}</StyledErrorMessage>
            )}

            {/* Info Tooltip -> right side help icon */}
            {tooltip && (
              <Box position="absolute" top="8px" right="24px">
                <InfoTooltip tooltip={tooltip} size={26} />
              </Box>
            )}
          </Box>
        </Flex>
      </Flex>

      {/* Description */}
      {description && <MntrHint text={description} color="mediumGrey" background="transparent" />}

      {/* Help Text */}
      {helpText && !error && (
        <StyledHelperText>
          <Text color="lightGrey">{helpText}</Text>
        </StyledHelperText>
      )}

      {helperText && (
        <StyledHelperText error={false}>
          <Text color="lightGrey">{helperText}</Text>
        </StyledHelperText>
      )}
    </FormControl>
  )
}

export default MntrTextFieldAdapter
