import {
  StyledTextArea,
  StyledTextareaFixedHeight,
} from '~/components/forms/adapters/shared/StyledInput'
import useAutofocus from '~/helpers/hooks/useAutofocus'

const MntrTextAreaAdapter = ({
  autoFocus,
  icon,
  input,
  name,
  meta,
  color,
  errorText,
  helpText,
  labelStyle,
  placeholderColor,
  disableUnderline,
  resize = 'none',
  helperText,
  transparentBg = false,
  multiline,
  minRows,
  maxRows,
  useReactTextareaAutosize = true,
  ...rest
}) => {
  const inputRef = useAutofocus()

  // react-textarea-autosize can't be turned off by passing props -> render a normal textarea
  if (!useReactTextareaAutosize) {
    return <StyledTextareaFixedHeight {...input} {...rest} />
  }

  return (
    <StyledTextArea
      inputRef={autoFocus ? inputRef : null}
      {...input}
      {...rest}
      autoFocus={autoFocus}
      color={color}
      id={`${name ?? input.name}-input`}
      name={name ?? input.name}
      fullWidth
      icon={icon ? 1 : 0}
      disableUnderline={disableUnderline}
      placeholderColor={placeholderColor}
      resize={resize}
      transparentBg={transparentBg}
      minRows={minRows}
      maxRows={maxRows}
      useTextareaAutosize={false}
    />
  )
}

export default MntrTextAreaAdapter
