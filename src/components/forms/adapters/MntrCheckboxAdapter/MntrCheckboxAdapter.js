import { css, styled } from 'styled-components'
import StyledHelperText from '~/components/forms/adapters/shared/StyledHelperText'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, FormControl, FormControlLabel } from '~/components/misc/Mntr'

const StyledFormControlLabel = styled(FormControlLabel)`
  position: relative !important;
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.black};
`

const StyledLabel = styled.div`
  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.4;
    `}
`

const MntrCheckboxAdapter = ({
  bg = 'primary',
  errorText,
  helperText,
  input,
  label,
  meta = {},
  size = 22,
  disabled,
  checked = input.checked,
  iconFill = true,
  iconChecked = 'check_box',
  iconUnchecked = 'check_box_outline_blank',
  ...rest
}) => {
  const errorLabel = meta.error || meta.submitError || errorText
  const error = !!errorLabel

  // change icon if input is radio
  let iconOn = input?.type === 'radio' ? 'radio_button_checked' : iconChecked
  let iconOff = input?.type === 'radio' ? 'radio_button_unchecked' : iconUnchecked

  // radio buttons without fill
  const fill = input?.type === 'radio' ? false : iconFill

  return (
    <FormControl error={error}>
      <StyledFormControlLabel
        control={
          <Flex mr={1} alignItems="center">
            <input
              type="checkbox"
              {...input}
              {...rest}
              hidden
              disabled={disabled}
              checked={checked}
            />
            <Icon color={disabled ? 'disabled' : bg} size={size} disabled={disabled} fill={fill}>
              {checked ? iconOn : iconOff}
            </Icon>
          </Flex>
        }
        label={<StyledLabel disabled={disabled}>{label}</StyledLabel>}
      />
      {error && <StyledHelperText>{errorLabel}</StyledHelperText>}
      {helperText && <StyledHelperText error={false}>{helperText}</StyledHelperText>}
    </FormControl>
  )
}

export default MntrCheckboxAdapter
