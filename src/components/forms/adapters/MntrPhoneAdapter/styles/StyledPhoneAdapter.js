import { css, styled } from 'styled-components'
import { baseInputStyle } from '~/components/forms/adapters/shared/StyledInput'

export const StyledWrapper = styled.div`
  opacity: ${({ loading }) => (loading ? 0.5 : 1)};

  .react-international-phone-input-container {
    display: flex;
    gap: ${({ theme }) => theme.space[1]}px;
  }

  .react-international-phone-country-selector-button {
    display: flex;
    height: 36px;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: 0px;
    margin: 0;
    appearance: button;
    background: ${({ theme }) => theme.paper.background};
    border-radius: ${({ theme }) => theme.paper.borderRadius}px;
    cursor: pointer;
    text-transform: none;
    user-select: none;

    &:hover {
      background: ${({ theme }) => theme.paper.backgroundHover};
    }
  }

  .react-international-phone-country-selector-button__flag-emoji {
    margin: 0 4px;
  }

  .react-international-phone-flag-emoji {
    width: 24px;
    height: 24px;
    box-sizing: border-box;
  }

  .react-international-phone-country-selector-button__button-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .react-international-phone-country-selector-dropdown__list-item {
    display: flex;
    min-height: 32px;
    box-sizing: border-box;
    align-items: center;
    gap: ${({ theme }) => theme.space[1]}px;
    color: ${({ theme }) => theme.popper.menuItem};
    padding: 0px ${({ theme }) => theme.space[2]}px;
    cursor: pointer;

    &:hover {
      background: ${({ theme }) => theme.buttons.primaryHover};
      color: ${({ theme }) => theme.colors[theme.buttons.primaryHoverColor]};
    }
  }

  .react-international-phone-country-selector-dropdown__list-item--selected,
  .react-international-phone-country-selector-dropdown__list-item--focused {
    background: ${({ theme }) => theme.menu.selectedBackground};
    color: ${({ theme }) => theme.colors[theme.buttons.primaryColor]};

    &:hover {
      background: ${({ theme }) => theme.menu.selectedBackground};
      color: ${({ theme }) => theme.colors[theme.buttons.primaryColor]};
    }
  }

  .react-international-phone-country-selector-dropdown__list-item-dial-code {
    font-size: 14px;
  }

  .react-international-phone-country-selector-dropdown {
    position: absolute;
    z-index: 1;
    top: 44px;
    left: 0;
    display: flex;
    width: 300px;
    max-height: 300px;
    flex-direction: column;
    padding: 4px 0;
    margin: 0;
    background-color: ${({ theme }) => theme.popper.background};
    box-shadow: ${({ theme }) => theme.paper.boxShadow};
    color: ${({ theme }) => theme.popper.label};
    list-style: none;
    overflow-y: scroll;
    border: 1px solid ${({ theme }) => theme.paper.border};
    border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  }

  & input {
    ${baseInputStyle}

    ${({ transparentBg }) =>
      Boolean(transparentBg) &&
      css`
        background: transparent;
      `}
  }
`
