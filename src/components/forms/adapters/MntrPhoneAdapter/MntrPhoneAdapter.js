import { useEffect, useRef, useState } from 'react'
import { PhoneInput } from 'react-international-phone'
import StyledHelperText from '~/components/forms/adapters/shared/StyledHelperText'
import { StyledErrorMessage } from '~/components/forms/adapters/shared/StyledInput'
import StyledInputLabel from '~/components/forms/adapters/shared/StyledInputLabel'
import { FormControl, Text } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'
import { StyledWrapper } from './styles/StyledPhoneAdapter'

/**
 * Component that renders a phone input with adaptable country code priority.
 *
 * Default country priority:
 * 1) `defaultCountry` prop - (e.g., author edit)
 * 2) `self.appSettings.country` - basic usage
 *
 */
const MntrPhoneAdapter = ({
  appStore: { appSettings },
  autoFocus,
  color,
  defaultCountry,
  disableUnderline,
  errorText,
  helpText,
  helperText,
  icon,
  input,
  label = 'ㅤ',
  labelStyle,
  maxRows,
  meta,
  minRows,
  multiline,
  name,
  placeholderColor,
  renderLabel = true,
  rows,
  transparentBg,
  ...rest
}) => {
  const errorLabel = meta.error || meta.submitError || errorText
  const error = !!errorLabel
  const initialCountry = defaultCountry || appSettings.country
  const [showError, setShowError] = useState(true)

  const handleChangeValue = (value, element) => {
    // skip sending values to BE if it's dial code
    const countryDial = `+${element.country.dialCode}`
    input.onChange(value !== countryDial ? value : '')

    setShowError(false) // Hide error message on change
  }

  useEffect(() => {
    setShowError(error)
  }, [error])

  // https://github.com/ybrusentsov/react-international-phone/issues/176#issuecomment-2056600900
  // https://react-international-phone.vercel.app/docs/Usage/PhoneInput#ref-forwarding
  // jesus fucking christ
  const phoneInputRef = useRef()

  useEffect(() => {
    if (phoneInputRef.current) {
      phoneInputRef.current.setCountry(initialCountry.toLowerCase())
    }
  }, [initialCountry])

  return (
    <FormControl error={error} fullWidth>
      {renderLabel && (
        <StyledInputLabel
          error={error}
          htmlFor={`${name ?? input.name}-input`}
          icon={icon}
          label={label}
          labelStyle={labelStyle}
        />
      )}

      <StyledWrapper transparentBg={transparentBg}>
        <PhoneInput
          defaultCountry={initialCountry.toLowerCase()}
          {...input}
          {...rest}
          onChange={handleChangeValue}
          ref={phoneInputRef}
        />
      </StyledWrapper>

      {helpText && !error && (
        <StyledHelperText>
          <Text color="lightGrey">{helpText}</Text>
        </StyledHelperText>
      )}

      {/* Error Tooltip Message */}
      {error && errorLabel && showError && (
        <StyledErrorMessage className="error-message">{errorLabel}</StyledErrorMessage>
      )}
      {helperText && (
        <StyledHelperText error={false}>
          <Text color="lightGrey">{helperText}</Text>
        </StyledHelperText>
      )}
    </FormControl>
  )
}

export default observer(MntrPhoneAdapter)
