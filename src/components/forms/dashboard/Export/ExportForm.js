import { Trans } from '@lingui/react/macro'
import { Form, FormSpy } from 'react-final-form'
import { css, styled } from 'styled-components'
import LoadExportSettings from '~/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings'
import SaveExportSettings from '~/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import FormAttachment from '~/components/reports/Content/ReportsList/FormAttachment/FormAttachment'
import { observer } from '~/helpers/mst'
import AdvancedSettingsForm from './AdvancedSettingsForm'
import RecommendLimit from './RecommendedLimit'

const StickyButtonsFlex = styled(Flex)`
  ${({ isMobile }) =>
    isMobile &&
    css`
      position: fixed;
      bottom: 0;
    `}

  padding-bottom: env(safe-area-inset-bottom);
  background-color: ${({ theme }) => theme.paper.background};
  z-index: 12000;
`

const PortableBox = styled(Flex)`
  flex-direction: column;
  ${({ isMobile }) =>
    !isMobile &&
    css`
      max-height: calc(100vh - 210px);
      overflow-y: auto;
      width: 100%;
    `}
`

const ExportForm = ({
  appStore: {
    viewport: { isMobile },
  },
  initialValues,
  onSubmit,
  isLoading,
  viewport,
  exportList,
}) => {
  return (
    <Form
      onSubmit={onSubmit}
      initialValues={initialValues}
      render={({ handleSubmit, values, change }) => {
        return (
          <form onSubmit={handleSubmit}>
            <FormSpy
              onChange={(spy) => {
                exportList.exportSettings.setParam('selected', spy.values.attachment)
              }}
            />
            <Flex flexWrap="wrap" pt={4} centerX>
              <PortableBox isMobile={isMobile} gap={3} px={3} pb={3}>
                <FormAttachment
                  disableEmptyAttachment
                  fullWidth
                  values={values}
                  change={change}
                  fileFormatLabel={<Trans>File format</Trans>}
                  unlockLicensedArticles={true}
                />
                <AdvancedSettingsForm values={values} fullWidth />
                <Flex flexWrap="nowrap">
                  <Box>
                    <RecommendLimit exportList={exportList} />
                  </Box>
                </Flex>
              </PortableBox>
              <StickyButtonsFlex width={1} isMobile={isMobile}>
                <Flex
                  {...(!viewport.isMobile
                    ? {
                        alignSelf: 'flex-end',
                        ml: 'auto',
                      }
                    : {
                        width: 1,
                      })}
                  px={3}
                  pb={3}
                  mt={2}
                  centerY
                  justifyContent="space-between"
                  flexWrap="wrap"
                  gap={1}
                >
                  <Flex justifyContent="space-between" width={[3 / 10]} gap={1}>
                    <LoadExportSettings exportList={exportList} />
                    <SaveExportSettings exportList={exportList} />
                  </Flex>

                  <MntrButton
                    label={<Trans>Download</Trans>}
                    disabled={isLoading}
                    icon={'download'}
                    bg="secondary"
                    type="submit"
                  />
                </Flex>
              </StickyButtonsFlex>
            </Flex>
          </form>
        )
      }}
    />
  )
}

export default observer(ExportForm)
