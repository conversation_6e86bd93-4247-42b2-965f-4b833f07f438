import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { useRouter } from 'next/router'
import staticFeeds from '~/constants/staticFeeds'
import * as format from '~/helpers/formatNumber'
import { observer } from '~/helpers/mst'

const RecommendedLimit = ({
  appStore: {
    account,
    monitoring: { feedMap },
  },
  exportList,
}) => {
  const { pathname } = useRouter()
  const selectedId = get(exportList, 'exportSettings.selected.export_file_format')
  const selectedFormat = account.enums.export.export_file_format.find(
    (item) => item.id === selectedId,
  )
  const isSelectedScreenshots = get(exportList, 'exportSettings.selected.include_image_preview')
  const limit =
    isSelectedScreenshots && get(selectedFormat, 'include_image_preview_export_limit') > 0
      ? get(selectedFormat, 'include_image_preview_export_limit')
      : get(selectedFormat, 'export_limit')

  const checkedArticles =
    pathname !== '/export' ? feedMap.get(staticFeeds.EXPORT_FEED).selectedCount : exportList.counter
  const isVisibleRecommendedLimit = checkedArticles > limit

  return (
    <span style={{ color: '#eb4b4b', display: isVisibleRecommendedLimit ? 'block' : 'none' }}>
      <Trans>Recomended limit</Trans>: {format.formatArticles(limit)}
    </span>
  )
}

export default observer(RecommendedLimit)
