import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { useState } from 'react'
import { Field } from 'react-final-form'
import MntrCheckboxAdapter from '~/components/forms/adapters/MntrCheckboxAdapter/MntrCheckboxAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const AdvancedSettingsForm = ({
  values,
  appStore: {
    account: { enums },
  },
  label = <Trans>Advanced export settings</Trans>,
  fullWidth,
}) => {
  const fileFormat = get(values, 'attachment.export_file_format')
  const formats = enums.export.export_file_format
  const selectedFormat = formats.find((item) => item.id === parseInt(fileFormat))
  const [advancedSettingsOpen, setAdvancedSettingsOpen] = useState(false)

  return (
    <>
      {selectedFormat && (
        <Flex>
          <MntrButton
            isChip
            bg="transparent"
            label={label}
            icon={!advancedSettingsOpen ? 'keyboard_arrow_down' : 'keyboard_arrow_up'}
            iconBg="transparent"
            iconColor="icon"
            onClick={() => {
              setAdvancedSettingsOpen(!advancedSettingsOpen)
            }}
          />
        </Flex>
      )}
      {advancedSettingsOpen && selectedFormat && (
        <Box width={[1, 1, fullWidth ? 1 : 2 / 3]}>
          <Flex flexWrap="wrap" rowGap={1}>
            {selectedFormat.can_separate_regional_duplicates && (
              <Box width={[1, 1 / 2]} pl={5}>
                <Field
                  type="checkbox"
                  name="attachment.separate_regional_duplicates"
                  key="separate_regional_duplicates"
                  component={MntrCheckboxAdapter}
                  label={<Trans>Separate regional duplicates</Trans>}
                />
              </Box>
            )}
            {selectedFormat.can_include_sentiment && (
              <Box width={[1, 1 / 2]} pl={5}>
                <Field
                  type="checkbox"
                  name="attachment.include_sentiment"
                  key="include_sentiment"
                  component={MntrCheckboxAdapter}
                  label={<Trans>Sentiment</Trans>}
                />
              </Box>
            )}
            {selectedFormat.can_include_article_tags && (
              <Box width={[1, 1 / 2]} pl={5}>
                <Field
                  type="checkbox"
                  name="attachment.include_article_tags"
                  key="include_article_tags"
                  component={MntrCheckboxAdapter}
                  label={<Trans>Tags</Trans>}
                />
              </Box>
            )}
            {selectedFormat.can_include_article_notes && (
              <Box width={[1, 1 / 2]} pl={5}>
                <Field
                  type="checkbox"
                  name="attachment.include_article_notes"
                  key="include_notes"
                  component={MntrCheckboxAdapter}
                  label={<Trans>Notes</Trans>}
                />
              </Box>
            )}
          </Flex>
        </Box>
      )}
    </>
  )
}

export default observer(AdvancedSettingsForm)
