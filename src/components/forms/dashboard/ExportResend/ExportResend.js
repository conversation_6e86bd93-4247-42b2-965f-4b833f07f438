import { Trans } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import { Field, Form, FormSpy } from 'react-final-form'
import { css, styled } from 'styled-components'
import MntrCmsEditorAdapter from '~/components/forms/adapters/MntrCmsEditorAdapter/MntrCmsEditorAdapter'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import AdvancedSettingsForm from '~/components/forms/dashboard/Export/AdvancedSettingsForm'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import LoadResendSettings from '~/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings'
import SaveResendSettings from '~/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings'
import FormAttachment from '~/components/reports/Content/ReportsList/FormAttachment/FormAttachment'
import ReportAdvancedSettings from '~/components/reports/Content/ReportsList/ReportAdvancedSettings'
import ReportsFormDeduplicate from '~/components/reports/Content/ReportsList/ReportsFormDeduplicate'
import ReportsTopMentionsMode from '~/components/reports/Content/ReportsList/ReportsTopMentionsMode'
import withModalEmailPreview from '~/helpers/modal/withModalEmailPreview'
import { observer } from '~/helpers/mst'
import { transformDataToSelectItems } from '~/helpers/objectToSelectItems'

const StickyButtonsFlex = styled(Flex)`
  ${({ isMobile }) =>
    isMobile &&
    css`
      position: fixed;
      bottom: 0;
    `}

  padding-bottom: env(safe-area-inset-bottom);
  background-color: ${({ theme }) => theme.paper.background};
  z-index: 12000;
`

const PortableBox = styled(Flex)`
  flex-direction: column;
  ${({ isMobile }) =>
    !isMobile &&
    css`
      max-height: calc(100vh - 210px);
      overflow-y: auto;
      width: 100%;
    `}
`

const ExportResend = ({
  appStore: {
    viewport: { isMobile },
  },
  initialValues,
  editorInitialValue,
  enums,
  onSubmit,
  isExportList,
  exportList,
  feedId,
  hasFieldFromEmail,
}) => {
  return (
    <div>
      <Form
        onSubmit={onSubmit}
        initialValues={initialValues}
        render={({ handleSubmit, values, form, submitting }) => {
          return (
            <form>
              <FormSpy
                onChange={(spy) => {
                  exportList.resendSettings.setSelected(spy.values)
                }}
              />
              <Flex flexWrap="wrap" pt={4} {...(isMobile && { mb: 50 })} centerX>
                <PortableBox isMobile={isMobile} gap={3} px={3} pb={3}>
                  <Flex gap={3}>
                    <Box width={[hasFieldFromEmail ? 2 / 3 : 1]}>
                      <Field
                        parse={identityFn}
                        name="emails"
                        component={MntrTextFieldAdapter}
                        label={<Trans>Recipient emails</Trans>}
                      />
                    </Box>

                    {hasFieldFromEmail && (
                      <Box width={[1 / 3]}>
                        <Field
                          parse={identityFn}
                          name="from_email"
                          autoComplete="something-new"
                          component={MntrTextFieldAdapter}
                          label={<Trans>From email</Trans>}
                        />
                      </Box>
                    )}
                  </Flex>

                  <Flex flexWrap={['wrap', 'nowrap']} gap={3}>
                    <Box width={[1, 2 / 3]}>
                      <Field
                        parse={identityFn}
                        name="subject"
                        component={MntrTextFieldAdapter}
                        label={<Trans>Subject</Trans>}
                      />
                    </Box>
                    <Box width={[1, 1 / 3]}>
                      <Field
                        label={<Trans>Template</Trans>}
                        name="template"
                        component={MntrSelectAdapter}
                        items={transformDataToSelectItems(enums.email_reports.template)}
                      ></Field>
                    </Box>
                  </Flex>

                  <ReportsTopMentionsMode enums={enums} values={values} />

                  <Field
                    name="headline"
                    isVisibleMenuBar
                    isVisibleBubbleMenu={false}
                    transparent={false}
                    value={editorInitialValue}
                    component={MntrCmsEditorAdapter}
                    label={<Trans>Summary</Trans>}
                  />

                  <ReportAdvancedSettings values={values} form={form} />
                  <ReportsFormDeduplicate enums={enums} values={values} />
                  <FormAttachment fullWidth values={values} />
                  <AdvancedSettingsForm
                    fullWidth
                    values={values}
                    label={<Trans>Advanced attachment settings</Trans>}
                  />
                </PortableBox>
                <StickyButtonsFlex width={1} isMobile={isMobile}>
                  <Flex
                    {...(!isMobile
                      ? {
                          alignSelf: 'flex-end',
                          ml: 'auto',
                        }
                      : {
                          width: 1,
                        })}
                    px={3}
                    pb={3}
                    mt={2}
                    centerY
                    justifyContent="space-between"
                    flexWrap="wrap"
                    gap={1}
                  >
                    <Flex justifyContent="space-between" width={[3 / 10]} gap={1}>
                      <LoadResendSettings exportList={exportList} />
                      <SaveResendSettings exportList={exportList} />
                      <MntrButton
                        bg="flat"
                        beforeOpen={() =>
                          exportList.previewResendArticles(values, isExportList, feedId)
                        }
                        icon={'preview'}
                        label={!isMobile && <Trans>Preview</Trans>}
                        {...withModalEmailPreview({
                          data: exportList.formResendPreview,
                          downloadEmailPreview: exportList.downloadEmailPreview,
                          onClose: () => {
                            exportList.setParam('formResendPreview', undefined)
                          },
                        })}
                      />
                    </Flex>

                    <MntrButton
                      bg="tertiary"
                      onClick={handleSubmit}
                      icon={'send'}
                      disabled={submitting}
                      label={<Trans>Send</Trans>}
                    />
                  </Flex>
                </StickyButtonsFlex>
              </Flex>
            </form>
          )
        }}
      />
    </div>
  )
}

export default observer(ExportResend)
