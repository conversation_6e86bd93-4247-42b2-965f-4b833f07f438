import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import { styled } from 'styled-components'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'
import SearchAccounts from './SearchAccounts'

const SearchWrapper = styled.div`
  position: fixed;
  top: 54px;
  height: 100%;
  left: 0;
  right: 0;
  z-index: 100;
  background: ${({ theme }) => theme.popper.background};
  overflow-y: auto;

  & .menu-heading {
    color: ${({ theme }) => theme.popper.heading} !important;
  }
`

const SearchAdmin = ({ appStore: { searchBar } }) => {
  const router = useRouter()
  const { lastSearchQuery, setIsOpenSearch } = searchBar

  const onPushEvent = (section) => {
    pushEvent(events.SEARCH_SUBMITTED, { search_scope: 'customers', autocomplete_used: section })
  }

  return (
    <SearchWrapper>
      {lastSearchQuery && (
        <>
          <MntrMenuHeading label={<Trans>Search customers</Trans>} fontSize={12} />
          <Link href={`${router.pathname}?query=${lastSearchQuery}`}>
            <MenuItem
              onClick={() => setIsOpenSearch(false)}
              leftIcon="history"
              label={lastSearchQuery}
              fontSize={12}
            />
          </Link>
        </>
      )}
      <SearchAccounts setIsOpenSearch={setIsOpenSearch} onPushEvent={onPushEvent} />
    </SearchWrapper>
  )
}

export default observer(SearchAdmin)
