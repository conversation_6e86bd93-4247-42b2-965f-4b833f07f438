import { msg } from '@lingui/core/macro'
import { useLingui } from '@lingui/react/macro'
import get from 'lodash/get'
import { useState } from 'react'
import { Field, Form, FormSpy } from 'react-final-form'
import { styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import useArrowForNavigationInSearch from '~/helpers/hooks/useArrowForNavigationInSearch'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'
import SearchBar from './SearchBar'

const StyledForm = styled.form`
  color: ${({ theme, isOpenSearch, hasQuery }) => {
    if (hasQuery) {
      return theme.searchBar.input
    }
    if (!isOpenSearch) {
      return theme.searchBar.placeholder
    }

    return theme.searchBar.input
  }};
`

const getScopedSearchContent = (pathname, canReadArchive, filter, topics, isPlacehoder = true) => {
  let scopedSearchContent = isPlacehoder
    ? canReadArchive
      ? msg`Search in archive`
      : msg`Search in topics`
    : canReadArchive
      ? 'archive'
      : 'topics'
  if (
    pathname.startsWith('/staff/admin/customer') ||
    pathname.startsWith('/staff/admin/user') ||
    pathname.startsWith('/staff/admin/workspace')
  ) {
    scopedSearchContent = isPlacehoder ? msg`Search customers` : 'customers'
  }

  const topicMonitor = get(filter, 'data.topic_monitors') || ''
  const topicMonitorArray = topicMonitor.split(',')

  if (topicMonitorArray.length > 0 && topicMonitorArray[0].length > 0) {
    const topicName = topics.getTopicNameById(topicMonitorArray[0])
    if (topicName) {
      scopedSearchContent = isPlacehoder ? msg`Search in ${topicName}` : topicName
    } else {
      scopedSearchContent = isPlacehoder ? msg`Search in topic` : 'topic'
    }
  }

  if (pathname.startsWith('/newsroom')) {
    scopedSearchContent = isPlacehoder ? msg`Search in Newsroom` : 'newsroom'
  }

  if (pathname.startsWith('/emailing')) {
    scopedSearchContent = isPlacehoder ? msg`Search in Emailing` : 'emailing'
  }

  if (topicMonitorArray.length > 1) {
    scopedSearchContent = isPlacehoder ? msg`Search in topics` : 'topics'
  }

  if (pathname === '/crisis-communication') {
    scopedSearchContent = isPlacehoder ? msg`Search` : 'search'
  }

  if (pathname.startsWith('/authors')) {
    scopedSearchContent = isPlacehoder ? msg`Search authors` : 'authors'
  }

  if (pathname.endsWith('/changelog')) {
    scopedSearchContent = isPlacehoder ? msg`Search changelog` : 'changelog'
  }

  return scopedSearchContent
}

const SearchForm = ({
  onSubmit,
  initialValues,
  topics,
  filter,
  pathname,
  canReadArchive,
  isQueryFocused,
  appStore: {
    searchBar,
    monitoring: { activeFeedMapItem, feedMap },
    router: { makeRouteWithQuery },
  },
  isOpenSearch,
}) => {
  const { i18n } = useLingui()
  const [lastSearchedPage, setLastSearchedPage] = useState(pathname)
  const isAdmin = pathname.startsWith('/staff/admin')
  const {
    setIsOpenSearch,
    setIsMobileSearchVisible,
    lastSearchQuery,
    search: searchBarSearch,
  } = searchBar

  const search = (val, force) => {
    setLastSearchedPage(pathname)
    searchBarSearch(val, force)
  }

  const { setArrowClickCounter, setNavigationHorizontal } = useArrowForNavigationInSearch()

  const resetArrowsValues = () => {
    setArrowClickCounter(0)
    setNavigationHorizontal(0)
  }

  return (
    <Box mt={0}>
      <Form
        onSubmit={onSubmit}
        initialValues={initialValues}
        render={({ handleSubmit, values, form: { change } }) => {
          return (
            <StyledForm
              onSubmit={handleSubmit}
              isOpenSearch={isOpenSearch}
              visiblePlaceholder={!(values.query?.length > 0)}
              hasQuery={values.query?.length > 0}
            >
              <Flex alignItems={'center'} position="relative" height="36px">
                <Box ml="10px">
                  <MntrButton
                    bg="header"
                    icon="search"
                    iconColor="currentColor"
                    onClick={() => {
                      if (values.query && values.query.length > 0) {
                        handleSubmit()
                      } else {
                        const input = document.querySelector('.search-input input')

                        if (input) {
                          input.select()
                        }
                      }
                    }}
                  />
                </Box>
                <Flex ml={1} width={1} className="search-input" height="36px">
                  <FormSpy
                    onChange={(spy) => {
                      if (spy.dirty && spy.values.query !== lastSearchQuery) {
                        setIsOpenSearch(true)
                      }
                      if (lastSearchQuery !== undefined && spy.values.query !== lastSearchQuery) {
                        search(spy.values.query)
                      }
                    }}
                  />
                  <SearchBar
                    handleVariation={(variation) => {
                      change('query', variation)
                      search(variation)

                      setIsOpenSearch(false)
                      setIsMobileSearchVisible(false)
                    }}
                    handleUserClick={() => {
                      change('query', '')

                      setIsOpenSearch(false)
                      setIsMobileSearchVisible(false)
                    }}
                    scope={getScopedSearchContent(pathname, canReadArchive, filter, topics, false)}
                    isOpenSearch={isOpenSearch}
                    setIsOpenSearch={setIsOpenSearch}
                  />
                  <Field
                    id="search-query"
                    disableUnderline
                    name="query"
                    autoComplete="off"
                    component={MntrTextFieldAdapter}
                    onFocus={() => {
                      filter.setParam('isQueryFocused', true)
                      setIsOpenSearch(true)

                      // Suggest
                      if (!values.query && !isAdmin) {
                        search('', true)
                      }

                      if (values.query && lastSearchedPage !== pathname) {
                        search(values.query, true)
                      }

                      pushEvent(events.SEARCH_OPENED, {
                        search_scope: getScopedSearchContent(
                          pathname,
                          canReadArchive,
                          filter,
                          topics,
                          false,
                        ),
                      })
                    }}
                    onChange={(event) => {
                      change('query', event.currentTarget.value)
                      search(event.currentTarget.value)
                    }}
                    onBlur={() => {
                      filter.setParam('isQueryFocused', false)
                      resetArrowsValues()
                    }}
                    placeholder={i18n
                      ._(getScopedSearchContent(pathname, canReadArchive, filter, topics))
                      .toUpperCase()}
                    pathname={pathname}
                    color="inherit"
                    placeholderColor="inherit"
                    onKeyPress={(event) => {
                      if (event.key === 'Enter') {
                        event.preventDefault()
                        const selectedElements = document.querySelectorAll('[isSelected="true"]')
                        if (selectedElements.length) {
                          selectedElements.forEach((element) => {
                            element.click()
                          })
                        } else {
                          pushEvent(events.SEARCH_SUBMITTED, { enter_used: true })
                          handleSubmit()
                        }
                      }
                    }}
                    transparentBg
                  />
                </Flex>
                {values.query && values.query.length > 0 && (
                  <Flex mr={10} justifyContent="space-between" centerY>
                    <MntrButton
                      mr={0.5}
                      bg={isQueryFocused ? 'flat' : 'header'}
                      icon="close"
                      iconColor="currentColor"
                      onClick={() => {
                        change('query', '')
                        setIsOpenSearch(false)
                        setIsMobileSearchVisible(false)

                        const newQueryString = filter.urlWithParam({
                          articleId: undefined,
                          query: undefined,
                          token: undefined,
                          topicMonitor: undefined,
                          workspaceId: undefined,
                        })

                        if (activeFeedMapItem) {
                          const feed = feedMap.get(activeFeedMapItem)
                          const url = `${window.location.pathname}?${feed.filter.queryRemoveParam({
                            query: undefined,
                          })}`
                          routerReplace(makeRouteWithQuery(), url, { shallow: true })
                          feed.loadUrl(url)
                          return false
                        }

                        routerPush(
                          `${window.location.pathname}${
                            newQueryString ? `?${newQueryString}` : ''
                          }`,
                        )
                      }}
                    />
                    <MntrButton
                      icon="search"
                      bg="flat"
                      iconColor="primary"
                      onClick={() => {
                        setIsOpenSearch(false)
                        setIsMobileSearchVisible(false)

                        if (values.query && values.query.length > 0) {
                          pushEvent(events.SEARCH_SUBMITTED, { icon_used: true })
                          handleSubmit()
                        }
                      }}
                    />
                  </Flex>
                )}
              </Flex>
            </StyledForm>
          )
        }}
      />
    </Box>
  )
}

export default observer(SearchForm)
