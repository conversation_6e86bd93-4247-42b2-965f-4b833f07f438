import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { styled } from 'styled-components'
import SearchSuggest from '~/components/layout/Header/SearchSuggest/SearchSuggest'
import Icon from '~/components/misc/Icon/Icon'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { observer } from '~/helpers/mst'
import SearchAccounts from './SearchAccounts'
import SearchAuthors from './SearchAuthors'
import SearchBarTopicSelector from './SearchBarTopicSelector'
import SearchDeclensions from './SearchDeclensions'
import SearchEmailingCampaigns from './SearchEmailingCampaigns'
import SearchEmailingEmailMessages from './SearchEmailingEmailMessages'
import SearchNewsroom from './SearchNewsroom'
import SearchVariations from './SearchVariations'
import SearchWarning from './SearchWarning'

const SearchBarWrapper = styled.div`
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  height: ${(props) => (props.isActive ? props.height : 40)}px;
  background: ${({ theme }) => theme.popper.background};
  z-index: 0;
  border-radius: 16px;
  box-shadow: ${({ isActive, theme }) => (isActive ? theme.searchBar.boxShadow : '0px')};
  padding-top: 0px;
  opacity: ${(props) => (props.isActive ? 1 : 0)};
  transition:
    opacity ${(props) => (props.isActive ? 0 : 100)}ms ease-in,
    height 120ms ease-in,
    box-shadow 150ms ease-in;
  overflow: hidden;
  color: red;

  & .menu-heading {
    color: ${({ theme }) => theme.popper.heading} !important;
  }
`

const SearchIcon = styled.div`
  position: absolute;
  top: 7px;
  left: 15px;
  color: ${({ theme }) => theme.popper.heading};
`
const Overlay = styled.div`
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
`

const SearchContent = styled.div`
  margin-top: 4px;
`

const SearchBar = ({
  appStore: { searchBar, viewport },
  handleVariation,
  handleUserClick,
  scope,
  isOpenSearch,
}) => {
  const onPushEvent = (section) => {
    pushEvent(events.SEARCH_SUBMITTED, { search_scope: scope, autocomplete_used: section })
  }
  const router = useRouter()
  const { setIsOpenSearch, lastSearchQuery, searchData, isLoading, height } = searchBar

  useEffect(() => {
    document.onkeyup = (e) => {
      if (e.key === 'ArrowDown') {
        if (document.getElementById('query-input') === document.activeElement) {
          setIsOpenSearch(true)
        }
      }

      if (e.key === 'Escape' || e.key === 'Enter') {
        setIsOpenSearch(false)
      }
    }

    return () => {
      document.onkeyup = null
    }
  }, [setIsOpenSearch])

  let includeResults = [
    'accounts',
    'authors',
    'topicSelector',
    'declensions',
    'variations',
    'warning',
    'categories',
    'suggest',
  ]

  const queries = searchData?.queries || []
  const isAdminPage = router.pathname.indexOf('/staff') === 0
  const isHidden =
    (lastSearchQuery === '' && queries.length === 0) || (isAdminPage && lastSearchQuery === '')
  const isMedialist = router.pathname.startsWith('/authors')
  const isChangelog = router.pathname.endsWith('/changelog')
  const isEmailing = router.pathname.startsWith('/emailing')

  const [debouncedIsLoading, setDebouncedIsLoading] = useState(false)

  if (isAdminPage) {
    includeResults = ['accounts']
  }

  if (isMedialist) {
    includeResults = ['authors']
  }

  if (isChangelog) {
    includeResults = []
  }

  if (isEmailing) {
    includeResults = ['emailing_campaigns', 'emailing_email_messages']
  }

  if (router.pathname.startsWith('/newsroom')) {
    includeResults = ['newsroom_posts']
  }

  // TODO: use skeleton
  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedIsLoading(isLoading)
    }, 150)

    return () => clearTimeout(timeout)
  }, [isLoading])

  return (
    <div>
      {isOpenSearch && !viewport.isMobile && <Overlay onClick={() => setIsOpenSearch(false)} />}
      {!viewport.isMobile && includeResults.length > 0 && (
        <SearchBarWrapper
          height={height}
          isActive={isOpenSearch && !isHidden}
          className="popper-menu"
        >
          <div style={{ height: 30 }}></div>
          <SearchIcon>
            {debouncedIsLoading ? (
              <MntrCircularProgress size={20} />
            ) : (
              <span>
                <Icon>search</Icon>
              </span>
            )}
          </SearchIcon>
          {lastSearchQuery && (
            <SearchContent>
              {includeResults.includes('warning') && <SearchWarning />}
              {includeResults.includes('variations') && (
                <SearchVariations handleVariation={handleVariation} />
              )}
              {includeResults.includes('topicSelector') && <SearchBarTopicSelector />}
              {includeResults.includes('declensions') && <SearchDeclensions />}
            </SearchContent>
          )}
          {includeResults.includes('newsroom_posts') && (
            <SearchNewsroom onPushEvent={onPushEvent} />
          )}
          {includeResults.includes('emailing_campaigns') && (
            <SearchEmailingCampaigns onPushEvent={onPushEvent} />
          )}
          {includeResults.includes('emailing_email_messages') && (
            <SearchEmailingEmailMessages onPushEvent={onPushEvent} />
          )}
          {includeResults.includes('suggest') && <SearchSuggest />}
          {lastSearchQuery && (
            <>
              {includeResults.includes('authors') && <SearchAuthors onPushEvent={onPushEvent} />}
              {includeResults.includes('accounts') && (
                <SearchAccounts handleUserClick={handleUserClick} onPushEvent={onPushEvent} />
              )}
            </>
          )}
        </SearchBarWrapper>
      )}
    </div>
  )
}

export default observer(SearchBar)
