import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import getWorkspaceAttributes from '~/components/staff/admin/workspace/getWorkspaceAttributes'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'

const Message = styled.div`
  opacity: ${(props) => (props.isEmpty ? 0 : 1)};
  height: auto;
`

const TextWrapper = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

const SearchWorkspaces = ({
  isAdminPage,
  handleUserClick,
  onPushEvent,
  appStore: { searchBar },
}) => {
  let workspacesData = isAdminPage
    ? get(searchBar, 'workspacesData')
    : get(searchBar, 'searchData.workspaces')
  const { setIsOpenSearch, setIsMobileSearchVisible } = searchBar

  if (!workspacesData) {
    return <span />
  }

  return (
    <Message isEmpty={workspacesData.length === 0}>
      {workspacesData.length > 0 && (
        <>
          <MntrMenuHeading label={<Trans>Workspaces</Trans>} fontSize={12} />
          {workspacesData.map((item, index) => {
            return (
              <Link key={index.toString()} href={`/staff/admin/workspaces/${item.id}`}>
                <MenuItem
                  hoverVariant="light"
                  onClick={(event) => {
                    onPushEvent('workspaces')
                    setIsOpenSearch(false)
                    setIsMobileSearchVisible(false)

                    handleUserClick()
                    if (event.ctrlKey || event.metaKey) {
                      return false
                    }
                  }}
                  leftIcon={
                    <span>
                      {getWorkspaceAttributes(item).map((i, index) => {
                        if (index > 0) {
                          return false
                        }

                        return (
                          <Icon key={i.text} color={i.backgroundColor || 'lightGrey'} size={26}>
                            account_circle
                          </Icon>
                        )
                      })}
                    </span>
                  }
                  label={
                    <Flex justifyContent="space-between" maxHeight="20px">
                      <TextWrapper>{item.name}</TextWrapper>
                      <Flex centerY>
                        {getWorkspaceAttributes(item).map((i, key) => {
                          return (
                            <MntrButton
                              key={key}
                              isChip
                              disabled
                              noPadding
                              ml={1}
                              height={'23px'}
                              bg={i.backgroundColor}
                              label={
                                <Text px={1} color="inherit">
                                  {i.text}
                                </Text>
                              }
                              labelColor={i.backgroundColor ? 'white' : 'black'}
                            />
                          )
                        })}
                      </Flex>
                    </Flex>
                  }
                />
              </Link>
            )
          })}
        </>
      )}
    </Message>
  )
}

export default observer(SearchWorkspaces)
