import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'

const Wrapper = styled.div`
  margin: 5px 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: ${(props) => (props.isEmpty ? 0 : 22)}px;
  transition: height 100ms ease-in;
  position: relative;
  z-index: 10;
  box-sizing: border-box;
  color: ${({ theme }) => theme.article.highlightColor};
`

const WrapperHeader = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: ${(props) => (props.isEmpty ? 0 : 26)}px;
  transition: height 100ms ease-in;
  position: relative;
  z-index: 10;
`

const Query = styled.span`
  padding: 0px 2px;
  font-size: 13px;
  margin-right: 2px;
  color: ${({ theme }) => theme.article.highlightColor};
  background: ${({ theme }) => theme.article.highlightBackground};
  border: 1px solid ${({ theme }) => theme.article.highlightBorder};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  box-sizing: border-box;
`

const SearchDeclensions = ({
  appStore: {
    searchBar,
    appSettings: { appName },
  },
}) => {
  const declensions = searchBar.searchData?.declensions || []

  return (
    <div>
      <WrapperHeader isEmpty={declensions.length === 0}>
        <MntrMenuHeading label={<Trans>{appName} will search</Trans>} fontSize={12} />
      </WrapperHeader>
      <Wrapper isEmpty={declensions.length === 0}>
        {declensions.map((item) => (
          <Query key={item}>{item}</Query>
        ))}
      </Wrapper>
    </div>
  )
}

export default observer(SearchDeclensions)
