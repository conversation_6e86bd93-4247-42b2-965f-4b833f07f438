import { Trans } from '@lingui/react/macro'
import { withRouter } from 'next/router'
import { styled } from 'styled-components'
import displayEmailingTitle from '~/components/emailing/helpers/displayEmailingTitle'
import { Flex, Text } from '~/components/misc/Mntr'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'

const Message = styled.div`
  height: auto;
`

const TextWrapper = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

const SearchEmailinEmailMessages = ({ handleUserClick, onPushEvent, appStore: { searchBar } }) => {
  const searchData = searchBar.searchData?.emailing_email_messages || []
  const { setIsOpenSearch } = searchBar

  return (
    <Message>
      <MntrMenuHeading label={<Trans>Emails</Trans>} fontSize={12} />

      {/* Zero results message */}
      {searchData.length === 0 && (
        <Text px={3} fontSize={1} color="mediumGrey" mb={2}>
          <Trans>No results found</Trans>.
        </Text>
      )}

      {/* Results */}
      {searchData.map((item, index) => {
        return (
          <Link
            key={index.toString()}
            href={`/emailing/campaign/${item.campaign}/email/${item.id}`}
          >
            <MenuItem
              hoverVariant="light"
              onClick={(event) => {
                onPushEvent('emailing')
                setIsOpenSearch(false)

                if (typeof handleUserClick === 'function') {
                  handleUserClick()
                }
                if (event.ctrlKey || event.metaKey) {
                  return false
                }
              }}
              leftIcon={'email'}
              label={
                <Flex justifyContent="space-between" style={{ maxHeight: 20 }}>
                  <TextWrapper>{displayEmailingTitle(item)}</TextWrapper>
                </Flex>
              }
            />
          </Link>
        )
      })}
    </Message>
  )
}

export default withRouter(observer(SearchEmailinEmailMessages))
