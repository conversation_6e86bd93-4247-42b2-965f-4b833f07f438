import { styled } from 'styled-components'
import { observer } from '~/helpers/mst'

const Message = styled.div`
  margin: 0px 16px;
  background: #f9f7e8;
  color: #ff4552;
  border-radius: 4px;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: ${(props) => (props.hasMessage ? 27 : 0)}px;
  transition: height 100ms ease-in;
`

const MessageBody = styled.div`
  padding: 4px 8px;
`

const SearchWarning = ({ appStore: { searchBar } }) => {
  const { warningMessage } = searchBar

  return (
    <Message hasMessage={warningMessage}>
      <MessageBody>{warningMessage}</MessageBody>
    </Message>
  )
}

export default observer(SearchWarning)
