import { styled } from 'styled-components'
import { observer } from '~/helpers/mst'

const Wrapper = styled.div`
  margin: 0px 10px;
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: ${(props) => (props.isEmpty ? 0 : 22)}px;
  transition: height 100ms ease-in;
  position: relative;
  z-index: 10;
`

const Declension = styled.span`
  padding: 1px 8px;
  background: #f5f5f5;
  cursor: pointer;
  border-radius: 15px;
  font-size: 13px;
  margin-right: 2px;
  color: rgba(0, 0, 0, 0.87);

  &:hover {
    background: rgb(230, 230, 230);
  }
`

const SearchVariations = ({ appStore: { searchBar }, handleVariation }) => {
  const variations = searchBar.searchData?.variations || []

  return (
    <Wrapper isEmpty={variations.length === 0}>
      {variations.map((item) => {
        return (
          <Declension
            key={item}
            onClick={() => {
              handleVariation(item)
            }}
          >
            {item}
          </Declension>
        )
      })}
    </Wrapper>
  )
}

export default observer(SearchVariations)
