import identityFn from 'lodash/identity'
import { useRouter } from 'next/router'
import SearchCustomers from '~/components/forms/dashboard/Search/SearchCustomers'
import SearchUsers from '~/components/forms/dashboard/Search/SearchUsers'
import SearchWorkspaces from '~/components/forms/dashboard/Search/SearchWorkspaces'

const SearchAccounts = ({ handleUserClick = identityFn, onPushEvent }) => {
  const { pathname } = useRouter()
  const isAdmin = pathname.startsWith('/staff/admin')

  return (
    <>
      <SearchCustomers
        isAdminPage={isAdmin}
        handleUserClick={handleUserClick}
        onPushEvent={onPushEvent}
      />
      <SearchWorkspaces
        isAdminPage={isAdmin}
        handleUserClick={handleUserClick}
        onPushEvent={onPushEvent}
      />
      <SearchUsers
        isAdminPage={isAdmin}
        handleUserClick={handleUserClick}
        onPushEvent={onPushEvent}
      />
    </>
  )
}

export default SearchAccounts
