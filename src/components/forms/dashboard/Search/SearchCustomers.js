import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { styled } from 'styled-components'
import Flag from '~/components/Flag/Flag'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'

const Message = styled.div`
  opacity: ${(props) => (props.isEmpty ? 0 : 1)};
  height: auto;
`

const TextWrapper = styled(Flex)`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-direction: row;
  max-height: 20px;
`

const SecondaryText = styled(Box)`
  color: currentColor;
  opacity: 0.7;
  margin-left: 8px;
`
const Country = styled(Box)`
  position: absolute;
  bottom: 1px;
  left: 28px;
`

const SearchCustomers = ({
  isAdminPage,
  handleUserClick,
  onPushEvent,
  appStore: { searchBar },
}) => {
  let customersData = isAdminPage
    ? get(searchBar, 'customersData')
    : get(searchBar, 'searchData.customers')
  const { setIsOpenSearch, setIsMobileSearchVisible } = searchBar

  if (!customersData) {
    return <span />
  }
  return (
    <Message isEmpty={customersData.length === 0}>
      {customersData.length > 0 && (
        <>
          <MntrMenuHeading label={<Trans>Customers</Trans>} fontSize={12} />
          {customersData.map((item, index) => {
            return (
              <Link key={index.toString()} href={`/staff/admin/customers/${item.id}/workspaces`}>
                <MenuItem
                  hoverVariant="light"
                  onClick={(event) => {
                    onPushEvent('customers')
                    setIsOpenSearch(false)
                    setIsMobileSearchVisible(false)

                    handleUserClick()
                    if (event.ctrlKey || event.metaKey) {
                      return false
                    }
                  }}
                  leftIcon={
                    <>
                      {item.logo_image_url ? (
                        <img src={item.logo_image_url} width={26} height={26} alt="" />
                      ) : (
                        <Icon size={26} color="primary">
                          account_circle
                        </Icon>
                      )}
                      {item.country && (
                        <Country>
                          <Flag country={item.country} size={11} />
                        </Country>
                      )}
                    </>
                  }
                  label={
                    <TextWrapper>
                      {item.name}
                      <SecondaryText>{item.reg_no}</SecondaryText>
                    </TextWrapper>
                  }
                />
              </Link>
            )
          })}
        </>
      )}
    </Message>
  )
}

export default observer(SearchCustomers)
