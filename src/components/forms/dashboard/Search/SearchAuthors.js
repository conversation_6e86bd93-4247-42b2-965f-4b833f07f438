import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { styled } from 'styled-components'
import Flag from '~/components/Flag/Flag'
import AuthorInfoDetail from '~/components/medialist/content/AuthorInfoDetail'
import AuthorPhoto from '~/components/medialist/content/FeedMedialist/AuthorPhoto/AuthorPhoto'
import { Box, Flex } from '~/components/misc/Mntr'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'

const Message = styled.div`
  opacity: ${(props) => (props.isEmpty ? 0 : 1)};
  height: ${(props) => (props.isEmpty ? 0 : 'auto')};
`

const TextWrapper = styled(Flex)`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-direction: row;
`

const Country = styled(Box)`
  position: absolute;
  bottom: 1px;
  left: 28px;
`

const SearchAuthors = ({ appStore: { searchBar }, onPushEvent }) => {
  let authorsData = get(searchBar, 'searchData.authors') || []
  const { setIsOpenSearch } = searchBar

  return (
    <Message isEmpty={authorsData.length === 0}>
      {authorsData.length > 0 && (
        <>
          <MntrMenuHeading className="menu-heading" label={<Trans>Authors</Trans>} fontSize={12} />
          {authorsData.map((item, index) => {
            return (
              <Link href={`/author/${item.id}`} key={index.toString()}>
                <MenuItem
                  hoverVariant="light"
                  onClick={(event) => {
                    onPushEvent('authors')
                    setIsOpenSearch(false)

                    if (event.ctrlKey || event.metaKey) {
                      return false
                    }
                  }}
                  label={
                    <TextWrapper>
                      {item.name}
                      <Box ml="8px">
                        <AuthorInfoDetail author={item} fontSize={14} opacity={0.7} />
                      </Box>
                    </TextWrapper>
                  }
                  leftIcon={
                    <>
                      <AuthorPhoto
                        size={26}
                        authorType={item.author_type}
                        image={item.photo_url.small}
                      />

                      {item.country && (
                        <Country>
                          <Flag country={item.country.code} size={11} />
                        </Country>
                      )}
                    </>
                  }
                />
              </Link>
            )
          })}
        </>
      )}
    </Message>
  )
}

export default observer(SearchAuthors)
