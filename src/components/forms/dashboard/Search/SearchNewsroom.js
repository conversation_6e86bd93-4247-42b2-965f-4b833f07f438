import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import { Flex } from '~/components/misc/Mntr'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'

const Message = styled.div`
  opacity: ${(props) => (props.isEmpty ? 0 : 1)};
  height: auto;
`

const TextWrapper = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

const SearchNewsroom = ({ handleUserClick, appStore: { searchBar }, onPushEvent }) => {
  const searchData = searchBar.searchData?.newsroom_posts
  const { setIsOpenSearch } = searchBar

  if (!searchData) {
    return <span />
  }
  return (
    <Message isEmpty={searchData.length === 0}>
      {searchData.length > 0 && (
        <>
          <MntrMenuHeading label={<Trans>Articles</Trans>} fontSize={12} />
          {searchData.map((item, index) => {
            return (
              <Link key={index.toString()} href={`/newsroom/${item.blog_id}/post/${item.id}`}>
                <MenuItem
                  hoverVariant="light"
                  onClick={(event) => {
                    onPushEvent('newsroom')
                    setIsOpenSearch(false)

                    if (typeof handleUserClick === 'function') {
                      handleUserClick()
                    }
                    if (event.ctrlKey || event.metaKey) {
                      return false
                    }
                  }}
                  leftIcon={'newspaper'}
                  label={
                    <Flex justifyContent="space-between" maxHeight="20px">
                      <TextWrapper>{item.title}</TextWrapper>
                    </Flex>
                  }
                />
              </Link>
            )
          })}
        </>
      )}
    </Message>
  )
}

export default observer(SearchNewsroom)
