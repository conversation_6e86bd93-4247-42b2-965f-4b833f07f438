import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import getUserAttributes from '~/components/staff/admin/user/getUserAttributes'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'

const Message = styled.div`
  opacity: ${(props) => (props.isEmpty ? 0 : 1)};
  height: auto;
`

const TextWrapper = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

const SearchUsers = ({ isAdminPage, handleUserClick, onPushEvent, appStore: { searchBar } }) => {
  let usersData = isAdminPage ? get(searchBar, 'usersData') : get(searchBar, 'searchData.users')
  const { setIsOpenSearch, setIsMobileSearchVisible } = searchBar

  if (!usersData) {
    return <span />
  }
  return (
    <Message isEmpty={usersData.length === 0}>
      {usersData.length > 0 && (
        <>
          <MntrMenuHeading label={<Trans>Users</Trans>} fontSize={12} />
          {usersData.map((item, index) => {
            return (
              <Link key={index.toString()} href={`/staff/admin/users/${item.id}`}>
                <MenuItem
                  hoverVariant="light"
                  onClick={(event) => {
                    onPushEvent('users')
                    setIsOpenSearch(false)
                    setIsMobileSearchVisible(false)

                    handleUserClick()
                    if (event.ctrlKey || event.metaKey) {
                      return false
                    }
                  }}
                  leftIcon={
                    <span>
                      {getUserAttributes(item).map((i, index) => {
                        if (index > 0) {
                          return false
                        }

                        return (
                          <Icon key={i.text} color={i.backgroundColor} size={26}>
                            account_circle
                          </Icon>
                        )
                      })}
                    </span>
                  }
                  label={
                    <Flex justifyContent="space-between" maxHeight="20px">
                      <TextWrapper>{item.email}</TextWrapper>
                      <Flex centerY>
                        {getUserAttributes(item).map(
                          (i, key) =>
                            i.text && (
                              <MntrButton
                                key={key}
                                isChip
                                disabled
                                noPadding
                                ml={1}
                                height={'23px'}
                                bg={i.backgroundColor}
                                label={
                                  <Text color="inherit" px={1}>
                                    {i.text}
                                  </Text>
                                }
                              />
                            ),
                        )}
                        <MntrButton
                          isChip
                          disabled
                          noPadding
                          ml={1}
                          height={'23px'}
                          bg={item.is_active ? 'secondary' : 'error'}
                          label={
                            <Text px={1} color="inherit">
                              {item.is_active ? t`Active` : t`Inactive`}
                            </Text>
                          }
                        />
                      </Flex>
                    </Flex>
                  }
                />
              </Link>
            )
          })}
        </>
      )}
    </Message>
  )
}

export default observer(SearchUsers)
