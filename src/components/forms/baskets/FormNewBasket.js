import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { Field, Form } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'

const FormNewBasket = ({ onSubmit, initialValues, title = <Trans>New list</Trans>, errors }) => {
  return (
    <Form
      onSubmit={onSubmit}
      initialValues={initialValues}
      render={({ handleSubmit, dirtyFieldsSinceLastSubmit }) => {
        return (
          <form onSubmit={handleSubmit}>
            <Box pt={2}>
              <Flex flexWrap="nowrap" p={3} pt={0} pl="16px" center>
                <Box width={1}>
                  <Box pr={2}>
                    <Field
                      name="label"
                      autoFocus
                      autoComplete="off"
                      placeholder={t`List name`}
                      component={MntrTextFieldAdapter}
                      errorText={!dirtyFieldsSinceLastSubmit['label'] ? errors?.label : undefined}
                      transparentBg
                      label={<MntrMenuHeading label={title} noPadding />}
                    />
                  </Box>
                </Box>
                <Box mt={4}>
                  <MntrButton mt={2} type="submit" icon="add" bg="primary" />
                </Box>
              </Flex>
            </Box>
          </form>
        )
      }}
    />
  )
}

export default observer(FormNewBasket)
