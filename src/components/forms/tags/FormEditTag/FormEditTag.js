import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { Field, Form } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import ColorPicker from '~/components/misc/ColorPicker/ColorPicker'
import { Box, ButtonGroup, Flex } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { tagColors } from '~/constants/colors'
import { observer } from '~/helpers/mst'

const FormEditTag = ({ onSubmit, initialValues, isTouchDevice, errors }) => {
  return (
    <Form
      onSubmit={onSubmit}
      initialValues={initialValues}
      render={({ handleSubmit, values, form: { change }, dirtyFieldsSinceLastSubmit }) => {
        return (
          <form onSubmit={handleSubmit}>
            <Box pt={2}>
              <Box px={3}>
                <Field
                  name="label"
                  autoFocus
                  autoComplete="off"
                  placeholder={t`Tag name`}
                  component={MntrTextFieldAdapter}
                  errorText={!dirtyFieldsSinceLastSubmit['label'] ? errors?.label?.[0] : undefined}
                  label={<MntrMenuHeading label={<Trans>Tag name</Trans>} noPadding />}
                />
                <ColorPicker
                  color={values.color}
                  onColorChange={(color) => change('color', color)}
                  replaceColorPalette
                  colorPalette={tagColors}
                  isTouchDevice={isTouchDevice}
                />
              </Box>

              <Flex column alignItems="end" p={3} pt={2}>
                {/* TODO: refactor to MntrForm and do the errors handling cleanup */}
                <ButtonGroup
                  buttons={[
                    {
                      rounded: true,
                      icon: 'save',
                      bg: 'secondary',
                      type: 'submit',
                      label: <Trans>Save</Trans>,
                    },
                  ]}
                />
              </Flex>
            </Box>
          </form>
        )
      }}
    />
  )
}

export default observer(FormEditTag)
