import { css, styled } from 'styled-components'
import ButtonTooltip from '~/components/misc/MntrButton/modules/ButtonTooltip'
import { observer } from '~/helpers/mst'

const FlagWrapper = styled.div`
  position: relative;
  width: ${({ size }) => size}px;
  height: ${({ size }) => size}px;
  border-radius: 50%;
  border: 1px solid ${({ theme }) => theme.flag.border};
  overflow: hidden;
  ${(props) =>
    props.inline &&
    css`
      display: inline-block;
      top: 2px;
      left: 2px;
    `}

  ${(props) =>
    props.disableBorder &&
    css`
      border: 0px;
    `}
`

const FlagImage = styled.img`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
`

const LANGUAGE_2_FLAG = {
  cs: 'cz',
  sk: 'sk',
  en: 'gb',
  hu: 'hu',
  pl: 'pl',
  de: 'de',
  tr: 'tr',
  fr: 'fr',
  sl: 'si',
  uk: 'ua',
  ru: 'ru',
  es: 'es',
  nl: 'nl',
  hr: 'hr',
  bs: 'ba',
  mk: 'mk',
  ro: 'ro',
  sr: 'rs',
  sv: 'se',
  sq: 'al',
  be: 'by',
  bg: 'bg',
  da: 'dk',
  et: 'ee',
  fi: 'fi',
  ga: 'ie',
  it: 'it',
  he: 'il',
  kk: 'kz',
  lt: 'lt',
  lv: 'lv',
  lb: 'lu',
  ar: 'sa',
  no: 'no',
  pt: 'pt',
  el: 'gr',
  zh: 'cn',
  hi: 'in',
  ja: 'jp',
  ko: 'kr',
  af: 'za',
  am: 'et',
  hy: 'am',
  as: 'in',
  ay: 'bo',
  az: 'az',
  bm: 'ml',
  eu: 'es',
  bn: 'bd',
  ca: 'es',
  co: 'fr',
  dv: 'mv',
  eo: 'xx', // Esperanto isn't specific to any country
  ee: 'gh',
  fy: 'nl',
  gl: 'es',
  ka: 'ge',
  gn: 'py',
  gu: 'in',
  ht: 'ht',
  ha: 'ng',
  is: 'is',
  ig: 'ng',
  id: 'id',
  jv: 'id',
  kn: 'in',
  km: 'kh',
  rw: 'rw',
  ku: 'iq',
  ky: 'kg',
  lo: 'la',
  la: 'va',
  ln: 'cd',
  lg: 'ug',
  mg: 'mg',
  ms: 'my',
  ml: 'in',
  mt: 'mt',
  mi: 'nz',
  mr: 'in',
  mn: 'mn',
  my: 'mm',
  ne: 'np',
  ny: 'mw',
  or: 'in',
  om: 'et',
  ps: 'af',
  fa: 'ir',
  pa: 'in',
  qu: 'pe',
  sm: 'ws',
  sa: 'in',
  qd: 'gb', // Scots Gaelic, using UK for now
  st: 'ls',
  sn: 'zw',
  sd: 'pk',
  si: 'lk',
  so: 'so',
  su: 'id',
  sw: 'tz',
  tl: 'ph',
  tg: 'tj',
  ta: 'in',
  tt: 'ru',
  te: 'in',
  th: 'th',
  ti: 'et',
  ts: 'za',
  tk: 'tm',
  ak: 'gh',
  ur: 'pk',
  ug: 'cn',
  uz: 'uz',
  vi: 'vn',
  cy: 'gb',
  xh: 'za',
  yi: 'xx', // Yiddish, no specific country
  yo: 'ng',
  zu: 'za',
}

const Flag = ({
  appStore,
  country,
  language,
  noTooltip,
  display,
  size = 26,
  disableBorder = false,
}) => {
  const media_language = appStore?.account.enums.media_language
  const media_country = appStore?.account.enums.media_country

  const imageUrl = `/static/flags/${country || LANGUAGE_2_FLAG[language] || 'xx'}.png`
  const element = (
    <FlagWrapper size={size} inline={display === 'inline'} disableBorder={disableBorder}>
      <FlagImage src={imageUrl} alt={`${country || language}`} width={size} height={size} />
    </FlagWrapper>
  )
  if (noTooltip) return element
  return (
    <ButtonTooltip
      button={element}
      tooltip={
        language
          ? media_language?.find((item) => item.id === language)?.text
          : media_country?.find((item) => item.code === country)?.text
      }
      placement="bottom-center"
    />
  )
}

export default observer(Flag)
