import PageContent from '~/components/layout/PageContent/PageContent'
import { Box } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const Content = ({
  appStore: { viewport, account },
  anonymous,
  children,
  visibleSidebar,
  visibleSidebarPrimary,
}) => {
  return (
    <Box px={[2, 4]} pb={4} pt="15px">
      {anonymous ? (
        <PageContent>{children}</PageContent>
      ) : (
        account.user.isActiveUser &&
        account.isLoaded &&
        account.workspace && (
          <PageContent
            visibleSidebarPrimary={visibleSidebarPrimary}
            visibleSidebar={
              typeof visibleSidebar !== 'undefined' ? visibleSidebar : viewport.visibleSidebar
            }
          >
            {children}
          </PageContent>
        )
      )}
    </Box>
  )
}

export default observer(Content)
