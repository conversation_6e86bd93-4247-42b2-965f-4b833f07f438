import { useState } from 'react'
import { Box, IStyleProps } from '~/components/misc/Mntr'
import toKebabCase from '~/helpers/toKebabCase'

interface PlaceholderFormSchema {
  name: string
  label: string
  onFocus: () => void
  onBlur: () => void
}

interface UsePlaceholdersProps {
  initialPlaceholders: string[]
}

interface UsePlaceholdersReturn {
  activePlaceholder: string
  setActivePlaceholder: (placeholder: string) => void
  generateFormSchema: () => PlaceholderFormSchema[]
}

export const usePlaceholders = ({
  initialPlaceholders,
}: UsePlaceholdersProps): UsePlaceholdersReturn => {
  const [activePlaceholder, setActivePlaceholder] = useState('')

  const generateFormSchema = (): PlaceholderFormSchema[] => {
    const uniqueItems = new Set(initialPlaceholders)

    return Array.from(uniqueItems).map((item) => {
      const itemLowerCase = toKebabCase(item)

      return {
        name: itemLowerCase,
        label: item,
        onFocus: () => setActivePlaceholder(itemLowerCase),
        onBlur: () => setActivePlaceholder(''),
      }
    })
  }

  return {
    activePlaceholder,
    setActivePlaceholder,
    generateFormSchema,
  }
}

interface IPlaceholderHighlighterProps extends IStyleProps {
  text: string
  placeholders: string[]
  activePlaceholder: string
}

export const PlaceholderHighlighter = ({
  text,
  placeholders,
  activePlaceholder,
  ...props
}: IPlaceholderHighlighterProps) => {
  const highlightPlaceholders = (content: string): string => {
    let highlightedText = content
    placeholders.forEach((placeholder) => {
      const regex = new RegExp(`\\[${placeholder}\\]`, 'gi')
      const placeholderLowerCase = toKebabCase(placeholder)
      highlightedText = highlightedText.replace(
        regex,
        (match) =>
          `<mark name="${placeholderLowerCase}" class="placeholder${
            activePlaceholder === placeholderLowerCase ? ' active' : ''
          }">${match}</mark>`,
      )
    })
    return highlightedText
  }

  return <Box dangerouslySetInnerHTML={{ __html: highlightPlaceholders(text) }} {...props} />
}
