import { css, keyframes, styled } from 'styled-components'

import { Box, Flex, IStyleProps } from '~/components/misc/Mntr'

export const AiToolsResultHeader = styled(Flex)`
  position: sticky;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.paper.background};
`

export const AiToolsResultList = styled(Flex)`
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
`

const fadeIn = keyframes`
  from {
    opacity: .01;
    transform: translateY(-20px);
  }
  to {
    opacity: .99;
    transform: translateY(0);
  }
`

interface IAiToolsResultListItemProps extends IStyleProps {
  isAccented?: boolean
  isActive?: boolean
  isLoading?: boolean
  noPadding?: boolean
}

export const AiToolsResultListItem = styled(Box)<IAiToolsResultListItemProps>`
  border-bottom: 1px solid ${({ theme }) => theme.paper.border};
  transition: opacity 0.1s cubic-bezier(0.2, 1, 0.2, 1);
  opacity: 0;

  ${({ noPadding = false }) => {
    return (
      !noPadding &&
      css`
        padding: ${({ theme }) => theme.space[3]}px;
      `
    )
  }}

  &:last-child {
    border-bottom: none;
  }

  ${[...Array(15)].map(
    (_, i) => css`
      &:nth-child(${i + 1}) {
        animation: ${fadeIn} 0.1s forwards cubic-bezier(0.2, 1, 0.2, 1);
        animation-delay: ${i * 0.03}s;
      }
    `,
  )}

  .accordion-content {
    display: none;
  }

  ${({ isActive }) => {
    return (
      isActive &&
      css`
        .accordion-content {
          display: block;
        }
      `
    )
  }}

  ${({ isLoading }) => {
    return (
      !isLoading &&
      css`
        &:hover {
          cursor: pointer;
          background-color: ${({ theme }) => theme.paper.backgroundHover};
        }
      `
    )
  }}

  ${({ isAccented }) => {
    return (
      isAccented &&
      css`
        background-color: ${({ theme }) => theme.inspector.toolbarBackground};
      `
    )
  }}

  label {
    padding: ${({ theme }) => theme.space[3]}px;
  }
`

export default AiToolsResultList
