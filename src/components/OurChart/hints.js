import { Trans } from '@lingui/react'
import { Text } from '~/components/misc/Mntr'

export function prime() {
  return (
    <>
      {/* <Trans id="help.prime" message="TODO" /> */}
      <Text>
        <a href="https://mediaboard.com/cs/TODO/" target="_blank">
          https://mediaboard.com/cs/TODO/
        </a>
      </Text>
    </>
  )
}

export function grp() {
  return (
    <>
      <Trans
        id="help.grp"
        message="The media impact aims to present the true media image of the subject as it reaches the widest group of listeners, viewers and media readers, rather than absolute numbers of articles. It is based primarily on readership (press), listenership (radio), viewership (TV) and monthly visits to the website (online). The unit of measurement of media impact is GRP (Gross Rating Points), when one GRP point corresponds to one percent of the population over 15 years of age (i.e. in Czech republic it is a group of 90,000 individuals, in Slovakia 45,000 individuals, etc.). The group consists of readers, listeners or viewers who could be addressed by the published article. A reader who could read more than one article is counted multiple times. OTS (Opportunity to See) then indicates the number of times, when a member of the target group could read or watch the article on average. In the case of the target group of all inhabitants of the Czech Republic over 15 years of age: OTS = GRP / 100."
      />
      <Text>
        <a href="https://mediaboard.com/cs/co-je-grp-a-k-cemu-slouzi/" target="_blank">
          https://mediaboard.com/cs/co-je-grp-a-k-cemu-slouzi/
        </a>
      </Text>
    </>
  )
}

export function ave() {
  return (
    <>
      <Trans
        id="help.ave"
        message="<0>The AVE Coefficient (Advertising Value Equivalent) represents a financial appreciation of media activities. It is equivalent to what would be the amount of space gained by content converted into advertising space according to the price list of the medium.</0><1>The following variables are used for AVE machine calculation:</1><2><3>the unit price of advertising in the medium (e.g.: price per standard page in the press / 1s of broadcast news on TV or radio)</3><4>size of article in press / length of reportage on TV or radio</4><5>scope of information on the topic in the paper</5></2>"
        components={{
          0: <p key={0} />,
          1: <p key={1} />,
          2: <ul key={2} />,
          3: <li key={3} />,
          4: <li key={4} />,
          5: <li key={5} />,
        }}
      />
      <Text>
        <a href="https://mediaboard.com/cs/ave-advertising-value-equivalent/" target="_blank">
          https://mediaboard.com/cs/ave-advertising-value-equivalent/
        </a>
      </Text>
    </>
  )
}

export function socialShares() {
  return (
    <Trans
      id="help.socialInteractionsOnline"
      message="<0>The number of social interactions (likes, shares, comments) for online articles on Facebook.</0><1>Statistics are updated every 24 hours.</1>"
      components={{
        0: <p key={0} />,
        1: <p key={1} />,
      }}
    />
  )
}

export function influenceScore() {
  return (
    <Trans
      id="help.influenceScore"
      message="<0>Influence Score is a number (from 1 to 10) calculated for each social media mention. This score is based on two things:</0><1><2>how likely it is for the mention to be seen,</2><3>how many times a mention has been viewed, shared, or retweeted.</3></1><4>We believe it helps you discover statements, authors, and sites that are the most popular and influential. This way your influencer marketing campaigns can get more metrics for analysis and be more data-driven.</4>"
      components={{
        0: <p key={0} />,
        1: <ol key={1} />,
        2: <li key={2} />,
        3: <li key={3} />,
        4: <p key={4} />,
      }}
    />
  )
}

export function engagementRate() {
  return (
    <Trans
      id="help.engagementRate"
      message="<0>Engagement Rate is a metric used to assess the average number of interactions the content receives per follower. It helps in relative comparison of mentions from different channels and sources. Use it to: </0><1><2>find your best & worst performing posts, </2><3>compare the engagement you generate on different social media channels, </3><4>compare your results to competitors, </4><5>evaluate influencers. </5></1>"
      components={{
        0: <p key={0} />,
        1: <ol key={1} />,
        2: <li key={2} />,
        3: <li key={3} />,
        4: <li key={4} />,
        5: <li key={5} />,
      }}
    />
  )
}

export function socialInteractions() {
  return (
    <Trans
      id="help.socialInteractions"
      message="<0>The number of social interactions (likes, shares, comments, views, retweets) for the mention.</0>"
      components={{
        0: <p key={0} />,
      }}
    />
  )
}
