import { t } from '@lingui/core/macro'
import { Trans, useLingui } from '@lingui/react/macro'
import { createElement } from 'react'
import { css, styled } from 'styled-components'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { Table, TableBody, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import getGranularityLabel from '~/helpers/charts/getGranularityLabel'
import makeGranularityMenu from '~/helpers/charts/makeGranularityMenu'
import withModalHelp from '~/helpers/modal/withModalHelp'
import { observer } from '~/helpers/mst'

const StyledBox = styled(Box)`
  background: ${({ theme }) => theme.table.background};
  ${({ aside }) =>
    aside &&
    css`
      flex: 1;
    `}
  ${({ isMobile }) =>
    isMobile
      ? css`
          overflow-x: auto;
        `
      : css`
          overflow-y: auto;
        `}
  ${({ isLoading }) =>
    isLoading
      ? css`
          opacity: 0.5;
          pointer-events: none;
        `
      : css`
          opacity: 1;
          pointer-events: auto;
        `}
`

const OurChartAdvanced = ({
  appStore: {
    account: { enums },
    capture: { isCapturing },
    chart: { downloadCSV, downloadXLS, exportChart, goFullScreen, printChart },
    viewport: { isMobile },
  },
  aside,
  asideWidthRatio,
  chart,
  chartId,
  filename,
  formatter,
  footer,
  granularity,
  hint,
  isLoading,
  leftIcon,
  setGranularity: setGranularityViaProps,
  store,
  switches = [],
  table,
  tablePlaceholder,
  tableTemplate,
  tableTemplateForSentiment,
  title,
  titleComponent,
  titleDetached,
  type,
  unit,
  zIndex,
}) => {
  const { i18n } = useLingui()

  const {
    hasMultiSeries,
    isActiveChartFullView,
    isActiveChartSentiment,
    isMultiTopic,
    isPercentageChartType,
    setChartType,
    tableData,
    toggleChartFullView,
    toggleChartSentiment,
  } = store

  const setGranularity = store.setGranularity || setGranularityViaProps

  return (
    <Box width={1}>
      {titleDetached && (
        <Box my={2} color="heading" textAlign="center" textTransform="uppercase">
          {titleComponent || title}
        </Box>
      )}
      <MntrPaper>
        <Box className="capture-single" style={{ ...(aside ? { display: 'flex' } : {}) }}>
          <Box width={aside ? 1 - asideWidthRatio : 1}>
            <MntrPaperToolbar
              actions={
                !isCapturing
                  ? [
                      switches.sentiment &&
                        (!isMultiTopic || tableData) && {
                          icon: 'mood',
                          bg: isActiveChartSentiment ? 'primary' : 'flat',
                          tooltip: t`Sentiment`,
                          onClick: () => {
                            toggleChartSentiment()
                          },
                        },
                      switches.fullView && {
                        icon: 'sort',
                        bg: isActiveChartFullView ? 'primary' : 'flat',
                        tooltip: t`Show All`,
                        onClick: () => {
                          toggleChartFullView()
                        },
                      },
                      (switches.type || switches.granularity) && {
                        icon: 'bar_chart',
                        ...(switches.granularity === 'full'
                          ? {
                              label: i18n._(getGranularityLabel(granularity)),
                            }
                          : {
                              tooltip: t`Chart Settings`,
                            }),
                        bg: 'flat',
                        popup: (closePopup) => {
                          return (
                            <MntrMenu
                              menuItems={[
                                switches.type && [
                                  {
                                    label: t`Chart Type`,
                                  },
                                  {
                                    hoverVariant: 'secondary',
                                    label: t`Bar`,
                                    leftIcon: 'bar_chart',
                                    onClick: () => setChartType('column'),
                                    rightIcon: type === 'column' ? 'check' : ' ',
                                  },
                                  {
                                    hoverVariant: 'secondary',
                                    label: t`Area`,
                                    leftIcon: 'landscape',
                                    onClick: () => setChartType('area'),
                                    rightIcon: type === 'area' ? 'check' : ' ',
                                  },
                                  hasMultiSeries && {
                                    hoverVariant: 'secondary',
                                    label: t`Percent Share`,
                                    leftIcon: 'broken_image',
                                    onClick: () => setChartType('custom-area-percentage'),
                                    rightIcon: type === 'custom-area-percentage' ? 'check' : ' ',
                                  },
                                  {
                                    hoverVariant: 'secondary',
                                    label: t`Line`,
                                    leftIcon: 'show_chart',
                                    onClick: () => setChartType('line'),
                                    rightIcon: type === 'line' ? 'check' : ' ',
                                  },
                                ],
                                {},
                                switches.granularity &&
                                  makeGranularityMenu({ granularity, setGranularity }),
                              ]}
                              closePopup={closePopup}
                            />
                          )
                        },
                        zIndex,
                      },
                      filename && {
                        icon: 'download',
                        bg: 'flat',
                        tooltip: t`Download`,
                        popup: (closePopup) => {
                          return (
                            <MntrMenu
                              menuItems={[
                                {
                                  label: t`Save in format`,
                                },
                                {
                                  leftIcon: 'insert_photo',
                                  label: 'PNG',
                                  hoverVariant: 'secondary',
                                  onClick: () => {
                                    exportChart(chartId, { type: 'image/png' })
                                  },
                                },
                                {
                                  leftIcon: 'insert_photo',
                                  label: 'JPEG',
                                  hoverVariant: 'secondary',
                                  onClick: () => {
                                    exportChart(chartId, { type: 'image/jpeg' })
                                  },
                                },
                                {
                                  leftIcon: 'insert_photo',
                                  label: 'SVG',
                                  hoverVariant: 'secondary',
                                  onClick: () => {
                                    exportChart(chartId, { type: 'image/svg+xml' })
                                  },
                                },
                                {
                                  leftIcon: 'picture_as_pdf',
                                  label: 'PDF',
                                  hoverVariant: 'secondary',
                                  onClick: () => {
                                    exportChart(chartId, { type: 'application/pdf' })
                                  },
                                },
                                {
                                  leftIcon: 'list_alt',
                                  label: 'CSV',
                                  hoverVariant: 'secondary',
                                  onClick: () => {
                                    downloadCSV(chartId)
                                  },
                                },
                                {
                                  leftIcon: 'list_alt',
                                  label: 'XLS',
                                  hoverVariant: 'secondary',
                                  onClick: () => {
                                    downloadXLS(chartId)
                                  },
                                },
                              ]}
                              closePopup={closePopup}
                            />
                          )
                        },
                        zIndex,
                      },
                      (switches.fullScreen || switches.print || hint) && {
                        icon: 'more_vert',
                        bg: 'flat',
                        tooltip: t`More`,
                        popup: (closePopup) => {
                          return (
                            <MntrMenu
                              menuItems={[
                                switches.fullScreen && {
                                  label: t`View in full screen`,
                                  leftIcon: 'fullscreen',
                                  onClick: () => {
                                    goFullScreen(chartId)
                                  },
                                },
                                switches.print && {
                                  label: t`Print`,
                                  leftIcon: 'print',
                                  onClick: () => {
                                    printChart(chartId)
                                  },
                                },
                                {},
                                hint && {
                                  label: t`Help`,
                                  leftIcon: 'help',
                                  hoverVariant: 'tertiary',
                                  ...withModalHelp({
                                    title,
                                    message: hint(),
                                  }),
                                },
                              ]}
                              closePopup={closePopup}
                            />
                          )
                        },
                        zIndex,
                      },
                    ]
                      .filter(Boolean)
                      .map((action, index, actions) => {
                        if (index === actions.length - 1) {
                          return {
                            ...action,
                            popupPlacement: 'bottom-end',
                            transformOrigin: '100% 0',
                          }
                        }

                        return action
                      })
                  : []
              }
              icon={leftIcon}
              isLoading={isLoading}
              title={!titleDetached && (titleComponent || title)}
            />
            {chart}
          </Box>
          {(table || tableTemplate || tablePlaceholder) && (
            <StyledBox
              width={asideWidthRatio}
              aside={aside}
              isMobile={isMobile}
              isLoading={isLoading}
            >
              {isLoading ? (
                <Table>
                  <TableBody>
                    <Tr>
                      <Th>
                        <Trans>Loading...</Trans>
                      </Th>
                    </Tr>
                  </TableBody>
                </Table>
              ) : (
                table ||
                (tableData !== 0 &&
                  createElement(
                    isActiveChartSentiment && tableTemplateForSentiment
                      ? tableTemplateForSentiment
                      : tableTemplate,
                    {
                      data: tableData,
                      enums,
                      formatter,
                      isMobile,
                      isMultiTopic,
                      isPercentageChartType,
                      unit,
                    },
                  ))
              )}
            </StyledBox>
          )}
        </Box>

        {/* Custom footer component */}
        {footer && <Box opacity={isLoading ? 0.5 : 1}>{footer}</Box>}
      </MntrPaper>
    </Box>
  )
}

export default observer(OurChartAdvanced)
