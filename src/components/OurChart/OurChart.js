import { useLingui } from '@lingui/react/macro'
import get from 'lodash/get'
import dynamic from 'next/dynamic'
import { useMemo } from 'react'
import { useSize } from 'react-use'
import { styled } from 'styled-components'
import { Box, Skeleton } from '~/components/misc/Mntr'
import dataTypes from '~/constants/analytics'
import { observer } from '~/helpers/mst'
import OurChartAdvanced from './OurChartAdvanced'

const SkeletonWrapper = styled(Box)`
  padding: 12px;

  ${Skeleton} {
    border-radius: 3px;
  }
`

const Renderer = ({ chartHasData, height, renderer, ...props }) => {
  let HighchartsRenderer

  if (height === 'auto') {
    // `height` should be a static prop
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const [sized] = useSize(
      ({ height: calcHeight }) => {
        return (
          <Box style={{ width: '100%', height: '100%' }}>
            <Renderer
              {...props}
              chartHasData={chartHasData}
              height={calcHeight}
              renderer={renderer}
            />
          </Box>
        )
      },
      { width: '100%', height: '100%' },
    )

    return sized
  }

  switch (renderer) {
    case 'highcharts':
    default:
      HighchartsRenderer = dynamic(() => import('./HighchartsRenderer'), {
        loading() {
          return (
            <SkeletonWrapper>
              <Skeleton borderRadius={5} size={`${height - 24}px`} fullWidth />
            </SkeletonWrapper>
          )
        },
      })

      return <HighchartsRenderer {...props} hasData={chartHasData} height={height} />
  }
}

const OurChart = ({
  appStore: {
    viewport: { isMobile },
  },
  data: rawData,
  dataLabel,
  filename: filenameBase,
  formatter,
  height = isMobile ? 300 : 200,
  footer,
  hideEmptyLabel,
  hint,
  id,
  isLoading,
  leftIcon,
  renderer,
  setGranularity,
  store = { data: rawData },
  switches,
  table,
  tablePlaceholder,
  title: titleDefault,
  titleComponent,
  titleDetached = isMobile,
  titleForSentiment,
  titleWithSuffix,
  type: defaultType,
  unit,
  view,
  zIndex, // this shit will need to go: https://trello.com/c/ZSJfPIBl
  ...overloads
}) => {
  const { i18n } = useLingui()

  // TODO: Group base props/config options here and use ?? operator with
  // "overloads" when passing props to wrapper components below.
  const baseProps = {
    ...dataTypes[id]?.tablePreset,
  }

  const {
    baseRouteParams,
    data,
    filenameSuffix,
    granularity: ownGranularity,
    isActiveChartSentiment,
    isMultiTopic,
    isPercentageChartType,
    titleSuffix: titleSuffixForTopic,
    topicCount,
    type,
  } = store

  const chartHasData = useMemo(() => {
    return get(data, 'series', []).some(({ data = {} }) => {
      return data.some(
        ({ y, weight, value, ...dataset }) =>
          Boolean(y) || Boolean(weight) || Boolean(value) || Boolean(dataset[1]),
      )
    })
  }, [data])

  const filename = filenameBase && `${filenameBase}-${filenameSuffix}`
  const titleMsg = isActiveChartSentiment && titleForSentiment ? titleForSentiment : titleDefault
  const title = titleMsg && i18n._(titleMsg)
  const titleSuffix = titleWithSuffix && titleSuffixForTopic
  const chartType = type || defaultType || (isMultiTopic ? 'line' : 'column')
  const granularity = ownGranularity || overloads.granularity
  const aside = (overloads.aside ?? baseProps.aside) && !isMobile
  const asideWidthRatio = aside ? 1 / 3 : 1

  const chart = (
    <Renderer
      baseRouteParams={baseRouteParams}
      chartHasData={chartHasData}
      data={data}
      dataLabel={dataLabel && i18n._(dataLabel)}
      filename={filename}
      formatter={formatter}
      granularity={granularity}
      height={height}
      id={id}
      isPercentageChartType={isPercentageChartType}
      renderer={renderer}
      title={title}
      titleSuffix={titleSuffix}
      topicCount={topicCount}
      type={chartType}
      unit={unit}
      // TODO: Move "overloads" to wrapper components below, since there's more
      // props to pass. They won't need to be destructured above, only the ones
      // needed for Chart (renderer).
      {...overloads}
    />
  )

  const basicChart =
    !view || view === 'bare' ? (
      chart
    ) : (
      <Box
        width={1}
        height={1}
        style={{
          ...(isLoading ? { opacity: 0.5, pointerEvents: 'none' } : {}),
          ...(!chartHasData && (isLoading || hideEmptyLabel) ? { opacity: 0 } : {}),
        }}
      >
        {chart}
      </Box>
    )

  switch (view) {
    case 'advanced':
      return (
        <OurChartAdvanced
          {...baseProps}
          aside={aside}
          asideWidthRatio={asideWidthRatio}
          chart={basicChart}
          chartHasData={chartHasData}
          chartId={id}
          filename={filename}
          formatter={formatter}
          granularity={granularity}
          hint={hint}
          isLoading={isLoading}
          leftIcon={leftIcon}
          setGranularity={setGranularity}
          store={store}
          switches={switches}
          table={isLoading ? false : table}
          tablePlaceholder={tablePlaceholder}
          title={title}
          titleComponent={titleComponent}
          titleDetached={titleDetached}
          type={chartType}
          unit={unit}
          zIndex={zIndex}
          footer={footer}
        />
      )

    case 'basic':
      return basicChart

    case 'bare':
    default:
      return chart
  }
}

export default observer(OurChart)
