import { t } from '@lingui/core/macro'
import { useLingui } from '@lingui/react/macro'
import color from 'color'
import { endOfDay, isEqual as isEqualDate, isSameWeek, isValid as isValidDate } from 'date-fns'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import 'highcharts/modules/export-data'
import 'highcharts/modules/exporting'
import 'highcharts/modules/no-data-to-display'
import 'highcharts/modules/offline-exporting'
import 'highcharts/modules/treemap'
import 'highcharts/modules/wordcloud'
import merge from 'lodash/merge'
import { Fragment, useEffect, useRef, useState } from 'react'
import { renderToStaticMarkup } from 'react-dom/server'
import { styled, useTheme } from 'styled-components'
import { WEEK_STARTS_ON } from '~/constants'
import { unit as unitFormatter } from '~/helpers/charts/formatters'
import endOfByPeriodType from '~/helpers/date/endOfByPeriodType'
import formatDate from '~/helpers/date/format'
import parseDate from '~/helpers/date/parse'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'
import { nbsp } from '~/helpers/string'

const HighchartsWrapper = styled.div`
  & tspan {
    fill: ${({ theme }) => theme.colors.lightGrey};
  }

  & .highcharts-axis-labels text {
    fill: ${({ theme }) => theme.colors.lightGrey} !important;
  }

  & .highcharts-grid-line {
    stroke: ${({ theme }) => theme.paper.border};
  }

  & .highcharts-xaxis-grid .highcharts-grid-line {
    stroke: transparent;
  }

  & .highcharts-legend tspan {
    fill: ${({ theme }) => theme.colors.black};
  }

  & .highcharts-legend .highcharts-legend-item-hidden tspan {
    opacity: 0.3;
  }
`

const HighchartsRenderer = ({
  type,
  allowNullDataPoints = ['pie', 'wordcloud', 'treemap'].includes(type),
  appStore: {
    capture: { isCapturing },
    chart: { addChart },
    router: { makeRouteWithQuery },
    viewport: { isMobile, isTouchDevice },
  },
  baseRouteParams,
  clickCallback,
  clickRoute,
  data: { series, ...optionsDefinedDuringDataMapping } = { series: [] },
  dataLabel,
  dateBoundaries,
  dynamicPointWidth,
  filename,
  formatter,
  granularity,
  handleClick = true,
  hasData,
  height,
  id,
  initial,
  isPercentageChartType,
  options,
  promotePercentage,
  stackSeries = true,
  subtitle,
  title,
  titleSuffix,
  tooltipTotalCountVisible = true,
  topicCount,
  unit,
  withTime,
  ...otherProps
}) => {
  const { i18n } = useLingui()
  const chartComponent = useRef()
  const theme = useTheme()

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const setVariableHeight = (chart) => {
    const pointWidth = chart.options?.plotOptions.series.pointWidth

    let seriesLength = 0

    for (let i = 0; i < chart.series.length; i++) {
      // set default length to 15 to avoid division by zero
      seriesLength += chart.series[i]?.yData?.length || 15
    }

    if (dynamicPointWidth && type === 'bar') {
      // calculate `pointWidth` based on chart height and number of series
      chart.update(
        {
          plotOptions: {
            bar: {
              pointWidth:
                (chart.chartHeight - 150) / seriesLength - (chart.chartHeight / seriesLength) * 0.2,
            },
          },
        },
        true,
        true,
      )
    } else if (pointWidth) {
      const length = (
        chart.series.length === 1
          ? chart.series[0].data
          : [
              ...new Set(
                chart.series.reduce((output, { data }) => {
                  return output.concat(...data.map(({ category }) => category))
                }, []),
              ),
            ]
      ).length

      const padding = 12

      chart.setSize(
        undefined,
        chart.chartHeight -
          chart.plotBox.height +
          length *
            (pointWidth * (topicCount || 1) +
              padding +
              (topicCount && topicCount > 1 ? padding / topicCount : 0)),
      )
    }
  }

  useEffect(() => {
    if (chartComponent.current?.chart) {
      setVariableHeight(chartComponent.current.chart)
    }
  }, [setVariableHeight])

  let dateOptions

  switch (granularity) {
    case 'month':
      dateOptions = {
        month: 'long',
        year: 'numeric',
      }

      break

    case 'year':
      dateOptions = {
        year: 'numeric',
      }

      break

    case 'week':
    case 'day':
    default:
      dateOptions = {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      }
  }

  if (withTime) {
    dateOptions = {
      ...dateOptions,
      hour: 'numeric',
      minute: 'numeric',
    }
  }

  const shouldHandleClick = !isTouchDevice && handleClick
  const [isFullScreen, setIsFullScreen] = useState()

  return (
    <HighchartsWrapper>
      <HighchartsReact
        highcharts={Highcharts}
        options={
          initial
            ? merge({ series }, options)
            : merge(
                {
                  ...(series && series[0]?.data.length === 0
                    ? {}
                    : hasData || allowNullDataPoints
                      ? {
                          series: series?.map((series) => {
                            return {
                              ...series,
                              data: series.data.map((point) => {
                                if (Array.isArray(point)) {
                                  return point
                                }

                                return {
                                  ...point,
                                  ...(point.y === 0 ? { borderColor: 'transparent' } : {}),
                                }
                              }),
                            }
                          }),
                        }
                      : {}),
                },
                {
                  chart: {
                    animation: !isCapturing,
                    backgroundColor: isFullScreen ? '#fff' : 'transparent',
                    events: {
                      fullscreenOpen() {
                        setIsFullScreen(true)
                      },
                      fullscreenClose() {
                        setIsFullScreen(false)
                      },
                    },
                    height,
                    style: {
                      fontFamily: theme.fontFamily,
                    },
                    type: type.startsWith('custom-') ? type.split('-')[1] : type,
                    ...(['pie', 'wordcloud', 'treemap'].includes(type)
                      ? {
                          spacing: 20,
                        }
                      : {
                          spacingBottom: 20,
                          spacingRight: 20,
                        }),
                  },
                  credits: {
                    enabled: false,
                  },
                  exporting: {
                    chartOptions: {
                      legend: {
                        itemStyle: {
                          color: '#686868',
                          fontSize: '11px',
                          fontWeight: 'normal',
                        },
                      },
                      chart: {
                        backgroundColor: '#fff',
                        height: height * 1.5,
                      },
                      plotOptions: {
                        pie: {
                          showInLegend: false,
                          dataLabels: {
                            enabled: true,
                            formatter() {
                              return renderToStaticMarkup(
                                <>
                                  <br />
                                  <span
                                    style={{
                                      color: color(this.color)
                                        .darken(theme.chart.darken)
                                        .toString(),
                                    }}
                                  >
                                    <span dangerouslySetInnerHTML={{ __html: this.key }} />{' '}
                                    {unitFormatter(this.percentage, '%', 1, true)}
                                  </span>
                                  <br />
                                  <span style={{ color: '#333', whiteSpace: 'pre' }}>
                                    {formatter(this.y, unit)}
                                  </span>
                                </>,
                              )
                            },
                          },
                        },
                      },
                      subtitle: {
                        text: subtitle,
                      },
                      title: {
                        text: titleSuffix ? `${title} ${titleSuffix}` : title,
                        style: {
                          fontWeight: 'normal',
                        },
                      },
                      ...(type === 'treemap'
                        ? {}
                        : {
                            yAxis: {
                              title: {
                                style: {
                                  color: '#686868',
                                },
                              },
                            },
                          }),
                    },
                    buttons: {
                      contextButton: {
                        enabled: false,
                      },
                    },
                    filename,
                    pdfFont: {
                      normal: '/static/lato_400_normal.ttf',
                      italic: '/static/lato_400_italic.ttf',
                      bold: '/static/lato_700_normal.ttf',
                    },
                  },
                  legend: {
                    itemStyle: {
                      color: theme.colors.black,
                    },
                  },
                  noData: {
                    style: {
                      color: theme.colors.black,
                      fontSize: '11px',
                      fontWeight: 'normal',
                    },
                  },
                  plotOptions: {
                    area: {
                      dataLabels: {
                        enabled: false,
                      },
                      stacking: 'normal',
                    },
                    bar: {
                      dataLabels: {
                        align: 'right',
                        enabled: true,
                        formatter() {
                          if (this.y > 0) {
                            const formattedNumber = formatter(this.y, unit)

                            return renderToStaticMarkup(
                              <span
                                style={{
                                  color: color(this.color).darken(theme.chart.darken).toString(),
                                }}
                              >
                                {formattedNumber
                                  .toString()
                                  .split(/\s/)
                                  .reduce((output, part, partIndex, parts) => {
                                    // Trim the last space-separated part of
                                    // `formattedNumber`, which can contain
                                    // non-breaking spaces.
                                    if (partIndex < parts.length - 1) {
                                      return nbsp(`${output} ${part}`)
                                    }

                                    return output
                                  })}
                              </span>,
                            )
                          }
                        },
                        shadow: false,
                        style: { textOutline: false },
                      },
                      groupPadding: 0.1,
                      pointPadding: 0.1,
                      stacking: stackSeries ? 'normal' : undefined,
                    },
                    column: {
                      dataLabels: {
                        enabled: false,
                        color: 'white',
                      },
                      maxPointWidth: 40,
                      stacking: stackSeries ? 'normal' : undefined,
                    },
                    pie: {
                      dataLabels: {
                        enabled: true,
                        y: -20,
                        formatter() {
                          const formattedPercentage = unitFormatter(this.percentage, '%', 1, true)

                          return renderToStaticMarkup(
                            <>
                              <br />
                              <span
                                style={{
                                  color: color(this.color).darken(theme.chart.darken).toString(),
                                }}
                              >
                                <span dangerouslySetInnerHTML={{ __html: this.key }} />{' '}
                                {!promotePercentage && formattedPercentage}
                              </span>
                              <br />
                              <span style={{ color: theme.colors.black, whiteSpace: 'pre' }}>
                                {this.point && this.point.emoji && (
                                  <>
                                    {this.point.emoji}
                                    {'  '}
                                  </>
                                )}
                                {promotePercentage ? formattedPercentage : formatter(this.y, unit)}
                              </span>
                            </>,
                          )
                        },
                        useHTML: true,
                      },
                      showInLegend: true,
                    },
                    series: {
                      animation: !isCapturing,
                      borderRadius: 0,
                      cursor: shouldHandleClick ? 'pointer' : 'default',
                      point: {
                        events: {
                          ...(shouldHandleClick
                            ? {
                                click(event) {
                                  const lower_date = formatDate(this.x, 'yyyy-MM-dd')

                                  const upper_date = formatDate(
                                    granularity &&
                                      (granularity === 'day'
                                        ? this.x
                                        : endOfByPeriodType(this.x, granularity)),
                                    'yyyy-MM-dd',
                                  )

                                  if (clickCallback) {
                                    clickCallback(this)
                                  } else {
                                    const route = makeRouteWithQuery(clickRoute, undefined, {
                                      ...baseRouteParams,
                                      ...([lower_date, upper_date].includes('1970-01-01')
                                        ? {}
                                        : {
                                            lower_date,
                                            upper_date,
                                          }),
                                      ...this.routeParams,
                                    })

                                    // Items in the "publisher" category with null `id` should not be clickable.
                                    // For more context, see Slack issue: https://imper.slack.com/archives/CB3DFVCMB/p1731930774179809
                                    if (this.routeParams?.publisher !== null) {
                                      if (event.ctrlKey || event.metaKey) {
                                        window.open(route, '_blank')
                                      } else {
                                        routerPush(route)
                                      }
                                    }
                                  }
                                },
                              }
                            : {}),
                        },
                      },
                    },
                    treemap: {
                      dataLabels: {
                        enabled: true,
                        formatter() {
                          return renderToStaticMarkup(
                            <>
                              <span
                                dangerouslySetInnerHTML={{ __html: this.key }}
                                style={{
                                  color: color(this.color).darken(theme.chart.darken).toString(),
                                }}
                              />
                              {this.point.value && (
                                <>
                                  <br />
                                  <span
                                    style={{
                                      color: color(this.color)
                                        .darken(theme.chart.darken)
                                        .toString(),
                                    }}
                                  >
                                    {formatter(this.point.value)}
                                  </span>
                                </>
                              )}
                            </>,
                          )
                        },
                        style: { textOutline: false },
                      },
                    },
                    wordcloud: {
                      style: {
                        fontWeight: 700,
                      },
                    },
                  },
                  subtitle: {
                    text: isFullScreen ? subtitle : '',
                  },
                  title: {
                    text: isFullScreen ? `${titleSuffix ? `${title} ${titleSuffix}` : title}` : '',
                  },
                  tooltip: {
                    animation: false,
                    backgroundColor: theme.chart.tooltipBackground,
                    borderRadius: 3,
                    borderWidth: 0,
                    ...(formatter
                      ? {
                          formatter() {
                            let date = new Date(this.key)
                            let title

                            if (isValidDate(date)) {
                              if (granularity === 'week' && dateBoundaries) {
                                const lowerDateBoundary = parseDate(dateBoundaries.lower_date)

                                if (
                                  isSameWeek(lowerDateBoundary, date, {
                                    weekStartsOn: WEEK_STARTS_ON,
                                  })
                                ) {
                                  date = lowerDateBoundary
                                }
                              }

                              let upperDate = endOfByPeriodType(date, granularity)

                              if (granularity === 'week' && dateBoundaries) {
                                const upperDateBoundary = parseDate(dateBoundaries.upper_date)

                                if (
                                  isSameWeek(upperDateBoundary, date, {
                                    weekStartsOn: WEEK_STARTS_ON,
                                  })
                                ) {
                                  upperDate = upperDateBoundary
                                }
                              }

                              title = `${i18n.date(date, dateOptions)}${
                                ['day', 'week'].includes(granularity) &&
                                !isEqualDate(endOfDay(date), upperDate)
                                  ? ` - ${i18n.date(upperDate, dateOptions)}`
                                  : ''
                              }${
                                granularity && granularity === 'day'
                                  ? ` (${formatDate(date, 'iiii')})`
                                  : ''
                              }`
                            } else if (typeof this.x === 'string') {
                              title = this.x
                            } else {
                              title = this.key
                            }

                            if (this.point && this.point.emoji) {
                              title = `${title} ${this.point.emoji}`
                            }

                            return renderToStaticMarkup(
                              <>
                                <span
                                  style={{ color: '#fff' }}
                                  dangerouslySetInnerHTML={{ __html: title }}
                                />
                                {this.points ? (
                                  this.points.map(
                                    ({
                                      color: rawColor,
                                      percentage,
                                      point: {
                                        formatter: pointFormatter,
                                        id,
                                        series: { name },
                                      },
                                      y,
                                    }) => {
                                      const hasName = name && !name.toLowerCase().includes('series')
                                      const pointLabelColor = color(rawColor)

                                      const fmt = (value) => {
                                        return pointFormatter?.fn
                                          ? pointFormatter.fn(value, pointFormatter.unit || unit)
                                          : formatter(value, unit)
                                      }

                                      return (
                                        <Fragment key={id}>
                                          <br />
                                          {typeof this.x === 'string' && !hasName ? (
                                            <strong style={{ color: '#fff' }}>{fmt(y)}</strong>
                                          ) : (
                                            <>
                                              <span
                                                dangerouslySetInnerHTML={{
                                                  __html: `${hasName ? name : dataLabel}:`,
                                                }}
                                                style={{
                                                  color: pointLabelColor.isDark()
                                                    ? color(pointLabelColor).lighten(0.5)
                                                    : rawColor,
                                                }}
                                              />{' '}
                                              {isPercentageChartType
                                                ? unitFormatter(percentage, '%', 1, true)
                                                : fmt(y)}
                                            </>
                                          )}
                                        </Fragment>
                                      )
                                    },
                                  )
                                ) : (
                                  <>
                                    <br />
                                    <strong style={{ color: '#fff' }}>
                                      {formatter(!isNaN(this.y) ? this.y : this.point.value, unit)}
                                    </strong>
                                  </>
                                )}
                                {tooltipTotalCountVisible &&
                                  this.points &&
                                  this.points.length > 1 &&
                                  !isPercentageChartType &&
                                  !this.points.some(({ point }) => point.formatter?.fn) && (
                                    <>
                                      <br />
                                      <strong style={{ color: '#fff' }}>
                                        {t`Total: ${formatter(
                                          this.points.reduce((sum, { y }) => sum + y, 0),
                                          unit,
                                        )}`}
                                      </strong>
                                    </>
                                  )}
                              </>,
                            )
                          },
                        }
                      : {}),
                    hideDelay: 200,
                    outside: true,
                    padding: 4,
                    shadow: false,
                    shared: true,
                    style: {
                      color: '#fff',
                    },
                  },
                  xAxis: {
                    labels: {
                      formatter() {
                        if (this.isFirst && granularity === 'week' && dateBoundaries) {
                          this.value = Date.parse(dateBoundaries.lower_date)
                        }

                        const date = new Date(this.value)

                        if (
                          isValidDate(date) &&
                          !optionsDefinedDuringDataMapping?.xAxis?.categories
                        ) {
                          return i18n.date(date, {
                            ...dateOptions,
                            ...(['day', 'week'].includes(granularity)
                              ? {
                                  year: undefined,
                                }
                              : {}),
                          })
                        }

                        return this.axis.defaultLabelFormatter.call(this)
                      },
                    },
                    showEmpty: false,
                  },
                  yAxis: {
                    reversedStacks: false,
                    stackLabels: {
                      enabled: false,
                    },
                    title: {
                      text: isMobile ? '' : isPercentageChartType ? '%' : dataLabel,
                      style: {
                        color: theme.colors.mediumGrey,
                      },
                    },
                  },
                },
                { ...optionsDefinedDuringDataMapping },
                options,
              )
        }
        callback={(chart) => {
          // Highcharts creates a separate chart instance during export
          if (!chart.options.chart.forExport) {
            addChart(id, chart)
            setVariableHeight(chart)
          }
        }}
        id={id}
        {...otherProps}
        ref={chartComponent}
      />
    </HighchartsWrapper>
  )
}

export default observer(HighchartsRenderer)
