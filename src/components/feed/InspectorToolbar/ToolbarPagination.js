// eslint-disable-next-line
import { Plural } from '@lingui/react/macro'
import { styled } from 'styled-components'

const LabelWrapper = styled.span`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.lightGrey};
  display: inline-block;
  margin-right: 14px;
`

const LabelOlderWrapper = styled.span`
  color: ${({ theme }) => theme.colors.grey};
  display: inline-block;
  margin-left: 14px;
`

const ToolbarPagination = ({ isDashboard, inspector, totalCount, disableOlder }) => {
  const olderCount = inspector.feedIndex + 1 - totalCount
  const isOlder = olderCount > 0 && !disableOlder

  return (
    <LabelWrapper>
      {isDashboard && inspector.feedIndex + 1 > 0 && <>{inspector.feedIndex + 1}.</>}
      {!isDashboard && totalCount > 0 && (
        <>
          {isOlder ? `${totalCount} / ${totalCount}` : `${inspector.feedIndex + 1} / ${totalCount}`}
        </>
      )}
      {!isDashboard && isOlder && (
        <LabelOlderWrapper>
          <Plural value={olderCount} one="+ # older" other="+ # older" />
        </LabelOlderWrapper>
      )}
    </LabelWrapper>
  )
}

export default ToolbarPagination
