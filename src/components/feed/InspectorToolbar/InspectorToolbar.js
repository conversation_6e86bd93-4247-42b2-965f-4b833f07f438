import { Trans } from '@lingui/react/macro'
import { css, styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrAvatar from '~/components/misc/MntrAvatar/MntrAvatar'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { StyledFavicon } from '~/components/misc/MntrButton/style/StyledFavicon'
import isEmpty from '~/helpers/isEmpty'
import ToolbarPagination from './ToolbarPagination'

export const StyledPaperToolbar = styled.div`
  background: ${({ theme }) => theme.inspector.toolbarBackground};
  color: ${({ theme }) => theme.colors.black};
  height: 54px;
  line-height: 54px;
  font-size: 18px;
  overflow: hidden;
  box-shadow: inset 0 0 13px 1px rgba(0, 0, 0, 0.03);
  padding-right: 12px;
`
const IconWrapper = styled.div`
  display: inline-block;
  width: 54px;
  text-align: center;
`

const ModalTitle = styled.div`
  display: inline-block;
  padding-left: ${({ hasicon, theme }) => (hasicon ? 0 : theme.space[2])}px;
`
const WrapperTitle = styled.div`
  display: flex;
  box-sizing: border-box;
  flex-wrap: nowrap;
  position: relative;
  width: 100%;
  padding-left: ${({ theme }) => theme.space[1]}px;
`

const IconContent = styled.div`
  position: relative;
  top: 4px;
`

const RightCol = styled.div`
  margin-right: ${({ hasicon, theme }) => (hasicon ? 0 : theme.space[2])}px;
`

const BoxAlignRight = styled(Box)`
  text-align: right;

  ${({ totalCount, isTitleAfterScroll }) => {
    if (totalCount && isTitleAfterScroll) {
      return css`
        flex: 0 0 270px;
      `
    }

    if (isTitleAfterScroll) {
      return css`
        flex: 0 0 100px;
      `
    }

    return css`
      flex-grow: 1;
      width: auto;
    `
  }}
`

const MediaIconWrapper = styled.div`
  display: inline-block;
  margin-left: 20px;
  position: relative;
  top: 3px;
`

const Separator = styled.span`
  background-color: rgba(0, 0, 0, 0.176);
  display: inline-block;
  height: 32px;
  margin-left: 8px;
  width: 1px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 56px;
  position: relative;
  top: 9px;
  margin-right: 14px;
`

const FavIconPosition = styled.div`
  position: absolute;
  top: -34px;
  left: 12px;
`

const InspectorToolbar = ({
  viewPreview,
  viewAnnotation,
  markViewSource,
  icon,
  favicon,
  title,
  titleAfterScroll,
  actions = [],
  withPagination,
  inspector,
  totalCount,
  disableOlder,
  isDashboard,
  avatar,
  viewport,
}) => {
  const leftActions = []
  const displayViewPreview = viewPreview && inspector?.data.big_image_url

  if (displayViewPreview) {
    leftActions.push({
      label: <Trans>View preview</Trans>,
      onClick: () => markViewSource(true),
      isChip: true,
      bg: 'tertiary',
      ml: 3,
    })
  }

  if (viewAnnotation && viewport.isTablet) {
    leftActions.push({
      icon: 'fact_check',
      bg: 'tertiary',
      size: 'small',
      ml: displayViewPreview ? 1 : 3,
      tooltip: <Trans>Annotation</Trans>,
      onClick: () => inspector.annotation.enable(),
    })
  }

  if (inspector?.data.demographics && viewport.isTablet) {
    leftActions.push({
      icon: 'supervised_user_circle',
      bg: 'tertiary',
      size: 'small',
      ml: displayViewPreview ? 1 : 3,
      tooltip: <Trans>Demographics</Trans>,
      onClick: () => inspector.demographicsData.enable(),
    })
  }

  return (
    <StyledPaperToolbar>
      <Flex flexWrap="nowrap">
        <Box>
          {leftActions.map((button, index) => {
            return (
              <MntrButton
                key={index.toString()}
                ml={index !== actions.length - 1 ? 1 : 0}
                {...button}
              />
            )
          })}
          {avatar && (
            <MediaIconWrapper>
              <IconContent>
                <MntrAvatar src={avatar} size={32} shadow />
              </IconContent>
            </MediaIconWrapper>
          )}
          {favicon && (
            <IconWrapper>
              <IconContent>
                <FavIconPosition>
                  <StyledFavicon favicon={favicon} size={32} />
                </FavIconPosition>
              </IconContent>
            </IconWrapper>
          )}
          {icon && (
            <IconWrapper>
              <IconContent>
                <Icon size={28}>{icon}</Icon>
              </IconContent>
            </IconWrapper>
          )}
          <ModalTitle hasicon={icon ? 1 : 0}>{title}</ModalTitle>
        </Box>
        {titleAfterScroll && <WrapperTitle>{titleAfterScroll}</WrapperTitle>}
        <BoxAlignRight
          totalCount={totalCount > 0 ? 1 : 0}
          isTitleAfterScroll={titleAfterScroll ? 1 : 0}
        >
          <RightCol>
            {withPagination && (
              <ToolbarPagination
                isDashboard={isDashboard}
                inspector={inspector}
                totalCount={totalCount}
                disableOlder={disableOlder}
              />
            )}

            {actions.map((button, index) => {
              if (isEmpty(button)) {
                return <Separator key={index.toString()} />
              }

              return (
                <MntrButton
                  key={index.toString()}
                  {...button}
                  mr={index !== actions.length - 1 ? 1 : 0}
                />
              )
            })}
          </RightCol>
        </BoxAlignRight>
      </Flex>
    </StyledPaperToolbar>
  )
}

export default InspectorToolbar
