import { styled } from 'styled-components'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import ToolbarPagination from './ToolbarPagination'

const StyledPaperToolbar = styled.div`
  background: ${({ theme }) => theme.paper.background};
  color: ${({ theme }) => theme.colors.black};
  height: 54px;
  line-height: 54px;
  font-size: 18px;
  overflow: hidden;
  position: fixed;
  bottom: env(safe-area-inset-bottom);
  width: 100%;
  box-shadow: inset 0 0 13px 1px rgba(0, 0, 0, 0.03);
`

const BoxMiddle = styled(Box)`
  text-align: center;
`

const InspectorFooterToolbar = ({
  inspector,
  totalCount,
  disableOlder,
  disableLoadNext,
  previousArticle,
  nextArticle,
  isDashboard,
}) => {
  return (
    <StyledPaperToolbar>
      <Flex flexWrap="nowrap">
        <Box>
          <MntrButton
            icon="chevron_left"
            disabled={inspector.feedIndex === 0}
            onClick={previousArticle}
            ml={4}
          />
        </Box>
        <BoxMiddle width={1}>
          <ToolbarPagination
            inspector={inspector}
            totalCount={totalCount}
            disableOlder={disableOlder}
            isDashboard={isDashboard}
          />
        </BoxMiddle>
        <Box>
          <MntrButton
            icon="chevron_right"
            disabled={disableLoadNext || inspector.feed?.disableLoadNext}
            onClick={nextArticle}
            mr={4}
          />
        </Box>
      </Flex>
    </StyledPaperToolbar>
  )
}

export default InspectorFooterToolbar
