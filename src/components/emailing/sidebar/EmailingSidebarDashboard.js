import { t } from '@lingui/core/macro'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const EmailingSidebarDashboard = ({ activePath, mobileNavigation, appStore: { account } }) => {
  const menuItems = []
  const menuItemsAuthorList = []

  const sharedProps = {
    accent: true,
    rounded: !mobileNavigation,
  }

  // Emailing Campaigns
  menuItems.push({
    label: t`Campaigns`,
    leftIcon: 'mail',
    selected: activePath === 'emailing',
    href: `/emailing`,
    ...sharedProps,
  })

  // Emailing Settings
  if (account.workspace?.permissions.newsroom_emailing.can_write) {
    menuItems.push({
      label: t`Settings`,
      leftIcon: 'settings',
      selected: activePath === 'settings',
      href: `/emailing/settings`,
      ...sharedProps,
    })
  }

  if (!account.workspace?.permissions.newsroom_emailing.can_read) {
    return null
  }

  return (
    <>
      <MntrMenu menuItems={menuItems} />
      <Box mt={-2}>
        <MntrMenu menuItems={menuItemsAuthorList} />
      </Box>
    </>
  )
}

export default observer(EmailingSidebarDashboard)
