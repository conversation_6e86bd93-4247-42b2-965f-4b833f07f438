import { PropsWithChildren } from 'react'
import PromoEmailing from '~/components/emailing/content/promo/PromoEmailing'
import EmailingSenderContent from '~/components/emailing/content/sender/EmailingSenderContent'
import EmailingSenderVerifyContent from '~/components/emailing/content/sender/EmailingSenderVerifyContent'
import { PageContentOffset } from '~/components/layout/PageContent/PageContent'
import Sidebar from '~/components/layout/Sidebar/Sidebar'
import PageGuard from '~/components/misc/PageGuard/PageGuard'
import { ObservedFC, observer } from '~/helpers/msts'

const EmailingPageGuard: ObservedFC<PropsWithChildren<{ condition?: boolean }>> = ({
  appStore: { account, emailing },
  children,
  condition = account.workspace?.permissions.newsroom_emailing.can_read,
}) => {
  return (
    <PageGuard
      condition={condition}
      fallback={
        !account.workspace?.permissions.newsroom_emailing.can_read && (
          <>
            <Sidebar activeMainNav="emailing" />
            <PageContentOffset>
              <PromoEmailing />
            </PageContentOffset>
          </>
        )
      }
    >
      {emailing.settings.senders.isLoaded && (
        <PageGuard
          condition={!emailing.settings.senders.allInvalid || !emailing.settings.senders.isEmpty}
          fallback={
            <>
              <Sidebar activeMainNav="emailing" />
              <EmailingSenderContent
                disabled={!account.workspace?.permissions.newsroom_emailing.can_write}
              />
            </>
          }
        >
          <PageGuard
            condition={emailing.settings.senders.isValid}
            fallback={
              <>
                <Sidebar activeMainNav="emailing" />
                <EmailingSenderVerifyContent />
              </>
            }
          >
            {children}
          </PageGuard>
        </PageGuard>
      )}
    </PageGuard>
  )
}

export default observer(EmailingPageGuard)
