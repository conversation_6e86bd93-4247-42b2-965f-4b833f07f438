import { t } from '@lingui/core/macro'
import copy from 'copy-html-to-clipboard'
import identityFn from 'lodash/identity'
import { useEffect, useState } from 'react'
import { Field, Form } from 'react-final-form'
import sendersVerifications from '~/components/emailing/constants/senderVerifications.constants'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { Box, ButtonGroup, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import MntrSegmentControl from '~/components/misc/MntrSegmentControl/MntrSegmentControl'
import { observer } from '~/helpers/mst'

const FormSenderSettings = ({
  onSubmit,
  initialValues,
  closeModal,
  appStore: { account, notification },
}) => {
  const [smtpErrorMessage, setSmtpErrorMessage] = useState(initialValues.smtp_error)

  useEffect(() => {
    setSmtpErrorMessage(initialValues.smtp_error)
  }, [initialValues.smtp_error])

  // data for input select
  const smtpEncryptionMethod = account.enums.emailing.smtp_encryption_method.map((item) => ({
    label: item.text,
    value: item.id,
  }))

  // data for input select
  const backendVerificationMethod = account.enums.emailing.email_backend
    .filter((item) => item.is_user_configurable === true)
    .map((item) => ({
      label: item.text,
      value: item.id,
      id: item.id,
    }))

  // TODO: Factor this out of FC scope in case of reuse.
  const renderHeading = (description) => {
    return (
      <Box p={2}>
        <Text>{description}</Text>
      </Box>
    )
  }

  // TODO: Factor this out of FC scope in case of reuse.
  const renderFields = (fields) => {
    return fields.map((field, index) => {
      return (
        <Flex key={index} my={2} px={2} flex={1}>
          <Flex flex={1}>
            <Field {...field} autoComplete="off" />
          </Flex>
          {field.copyToClipboard && (
            <Flex justifyContent="middle" alignItems="end" ml={2}>
              <MntrButton
                icon="content_copy"
                tooltip={t`Copy to clipboard`}
                onClick={() => {
                  copy(field.copyToClipboard)

                  notification.add(t`Copied to the clipboard.`, 'success')
                }}
              />
            </Flex>
          )}
        </Flex>
      )
    })
  }

  const fieldsSmtp = [
    {
      name: 'smtp_host',
      label: t`Host`,
      placeholder: t`Enter the hostname of your SMTP server`,
      component: MntrTextFieldAdapter,
      tooltip: t`Enter the hostname of your SMTP server. This is usually in the format of "smtp.yourdomain.com".`,
    },
    {
      name: 'smtp_username',
      label: `${t`Username`} (${t`optional`})`,
      placeholder: t`Enter the username to login to SMTP server`,
      component: MntrTextFieldAdapter,
      tooltip: t`Enter the username to login to SMTP server. If left blank, "${initialValues.email}" is used by default.`,
    },
    {
      name: 'smtp_password',
      label: t`Password`,
      type: 'password',
      placeholder: t`Enter the password to login to SMTP server`,
      component: MntrTextFieldAdapter,
      tooltip: t`Enter the password to login to SMTP server. This is usually the same password you use for your email.`,
      parse: identityFn,
    },
    {
      name: 'smtp_port',
      label: `${t`Port`} (${t`optional`})`,
      placeholder: t`Leave blank to use the default port`,
      type: 'number',
      component: MntrTextFieldAdapter,
      tooltip: t`This is the port number that your SMTP server uses to send email. If you're not sure, leave it blank to use the default port.`,
    },
    {
      label: t`Encryption method`,
      component: MntrSelectAdapter,
      items: smtpEncryptionMethod,
      name: 'smtp_encryption_method',
      tooltip: t`Select the encryption method used by your SMTP server.`,
    },
  ]

  const fieldsDkimHostName = [
    {
      name: 'dns.dkim_hostname',
      label: `${t`Hostname`} (${t`TXT record`})`,
      disabled: true,
      fullWidth: true,
      component: MntrTextFieldAdapter,
      copyToClipboard: initialValues.dns?.dkim_hostname,
    },
  ]

  const fieldsDkimValue = [
    {
      name: 'dns.dkim_value',
      label: `${t`Value`}`,
      disabled: true,
      component: MntrTextFieldAdapter,
      copyToClipboard: initialValues.dns?.dkim_value,
    },
  ]

  return (
    <Form
      onSubmit={(model) => {
        return onSubmit(model)
          .then((res) => {
            if (typeof closeModal === 'function' && res) {
              closeModal()
            }
            return res
          })
          .catch((err) => {
            if (err.smtp_error) {
              setSmtpErrorMessage(err.smtp_error)
            }

            return err
          })
      }}
      initialValues={initialValues}
      render={({ handleSubmit, values, form, submitting }) => {
        const actionsSmtp = []
        const actionsDns = []

        // if DNS settings are available, show verify button
        if (initialValues.dns) {
          actionsDns.push({
            tooltip: t`Test DNS Settings`,
            label: t`Validate`,
            bg: 'tertiary',
            onClick: () => {
              form.change('skip_close_modal', false)
              // handleSubmit()
              initialValues.verifyDns()
            },
            rounded: true,
            disabled: submitting,
          })
        }

        // Modal footer actions
        let submitActions = []

        if (values.email_backend.id === sendersVerifications.SMTP_VERIFICATION) {
          submitActions = actionsSmtp
        }

        if (values.email_backend.id === sendersVerifications.DNS_VERIFICATION) {
          submitActions = actionsDns
        }

        submitActions.push(closeModal)
        submitActions.push({
          label: t`Save`,
          type: 'submit',
          disabled: submitting,
          bg: 'secondary',
          rounded: true,
        })

        // Segment control options
        const options = [...backendVerificationMethod]

        return (
          <form onSubmit={handleSubmit} className="scroll-content-wrapper">
            <Box className="scroll-content">
              <Box p={3}>
                <Heading fontSize={2} mb={2}>{t`Select verification method`}</Heading>
                <Text>{t`Verifying your email address with SMTP or DNS can improve email deliverability, protect against spoofing, improve sender reputation, and provide better analytics. It demonstrates legitimacy and helps email providers ensure that emails are not spam.`}</Text>
              </Box>
              <Box mb={3}>
                <MntrSegmentControl
                  options={options}
                  value={values.email_backend.id}
                  onChange={(id) => {
                    form.change('email_backend', { id: id })
                  }}
                />
              </Box>
              <Box bg="background">
                {/* Tab 1 - No verification */}
                {values.email_backend.id === sendersVerifications.NO_VERIFICATION && (
                  <Box px={3} py={3}>
                    <MntrHint
                      color="tertiary"
                      background="beigeSecondary"
                      heading={t`We recommend that you verify your email address`}
                      text={t`Without a verified email address, your emails risk being marked as spam and rejected by providers, potentially damaging your reputation. You will also be limited to sending an email to only ${values.unverified_recipients_limit} recipients at a time.`}
                    />
                  </Box>
                )}

                {/* DNS Settings - null values */}
                {!initialValues.dns &&
                  values.email_backend.id === sendersVerifications.DNS_VERIFICATION && (
                    <Box px={3} py={3}>
                      <MntrHint
                        color="tertiary"
                        background="beigeSecondary"
                        heading={t`The DNS Verification Is Unavailable`}
                        text={t`The DNS verification settings for this email are not accessible. We suggest opting for SMTP (Simple Mail Transfer Protocol) as an alternative way of verification. If you need any additional information or help, our support team is here to assist you.`}
                      />
                    </Box>
                  )}

                {/* DNS Settings - with initial values */}
                {initialValues.dns &&
                  values.email_backend.id === sendersVerifications.DNS_VERIFICATION && (
                    <Box py={1}>
                      {/* DKIM Settings */}
                      <Box m={3}>
                        <MntrPaper>
                          <MntrPaperToolbar title={`DKIM ${t`Settings`}`} bg="light" />

                          {values.dns?.dkim_error && (
                            <Box mb={2} px={2}>
                              <MntrHint text={values.dns.dkim_error} color="error" />
                            </Box>
                          )}
                          {renderHeading(
                            t`Configuring DKIM (DomainKeys Identified Mail) enhances the integrity and authenticity of your emails, reducing the likelihood of them being marked as spam:`,
                          )}

                          <Flex>
                            <Flex flex={1}>{renderFields(fieldsDkimHostName)}</Flex>
                            <Flex alignItems="center" gap={1} pr={3}>
                              <Flex mt={3} gap={1}>
                                <strong>{t`Domain`}:</strong>
                                <Text>{initialValues.dns.cname_hostname}</Text>
                              </Flex>
                            </Flex>
                          </Flex>
                          {renderFields(fieldsDkimValue)}
                        </MntrPaper>
                      </Box>
                    </Box>
                  )}

                {/* Tab 3 - SMTP Settings */}
                {values.email_backend.id === sendersVerifications.SMTP_VERIFICATION && (
                  <Box py={1}>
                    <Box m={3}>
                      <MntrPaper>
                        <MntrPaperToolbar title={`SMTP ${t`Settings`}`} bg="light" />
                        {/* SMTP Settings */}
                        {renderHeading(
                          t`To ensure the successful delivery of emails from our system, it's necessary to configure your SMTP server with the following details:`,
                        )}

                        {smtpErrorMessage && (
                          <Box mb={2} px={2}>
                            <MntrHint text={smtpErrorMessage} color="error" />
                          </Box>
                        )}

                        {renderFields(fieldsSmtp)}
                      </MntrPaper>
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>
            <Flex column alignItems="end" p={3} pt={2}>
              <ButtonGroup buttons={submitActions} />
            </Flex>
          </form>
        )
      }}
    />
  )
}

export default observer(FormSenderSettings)
