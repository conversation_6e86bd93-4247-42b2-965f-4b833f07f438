import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import color from 'color'
import { useState } from 'react'
import { styled } from 'styled-components'
import BasketIcon from '~/components/layout/Sidebar/modules/SidebarBaskets/BasketIcon'
import AdditionalInfoTagsOrListsAuthors from '~/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors'
import AuthorHeader from '~/components/medialist/content/FeedMedialist/AuthorHeader'
import { Author } from '~/components/medialist/content/FeedMedialist/ItemProps'
import { Box, Flex, Modal, Skeleton } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { ObservedFC, observer } from '~/helpers/msts'
import { IAuthorBasketDefinitionsStoreArrItem } from '~/store/models/authors/Baskets/AuthorBasketDefinitionsStoreArrItem'

const StyledFlex = styled(Flex)`
  position: relative;
  flex-wrap: nowrap;
  width: 100%;
  justify-content: space-between;
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: 8px;
  margin-bottom: 5px;
  flex-grow: 1;
  word-break: normal;
`

const LoadMoreLink = styled(Flex)`
  cursor: pointer;
  color: ${({ theme }) => theme.colors.mediumGrey};
  justify-content: flex-end;
  width: 100%;
`

interface IFormEmailRecipientsBasketProps {
  getSubItems: (
    id: number,
  ) => Promise<{ authors: Author[]; has_next: boolean; medialist_hash: string }>
  list: IAuthorBasketDefinitionsStoreArrItem[]
  icon?: string
  onSubmit: (value: number) => void
  onSubmitItem: (value: number) => void
  onLoadMore: (
    medialistHash: string,
  ) => Promise<{ authors: Author[]; has_next: boolean; medialist_hash: string }>
}

const FormEmailRecipientsBasket: ObservedFC<IFormEmailRecipientsBasketProps> = ({
  getSubItems,
  list,
  icon,
  onSubmit,
  onSubmitItem,
  onLoadMore,
}) => {
  const [openGroupIndexes, setOpenGroupIndexes] = useState<number[]>([])
  const [subItemsMap, setSubItemsMap] = useState<Record<number, Author[]>>({})
  const [loadingSubItems, setLoadingSubItems] = useState<Record<number, boolean>>({})
  const [hasNextMap, setHasNextMap] = useState<
    Record<number, { hasNext: boolean; medialistHash: string }>
  >({})

  const isGroupOpen = (id: number) => openGroupIndexes.includes(id)

  const loadMore = async (id: number) => {
    const medialistHash = hasNextMap[id]?.medialistHash
    const res = await onLoadMore(medialistHash)

    setSubItemsMap((prev) => ({
      ...prev,
      [id]: [...(prev[id] || []), ...res.authors],
    }))

    setHasNextMap((prev) => ({
      ...prev,
      [id]: { hasNext: res.has_next, medialistHash: res.medialist_hash },
    }))
  }

  const toggleGroup = async (id: number) => {
    const updatedIndexes = openGroupIndexes.includes(id)
      ? openGroupIndexes.filter((i) => i !== id)
      : [...openGroupIndexes, id]
    setOpenGroupIndexes(updatedIndexes)

    if (!subItemsMap[id] && !loadingSubItems[id]) {
      setLoadingSubItems((prev) => ({ ...prev, [id]: true }))
      const res = await getSubItems(id)
      setSubItemsMap((prev) => ({ ...prev, [id]: res.authors }))
      setHasNextMap((prev) => ({
        ...prev,
        [id]: { hasNext: res.has_next, medialistHash: res.medialist_hash },
      }))
      setLoadingSubItems((prev) => ({ ...prev, [id]: false }))
    }
  }

  const createItem = (item?: Author, loading: boolean = false) => {
    return {
      label: (
        <StyledFlex>
          {loading ? (
            <Skeleton height={'36px'} />
          ) : (
            <>
              <AuthorHeader size={26} pl={2} item={item} />
              <AdditionalInfoTagsOrListsAuthors item={item} />
            </>
          )}
        </StyledFlex>
      ),
      hoverVariant: null,
      pt: 0,
      pb: 0,
      ...(item
        ? {
            onClick: loading ? undefined : () => onSubmitItem(item.id),
          }
        : {}),
    }
  }

  if (list.length === 0) return null

  const menuItems = list.map((item) => {
    const hasSubItems = (subItemsMap[item.id] || []).length > 0
    const loading = loadingSubItems[item.id]
    const hasNext = hasNextMap[item.id]?.hasNext

    const subItems = isGroupOpen(item.id)
      ? loading
        ? Array(3).fill(createItem(undefined, true))
        : hasSubItems
          ? (subItemsMap[item.id] || []).map((subItem) => createItem(subItem))
          : [{ label: <Trans>This list is empty</Trans>, hoverVariant: 'none' }]
      : null
    if (hasNext) {
      subItems?.push({
        label: (
          <LoadMoreLink onClick={() => loadMore(item.id)}>
            <Trans>Load more...</Trans>
          </LoadMoreLink>
        ),
        hoverVariant: 'none',
      })
    }

    return {
      label: item.label,
      leftIcon: icon ? (
        icon
      ) : (
        <BasketIcon label={item.label} bg={color(item.color).darken(0.3).toString()} />
      ),
      ...(icon
        ? {
            leftIconColor: color(item.color).darken(0.4).toString(),
            leftIconHoverColor: color(item.color).darken(0.6).toString(),
            leftIconActiveColor: color(item.color).darken(0.6).toString(),
            leftIconBg: item.color,
          }
        : {}),
      buttonGroup: [
        {
          label: item.author_count,
          bg: 'transparent',
          size: 'small',
        },
        {
          ...(item.author_count !== 0
            ? {
                icon: isGroupOpen(item.id) ? 'expand_less' : 'expand_more',
                iconBg: 'flat',
                size: 'small',
                buttonGroupTop: '70px',
                onClick: () => toggleGroup(item.id),
              }
            : { bg: 'transparent', hoverVariant: 'none' }),
        },
      ],
      mlSubItems: 0,
      bgSubItems: 'transparent',
      subItems: subItems,
      modal: (closeModal: () => void) => {
        return (
          <Modal
            contentPadding={3}
            gap={2}
            actions={[
              {
                label: t`Add`,
                bg: 'secondary',
                rounded: true,
                onClick: () => {
                  onSubmit(item.id)
                  closeModal()
                },
              },
            ]}
          >
            <Trans>Do you want to add author addresses from this list?</Trans>
          </Modal>
        )
      },
      isVisibleSubItems: isGroupOpen(item.id),
      hoverVariant: 'light',
      modalTitle: item.label,
    }
  })

  return (
    <Box mt={1}>
      {/* @ts-expect-error TODO refactor MntrMenu to tsx */}
      <MntrMenu menuItems={menuItems} />
    </Box>
  )
}

export default observer(FormEmailRecipientsBasket)
