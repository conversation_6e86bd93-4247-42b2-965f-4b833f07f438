import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import { Field, Form, FormSpy } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'

const FormFilter = ({ onSubmit }) => {
  // get filter value from url
  const { query: urlParams } = useRouter()
  const filterLabel = urlParams.filter || ''

  return (
    <Form
      onSubmit={onSubmit}
      initialValues={{ filterLabel }}
      render={({ handleSubmit }) => {
        return (
          <form onSubmit={handleSubmit}>
            <Flex width={[1]} flexDirection="row" alignItems="center" ml="9px">
              <FormSpy
                onChange={(spy) => {
                  if (spy.dirty) {
                    onSubmit(spy.values)
                  }
                }}
              />
              <Flex mr={1} color="lightGrey" centerY>
                <Icon>search</Icon>
              </Flex>
              <Box width={1}>
                <Field
                  name="filterLabel"
                  component={MntrTextFieldAdapter}
                  placeholder={t`Filter by name`}
                />
              </Box>
            </Flex>
          </form>
        )
      }}
    />
  )
}

export default FormFilter
