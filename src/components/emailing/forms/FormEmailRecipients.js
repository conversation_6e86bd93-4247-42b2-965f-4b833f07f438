import { t } from '@lingui/core/macro'
import { useState } from 'react'
import { Field, Form, FormSpy } from 'react-final-form'
import { styled } from 'styled-components'
import Flag from '~/components/Flag/Flag'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import AuthorInfoDetail from '~/components/medialist/content/AuthorInfoDetail'
import AuthorPhoto from '~/components/medialist/content/FeedMedialist/AuthorPhoto/AuthorPhoto'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'

const StyledAutoComplete = styled(Box)`
  background: ${({ theme }) => theme.popper.background};
`

const FormEmailRecipients = ({ onSubmit, appStore: { authors, account } }) => {
  const [inputValue, setInputValue] = useState('')

  // Validate the email addresses
  const validateEmails = (values) => {
    const { emails } = values
    if (!emails) {
      return {}
    }

    // Split the input into individual email addresses
    const emailList = emails.split(/[,; ]+/).filter((email) => email.trim().length > 0)

    // Check if all the email addresses are valid
    const invalidEmails = emailList.filter((email) => !/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(email))

    // If some of the emails are invalid, return an error with the list of invalid emails
    if (invalidEmails.length > 0) {
      return { emails: `${t`Invalid`}: ${invalidEmails.join(', ')}` }
    }
  }

  const menuItems = []

  authors.autocompleteData.map((item) => {
    const country = account.enums.media_country.find((country) => country.id === item.country)

    menuItems.push({
      label: item.name,
      secondaryText: <AuthorInfoDetail author={item} />,
      hoverVariant: 'light',
      leftIcon: (
        <>
          <AuthorPhoto size={26} authorType={item.author_type} image={item.photo_url.small} />
          {item.country && (
            <Box position="absolute" bottom="14px" left="28px">
              <Flag country={country.code} size={11} />
            </Box>
          )}
        </>
      ),
      onClick: () => {
        onSubmit({
          author_id: item.id,
        }).then(() => {
          setInputValue('')
        })
      },
    })
  })

  return (
    <Box position="relative">
      <Form
        onSubmit={(values, form) => {
          onSubmit(values).then(() => {
            form.reset()
          })

          setInputValue('')
        }}
        validate={validateEmails}
        initialValues={{ emails: inputValue }}
        render={({ handleSubmit, submitting, submitFailed, values }) => {
          return (
            <form onSubmit={handleSubmit}>
              {/* Autocomplete for authors */}
              <FormSpy
                onChange={(spy) => {
                  const emails = spy.values.emails
                  if (!spy.submitSucceeded) {
                    setInputValue(emails)
                  }
                  if (emails?.length <= 100) {
                    authors.loadAutocomplete(emails)
                  }
                }}
              />

              {/* Heading */}

              <Flex flexWrap="nowrap" pt={0}>
                <Box width={[1]}>
                  <Flex px={3} alignItems="center">
                    <Field
                      name="emails"
                      autoFocus
                      autoComplete="off"
                      placeholder={t`Separate emails with a space, comma, or semicolon`}
                      component={MntrTextFieldAdapter}
                      displayError={submitFailed}
                      label={
                        <MntrMenuHeading
                          label={t`Add single authors, author’s lists or emails`}
                          noPadding
                        />
                      }
                    />

                    <Box ml={2} mt={'36px'}>
                      <MntrButton
                        label={t`Add`}
                        bg="secondary"
                        type="submit"
                        disabled={submitting || values.emails === '' || !values.emails}
                        rounded
                      />
                    </Box>
                  </Flex>

                  {menuItems.length > 0 && (
                    <Box mt={2}>
                      <MntrMenuHeading label={t`Authors`} />
                      <StyledAutoComplete>
                        <MntrMenu menuItems={menuItems} />
                      </StyledAutoComplete>
                    </Box>
                  )}
                </Box>
              </Flex>
            </form>
          )
        }}
      />
    </Box>
  )
}

export default observer(FormEmailRecipients)
