import { t } from '@lingui/core/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { Box, Flex } from '~/components/misc/Mntr'
import ButtonOAuth from '~/components/misc/MntrButton/modules/ButtonOAuth'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import { ObservedFC, observer } from '~/helpers/msts'

interface IFormEmailingSettingsSenderProps extends IMntrFormProps {
  description: string
}

const FormEmailingSettingsSender: ObservedFC<IFormEmailingSettingsSenderProps> = ({
  appStore: {
    account: { token },
  },
  onSubmit,
  description,
  disabled,
}) => {
  const formSchema: IFormSchemaItem[] = [
    {
      info: description,
    },
    {
      disabled,
      name: 'name',
      label: t`Name`,
    },
    {
      disabled,
      name: 'email',
      label: t`Email`,
    },
    {
      actions: ({ pristine, submitting }) => [
        {
          label: t`Add`,
          type: 'submit',
          bg: 'secondary',
          disabled: pristine || submitting || disabled,
        },
      ],
    },
  ]

  const returnUrl = new URL('/emailing/settings', document.baseURI).href

  return (
    <MntrPaper>
      <Box px={1} pb={1}>
        <MntrPaperToolbar title={t`Add new sender`} icon="contact_mail" />

        <MntrForm
          contentPadding={2}
          formGap={3}
          schema={formSchema}
          onSubmit={(values, form) => {
            // @ts-expect-error FIXME
            return onSubmit(values)
              .then(() => {
                form.reset()
              })
              .catch((err: unknown) => {
                return err
              })
          }}
        />
      </Box>
      <MntrPaperToolbar title={t`Or use an external service`} icon="assignment_ind" />
      <Flex p={2} pb={3} gap={3}>
        <ButtonOAuth
          disabled={disabled}
          type="google"
          url={`https://media.mediaboard.com/emailing/oauth2/gmail/start?token=${token}&return_url=${returnUrl}`}
          label={t`Use Google account as sender`}
        />
        <ButtonOAuth
          disabled={disabled}
          type="microsoft"
          url={`https://media.mediaboard.com/emailing/oauth2/o365/start?token=${token}&return_url=${returnUrl}`}
          label={t`Use Microsoft 365 account as sender`}
        />
      </Flex>
    </MntrPaper>
  )
}

export default observer(FormEmailingSettingsSender)
