/**
 * @jest-environment jsdom
 */

import { fireEvent, render, screen } from '@testing-library/react'
import noopFn from 'lodash/noop'
import { Providers as ThemeProvider } from '~/components/misc/MntrProviders/StyleProvider'
import FormEmailRecipients from './FormEmailRecipients'

const theme = {
  breakpoints: [],
  buttons: {},
  label: '',
  colors: {},
  space: {},
  form: {},
  textfield: {},
  timepicker: {},
}

describe('FormEmailRecipients', () => {
  it.failing('renders', () => {
    render(
      <ThemeProvider theme={theme}>
        <FormEmailRecipients
          appStore={{ authors: { autocompleteData: [], loadAutocomplete: noopFn } }}
        />
      </ThemeProvider>,
    )
  })

  it.failing('calls autocomplete on render', () => {
    const loadAutocomplete = jest.fn()
    render(
      <ThemeProvider theme={theme}>
        <FormEmailRecipients appStore={{ authors: { autocompleteData: [], loadAutocomplete } }} />
      </ThemeProvider>,
    )
    expect(loadAutocomplete).toHaveBeenCalledTimes(1)
  })

  it.failing('does not call autocomplete on change when longer than 100 chars', () => {
    const loadAutocomplete = jest.fn()
    render(
      <ThemeProvider theme={theme}>
        <FormEmailRecipients appStore={{ authors: { autocompleteData: [], loadAutocomplete } }} />
      </ThemeProvider>,
    )
    const input = screen.getByRole('textbox')
    fireEvent.change(input, {
      target: {
        value:
          'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
      },
    })
    expect(loadAutocomplete).toHaveBeenCalledTimes(1)
  })

  it.failing('it calls autocomplete on change when value is not empty', () => {
    const loadAutocomplete = jest.fn()
    render(
      <ThemeProvider theme={theme}>
        <FormEmailRecipients appStore={{ authors: { autocompleteData: [], loadAutocomplete } }} />
      </ThemeProvider>,
    )
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: '123' } })
    expect(loadAutocomplete).toHaveBeenCalledTimes(3) //TODO should have been called only once
  })

  it.failing('it calls autocomplete on change when value is emptied', () => {
    const loadAutocomplete = jest.fn()
    render(
      <ThemeProvider theme={theme}>
        <FormEmailRecipients appStore={{ authors: { autocompleteData: [], loadAutocomplete } }} />
      </ThemeProvider>,
    )
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'ale' } })
    fireEvent.change(input, { target: { value: '' } })
    expect(loadAutocomplete).toHaveBeenCalledTimes(3) //TODO should have been called only once
  })
})
