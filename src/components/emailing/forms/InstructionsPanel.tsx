import { MessageDescriptor } from '@lingui/core'
import { useState } from 'react'

import { useLingui } from '@lingui/react/macro'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

interface Point {
  title: MessageDescriptor
  point: MessageDescriptor
}

interface IInstructionsPanelProps {
  onClick?: () => void
  instructions: {
    title: MessageDescriptor
    points: Point[]
  }
  title?: string
}

const StyledList = styled.ul`
  margin: 0;
  padding-left: ${({ theme }) => theme.space[3]}px;
  padding-top: ${({ theme }) => theme.space[1]}px;
  padding-bottom: ${({ theme }) => theme.space[5]}px;

  li {
    line-height: 2;
  }
`

const InstructionsPanelWrapper = styled(Flex)`
  justify-content: space-between;
  cursor: pointer;
`

const InstructionsPanel = ({ onClick, instructions, title }: IInstructionsPanelProps) => {
  const [opened, setOpened] = useState(false)
  const { i18n } = useLingui()

  return (
    <Flex column>
      <InstructionsPanelWrapper
        p={2}
        onClick={() => {
          onClick?.()
          setOpened(!opened)
        }}
      >
        <Flex gap={1} centerY>
          <Icon color="inherit" size={24}>
            help
          </Icon>
          <Text fontSize={2} color="inherit">
            {title}
          </Text>
        </Flex>
        <MntrButton icon={opened ? 'keyboard_arrow_up' : 'keyboard_arrow_down'} bg="transparent" />
      </InstructionsPanelWrapper>
      {opened ? (
        <Flex column p={2}>
          {i18n._(instructions.title)}
          <StyledList>
            {instructions.points.map((point, idx) => {
              return (
                <li key={idx}>
                  <strong>{i18n._(point.title)} </strong>
                  {i18n._(point.point)}
                </li>
              )
            })}
          </StyledList>
        </Flex>
      ) : null}
    </Flex>
  )
}

export default InstructionsPanel
