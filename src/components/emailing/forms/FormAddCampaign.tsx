import { t } from '@lingui/core/macro'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'

interface IFormAddCampaignProps {
  onSubmit: (values: Record<string, unknown>) => Promise<void>
  closeModal: () => void
  initialValues?: IMntrFormProps
}

const FormAddCampaign = ({ onSubmit, closeModal, initialValues }: IFormAddCampaignProps) => {
  const formSchema: IFormSchemaItem[] = [
    {
      name: 'name',
      autoFocus: true,
      placeholder: t`Label`,
    },
    {
      actions: ({ submitting }) => [
        {
          label: t`Continue`,
          rounded: true,
          bg: 'secondary',
          type: 'submit',
          disabled: submitting,
        },
      ],
    },
  ]

  return (
    <MntrForm
      contentPadding={3}
      schema={formSchema}
      onSubmit={async (values) => {
        try {
          await onSubmit(values)
          if (typeof closeModal === 'function') {
            closeModal()
          }
        } catch (err) {
          return err
        }
      }}
      initialValues={initialValues}
    />
  )
}

export default FormAddCampaign
