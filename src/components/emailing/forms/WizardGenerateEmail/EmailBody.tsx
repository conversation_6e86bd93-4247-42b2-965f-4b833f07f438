import { styled } from 'styled-components'

import { PlaceholderHighlighter } from '~/components/aiTools'
import { Box, Flex, Text } from '~/components/misc/Mntr'

const StyledAnchor = styled.a`
  box-sizing: border-box;
  display: block;
  max-width: 534px;
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  overflow: hidden;
  cursor: pointer;
`

interface IBlogPostWidgetProps {
  id: string
  type: string
  title: string
  cover_image_url: string
  perex: string
  url: string
}

const BlogPostWidget = ({ cover_image_url, perex, title, type, url }: IBlogPostWidgetProps) => {
  return (
    <Flex centerX py={6}>
      <StyledAnchor href={url} target="_blank">
        {type === 'image_title_perex' || type === 'image_title' ? (
          <>
            {cover_image_url ? <img src={cover_image_url} alt={title} width="100%" /> : null}
            <Text fontSize="40px" p={4} pb={0}>
              {title}
            </Text>
            {perex ? (
              <Text fontSize={4} p={4}>
                {perex}
              </Text>
            ) : null}
          </>
        ) : (
          <Text fontSize={4} p={4} textAlign="center" color="link">
            {title}
          </Text>
        )}
      </StyledAnchor>
    </Flex>
  )
}

interface IEmailBodyProps {
  activePlaceholder: string
  content: string
  placeholders: string[]
}

const parseHTMLContent = (htmlString: string) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlString, 'text/html')
  const fragments: { type: 'text' | 'widget'; content?: string; attributes?: NamedNodeMap }[] = []

  Array.from(doc.body.childNodes).forEach((node) => {
    if (
      node.nodeType === Node.ELEMENT_NODE &&
      (node as Element).nodeName === 'COMPONENT-BLOG-POST-WIDGET'
    ) {
      fragments.push({ type: 'widget', attributes: (node as Element).attributes })
    } else {
      fragments.push({ type: 'text', content: (node as Element).outerHTML || '' })
    }
  })

  return fragments
}

const EmailBody = ({ content = '', placeholders, activePlaceholder }: IEmailBodyProps) => {
  const parsedContent = parseHTMLContent(content)

  return (
    <Box>
      {parsedContent.map((fragment, index) => {
        if (fragment.type === 'widget') {
          const props: Partial<IBlogPostWidgetProps> = {}
          fragment.attributes &&
            Array.from(fragment.attributes).forEach((attr) => {
              props[attr.name as keyof IBlogPostWidgetProps] = attr.value
            })

          return <BlogPostWidget key={`widget-${index}`} {...(props as IBlogPostWidgetProps)} />
        }

        return fragment.content ? (
          <PlaceholderHighlighter
            key={`placeholder-${index}`}
            text={fragment.content}
            placeholders={placeholders}
            activePlaceholder={activePlaceholder}
            fontSize={4}
          />
        ) : null
      })}
    </Box>
  )
}

export default EmailBody
