import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useEffect, useState } from 'react'

import {
  AiToolsResultHeader,
  AiToolsResultList,
  AiToolsResultListItem,
  usePlaceholders,
} from '~/components/aiTools'
import Icon from '~/components/misc/Icon/Icon'
import { Box, ButtonGroup, Flex, Skeleton, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrForm from '~/components/misc/MntrForm/MntrForm'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { ObservedFC, observer } from '~/helpers/msts'
import EmailBody from './EmailBody'
import { IWizardDataProps } from './WizardGenerateEmail'

interface IStepPreviewGeneratedProps {
  campaignId?: string | string[]
  jumpTo: (i: number) => void
  setModalWidth: (width: number) => void
  wizardData?: IWizardDataProps
  setWizardData: (data: IWizardDataProps) => void
}

const StepPreviewGenerated: ObservedFC<IStepPreviewGeneratedProps> = ({
  appStore: { emailing, router, viewport },
  campaignId,
  jumpTo,
  setModalWidth,
  wizardData,
  setWizardData,
}) => {
  const { isLoading, placeholders } = emailing.aiTools.generateContent
  const { activePlaceholder, generateFormSchema } = usePlaceholders({
    initialPlaceholders: placeholders,
  })
  const [replacingPlaceholders, setReplacingPlaceholders] = useState(false)

  useEffect(() => {
    if (!isLoading) {
      placeholders.length < 1 ? setModalWidth(850) : setModalWidth(1280)
    }
  }, [isLoading, placeholders, setModalWidth])

  return (
    <>
      <Flex flexDirection={viewport.isMobile ? 'column-reverse' : 'row'} minHeight={'80vh'}>
        <Flex className="scroll-content-wrapper" flex={'2 !important'}>
          <Box overflowY="auto" p={5}>
            <Box>
              {isLoading ? (
                <Flex column gap={6} py={4}>
                  <Skeleton height={'24px'} />
                  {Array.from({ length: 2 }, (_, index) => {
                    return (
                      <Flex column gap={3} key={index}>
                        <Skeleton height={'24px'} repeat={4} />
                      </Flex>
                    )
                  })}
                  <Flex centerX py={4}>
                    <Flex column width="534px" gap={3}>
                      <Skeleton height={'275px'} fullWidth />
                      <Flex column width={1} gap={5}>
                        <Skeleton height={'48px'} />
                        <Flex column width={1} gap={2}>
                          <Skeleton height={'24px'} repeat={2} />
                        </Flex>
                      </Flex>
                    </Flex>
                  </Flex>
                  <Flex column gap={3}>
                    <Skeleton height={'24px'} repeat={4} />
                  </Flex>
                </Flex>
              ) : (
                <Flex column gap={6}>
                  <MntrForm
                    initialValues={{
                      subject: wizardData?.subject || emailing.aiTools.generateContent.subject,
                    }}
                    schema={[{ name: 'subject', label: t`Subject` }]}
                    onChange={({ values }) => {
                      const data = {
                        ...wizardData,
                        ...values,
                      }
                      setWizardData(data)
                    }}
                    onSubmit={() => undefined}
                    visibleSubmitButton={false}
                  />
                  <EmailBody
                    content={emailing.aiTools.generateContent.body}
                    placeholders={placeholders}
                    activePlaceholder={activePlaceholder}
                  />
                </Flex>
              )}
            </Box>
            {isLoading ? null : (
              <Flex p={3} centerX>
                <MntrButton
                  label={
                    <Flex centerY gap={1}>
                      {t`Regenerate content`}
                      <Icon color="inherit">psychology</Icon>
                    </Flex>
                  }
                  rounded={true}
                  bg="primary"
                  disabled={emailing.aiTools.generateContent.isLoading}
                  onClick={() => {
                    setModalWidth(850)
                    pushEvent(events.EMAILING_EMAIL_CREATE_PREVIEW_REGENERATE)
                    emailing.aiTools.generateContent.generateEmail({
                      ...wizardData,
                    })
                  }}
                />
              </Flex>
            )}
          </Box>
        </Flex>
        {isLoading ||
        viewport.isMobile ||
        replacingPlaceholders ||
        (!isLoading && placeholders.length < 1) ? null : (
          <Flex className="scroll-content-wrapper">
            <Box overflowY="auto" p={2} pt={0}>
              <AiToolsResultHeader width={1} centerY px={3} py={4} zIndex={10} top={0}>
                <Text fontSize={4} fontWeight="bold" width={1}>
                  {isLoading ? <Skeleton height={'24px'} /> : <Trans>Add missing data</Trans>}
                </Text>
              </AiToolsResultHeader>

              <AiToolsResultList column>
                {isLoading ? (
                  Array.from({ length: 10 }, (_, index) => {
                    return (
                      <AiToolsResultListItem display="flex" gap={2} key={index} isLoading>
                        <Skeleton height={'21px'} />
                      </AiToolsResultListItem>
                    )
                  })
                ) : (
                  <MntrForm
                    contentPadding={3}
                    schema={[
                      {
                        info: t`Some data are missing in the generated content. Add them manually before proceeding.`,
                      },
                      ...generateFormSchema(),
                      {
                        actions: () => [
                          {
                            label: t`Apply`,
                            type: 'submit',
                            rounded: true,
                            bg: 'secondary',
                          },
                        ],
                      },
                    ]}
                    onSubmit={(data) => {
                      setModalWidth(850)
                      setReplacingPlaceholders(true)
                      pushEvent(events.EMAILING_EMAIL_CREATE_PREVIEW_PLACEHOLDERS)
                      emailing.aiTools.generateContent
                        .replacePlaceholders({
                          body: emailing.aiTools.generateContent.body,
                          subject: wizardData?.subject || emailing.aiTools.generateContent.subject,
                          placeholders: data,
                        })
                        .then(() => setReplacingPlaceholders(false))
                    }}
                  />
                )}
              </AiToolsResultList>
            </Box>
          </Flex>
        )}
      </Flex>
      <Flex column alignItems="end" p={3} pt={2}>
        <ButtonGroup
          buttons={[
            {
              label: (
                <Flex centerY gap={1}>
                  {t`Start editing`}
                  <Icon color="inherit">draw</Icon>
                </Flex>
              ),
              rounded: true,
              bg: 'secondary',
              onClick: async () => {
                const res = await emailing.campaign_detail.createEmail(campaignId, {
                  body: emailing.aiTools.generateContent.body,
                  language: wizardData?.language,
                  subject: wizardData?.subject || emailing.aiTools.generateContent.subject,
                })
                pushEvent(events.EMAILING_EMAIL_CREATE_PREVIEW)
                await router.redirectTo(`/emailing/campaign/${campaignId}/email/edit/${res.id}`)
              },
            },
          ]}
          buttonsLeft={[
            {
              label: t`Start over`,
              icon: 'arrow_back_ios',
              onClick: () => {
                setModalWidth(800)
                jumpTo(0)
                // setWizardData({})
              },
              bg: 'transparent',
              rounded: true,
            },
          ]}
        />
      </Flex>
    </>
  )
}

export default observer(StepPreviewGenerated)
