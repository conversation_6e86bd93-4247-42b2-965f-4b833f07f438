import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

import Icon from '~/components/misc/Icon/Icon'
import Wizard from '~/components/misc/Wizard/Wizard'
import WizardChoice from '~/components/misc/Wizard/WizardChoice'
import events, { method } from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import ModalAddArticle, { IWidgetType } from '~/helpers/modal/withModalAddArticle/ModalAddArticle'
import StepDetailedInstructions from './StepDetailedInstructions'
import StepPreviewGenerated from './StepPreviewGenerated'

const choices = [
  {
    id: 0,
    isDefault: true,
    imgLabel: 'psychology',
    title: <Trans>Write with AI assistant</Trans>,
    points: [
      {
        icon: 'approval_delegation',
        text: (
          <Trans>Assistant creates a draft of the email content based on your specific needs</Trans>
        ),
      },
      {
        icon: 'approval_delegation',
        text: (
          <Trans>Regenerate until the email text aligns perfectly with your requirements</Trans>
        ),
      },
      {
        icon: 'approval_delegation',
        text: (
          <Trans>
            The email content can be automatically adjusted to include personalized details for each
            recipient
          </Trans>
        ),
      },
    ],
  },
  {
    id: 1,
    isDefault: false,
    imgLabel: 'edit_document',
    title: <Trans>Manual writing</Trans>,
    points: [
      {
        icon: 'approval_delegation',
        text: <Trans>Article can still be attached later</Trans>,
      },
      {
        icon: 'close',
        text: (
          <Trans>
            Sending a generic email without an attached article provides no useful data for tracking
          </Trans>
        ),
      },
    ],
  },
]

export interface IWizardDataProps {
  instruction?: string
  language: string
  subject?: string
  newsroom_blog_post_id?: number
  newsroom_id?: number
  widget_type: IWidgetType
}

interface IWizardGenerateEmailProps {
  setModalWidth: (width: number) => void
}

const WizardGenerateEmail = ({ setModalWidth }: IWizardGenerateEmailProps) => {
  const router = useRouter()
  const { campaignId } = router.query
  const [data, setData] = useState<IWizardDataProps>()

  useEffect(() => {
    pushEvent(events.EMAILING_EMAIL_CREATE)
  }, [])
  return (
    <Wizard
      steps={({ prevStep, jumpTo, nextStep }) => {
        return [
          <>
            <WizardChoice
              onClick={(activeIndex) => {
                pushEvent(events.EMAILING_EMAIL_CREATE_METHOD, {
                  method: activeIndex === 0 ? method.GENERATIVE : method.MANUAL,
                })

                if (activeIndex === 0) {
                  nextStep()
                } else {
                  router.replace(`/emailing/campaign/${campaignId}/email/create`)
                }
              }}
              choices={choices}
              title={<Trans>Select a method</Trans>}
            />
          </>,
          <>
            <ModalAddArticle
              prevStep={prevStep}
              withTitle
              {...(data?.newsroom_blog_post_id && {
                initialValues: {
                  variant: data.widget_type,
                  article: data.newsroom_blog_post_id,
                  newsroom: data.newsroom_id,
                },
              })}
              customActions={[
                {
                  bg: 'flat',
                  rounded: true,
                  label: t`Skip`,
                  endIconElement: <Icon>double_arrow</Icon>,
                  onClick: () => {
                    pushEvent(events.EMAILING_EMAIL_CREATE_ARTICLE, { article: 'skipped' })
                    nextStep()
                  },
                },
              ]}
              onSubmit={({ id, newsroomId, type, language }) => {
                setData({
                  newsroom_blog_post_id: id,
                  newsroom_id: newsroomId,
                  widget_type: type,
                  language,
                })
                pushEvent(events.EMAILING_EMAIL_CREATE_ARTICLE, { article: 'selected' })
                nextStep()
              }}
            />
          </>,
          <>
            <StepDetailedInstructions
              prevStep={prevStep}
              nextStep={nextStep}
              setModalWidth={setModalWidth}
              wizardData={data}
              setWizardData={setData}
            />{' '}
          </>,
          <>
            <StepPreviewGenerated
              jumpTo={jumpTo}
              setModalWidth={setModalWidth}
              wizardData={data}
              setWizardData={setData}
              campaignId={campaignId}
            />
          </>,
        ]
      }}
    />
  )
}

export default WizardGenerateEmail
