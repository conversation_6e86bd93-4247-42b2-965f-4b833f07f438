import { msg, t } from '@lingui/core/macro'

import InstructionsPanel from '~/components/emailing/forms/InstructionsPanel'
import Icon from '~/components/misc/Icon/Icon'
import { Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrForm from '~/components/misc/MntrForm/MntrForm'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import config from '~/config'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { ObservedFC, observer } from '~/helpers/msts'
import { IAppLanguageEnumsStoreArrItem } from '~/store/models/account/enums/appLanguage/AppLanguageEnumsStoreArrItem'
import { IWizardDataProps } from './WizardGenerateEmail'

interface IStepDetailedInstructionsProps {
  prevStep: () => void
  nextStep: () => void
  setModalWidth: (width: number) => void
  setWizardData: (data: IWizardDataProps) => void
  wizardData?: IWizardDataProps
}

const StepDetailedInstructions: ObservedFC<IStepDetailedInstructionsProps> = ({
  appStore: { account, emailing },
  prevStep,
  nextStep,
  setWizardData,
  wizardData,
  setModalWidth,
}) => {
  const instructions = {
    title: msg`Provide information such as:`,
    points: [
      {
        title: msg`Email Subject:`,
        point: msg`Define the subject line to set the focus and tone.`,
      },

      {
        title: msg`Target Audience:`,
        point: msg`Describe who will receive the email (demographics, interests).`,
      },

      {
        title: msg`Main Objective:`,
        point: msg`Specify the primary goal (inform, persuade, invite, etc.).`,
      },

      {
        title: msg`Tone and Style:`,
        point: msg`Indicate the desired tone (formal, casual) and style (informative, promotional).`,
      },

      {
        title: msg`Key Points:`,
        point: msg`Outline the main content or details you want included.`,
      },

      {
        title: msg`Call to Action (CTA):`,
        point: msg`Define the action you want recipients to take.`,
      },

      {
        title: msg`Personalization:`,
        point: msg`Mention any personalization details (name, company).`,
      },
    ],
  }

  return (
    <>
      <MntrForm
        contentPadding={5}
        initialValues={{ language: wizardData?.language || account.user.app_language }}
        schema={[
          {
            fieldset: {
              legend: t`Detailed instructions for email text creation`,
              name: 'detailed_instructions',
              fields: [
                {
                  customComponent: ({ form, values }) => {
                    const language = account.enums.app_language.find(
                      (lang: IAppLanguageEnumsStoreArrItem) => lang.id === values.language,
                    )

                    return (
                      <Flex>
                        <MntrButton
                          isChip
                          label={language.text}
                          flagLanguage={values.language}
                          popupPlacement={'bottom-start'}
                          transformOrigin="0 0"
                          popup={(closePopup) => {
                            const menuItems = Object.entries(config.locales)
                              .filter((item) => item[1].newsroom)
                              .map(([key, val]) => {
                                return {
                                  label: val.label,
                                  flag: key,
                                  rightIcon: values.language === key ? 'check' : null,
                                  onClick: () => {
                                    form?.change('language', key)
                                    closePopup()
                                  },
                                }
                              })
                            // @ts-expect-error TODO refactor MntrMenu to tsx
                            return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
                          }}
                          zIndex={9000}
                        />
                      </Flex>
                    )
                  },
                },
                {
                  label: t`Instructions`,
                  name: 'instruction',
                  multiline: true,
                  minRows: 4,
                  placeholder: t`Type your subject or other instructions. Clearly outline the main message or information you want to convey.Provide instructions on how to structure the information, for example: use bullet points or numbered lists.`,
                },
                {
                  customComponent: () => {
                    return (
                      <InstructionsPanel
                        onClick={() => {
                          pushEvent(events.EMAILING_EMAIL_CREATE_INSTRUCTIONS_HELP)
                        }}
                        instructions={instructions}
                        title={t`How to help the AI generate a more satisfying and detailed email`}
                      />
                    )
                  },
                },
              ],
            },
          },
          {
            actions: () => {
              return [
                {
                  label: (
                    <Flex centerY gap={1}>
                      {t`Generate text`}
                      <Icon color="inherit">psychology</Icon>
                    </Flex>
                  ),
                  rounded: true,
                  bg: 'primary',
                  type: 'submit',
                },
              ]
            },
          },
          {
            actionsLeft: () => [
              {
                label: t`Back`,
                icon: 'arrow_back_ios',
                onClick: prevStep,
                bg: 'flat',
                rounded: true,
              },
            ],
          },
        ]}
        onSubmit={(values) => {
          // Validate required fields after submit
          interface IErrorsProps {
            instruction?: string
            instruction_id?: string
          }
          const errors: IErrorsProps = {}
          if (
            values.instruction === '' ||
            values.instruction_id === '' ||
            (!values.instruction && !values.instruction_id && !wizardData?.newsroom_blog_post_id)
          ) {
            errors.instruction = t`This field is required`
            return errors
          }

          const data = {
            ...wizardData,
            ...values,
          }
          setWizardData(data)
          setModalWidth(850)
          pushEvent(events.EMAILING_EMAIL_CREATE_INSTRUCTIONS, {
            instruction: !!data.instruction?.length,
            language: data.language,
          })
          nextStep()

          return emailing.aiTools.generateContent.generateEmail(data)
        }}
      />
    </>
  )
}

export default observer(StepDetailedInstructions)
