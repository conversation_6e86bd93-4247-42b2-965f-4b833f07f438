import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import sendersVerifications from '~/components/emailing/constants/senderVerifications.constants'
import { StyledRecipientsBox } from '~/components/emailing/style/StyledRecipientsList'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import RequestFeatureButton from '~/components/misc/RequestFeatureButton/RequestFeatureButton'
import ProgressBarLimit from '~/components/tariff/TariffLimits/ProgressBarLimit/ProgressBarLimit'
import { observer } from '~/helpers/mst'
import defaultTheme from '~/styles/theme/defaultTheme'

const EmailingRecipientsLimitWarning = ({
  activeSenderId,
  recipientsCount,
  plain = false, // plain true will show only progress bar
  appStore: { account, emailing },
}) => {
  // find active sender
  const senderItem = emailing.settings.senders.senderById(activeSenderId)

  if (!senderItem) {
    return null
  }

  // get email backend item
  const emailBackendItem = account.enums.emailing.email_backend.find(
    (item) => parseInt(item.id) === parseInt(senderItem.email_backend.id),
  )

  if (!emailBackendItem) {
    return null
  }

  // if it's under limit && plain, return false
  if (plain) {
    if (recipientsCount <= senderItem.recipientsLimit) {
      return null
    }
  }

  return (
    <Box px={3} my={3} width="100%">
      <ProgressBarLimit
        label={t`Recipients limit`}
        limitReachedMessage={
          <Text fontSize={13} color="inherit">
            <Trans>You have reached the limit on the number of recipients.</Trans>
          </Text>
        }
        color={defaultTheme.sidebar.activeBorder}
        height={8}
        maxValue={senderItem.recipientsLimit}
        value={recipientsCount}
      />

      {senderItem.email_backend.id === sendersVerifications.NO_VERIFICATION ? (
        <Flex mt={3}>
          <StyledRecipientsBox bg="error">
            <MntrHint
              background="transparent"
              color="tertiary"
              text={
                <Flex flexDirection="column" gap={3}>
                  {!plain && (
                    <>
                      <Box>
                        {t`The recipient limit is set to ${senderItem.unverified_recipients_limit}. For a higher limit of ${senderItem.verified_recipients_limit} recipients, enable DNS or SMTP verification.`}
                      </Box>

                      <Box textAlign={'right'}>
                        <MntrButton
                          icon="settings"
                          bg="error"
                          href="/emailing/settings"
                          label={t`Settings`}
                        />
                      </Box>
                    </>
                  )}
                </Flex>
              }
            />
          </StyledRecipientsBox>
        </Flex>
      ) : recipientsCount > senderItem.recipientsLimit ? (
        <Flex mt={3}>
          <StyledRecipientsBox bg="error">
            <MntrHint
              background="transparent"
              color="tertiary"
              heading={t`You have reached the limit of recipients per email`}
              text={
                <Flex width="100%" flexDirection="column">
                  <Box>{t`Please remove some recipients.`}</Box>

                  <Box textAlign={'right'} mt={3}>
                    <RequestFeatureButton
                      id="Emailing_Recipients_Limit"
                      showNotification
                      confirmTitle={t`Increase limit?`}
                      label={<Trans>Increase limit</Trans>}
                      bg="error"
                    />
                  </Box>
                </Flex>
              }
            />
          </StyledRecipientsBox>
        </Flex>
      ) : null}
    </Box>
  )
}

export default observer(EmailingRecipientsLimitWarning)
