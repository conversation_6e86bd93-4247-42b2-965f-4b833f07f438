import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { IWithModalRemoveRecipientsProps } from '~/components/emailing/components/Types/IRecipients'
import { Flex, Modal, Text } from '~/components/misc/Mntr'

const withModalRemoveRecipients = ({
  onSubmit,
  submitLabel,
  title,
  modalIcon,
  isStackedModal = false,
  closeParentModal,
  modalZIndex,
  renderRecipientButton,
  recipients,
  skipRemoveRecipientsArr,
  setSkipRemoveRecipientsArr,
}: IWithModalRemoveRecipientsProps) => {
  return {
    modalIcon: modalIcon || 'delete',
    modalBg: 'error',
    modalTitle: title || (
      <span>
        <Trans>Remove</Trans>?
      </span>
    ),
    isStackedModal,
    modalZIndex,
    modal: (closeModal: () => void, closePopup: () => void) => {
      return (
        <Modal
          contentPadding={3}
          gap={2}
          actions={[
            {
              bg: 'error',
              rounded: true,
              label:
                submitLabel || `${t`Remove`} ${recipients.length - skipRemoveRecipientsArr.length}`,
              onClick: () => {
                closeModal()
                if (isStackedModal && typeof closeParentModal === 'function') {
                  closeParentModal()
                }

                if (typeof closePopup === 'function') {
                  setTimeout(() => {
                    onSubmit()
                  }, 130)
                  closePopup()
                } else {
                  onSubmit()
                }
              },
            },
          ]}
        >
          <Flex flexDirection="column" gap={3}>
            <Text>
              <Trans>
                You are about to remove the selected recipients. However, you can keep some of them
                by clicking on the recipients.
              </Trans>
            </Text>

            <Flex gap={1} flexWrap="wrap">
              {recipients.map((item, index) => {
                const bg = skipRemoveRecipientsArr.includes(item.id)
                  ? 'secondary'
                  : 'defaultPrimary'

                return renderRecipientButton(
                  {
                    ...item,
                    onClick: () => {
                      if (skipRemoveRecipientsArr.includes(item.id)) {
                        setSkipRemoveRecipientsArr(
                          skipRemoveRecipientsArr.filter((id) => id !== item.id),
                        )
                      } else {
                        setSkipRemoveRecipientsArr([...skipRemoveRecipientsArr, item.id])
                      }
                    },
                  },
                  index,
                  bg,
                  recipients,
                )
              })}
            </Flex>
          </Flex>
        </Modal>
      )
    },
  }
}

export default withModalRemoveRecipients
