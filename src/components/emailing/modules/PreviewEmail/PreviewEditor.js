import { t } from '@lingui/core/macro'
import identityFn from 'lodash/identity'
import { useEffect } from 'react'
import { Field } from 'react-final-form'
import { styled, useTheme } from 'styled-components'
import {
  StyledAttachmentsList,
  StyledContentPreview,
} from '~/components/emailing/style/StyledEmailEdit'
import { StyledRecipientsBox } from '~/components/emailing/style/StyledRecipientsList'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import AttachmentsList from '~/components/newsroom/content/post/AttachmentsList'
import { observer } from '~/helpers/msts'

const StyledIframe = styled.iframe`
  width: 100%;
  height: 65vh;
  border: none;
`
// check if html string is empty
const isEmptyHtml = (htmlString) => {
  if (!htmlString) return true
  const trimmedHtml = htmlString.trim()
  return /^<p>\s*<\/p>$/.test(trimmedHtml)
}

const PreviewEditor = ({ appStore: { emailing }, selectedRecipient, goStep }) => {
  const theme = useTheme()

  useEffect(() => {
    // Wait until the iframe loads
    const iframe = document.querySelector('iframe')
    if (iframe) {
      iframe.onload = () => {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
        const links = iframeDoc.querySelectorAll('a')
        links.forEach((link) => {
          link.addEventListener('click', (event) => {
            event.preventDefault()
          })
        })
      }
    }
  }, [selectedRecipient.preview_body])

  return (
    <StyledContentPreview>
      <Flex flexDirection="column" gap={3}>
        <MntrPaper>
          <Box px={38} py={3}>
            <Flex gap={3} flexDirection="column">
              <Box>
                <Field
                  label={t`Sender`}
                  component={MntrTextFieldAdapter}
                  items={emailing.settings.senders.selectorData}
                  name="sender"
                  disabled={true}
                  input={{ value: emailing.email_edit.result.sender_full_email }}
                  disabledColor={theme.colors.black}
                />
              </Box>
              <Box>
                <Flex>
                  <Field
                    label={t`Recipient`}
                    component={MntrTextFieldAdapter}
                    name="recipient"
                    disabled
                    input={{ value: selectedRecipient.email }}
                    disabledColor={theme.colors.black}
                    borderBottomColor={!selectedRecipient.email ? theme.colors.error : null}
                  />
                  {!selectedRecipient.email && (
                    <MntrButton
                      mt={3}
                      bg="header"
                      icon="help"
                      tooltip={t`Recipient has no email address`}
                      iconColor={theme.colors.error}
                      onClick={() => goStep(2)}
                    />
                  )}
                </Flex>
              </Box>
              <Flex gap={3} center>
                <Field
                  label={t`Subject`}
                  name="subject"
                  component={MntrTextFieldAdapter}
                  parse={identityFn}
                  disabled
                  disabledColor={theme.colors.black}
                />
              </Flex>
            </Flex>
          </Box>
        </MntrPaper>
        {isEmptyHtml(emailing.email_edit.result.body) ? (
          <StyledRecipientsBox bg="error">
            <MntrHint
              color="error"
              heading={t`Warning`}
              text={
                <Flex flexDirection="column" gap={3}>
                  {
                    <>
                      <Box>
                        {t`The email is currently empty. Please add some content to the email.`}
                      </Box>
                      <Box textAlign={'right'}>
                        <MntrButton
                          bg="error"
                          label={t`Add content`}
                          icon="edit"
                          onClick={() => goStep(1)}
                        />
                      </Box>
                    </>
                  }
                </Flex>
              }
            />
          </StyledRecipientsBox>
        ) : (
          <MntrPaper mb={2}>
            <StyledIframe
              srcDoc={selectedRecipient.preview_body}
              title="Email Preview"
              border="0"
            />
          </MntrPaper>
        )}
        {emailing.email_edit.attachments?.results?.length > 0 && (
          <StyledAttachmentsList mb={2}>
            <AttachmentsList
              displayEmpty
              attachments={emailing.email_edit.attachments}
              handleUploadMedia={emailing.email_edit.attachments.uploadMedia}
              disabled={true}
              isPreview
            />
          </StyledAttachmentsList>
        )}
      </Flex>
    </StyledContentPreview>
  )
}

export default observer(PreviewEditor)
