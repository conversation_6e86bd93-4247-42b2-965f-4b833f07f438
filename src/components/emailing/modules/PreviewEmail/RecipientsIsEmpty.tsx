import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

interface IRecipientsIsEmptyProps {
  title: string
  goStep: (value: number) => void
}

const StyledEmptyRecipients = styled.div`
  border: 1px dashed ${({ theme }) => theme.colors.border};
  border-radius: 20px;
  padding: 5px 10px;
  display: flex;
  gap: 10px;
  color: ${({ theme }) => theme.colors.lightGrey};
`

const RecipientsIsEmpty = ({ title, goStep }: IRecipientsIsEmptyProps) => {
  return (
    <>
      <MntrMenuHeading label={`${title} (0)`} />
      <StyledEmptyRecipients>
        <Icon>account_circle</Icon>
        {t`You will see your recipients here`}
      </StyledEmptyRecipients>
      <MntrButton
        mt={2}
        bg="secondary"
        label={t`Add recipients`}
        icon="group"
        rounded
        fullWidth
        onClick={() => goStep(2)}
      />
    </>
  )
}

export default RecipientsIsEmpty
