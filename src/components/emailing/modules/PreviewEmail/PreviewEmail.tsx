import { t } from '@lingui/core/macro'
import { useState } from 'react'
import { IRecipient } from '~/components/emailing/components/Types/IRecipients'
import PreviewEditor from '~/components/emailing/modules/PreviewEmail/PreviewEditor'
import { Box, Flex } from '~/components/misc/Mntr'
import { ObservedFC, observer } from '~/helpers/msts'
import RecipientsIsEmpty from './RecipientsIsEmpty'
import RecipientsList from './RecipientsList'
interface PreviewEmailProps {
  emailing: unknown
  recipients: IRecipient[]
  activeSenderId: string
  recipientsCount: number
  values: { is_locked: boolean }
  goStep: (step: number) => void
}

const PreviewEmail: ObservedFC<PreviewEmailProps> = ({ appStore: { emailing }, goStep }) => {
  const recipients = emailing.email_edit.recipients
  const allRecipients = [...recipients.authorsRecipientsArr, ...recipients.emailRecipientsArr]
  const allInvalidRecipients = recipients.allInvalidRecipients
  // State to manage selected recipient and preview content
  const [selectedRecipient, setSelectedRecipient] = useState<IRecipient>({
    ...recipients.results[0],
  })

  const handleSelectRecipient = (recipient: IRecipient) => {
    setSelectedRecipient({ ...recipient })
    emailing.email_edit.recipients.validateRecipients(emailing.email_edit.result.body)
  }

  return (
    <Box height="100%">
      <Flex height="100%" flexDirection="column">
        <Flex height="100%">
          <Flex
            width="300px"
            flex="0 0 auto"
            height="100%"
            overflowY="scroll"
            display={['none', 'none', 'flex']}
          >
            <Box width="100%" pt={1}>
              {allInvalidRecipients.length > 0 && (
                <RecipientsList
                  title={t`Missing recipient info`}
                  recipients={allInvalidRecipients}
                  selectedRecipientId={selectedRecipient?.id || ''}
                  onSelectRecipient={handleSelectRecipient}
                  removeRecipients={recipients.removeRecipients}
                />
              )}
              {allRecipients.length > 0 && (
                <RecipientsList
                  title={t`Recipients`}
                  recipients={allRecipients}
                  selectedRecipientId={selectedRecipient?.id || ''}
                  onSelectRecipient={handleSelectRecipient}
                  removeRecipients={recipients.removeRecipients}
                />
              )}
              {allRecipients.length === 0 && allInvalidRecipients.length === 0 && (
                <RecipientsIsEmpty title={t`Recipients`} goStep={(step: number) => goStep(step)} />
              )}
            </Box>
          </Flex>
          {/* @ts-expect-error - refactoring PreviewEditor */}
          <PreviewEditor selectedRecipient={selectedRecipient} goStep={(step) => goStep(step)} />
        </Flex>
      </Flex>
    </Box>
  )
}

export default observer(PreviewEmail)
