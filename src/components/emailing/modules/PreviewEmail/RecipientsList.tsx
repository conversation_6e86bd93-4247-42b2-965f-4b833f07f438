import { t } from '@lingui/core/macro'
import { useState } from 'react'
import { styled } from 'styled-components'
import { IRecipient } from '~/components/emailing/components/Types/IRecipients'
import { StyledInputTransparent } from '~/components/forms/adapters/shared/StyledInput'
import AuthorPhoto from '~/components/medialist/content/FeedMedialist/AuthorPhoto/AuthorPhoto'
import Icon from '~/components/misc/Icon/Icon'
import { Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import removeAccents from '~/helpers/removeAccents'

interface IRecipientsListProps {
  title: string
  recipients: IRecipient[]
  selectedRecipientId: string
  onSelectRecipient: (recipient: IRecipient) => void
  isInvalid?: boolean
  closePopup?: () => void
  removeRecipients: (idsToRemove: string[]) => void
}

const SearchWrapper = styled.div`
  background: ${({ theme }) => theme.popper.background};
  border-radius: 20px;
  padding: 0 10px;
`

const RecipientsList = ({
  title,
  recipients,
  selectedRecipientId,
  onSelectRecipient,
  closePopup,
  removeRecipients,
}: IRecipientsListProps) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [showAll, setShowAll] = useState(false)
  const displayLimit = 7

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value)
  }

  const filteredRecipients = recipients.filter((recipient) =>
    removeAccents(recipient.author?.name || recipient.email || '')
      .toLowerCase()
      .includes(removeAccents(searchQuery).toLowerCase()),
  )

  const displayedRecipients = showAll
    ? filteredRecipients
    : filteredRecipients.slice(0, displayLimit)

  const createRecipientList = () => {
    return displayedRecipients.map((item) => {
      const leftIcon = item.author ? (
        <AuthorPhoto
          size={30}
          authorType={item.author.author_type}
          image={item.author.photo_url.small}
        />
      ) : (
        'email'
      )

      return {
        label: item.author?.name || item.email || '',
        selected: item.id === selectedRecipientId,
        accent: true,
        rounded: true,
        leftIcon,
        backgroundColor: 'transparent',
        onClick: () => onSelectRecipient(item),
        buttonGroup: [
          {
            icon: 'more_vert',
            popupPlacement: 'bottom-end',
            bg: 'flat',
            size: 'small',
            popup: () => {
              return (
                // @ts-expect-error: refactor MntrMenu to TS
                <MntrMenu
                  menuItems={[
                    {
                      label: t`Remove`,
                      leftIcon: 'delete',
                      hoverVariant: 'error',
                      onClick: () => {
                        removeRecipients([item.id])
                      },
                    },
                  ]}
                />
              )
            },
          },
        ],
      }
    })
  }

  return (
    <>
      <MntrMenuHeading label={`${title} (${filteredRecipients.length})`} />
      {/* Search input with an icon */}
      <SearchWrapper>
        <Flex alignItems="center" color="lightGrey">
          <Icon>search</Icon>
          <StyledInputTransparent
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="Search recipients"
          />
        </Flex>
      </SearchWrapper>
      {/* @ts-expect-error: refactor MntrMenu to TS */}
      <MntrMenu closePopup={closePopup} menuItems={createRecipientList()} />
      {filteredRecipients.length > displayLimit && (
        <MntrButton
          mb={3}
          onClick={() => setShowAll(!showAll)}
          bg="flat"
          label={
            showAll
              ? t`Collapse`
              : t`Show all recipients (+${filteredRecipients.length - displayLimit})`
          }
          icon={showAll ? 'expand_less' : 'expand_more'}
        />
      )}
    </>
  )
}

export default RecipientsList
