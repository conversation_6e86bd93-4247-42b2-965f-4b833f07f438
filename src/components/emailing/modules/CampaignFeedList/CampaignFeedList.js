import { t } from '@lingui/core/macro'
import EmailingFeedItem from '~/components/emailing/components/EmailingFeed/EmailingFeedItem'
import withModalAddCampaign from '~/components/emailing/modules/withModalAddCampaign'
import { Box, Flex, Heading, Skeleton, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import LoadMore from '~/components/monitoring/FeedList/LoadMore/LoadMore'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'

const CampaignFeedList = ({ appStore: { account, emailing } }) => {
  return (
    <>
      {/* Campaigns */}
      {emailing.campaigns.isLoaded ? (
        <Flex flexDirection="column" gap={2}>
          {emailing.campaigns.results.map((item, index) => {
            // Menu Items [more_vert icon]
            const menuItems = [
              {
                label: t`Open`,
                leftIcon: 'open_in_full',
                href: `/emailing/campaign/${item.id}`,
              },
            ]

            if (account.workspace?.permissions.newsroom_emailing.can_write) {
              menuItems.push(
                {
                  label: t`New Email`,
                  leftIcon: 'mail',
                  href: `/emailing/campaign/${item.id}/email/create`,
                },
                {
                  label: t`Edit Campaign`,
                  leftIcon: 'edit',
                  ...withModalAddCampaign({
                    modalTitle: t`Edit Campaign`,
                    onSubmit: item.update,
                    emailing: emailing,
                    initialValues: item,
                  }),
                },
              )
            }

            if (item.can_delete && account.workspace?.permissions.newsroom_emailing.can_write) {
              menuItems.push({})

              menuItems.push({
                label: t`Remove Campaign`,
                leftIcon: 'delete',
                hoverVariant: 'error',
                ...withModalRemove({
                  message: t`Campaign will be removed`,
                  onSubmit: item.remove,
                }),
              })
            }

            return (
              <EmailingFeedItem
                item={item}
                key={index.toString()}
                href={'/emailing/campaign/[campaignId]'}
                as={`/emailing/campaign/${item.id}`}
                menuItems={menuItems}
                visibleAvatar
                visibleEmails
              />
            )
          })}
        </Flex>
      ) : (
        <Skeleton height="78px" width="100%!important" repeat={3} />
      )}

      {/* No campaigns */}
      {emailing.campaigns.isFeedEmpty && (
        <div>
          <Box my={4} textAlign="center">
            <Heading color="heading" fontSize={4} mb={2}>{t`No campaigns yet`}</Heading>

            {account.workspace?.permissions.newsroom_emailing.can_write && (
              <>
                <Text
                  mb={6}
                  color="mediumGrey"
                >{t`You can create your first campaign by clicking the button below.`}</Text>

                <MntrButton
                  label={t`New Campaign`}
                  {...withModalAddCampaign({ emailing })}
                  bg="secondary"
                />
              </>
            )}
          </Box>
        </div>
      )}

      {/* Pagination */}
      {emailing.campaigns.hasMore && !emailing.campaigns.isLoadingMore && (
        <LoadMore
          isLoading={false}
          onLoadMore={() => {
            emailing.campaigns.loadMore()
          }}
          shouldAutoLoadMore
        />
      )}
    </>
  )
}

export default observer(CampaignFeedList)
