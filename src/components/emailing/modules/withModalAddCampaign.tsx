import { t } from '@lingui/core/macro'
import FormAddCampaign from '~/components/emailing/forms/FormAddCampaign'
import { IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import { IEmailingStore } from '~/store/models/emailing/EmailingStore'

export interface IWithModalAddCampaignProps {
  onSubmit?: (values: Record<string, unknown>) => Promise<void>
  modalTitle?: string
  initialValues?: IMntrFormProps
  emailing: IEmailingStore
}

const withModalAddCampaign = ({
  onSubmit,
  emailing,
  modalTitle,
  initialValues,
}: IWithModalAddCampaignProps) => {
  return {
    modalTitle: modalTitle || t`New Campaign`,
    modal: (closeModal: () => void) => {
      return (
        <FormAddCampaign
          onSubmit={onSubmit || emailing.campaigns.create}
          closeModal={closeModal}
          initialValues={initialValues}
        />
      )
    },
  }
}

export default withModalAddCampaign
