import type { JSX } from 'react'

export interface IRecipient {
  id: string
  email?: string
  author?: {
    id: string
    name: string
    photo_url: { small: string }
    author_type: string
  }
  missing_merge_tags: Array<{ id: string; text: string }>
  is_missing_email: boolean
  remove: () => void
  onClick?: () => void
  preview_body: string
}

export interface IEmailRecipientsListProps {
  emailBody: string
}

export interface IRenderInvalidRecipientsProps {
  invalidRecipients: IRecipient[]
  renderRecipientButton: (
    item: IRecipient,
    index: number,
    bg: string,
    recipientsArr: IRecipient[],
    tooltip?: JSX.Element,
    withAddMissingInfo?: boolean,
  ) => JSX.Element
  renderRemovalAllButton: (recipientsArr: IRecipient[]) => JSX.Element
}

export interface IRenderAllRecipientsProps {
  allRecipients: IRecipient[]
  renderRecipientButton: (
    item: IRecipient,
    index: number,
    bg: string,
    recipientsArr: IRecipient[],
    tooltip?: JSX.Element,
    withAddMissingInfo?: boolean,
  ) => JSX.Element
  renderRemovalAllButton: (recipientsArr: IRecipient[]) => JSX.Element
  isLoadedAddRecipients: boolean
}

export interface IWithModalRemoveRecipientsProps {
  onSubmit: () => void
  submitLabel: string
  title: string
  modalIcon: string
  isStackedModal: boolean
  closeParentModal?: () => void
  modalZIndex?: number
  renderRecipientButton: (
    item: IRecipient,
    index: number,
    bg: string,
    recipientsArr: IRecipient[],
    tooltip?: JSX.Element,
    withAddMissingInfo?: boolean,
  ) => JSX.Element
  recipients: IRecipient[]
  skipRemoveRecipientsArr: string[]
  setSkipRemoveRecipientsArr: (arr: string[]) => void
}

export interface IAddMissingInfoProps {
  recipients: IRecipient[]
  emailBody: string
  totalCount: number
  currentIndex: number
  setCurrentIndex: (index: number) => void
  modalIsOpen: boolean
  setModalIsOpen: (isOpen: boolean) => void
}
