import { t } from '@lingui/core/macro'
import noopFn from 'lodash/noop'
import sendersVerifications from '~/components/emailing/constants/senderVerifications.constants'
import FormSenderSettings from '~/components/emailing/forms/FormSenderSettings'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { Box } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import { IEmailingSettingsStore } from '~/store/models/emailing/settings/EmailingSettingsStore'

const EmailingSendersList = ({ senders }: IEmailingSettingsStore) => {
  const list = senders.results
  // @ts-expect-error TODO refactor MntrMenu to tsx */
  const menuItems = []

  // senders list
  list.forEach((item) => {
    const buttonGroup = []

    if (item.is_primary) {
      buttonGroup.push({
        icon: 'star',
        mr: 1,
        iconFill: true,
        iconColor: 'secondary',
        bg: 'transparent',
        tooltip: t`The primary sender is used as the default sender for emails. You can change this when you create an email.`,
      })
    }

    // right button group
    buttonGroup.push({
      'data-e2e': `sender-more-button-${item.email}`,
      icon: 'more_vert',
      popupPlacement: 'bottom-end',
      mr: 2,
      popup: (closePopup: () => void) => {
        const menuItems = []

        // Edit
        menuItems.push({
          label: t`Edit`,
          leftIcon: 'edit',
          modalTitle: `${t`Edit Sender`} (${item.email})`,
          modal: (closeModal: () => void) => {
            const formSchema: IFormSchemaItem[] = [
              {
                name: 'name',
                label: t`Name`,
              },
              {
                actions: ({ pristine, submitting }) => [
                  {
                    label: t`Save`,
                    type: 'submit',
                    bg: 'secondary',
                    disabled: pristine || submitting,
                  },
                ],
              },
            ]

            return (
              <MntrForm
                contentPadding={3}
                formGap={2}
                schema={formSchema}
                onSubmit={(model) => {
                  return item
                    .edit(model)
                    .then(() => {
                      if (typeof closeModal === 'function') {
                        closeModal()
                      }
                    })
                    .catch((err) => {
                      return err
                    })
                }}
                initialValues={{ ...item }}
              />
            )
          },
        })

        // Set as primary
        if (!item.is_primary && item.isActivated) {
          menuItems.push({
            label: t`Set as primary`,
            leftIcon: 'star',
            onClick: item.setAsPrimary,
          })
        }

        // Resend verification email
        if (!item.isActivated) {
          menuItems.push({
            label: t`Resend verification email`,
            leftIcon: 'send',
            onClick: () => {
              item.resend()
              closePopup()
            },
          })
        }

        // Divider
        menuItems.push({})

        // Delete
        if (!item.isActivated) {
          menuItems.push({
            label: t`Delete`,
            leftIcon: 'delete',
            hoverVariant: 'error',
            ...withModalRemove({
              title: t`Delete Sender`,
              message: t`Are you sure you want to delete this sender?`,
              onSubmit: item.remove,
            }),
          })
        }
        // @ts-expect-error TODO refactor MntrMenu to tsx
        return <MntrMenu menuItems={menuItems} />
      },
    })

    // left icon according to verification status
    let leftIconColor = 'error'
    let leftIcon = 'gpp_bad'

    if (item.isActivated && item.email_backend.id !== sendersVerifications.NO_VERIFICATION) {
      leftIconColor = 'secondary'
      leftIcon = 'verified_user'
    }

    if (item.isActivated && item.email_backend.id === sendersVerifications.NO_VERIFICATION) {
      leftIconColor = 'tertiary'
      leftIcon = 'shield'
    }

    // list item
    menuItems.push({
      label: item.full_email,
      leftIcon,
      leftIconColor,
      secondaryText: item.isActivated ? t`Active` : t`Not verified`,
      hoverVariant: 'light',
      buttonGroup: buttonGroup,
      modalTitle: item.full_email,
      disabled: !item.isActivated || !item.email_backend.is_user_configurable,
      modalWidth: 800,
      bg: 'transparent',
      modal: item.isActivated
        ? (closeModal: () => void) => {
            return (
              <FormSenderSettings
                initialValues={item}
                onSubmit={item.verifyByValues}
                closeModal={closeModal}
              />
            )
          }
        : noopFn,
    })
  })

  if (list.length === 0) {
    return (
      <MntrHint
        icon="manage_accounts"
        color="error"
        heading={t`No senders`}
        text={t`Add a sender to activate Emailing.`}
      />
    )
  }

  return (
    <div>
      <MntrPaper>
        <MntrPaperToolbar title={t`Senders`} icon="manage_accounts" />
        <Box pb={3}>
          {/* @ts-expect-error TODO refactor MntrMenu to tsx */}
          <MntrMenu menuItems={menuItems} />
        </Box>
      </MntrPaper>
    </div>
  )
}

export default observer(EmailingSendersList)
