import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import { Box, Heading, Skeleton, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import LoadMore from '~/components/monitoring/FeedList/LoadMore/LoadMore'
import { observer } from '~/helpers/mst'
import RecipientsFeed from './RecipientsFeed'

const RecipientsFeedContent = ({ appStore: { account }, recipientsStore, url }) => {
  const {
    query: { campaignId },
    query: { filter },
  } = useRouter()

  const results = recipientsStore.results
  const recipientsList = results || []

  return (
    <>
      {/* Recipients Feed */}
      {recipientsStore.isLoaded ? (
        recipientsList.length > 0 ? (
          <RecipientsFeed>
            {recipientsList.map((item, index) => {
              const eventData = item.furthest_funnel_group
                ? account.enums.emailing.funnel_group.find(
                    (group) => group.id === item.furthest_funnel_group,
                  )
                : null

              return (
                <RecipientsFeed.Item
                  item={item}
                  eventData={eventData}
                  id={index + 1}
                  key={index.toString()}
                  status={index % 3 === 0 ? 'draft' : 'sent'}
                />
              )
            })}
          </RecipientsFeed>
        ) : null
      ) : (
        <Skeleton height={'78px'} repeat={5} fullWidth />
      )}

      {/* No recipients */}
      {recipientsStore.isFeedEmpty && !recipientsStore.filterStatus && !filter && (
        <Box textAlign="center" my={4}>
          <Heading color="heading" fontSize={4} mb={2}>{t`No recipients yet`}</Heading>
          <Text mb={6} color="mediumGrey">{t`You can edit recipients in email settings.`}</Text>

          <MntrButton
            label={t`Emails`}
            bg="secondary"
            icon="email"
            rounded
            href={`/emailing/campaign/${campaignId}`}
          />
        </Box>
      )}

      {/* No emails with applied filter */}
      {recipientsStore.isFeedEmpty && (recipientsStore.filterStatus || filter) && (
        <Box textAlign="center" my={4}>
          <Heading color="heading" fontSize={4} mb={2}>{t`No recipients found`}</Heading>
          <Text
            mb={6}
            color="mediumGrey"
          >{t`You can reset your filter by clicking the button below.`}</Text>

          <MntrButton ml={2} label={t`Reset filter`} bg="secondary" href={url} />
        </Box>
      )}

      {/* Pagination */}
      {recipientsStore.hasMore && !recipientsStore.isLoadingMore && (
        <LoadMore
          isLoading={false}
          onLoadMore={() => {
            recipientsStore.loadMore()
          }}
          shouldAutoLoadMore
        />
      )}
    </>
  )
}

export default observer(RecipientsFeedContent)
