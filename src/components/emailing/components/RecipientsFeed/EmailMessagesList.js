import { t } from '@lingui/core/macro'
import displayEmailingTitle from '~/components/emailing/helpers/displayEmailingTitle'
import { ListItemBox } from '~/components/emailing/style/StyledEmailing'
import { Box, Text } from '~/components/misc/Mntr'
import displayDateOrFromNow from '~/helpers/displayDateOrFromNow'

const EmailMessagesList = ({ list }) => {
  return (
    <Box px={3}>
      {list.map((item) => {
        return (
          <ListItemBox key={item.id} color={item.latest_event_type.color} my={2} px={2}>
            <Text color="black">{displayEmailingTitle(item, false) || `<${t`No title`}>`}</Text>
            <Text color="mediumGrey">
              {item.latest_event_type.text}
              {Boolean(item.sent_at) && `, ${displayDateOrFromNow(item.sent_at)}`}
            </Text>
          </ListItemBox>
        )
      })}
    </Box>
  )
}

export default EmailMessagesList
