import Flag from '~/components/Flag/Flag'
import { formatEmails } from '~/components/emailing/helpers/emailing.plurals'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import AuthorPhoto from '~/components/medialist/content/FeedMedialist/AuthorPhoto/AuthorPhoto'
import { Box, Flex, Heading, Link, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import EmailMessagesList from './EmailMessagesList'
import RecipientsEventsList from './RecipientsEventsList'

const RecipientsFeed = ({ children }) => {
  return (
    <Flex flexDirection="column" gap={2}>
      {children}
    </Flex>
  )
}

RecipientsFeed.Item = ({ item, eventData }) => {
  const Title = () => {
    return (
      <Flex>
        <Heading fontSize={3} mb={1} color="heading">
          {item.title}
        </Heading>
      </Flex>
    )
  }

  return (
    <MntrPaper>
      <Flex py={2} flexDirection={['column', 'column', 'row']}>
        <Flex flex={1} px={2} alignItems="center">
          <Box mr={3} ml={1} position="relative">
            <AuthorPhoto
              size={30}
              authorType={item.author?.author_type}
              image={item.author?.photo_url.small}
            />
            {item.author?.country && (
              <Box position="absolute" bottom="-4px" right="-4px">
                <Flag country={item.author?.country.code} size={15} />
              </Box>
            )}
          </Box>
          <Box>
            {item.author?.id ? (
              <Link href={`/author/${item.author.id}`} target="_blank" naked>
                <Title />
              </Link>
            ) : (
              <Title />
            )}
            <Box>
              <Text color="mediumGrey">{item.description}</Text>
            </Box>
          </Box>
        </Flex>
        <Flex
          justifyContent="flex-end"
          alignItems="center"
          height={['auto', 'auto', '56px']}
          gap={1}
          px={2}
        >
          {/* funnel data */}
          {eventData && item.recipient_events.length === 0 && (
            <Box width={['auto', '185px']}>
              <MntrButton
                disabled
                icon={eventData.icon}
                iconColor="white"
                iconFill
                iconBg={eventData.color}
                isChip
                label={`${eventData.text_single}`}
                bg="light"
              />
            </Box>
          )}

          {/* Modal Emails list */}
          {item.email_messages?.length > 0 && (
            <Box>
              <MntrButton
                icon="mail"
                iconBg="primary"
                isChip
                bg="light"
                label={formatEmails(item.email_messages.length)}
                popupPlacement={'bottom-end'}
                transformOrigin="100% 0"
                modalTitle={`${item.title} (${item.email_messages.length})`}
                modal={() => {
                  return <EmailMessagesList list={item.email_messages} />
                }}
              />
            </Box>
          )}

          {/* Modal Recipient Events */}
          {item.recipient_events?.length > 0 && (
            <Box>
              <MntrButton
                icon="circle"
                iconColor={item.recipient_events[0].event_type.color}
                iconFill
                iconBg="transparent"
                isChip
                bg="light"
                label={`${item.recipient_events[0].event_type.text}`}
                popupPlacement={'bottom-end'}
                transformOrigin="100% 0"
                modalTitle={`${item.title} (${item.recipient_events.length})`}
                modalIcon="mail"
                modal={() => {
                  return <RecipientsEventsList list={item.recipient_events} />
                }}
              />
            </Box>
          )}
        </Flex>
      </Flex>
    </MntrPaper>
  )
}

export default RecipientsFeed
