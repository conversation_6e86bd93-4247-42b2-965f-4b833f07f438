import { useEffect, useState } from 'react'
import { ListItemBox } from '~/components/emailing/style/StyledEmailing'
import { Box, Text } from '~/components/misc/Mntr'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import displayDateOrFromNow from '~/helpers/displayDateOrFromNow'

const RecipientsEventsList = ({ list }) => {
  const [processedList, setProcessedList] = useState([])

  // deduplicate list
  useEffect(() => {
    const newList = list.reduce((acc, current) => {
      const itemIndex = acc.findIndex((item) => item.event_type === current.event_type)

      if (itemIndex !== -1) {
        const item = acc[itemIndex]
        const updatedItem = {
          ...item,
          timestamps: [...item.timestamps, current.timestamp],
          label: item.timestamps.length + 1 > 1 ? `(${item.timestamps.length + 1}x)` : '',
        }
        return [...acc.slice(0, itemIndex), updatedItem, ...acc.slice(itemIndex + 1)]
      } else {
        const newItem = {
          ...current,
          timestamps: [current.timestamp],
          label: '',
        }
        return [...acc, newItem]
      }
    }, [])

    setProcessedList(newList)
  }, [list])

  return (
    <Box px={3}>
      {processedList.map((item, index) => {
        return (
          <ListItemBox key={index} color={item.event_type.color} my={2} px={2}>
            <Text color="black">
              {item.event_type.text}

              {item.label && (
                <Text as="span" color="mediumGrey">
                  {' '}
                  {item.label}
                </Text>
              )}
            </Text>
            {item.timestamps.length > 1 ? (
              <StyledTooltip
                tooltip={
                  <Box>
                    {item.timestamps.map((timestamp, index) => {
                      return (
                        <Text key={index} color="inherit">
                          {displayDateOrFromNow(timestamp, 'do MMMM yyyy hh:mm')}
                        </Text>
                      )
                    })}
                  </Box>
                }
                placement="left-start"
              >
                <Text color="mediumGrey">
                  {displayDateOrFromNow(
                    item.timestamps[item.timestamps.length - 1],
                    'do MMMM yyyy hh:mm',
                  )}
                </Text>
              </StyledTooltip>
            ) : (
              <Text color="mediumGrey">
                {displayDateOrFromNow(
                  item.timestamps[item.timestamps.length - 1],
                  'do MMMM yyyy hh:mm',
                )}
              </Text>
            )}
          </ListItemBox>
        )
      })}
    </Box>
  )
}

export default RecipientsEventsList
