import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'
import displayEmailingTitle from '~/components/emailing/helpers/displayEmailingTitle'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import displayDateOrFromNow from '~/helpers/displayDateOrFromNow'
import * as format from '~/helpers/formatNumber'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'
import ModalMentionedArticles from './ModalMentionedArticles'

const MetaDataWrapper = styled.div`
  display: inline-grid;
  grid-template-columns: repeat(auto-fit, minmax(${({ width }) => width || 250}px, 1fr));
  align-items: start;
`

export const StyledCharAvatar = styled(Flex)`
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-weight: 700;
  text-transform: uppercase;
  background-color: ${({ theme }) => theme.colors.highlight};
  color: ${({ theme }) => theme.colors.mediumGrey};
`

const IconWrapper = styled.div`
  display: inline-block;
  margin-right: 4px;
  position: relative;
  top: 1px;
`

const Wrapper = styled(Box)`
  position: relative;
  view-transition-name: out-${({ id }) => id};
  transition: all 0.2s ease-in-out;

  & a {
    text-decoration: none;
  }

  &.hidden {
    display: none;
  }

  &.flash {
    box-shadow: 1px 3px 12px ${({ theme }) => theme.colors.secondary};
    border-radius: ${({ theme }) => theme.paper.borderRadius}px;

    & a > div {
      border: 1px solid ${({ theme }) => theme.colors.secondary};
    }
  }
`

const StatusPercentage = ({ percentage, label }) => {
  return (
    <Flex justifyContent="center" alignItems="center" flexDirection="column">
      <Text textAlign="center" fontSize={3} mb={[0, 1]}>
        <strong>{Math.floor(percentage)}</strong>
        <small style={{ marginLeft: 2 }}>%</small>
      </Text>
      <Text textAlign="center" color="mediumGrey">
        {label}
      </Text>
    </Flex>
  )
}

const MetaDataItem = ({ 'data-e2e': dataE2E, label, value, width }) => {
  return (
    <MetaDataWrapper width={width}>
      <Text color="mediumGrey" fontSize={1}>
        {label}
        {label && ':'} <span data-e2e={dataE2E}>{value}</span>
      </Text>
    </MetaDataWrapper>
  )
}

const PaperContent = ({
  item,
  headingFontSize,
  displayDate,
  visibleAvatar,
  visibleEmails,
  href,
  as,
}) => {
  const title = displayEmailingTitle(item, false)
  const imgChar = title && title.charAt(0).toUpperCase()

  // show rates stats only for sent emails or for campaigns list
  const visibleRatesStats = item.visibleRatesStats
  const visibleMentionedArticles = item.mentioned_article_count > 0 && !visibleAvatar

  const TitleData = () => {
    return (
      <Flex flex={1} px={2} alignItems="top" width={1}>
        {visibleAvatar && imgChar && (
          <Flex mr={3} ml={1} mt={'10px'}>
            <StyledCharAvatar justifyContent="center" alignItems="center">
              {imgChar}
            </StyledCharAvatar>
          </Flex>
        )}

        <Flex flexDirection="column" flex={1}>
          <Flex>
            <Heading
              fontSize={headingFontSize || 3}
              mb={1}
              pr={4}
              lineHeight={[1.22]}
              wordBreak="break-all"
            >
              {title || (
                <Text color="mediumGrey" fontSize={2} fontStyle="italic">{`<${t`No title`}>`}</Text>
              )}
            </Heading>
          </Flex>

          <Box>
            <Flex
              flexDirection={['column', 'column', 'column', 'column', 'row']}
              gap={[0, 0, 0, 0, 3]}
            >
              {item.status && (
                <MetaDataItem
                  width={100}
                  value={
                    <span>
                      <IconWrapper>
                        <Text color={item.status.color}>
                          <Icon fill size={12}>
                            circle
                          </Icon>
                        </Text>
                      </IconWrapper>
                      {item.status.text}
                    </span>
                  }
                />
              )}
              <MetaDataItem
                data-e2e="datetime+from_now"
                label={t`Last update`}
                value={displayDate}
              />
              {visibleEmails && (
                <MetaDataItem
                  data-e2e="counter"
                  label={t`Emails`}
                  value={item.email_message_count}
                  width={100}
                />
              )}
              <MetaDataItem label={t`Recipients`} value={item.recipient_count || 0} width={100} />
            </Flex>
          </Box>
        </Flex>
      </Flex>
    )
  }

  return (
    <>
      <Flex py={2} gap={3} width={[1]} justifyContent={['space-between']}>
        {href && as && (
          <Link href={href} as={as} naked>
            <TitleData />
          </Link>
        )}

        {!href && !as && <TitleData />}

        <Flex flexDirection="row" mr={50} center gap={2}>
          {visibleMentionedArticles && (
            <MntrButton
              bg="flat"
              isChip
              icon="open_in_new"
              iconColor="currentColor"
              modalIcon="view_stream"
              iconBg="transparent"
              label={t`Show ${format.formatAttachedArticles(item.mentioned_article_count)}`}
              modalTitle={t`Attached articles`}
              modal={() => {
                return <ModalMentionedArticles item={item} />
              }}
            />
          )}

          {visibleRatesStats && (
            <Flex gap={2} flexDirection={'row'}>
              <Flex centerX display={['none', 'none', 'flex']}>
                <StatusPercentage
                  percentage={(item && item.delivery_rate) || 0}
                  label={t`Delivery rate`}
                />
              </Flex>

              <Flex centerX display={['none', 'none', 'flex']} alignSelf="end">
                <StatusPercentage percentage={item.open_rate || 0} label={t`Open rate`} />
              </Flex>
            </Flex>
          )}
        </Flex>
      </Flex>
      {visibleRatesStats && (
        <Flex
          justifyContent="end"
          p={2}
          pt={0}
          gap={2}
          display={['flex', 'flex', 'none']}
          flexWrap="wrap"
        >
          <MntrButton
            label={`${t`Open rate`}: ${Math.floor(item?.open_rate || 0)} %`}
            isChip
            disabled
          />
          <MntrButton
            label={`${t`Delivery rate`}: ${Math.floor(item?.delivery_rate || 0)} %`}
            isChip
            disabled
          />
        </Flex>
      )}
    </>
  )
}

const EmailingFeedItem = ({ item, href, as, menuItems, visibleAvatar, visibleEmails }) => {
  const displayDate = displayDateOrFromNow(item.updated_at)

  return (
    <Wrapper className={`email-feed-item-${item.id}`} id={item.id} data-name={item.name}>
      {href && as && (
        <MntrPaper>
          <PaperContent
            href={href}
            as={as}
            item={item}
            displayDate={displayDate}
            visibleAvatar={visibleAvatar}
            visibleEmails={visibleEmails}
          />
        </MntrPaper>
      )}

      {!href && !as && (
        <PaperContent
          item={item}
          headingFontSize={[3, 4]}
          displayDate={displayDate}
          visibleAvatar={visibleAvatar}
          visibleEmails={visibleEmails}
        />
      )}

      {menuItems && menuItems.length > 0 && (
        <Box p={2} position="absolute" top={10} right={0}>
          <MntrButton
            icon="more_vert"
            bg="transparent"
            popupPlacement={'bottom-end'}
            transformOrigin="100% 0"
            popup={(closePopup) => {
              return <MntrMenu closePopup={closePopup} menuItems={menuItems} closePopupOnSubmenu />
            }}
          />
        </Box>
      )}
    </Wrapper>
  )
}

export default observer(EmailingFeedItem)
