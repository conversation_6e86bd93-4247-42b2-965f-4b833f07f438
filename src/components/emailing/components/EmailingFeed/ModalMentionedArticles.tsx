import { useEffect, useState } from 'react'
import MentionedNewsroomPost from '~/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Flex } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import { ICampaignEmailStoreArrItem } from '~/store/models/emailing/campaigns/CampaignStore/emails/CampaignEmailsStore/CampaignEmailStoreArrItem'

interface IModalMentionedArticles {
  item: ICampaignEmailStoreArrItem
}

const ModalMentionedArticles = ({ item }: IModalMentionedArticles) => {
  const [articles, setArticles] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setIsLoading(true)
    async function getData() {
      const res = await item.getMentionedArticles()

      setArticles(res)
      setIsLoading(false)
    }

    getData()
  }, [item])

  return (
    <ListScrollWrapper maxWidth="600">
      <Flex flexDirection="column" gap={2} p={4}>
        {isLoading && (
          <Flex height={'300px'} center>
            <MntrCircularProgress size={40} />
          </Flex>
        )}
        {!isLoading &&
          articles?.map((article, index) => {
            return <MentionedNewsroomPost article={article} key={index} />
          })}
      </Flex>
    </ListScrollWrapper>
  )
}

export default ModalMentionedArticles
