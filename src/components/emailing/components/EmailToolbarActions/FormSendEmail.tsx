import { t } from '@lingui/core/macro'
import { addYears, isBefore } from 'date-fns'
import { useState } from 'react'
import { Field, FormProps } from 'react-final-form'

import emailStatus from '~/components/emailing/constants/emailStatus.constants'
import MntrDatepickerAdapter from '~/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import { ObservedFC, observer } from '~/helpers/msts'
import { IEmailSettingsSendersStoreArrItem } from '~/store/models/emailing/settings/EmailSettingsSendersStoreArrItem'

// TODO refactor to date-fns + FormScheduledDateTime
// Converts a Date object to a formatted string in 'dd.mm.yyyy' format.
const convertInitialDateToFormattedString = (date: Date) => {
  const formattedDate = `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1)
    .toString()
    .padStart(2, '0')}.${date.getFullYear()}`
  return formattedDate
}

function convertOrKeepDateFormat(dateStr: string) {
  const regexYYYYMMDD = /^\d{4}-\d{2}-\d{2}$/
  const regexDDMMYYYY = /^\d{2}\.\d{2}\.\d{4}$/

  if (regexYYYYMMDD.test(dateStr)) {
    const [year, month, day] = dateStr.split('-')
    return `${parseInt(day, 10)}.${month}.${year}`
  } else if (regexDDMMYYYY.test(dateStr)) {
    return dateStr
  } else {
    return 'Invalid format'
  }
}

// Converts an object containing a date and time into an ISO 8601 formatted string.
function formatDateTime(dateStr: string, timeStr: string) {
  // Check if dateStr and timeStr exist and are strings
  if (typeof dateStr !== 'string' || typeof timeStr !== 'string') {
    return null
  }

  // Validate date format (DD.MM.YYYY)
  const dateRegex = /^\d{2}\.\d{2}\.\d{4}$/
  if (!dateRegex.test(dateStr)) {
    throw new Error('Invalid date format. Use DD.MM.YYYY')
  }

  // Validate time format (HH:MM)
  const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/
  if (!timeRegex.test(timeStr)) {
    throw new Error('Invalid time format. Use HH:MM (24-hour format)')
  }

  // Parse the date string
  const [day, month, year] = convertOrKeepDateFormat(dateStr).split('.').map(Number)

  // Parse the time string
  const [hours, minutes] = timeStr.split(':').map((num) => parseInt(num, 10))

  // Create a new Date object
  // Note: months in JavaScript Date are 0-indexed, so we subtract 1 from the month
  const date = new Date(year, month - 1, day, hours, minutes)

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date or time values')
  }

  // Format the date to ISO 8601 format
  return date.toISOString()
}

interface IErrorsProps {
  subject?: string
  scheduled_date?: string
}

const errors: IErrorsProps = {}

const FormSendEmail: ObservedFC<IMntrFormProps> = ({
  appStore: { emailing },
  closeModal,
  onSubmit,
  initialValues,
  senders,
}) => {
  const [prevStatus, setPrevStatus] = useState(emailStatus.SENT)

  const onChange = (values: FormProps['FormValues'], form: FormProps['form']) => {
    if (values.values.status === emailStatus.SENT) {
      if (values.values.scheduled_date) form?.change('scheduled_date', null)
      if (values.values.scheduled_time) form?.change('scheduled_time', null)
      if (values.values.scheduled_at) form?.change('scheduled_at', null)
      delete errors.scheduled_date
    }

    if (values.values.status === emailStatus.SCHEDULED) {
      const scheduledDateField = form?.getFieldState('scheduled_date')

      if (
        prevStatus !== emailStatus.SCHEDULED &&
        (scheduledDateField?.value === null || scheduledDateField?.value === undefined)
      ) {
        const initialScheduledDate = convertInitialDateToFormattedString(
          new Date(new Date().setDate(new Date().getDate() + 1)),
        )

        form?.change('scheduled_date', initialScheduledDate)
      }
      if (!values.values.scheduled_time) form?.change('scheduled_time', '12:00')
      form?.change(
        'scheduled_at',
        formatDateTime(values.values.scheduled_date, values.values.scheduled_time),
      )
    }
    setPrevStatus(values.values.status)
  }

  const statusLabels = {
    [emailStatus.SENT]: t`Send`,
    [emailStatus.SCHEDULED]: t`Set send date`,
  }

  const formSchema: IFormSchemaItem[] = [
    {
      fieldset: {
        legend: t`Are you ready to send the email?`,
        name: 'send_email',
        fields: [
          {
            name: 'sender',
            adapter: 'select',
            label: t`Sender`,
            items: senders.map((item: IEmailSettingsSendersStoreArrItem) => ({
              value: item.id,
              label: item.full_email,
            })),
          },
          {
            label: t`Email subject`,
            name: 'subject',
          },
          {
            adapter: 'checkbox',
            itemsGap: 3,
            items: [
              {
                label: t`Send now`,
                description: t`Send this email immediately`,
                type: 'radio',
                name: 'status',
                value: emailStatus.SENT,
                parse: (value: unknown) => parseInt(value as string),
              },
              {
                label: t`Set send date`,
                description: t`Set this email to auto-send`,
                type: 'radio',
                name: 'status',
                value: emailStatus.SCHEDULED,
                parse: (value: unknown) => parseInt(value as string),
              },
            ],
          },
          {
            customComponent: ({ form }) => {
              const onFocus = () => {
                delete errors.scheduled_date
                form?.change('status', emailStatus.SCHEDULED)
              }

              return (
                <Flex gap={3} pb={6}>
                  <Field
                    label={t`Date`}
                    name="scheduled_date"
                    component={MntrDatepickerAdapter}
                    maxDate={addYears(new Date(), 1)}
                    onFocus={onFocus}
                    onSubmit={(day: string) => {
                      form?.change('scheduled_date', day)
                    }}
                    zIndex={6000}
                  />

                  <Flex minWidth="120px">
                    <Field
                      label={t`Time`}
                      name="scheduled_time"
                      type="time"
                      onFocus={onFocus}
                      // @ts-expect-error TODO refactor adapters to tsx
                      component={MntrTextFieldAdapter}
                    />
                  </Flex>
                </Flex>
              )
            },
          },
        ],
      },
    },
    {
      actions: ({ values, submitting }) => [
        {
          bg: 'secondary',
          icon: 'send',
          rounded: true,
          label: statusLabels[parseInt(values.status)],
          type: 'submit',
          disabled:
            submitting || (!values.scheduled_date && values.status === emailStatus.SCHEDULED),
        },
      ],
    },
  ]

  return (
    <Box p={3}>
      <MntrForm
        schema={formSchema}
        onSubmit={(values, form) => {
          delete errors.scheduled_date
          delete errors.subject

          if (values.subject === '' || !values.subject) {
            errors.subject = t`This field is required`
            return errors
          }

          if (values.scheduled_at) {
            const scheduledDateTime = new Date(values.scheduled_at)
            const currentDate = new Date()

            if (isBefore(scheduledDateTime, currentDate)) {
              errors.scheduled_date = t`Date and time must be in the future`
              return errors
            }
          }

          return onSubmit(values, form)?.then(() => {
            // refresh email listing after sending the email
            emailing.campaign_detail.result.emails.load()
            closeModal()
          })
        }}
        onChange={onChange}
        initialValues={{
          ...initialValues,
          status: emailStatus.SENT,
          scheduled_at: null,
        }}
      />
    </Box>
  )
}

export default observer(FormSendEmail)
