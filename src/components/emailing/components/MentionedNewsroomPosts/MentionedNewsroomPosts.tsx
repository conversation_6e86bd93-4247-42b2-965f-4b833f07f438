import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useEffect, useState } from 'react'
import { css, styled } from 'styled-components'

import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { ObservedFC, observer } from '~/helpers/msts'
import { IMentionedArticleStoreArrItem } from '~/store/models/emailing/shared/MentionedArticleStoreArrItem'
import MentionedNewsroomPost, { IMG_SIZE } from './MentionedNewsroomPost'

const MAX_ARTICLES = 12

interface IPostsProps {
  isExpanded: boolean
}

const Posts = styled.div<IPostsProps>`
  display: grid;
  gap: ${({ theme }) => theme.space[2]}px;

  ${({ isExpanded }) => {
    return (
      !isExpanded &&
      css`
        max-height: ${IMG_SIZE + 2}px;
        overflow: hidden;
      `
    )
  }}

  @media (min-width: ${({ theme }) => theme.breakpoints[1]}) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  @media (min-width: 1400px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  @media (min-width: 1920px) {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
`

interface IMentionedNewsroomPostsProps {
  articles: IMentionedArticleStoreArrItem[]
  isLoaded: boolean
}

const MentionedNewsroomPosts: ObservedFC<IMentionedNewsroomPostsProps> = ({
  appStore: { viewport },
  articles,
  isLoaded,
}) => {
  const viewportWidth = viewport.width
  const [articlesPerRow, setArticlesPerRow] = useState(3)
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    if (viewportWidth) {
      if (viewportWidth >= 1920) {
        setArticlesPerRow(4)
      } else if (viewportWidth >= 1400) {
        setArticlesPerRow(3)
      } else if (viewportWidth >= 800) {
        setArticlesPerRow(2)
      } else {
        setArticlesPerRow(1)
      }
    }
  }, [viewportWidth])

  const visibleArticles = isExpanded
    ? articles.slice(0, MAX_ARTICLES)
    : articles.slice(0, articlesPerRow)

  const handleToggle = () => {
    setIsExpanded(!isExpanded)
  }
  const isMore = articles.length > articlesPerRow
  return isLoaded && articles.length > 0 ? (
    <>
      <Flex position="relative" justifyContent="space-between">
        <Flex gap={1}>
          <Text textTransform="uppercase">
            <Trans>Newsroom Articles</Trans>
          </Text>
          <Box onClick={handleToggle} cursor={isMore ? 'pointer' : 'default'}>
            ({articles.length})
          </Box>
        </Flex>
        {isMore && (
          <Box position="absolute" right={0} top="-5px">
            <MntrButton
              onClick={handleToggle}
              label={isExpanded ? t`Show Less` : t`Show All`}
              icon={isExpanded ? 'expand_less' : 'expand_more'}
              bg="transparent"
              rounded
            />
          </Box>
        )}
      </Flex>

      <Flex column gap={2}>
        <Posts isExpanded={isExpanded}>
          {visibleArticles.map((article, index) => {
            return <MentionedNewsroomPost key={index} article={article} />
          })}
        </Posts>
      </Flex>
    </>
  ) : null
}

export default observer(MentionedNewsroomPosts)
