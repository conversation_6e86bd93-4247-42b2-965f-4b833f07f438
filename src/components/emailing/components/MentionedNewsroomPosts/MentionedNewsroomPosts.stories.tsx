import type { Meta, StoryObj } from '@storybook/react'
import { Box, Flex } from '~/components/misc/Mntr'
import MentionedNewsroomPosts from './MentionedNewsroomPosts'

const meta = {
  title: 'MentionedNewsroomPosts',
  component: MentionedNewsroomPosts,
  render({ ...args }) {
    return (
      <Box style={{ width: '100%', margin: '50px auto' }} p={3}>
        <Flex gap={4} column>
          <MentionedNewsroomPosts {...args} />
        </Flex>
      </Box>
    )
  },
} satisfies Meta<typeof MentionedNewsroomPosts>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    isLoaded: true,
    articles: Array.from({ length: 4 }, (_, i) => ({
      blog_id: i,
      url: null,
      title: `Title number ${i}`,
      published_at: '2025-01-21T10:49:05+01:00',
      cover_image_url: null,
      text: 'some text',
      visits: 22 * (i + 1),
      unique_visits: 12 * (i + 1),
    })),
  },
}
