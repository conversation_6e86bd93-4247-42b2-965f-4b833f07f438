import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'

import { Box, Flex, Text } from '~/components/misc/Mntr'
import { format } from '~/helpers/date/format'
import { IMentionedArticleStoreArrItem } from '~/store/models/emailing/shared/MentionedArticleStoreArrItem'

export const IMG_SIZE = 150

const PostImageWrapper = styled(Box)`
  width: ${IMG_SIZE}px;
  height: ${IMG_SIZE}px;
`

interface IPostImageProps {
  imageUrl: string | null
}

const PostImage = styled(Box)<IPostImageProps>`
  width: ${IMG_SIZE}px;
  height: ${IMG_SIZE}px;
  background: ${({ theme, imageUrl }) =>
    `url(${imageUrl}) center center / cover, ${theme.paper.backgroundHover}`};
`

const Post = styled.a`
  display: flex;
  gap: ${({ theme }) => `${theme.space[3]}px`};
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: ${({ theme }) => theme.space[1]}px;
  overflow: hidden;

  &:hover {
    border: 1px solid ${({ theme }) => theme.paper.borderHover};
    background: ${({ theme }) => theme.paper.backgroundHover};
  }
`

const PostTitle = styled(Text)`
  /* stylelint-disable */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* stylelint-enable */
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: bold;
`
interface IMentionedNewsroomPostProps {
  article: IMentionedArticleStoreArrItem
}

const MentionedNewsroomPost = ({ article }: IMentionedNewsroomPostProps) => {
  const { published_at, cover_image_url, title, visits, unique_visits, url } = article
  const articleUrl = url ? new URL(url) : null

  return (
    <Post href={`${articleUrl?.pathname}${articleUrl?.search}`} target="_blank">
      <PostImageWrapper>
        <PostImage imageUrl={cover_image_url} />
      </PostImageWrapper>
      <Flex column justifyContent="space-between" width={1} py={2}>
        <Flex column gap="3px" pr={1}>
          <PostTitle>{title}</PostTitle>
          <Text color="mediumGrey">
            {t`Published`}: {format(new Date(published_at), 'd. M. yyyy')}
          </Text>
        </Flex>
        {visits || unique_visits ? (
          <Flex justifyContent="space-between" alignItems="end" pr={1}>
            <Box>
              {visits ? (
                <Text color="mediumGrey">
                  {t`Visits`}: {visits}
                </Text>
              ) : null}
              {unique_visits ? (
                <Text color="mediumGrey">
                  {t`Unique visits`}: {unique_visits}
                </Text>
              ) : null}
            </Box>
          </Flex>
        ) : null}
      </Flex>
    </Post>
  )
}

export default MentionedNewsroomPost
