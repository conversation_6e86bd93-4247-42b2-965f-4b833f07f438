import { t } from '@lingui/core/macro'
import type { JSX } from 'react'
import { useState } from 'react'
import { IRecipient } from '~/components/emailing/components/Types/IRecipients'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

interface RecipientButtonProps {
  item: IRecipient
  index: number
  bg: string
  recipientsArr: IRecipient[]
  emailBody: string
  tooltip?: JSX.Element | string
  withAddMissingInfo?: boolean
  onClick?: () => void
}

const RecipientButton = ({ item, bg, tooltip, onClick }: RecipientButtonProps) => {
  const [disabled, setDisabled] = useState(false)
  const handleClick = () => {
    if (disabled) return
    item.remove()
    setDisabled(true)

    setTimeout(() => setDisabled(false), 1000)
  }

  return (
    <MntrButton
      isChip
      key={item.id}
      label={item.author?.name || item.email}
      onDelete={handleClick}
      onClick={onClick}
      image={item.author?.photo_url?.small}
      bg={bg}
      modalTitle={item.author?.name || item.email || t`Add Missing Info`}
      tooltip={tooltip || (item.author && item.author.name && item.email)}
    />
  )
}

export default RecipientButton
