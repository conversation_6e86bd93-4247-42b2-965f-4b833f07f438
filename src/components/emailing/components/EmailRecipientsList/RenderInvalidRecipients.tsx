// RenderInvalidRecipients.tsx
import { t } from '@lingui/core/macro'
import { IRenderInvalidRecipientsProps } from '~/components/emailing/components/Types/IRecipients'
import {
  StyledCounter,
  StyledMergeTagTooltip,
  StyledRecipientsBox,
} from '~/components/emailing/style/StyledRecipientsList'
import { Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrHint from '~/components/misc/MntrHint/MntrHint'

const RenderInvalidRecipients: React.FC<IRenderInvalidRecipientsProps> = ({
  invalidRecipients,
  renderRecipientButton,
  renderRemovalAllButton,
}) => {
  return (
    <>
      <Flex>
        {/* @ts-expect-error: refactor StyledRecipientsBox to TS */}
        <StyledRecipientsBox bg="warning" noPadding>
          <MntrHint
            text={t`Some recipients are missing information for merge tags or email. Please add the missing information or replace the recipients by clicking on them.`}
            color="warning"
            heading={t`Recipients with missing information`}
            icon="error"
            background="transparent"
          />
        </StyledRecipientsBox>
      </Flex>
      <Flex>
        {/* @ts-expect-error: refactor StyledRecipientsBox to TS */}
        <StyledRecipientsBox bg="chipTagBg" flex={true}>
          {invalidRecipients.map((item, index) => {
            const bg = 'warning'
            const tooltip = (
              <StyledMergeTagTooltip>
                <Heading color="inherit" as="h4" fontSize={1} fontWeight="bold">
                  {t`Missing data`}
                </Heading>
                <ul>
                  {item.missing_merge_tags.length > 0 ? (
                    item.missing_merge_tags.map((tag) => <li key={tag.id}>{tag.text}</li>)
                  ) : (
                    <li>{t`Email is missing`}</li>
                  )}
                </ul>
              </StyledMergeTagTooltip>
            )

            return renderRecipientButton(item, index, bg, invalidRecipients, tooltip)
          })}
        </StyledRecipientsBox>
      </Flex>
      <Flex alignItems="center" justifyContent="flex-end">
        <Text fontSize={1} color="mediumGrey">{t`Contacts`}</Text>
        <StyledCounter bg="chipTagBg" color="mediumGrey">
          {invalidRecipients.length}
        </StyledCounter>
        {renderRemovalAllButton(invalidRecipients)}
      </Flex>
    </>
  )
}

export default RenderInvalidRecipients
