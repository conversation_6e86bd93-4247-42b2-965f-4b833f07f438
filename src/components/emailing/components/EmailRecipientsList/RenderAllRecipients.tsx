import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { IRenderAllRecipientsProps } from '~/components/emailing/components/Types/IRecipients'
import {
  StyledCounter,
  StyledRecipientsBox,
} from '~/components/emailing/style/StyledRecipientsList'
import { Flex, Text } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import { Placeholder } from './EmailRecipientsList'

const RenderAllRecipients: React.FC<IRenderAllRecipientsProps> = ({
  allRecipients,
  renderRecipientButton,
  renderRemovalAllButton,
  isLoadedAddRecipients,
}) => {
  return (
    <>
      <Flex>
        {/* @ts-expect-error: refactor StyledRecipientsBox to TS */}
        <StyledRecipientsBox bg="chipTagBg" flex={true}>
          {isLoadedAddRecipients && (
            <Flex alignItems="center" justifyContent="center" m={3}>
              <MntrCircularProgress size={26} />
            </Flex>
          )}
          {allRecipients.length > 0 ? (
            allRecipients.map((item, index) =>
              renderRecipientButton(item, index, 'activeFilter', allRecipients),
            )
          ) : (
            <Placeholder>
              <Trans>You will see your recipients here</Trans>
            </Placeholder>
          )}
        </StyledRecipientsBox>
      </Flex>
      <Flex alignItems="center" justifyContent="flex-end">
        <Text fontSize={1} color="mediumGrey">{t`Authors`}</Text>
        <StyledCounter bg="chipTagBg" color="mediumGrey">
          {allRecipients.length}
        </StyledCounter>
        {renderRemovalAllButton(allRecipients)}
      </Flex>
    </>
  )
}

export default RenderAllRecipients
