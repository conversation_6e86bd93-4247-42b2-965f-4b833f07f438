import { t } from '@lingui/core/macro'
import noopFn from 'lodash/noop'
import { useState } from 'react'
import { styled } from 'styled-components'
import {
  IEmailRecipientsListProps,
  IRecipient,
} from '~/components/emailing/components/Types/IRecipients'
import withModalRemoveRecipients from '~/components/emailing/modules/withModalRemoveRecipients'
import { StyledIconWrapper } from '~/components/emailing/style/StyledRecipientsList'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { ObservedFC, observer } from '~/helpers/msts'
import AddMissingInfo from './AddMissingInfo'
import RecipientsButton from './RecipientsButton'
import RenderAllRecipients from './RenderAllRecipients'
import RenderInvalidRecipients from './RenderInvalidRecipients'

export const Placeholder = styled(Flex)`
  width: 100%;
  justify-content: center;
`

const renderLogMessage = (createdCount: number, requestedCount: number): string => {
  const notAddedCount = requestedCount - createdCount
  let message = ''

  if (createdCount > 0) {
    message = `${createdCount} item(s) were successfully added. `
  }
  if (notAddedCount > 0) {
    message += `${notAddedCount} item(s) were not added as they were already present.`
  }

  return message
}

const EmailRecipientsList: ObservedFC<IEmailRecipientsListProps> = ({
  appStore: { emailing },
  emailBody,
}) => {
  const [skipRemoveRecipientsArr, setSkipRemoveRecipientsArr] = useState<string[]>([])
  const recipients = emailing.email_edit.recipients
  const isLoadedAddRecipients = recipients.isLoadedAddRecipients
  const allRecipients = [...recipients.authorsRecipientsArr, ...recipients.emailRecipientsArr]
  const allInvalidRecipients = recipients.allInvalidRecipients

  const [modalIsOpen, setModalIsOpen] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)

  const renderRemovalAllButton = (recipientsArr: IRecipient[]) => {
    return (
      // @ts-expect-error refactor MntrButton and modal
      <MntrButton
        tooltip={t`Remove All`}
        onClick={noopFn}
        icon="cancel"
        bg="flatError"
        ml={1}
        {...withModalRemoveRecipients({
          onSubmit: () => {
            const idsToRemove = recipientsArr
              .filter((item) => !skipRemoveRecipientsArr.includes(item.id))
              .map((item) => item.id)
            recipients.removeRecipients(idsToRemove)
          },
          submitLabel: `${t`Remove`} ${recipientsArr.length - skipRemoveRecipientsArr.length}`,
          title: t`Remove Recipients`,
          modalIcon: 'delete',
          isStackedModal: false,
          renderRecipientButton: (item, index, bg, recipients) => {
            return (
              <RecipientsButton
                item={item}
                index={index}
                bg={bg}
                recipientsArr={recipients}
                emailBody={emailBody}
                onClick={item.onClick}
              />
            )
          },
          recipients: recipientsArr,
          skipRemoveRecipientsArr,
          setSkipRemoveRecipientsArr,
        })}
      />
    )
  }

  return (
    <Flex flexDirection="column" gap={3} px={3}>
      {recipients.log && (
        <Text fontSize={'14px'} color="blue">
          <StyledIconWrapper>
            <Icon size={16}>update</Icon>
          </StyledIconWrapper>
          {renderLogMessage(recipients.log.created_count, recipients.log.requested_count)}
        </Text>
      )}
      {allInvalidRecipients.length ? (
        <RenderInvalidRecipients
          invalidRecipients={allInvalidRecipients}
          renderRemovalAllButton={renderRemovalAllButton}
          renderRecipientButton={(item, index, bg, recipientsArr, tooltip) => {
            return (
              <RecipientsButton
                item={item}
                index={index}
                bg={bg}
                recipientsArr={recipientsArr}
                emailBody={emailBody}
                tooltip={tooltip}
                onClick={() => {
                  setModalIsOpen(true)
                  setCurrentIndex(index)
                }}
              />
            )
          }}
        />
      ) : null}
      <AddMissingInfo
        recipients={allInvalidRecipients}
        totalCount={allInvalidRecipients.length}
        emailBody={emailBody}
        setCurrentIndex={(index) => setCurrentIndex(index)}
        currentIndex={currentIndex}
        modalIsOpen={modalIsOpen}
        setModalIsOpen={setModalIsOpen}
      />
      <RenderAllRecipients
        allRecipients={allRecipients}
        renderRecipientButton={(item, index, bg, recipientsArr, tooltip) => {
          return (
            <RecipientsButton
              item={item}
              index={index}
              bg={bg}
              recipientsArr={recipientsArr}
              emailBody={emailBody}
              tooltip={tooltip}
            />
          )
        }}
        renderRemovalAllButton={renderRemovalAllButton}
        isLoadedAddRecipients={isLoadedAddRecipients}
      />
    </Flex>
  )
}

export default observer(EmailRecipientsList)
