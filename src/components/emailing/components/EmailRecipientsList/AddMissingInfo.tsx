import { t } from '@lingui/core/macro'
import { useEffect } from 'react'
import { IAddMissingInfoProps } from '~/components/emailing/components/Types/IRecipients'
import { StyledRecipientsBox } from '~/components/emailing/style/StyledRecipientsList'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import FixedField from '~/components/medialist/forms/modules/FixedField'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import ModalPortal from '~/components/misc/Modal/ModalPortal'
import authorTypes from '~/constants/authorTypes'
import { ObservedFC, observer } from '~/helpers/msts'
import { transformCountries, transformDataToSelectItems } from '~/helpers/objectToSelectItems'

const AddMissingInfo: ObservedFC<IAddMissingInfoProps> = ({
  appStore: { account, authors, emailing, loader },
  recipients,
  setCurrentIndex,
  currentIndex,
  emailBody,
  modalIsOpen,
  setModalIsOpen,
}) => {
  const isLoading = loader.isLoading('authors-detail')
  const currentItem = recipients[currentIndex]

  useEffect(() => {
    if (currentItem?.author?.id) {
      authors.loadAuthorIntoStore(currentItem.author.id)
    }
  }, [currentItem, currentIndex])

  const authorDetail = currentItem?.author ? authors.authorDetail : {}
  const authorType = authorDetail.author_type
  const initialEmail =
    authorDetail.emails?.length > 0 ? authorDetail.emails[0].text : currentItem?.email || null

  const isAuthorOrBlogger = authorType
    ? [authorTypes.JOURNALIST, authorTypes.BLOGGER].includes(authorType)
    : true
  const formSchema: IFormSchemaItem[] = [
    {
      name: 'name',
      label: t`Name`,
      initialValue: authorDetail.name,
    },
    {
      name: 'emails[0].text',
      label: t`Email`,
      initialValue: initialEmail,
    },
    {
      customComponent: () => {
        return (
          <>
            {isLoading && (
              <Flex position="absolute" top="30%" left="43%">
                <MntrCircularProgress size={45} />
              </Flex>
            )}
            <Flex mt={1}>
              <Box mr={2} flex={1}>
                <FixedField
                  name="author_type"
                  component={MntrSelectAdapter}
                  label={t`Author type`}
                  initialValue={authorDetail.author_type || authorTypes.JOURNALIST}
                  items={transformDataToSelectItems(
                    account.enums.authors.author_type.filter(
                      (item: { is_medialist_filter: boolean }) => item.is_medialist_filter,
                    ),
                  )}
                />
              </Box>
              <Box flex={1}>
                <FixedField
                  name="country"
                  component={MntrSelectAdapter}
                  label={t`Country`}
                  initialValue={authorDetail.country}
                  items={transformCountries({
                    object: account.enums.mediaCountryIsActive,
                    countryKey: 'code',
                  })}
                />
              </Box>
            </Flex>
            {!isAuthorOrBlogger && currentItem?.missing_merge_tags?.length > 0 && (
              <Flex>
                {/* @ts-expect-error: refactor StyledRecipientsBox to TS */}
                <StyledRecipientsBox bg="warning" noPadding>
                  <MntrHint
                    text={t`Authors with types “agency”, “publisher” or “editorial office” can’t use merge tags  *|LAST_NAME|*,  *|VOKATIV_L|*. If you want to apply these merge tags to the author, change their type to “author” or “blogger” and add the last name.`}
                    color="warning"
                    heading={t`Selected merge tags can not be applied to the author`}
                    icon="error"
                    background="transparent"
                  />
                </StyledRecipientsBox>
              </Flex>
            )}
          </>
        )
      },
    },
    {
      actions: () => [
        ...(recipients.length > 1
          ? [
              {
                bg: 'flat',
                icon: 'arrow_back_ios',
                onClick: () => {
                  const prevIndex = (currentIndex - 1 + recipients.length) % recipients.length
                  setCurrentIndex(prevIndex)
                },
              },
              {
                label: `${currentIndex + 1} / ${recipients.length}`,
                disabled: true,
              },
              {
                bg: 'flat',
                icon: 'arrow_forward_ios',
                onClick: () => {
                  const nextIndex = (currentIndex + 1) % recipients.length
                  setCurrentIndex(nextIndex)
                },
              },
            ]
          : []),
        {
          rounded: true,
          type: 'submit',
          bg: 'secondary',
          icon: 'restore_document',
          label: t`Update recipient`,
        },
      ],
      actionsLeft: () => [
        {
          rounded: true,
          type: 'button',
          bg: 'error',
          icon: 'delete',
          label: t`Delete recipient`,
          onClick: async () => {
            if (currentItem?.remove) {
              currentItem.remove()
            }
          },
        },
      ],
    },
  ]

  return (
    <ModalPortal
      close={() => setModalIsOpen(false)}
      buttonGroup={(close: void) => [{ icon: 'close', onClick: close }]}
      isOpen={modalIsOpen}
      modalWidth={600}
      modalTitle={currentItem?.author?.name || currentItem?.email || t`Add Missing Info`}
      modal={() => {
        return (
          <MntrForm
            key={currentItem?.id}
            contentPadding={3}
            onSubmit={async (model) => {
              const emailChanged = model['emails[0].text'] !== initialEmail
              const updatedModel = {
                ...model,
                id: authorDetail.id,
                edit_token: authorDetail.edit_token || null,
                isCreate: !authorDetail.id,
                ...(emailChanged && { 'emails[0].text': model['emails[0].text'] }),
              }
              try {
                if (authorDetail.id) {
                  await authors.editAuthor()
                }
                const res = await authors.updateAuthor(updatedModel, false)
                await emailing.email_edit.recipients.removeRecipients([currentItem.id])
                await emailing.email_edit.recipients.addRecipient({ author_id: res.id }, emailBody)
                await emailing.email_edit.recipients.load()
                await emailing.email_edit.recipients.validateRecipients(emailBody)
                recipients.length === 1 && setModalIsOpen(false)
              } catch (err) {
                return err
              }
            }}
            schema={formSchema}
          />
        )
      }}
    />
  )
}

export default observer(AddMissingInfo)
