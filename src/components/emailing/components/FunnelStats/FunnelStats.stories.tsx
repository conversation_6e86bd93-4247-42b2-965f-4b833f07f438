import type { <PERSON>a, StoryObj } from '@storybook/react'
import { Box } from '~/components/misc/Mntr'
import FunnelStats from './FunnelStats'

const meta = {
  title: 'FunnelStats',
  component: FunnelStats,
  render({ ...args }) {
    return (
      <Box style={{ maxWidth: '1024px', background: 'white' }} p={3}>
        <FunnelStats {...args} />
      </Box>
    )
  },
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof FunnelStats>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    funnelData: {
      1: {
        count: 32,
        unique_count: 9,
        percent: 100,
      },
      2: {
        count: 15,
        unique_count: null,
        percent: 46.875,
      },
      3: {
        count: 15,
        unique_count: null,
        percent: 46.875,
      },
      4: {
        count: 3,
        unique_count: null,
        percent: 9.375,
      },
      5: {
        count: 2,
        unique_count: null,
        percent: 22.22222222222222,
      },
      6: {
        count: 0,
        unique_count: null,
        percent: 0,
      },
      7: {
        count: 0,
        unique_count: null,
        percent: 0,
      },
      8: {
        count: 2,
        unique_count: null,
        percent: 22.22222222222222,
      },
    },
  },
}
