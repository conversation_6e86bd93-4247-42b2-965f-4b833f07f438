import { Trans } from '@lingui/react/macro'
import { css, styled } from 'styled-components'

import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'

const StatsBlock = styled(Flex)`
  width: 100%;
  height: 100%;
`

const BlockTitle = styled(Text)`
  padding: ${({ theme }) => theme.space[1]}px;
  text-transform: uppercase;
  text-align: center;
  border-bottom: 1px solid ${({ theme }) => theme.paper.border};
`

interface IBlockContentProps {
  isEmpty?: boolean
}

const BlockContent = styled.div<IBlockContentProps>`
  display: grid;
  grid-template-columns: repeat(${({ isEmpty }) => (isEmpty ? 1 : 2)}, minmax(0, 1fr));
  height: 100%;

  @media (min-width: ${({ theme }) => theme.breakpoints[1]}) {
    grid-template-columns: repeat(${({ isEmpty }) => (isEmpty ? 1 : 4)}, minmax(0, 1fr));
  }

  > :not(:first-child) {
    border-left: 1px solid ${({ theme }) => theme.paper.border};
  }
`

interface IStatBoxProps {
  perc?: number
  statColor?: string
}

const StatBox = styled(Flex)<IStatBoxProps>`
  justify-content: space-between;
  height: 100%;

  .stat {
    height: 70px;
  }

  ${({ perc, statColor }) => css`
    .stat {
      background: linear-gradient(180deg, transparent ${100 - (perc || 0)}%, ${statColor} 0%);
      border-bottom: 3px solid ${statColor};
    }
  `}
`
export interface IStatsProps {
  id: number
  text: string
  color: string
  icon: string
  count: number | null
  unique_count: number | null
  percent: number | null
}

interface IStatBlockProps {
  title: string
  stats: IStatsProps[]
}

const StatBlock = ({ title, stats }: IStatBlockProps) => {
  const isEmpty = stats.every((stat) => stat.count === undefined)

  return (
    <StatsBlock column>
      <BlockTitle>{title}</BlockTitle>
      <BlockContent isEmpty={isEmpty}>
        {!isEmpty ? (
          stats.map((stat) => {
            const { id, count, percent, unique_count, color, icon, text } = stat

            return (
              <StatBox column perc={percent || 0} key={id} statColor={color}>
                <Flex column p={2} height={1} justifyContent="space-between">
                  <Flex gap={1} justifyContent="space-between">
                    <Text as="small">{text}</Text>
                    <Box>
                      <Icon size={18} color="grey">
                        {icon}
                      </Icon>
                    </Box>
                  </Flex>
                  <Flex gap={1} alignItems="end">
                    <Heading lineHeight={1.2}>{count}</Heading>
                    <Flex
                      as="small"
                      fontWeight="bold"
                      whiteSpace="nowrap"
                      color="grey"
                      alignItems="center"
                    >
                      {unique_count || percent ? (
                        <>
                          ({unique_count ? <Icon size={14}>group</Icon> : null}
                          {unique_count || `${Number(percent?.toFixed(2))} %`})
                        </>
                      ) : null}
                    </Flex>
                  </Flex>
                </Flex>
                <Box>
                  <Box className="stat" />
                </Box>
              </StatBox>
            )
          })
        ) : (
          <Flex textAlign="center" height={1} minHeight={'140px'} width={1} p={2} center>
            <Text fontSize={3} color="grey">
              <Trans>Here you will see newsroom analytics affected by the campaign</Trans>
            </Text>
          </Flex>
        )}
      </BlockContent>
    </StatsBlock>
  )
}

export default StatBlock
