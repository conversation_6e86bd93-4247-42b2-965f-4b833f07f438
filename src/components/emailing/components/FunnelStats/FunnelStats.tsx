import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'

import { ObservedFC, observer } from '~/helpers/msts'
import { IEmailingFunnelGroupArrItem } from '~/store/models/account/enums/emailing/EmailingFunnelGroupArrItem'
import { IFunnelDataMapItem } from '~/store/models/emailing/shared/FunnelDataMapItem'
import StatBlock, { IStatsProps } from './StatBlock'

const StatsWrapper = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.space[4]}px;
  border: 1px solid ${({ theme }) => theme.paper.border};
  border-radius: 10px;
  overflow: hidden;

  @media (min-width: ${({ theme }) => theme.breakpoints[3]}) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0;
  }

  > :not(:first-child) {
    border-left: 1px solid ${({ theme }) => theme.paper.border};
  }
`

interface IFunnelStatsProps {
  funnelData: Record<number, IFunnelDataMapItem>
}

interface IGroupedData {
  [key: string]: IStatsProps[]
}

function groupDataByCategory(
  funnelData: Record<number, IFunnelDataMapItem>,
  enumItems: IEmailingFunnelGroupArrItem[],
): IGroupedData {
  const categories: IGroupedData = Object.keys(funnelData).reduce<IGroupedData>((acc, id) => {
    const enumItem = enumItems.find((item) => item.id === parseInt(id))

    if (!enumItem) return acc

    const { category, text, text_single, color, icon } = enumItem
    const { count, unique_count, percent } = funnelData[parseInt(id)] || {}

    if (!acc[category]) {
      acc[category] = []
    }

    const displayText = count === 1 ? text_single : text

    acc[category].push({
      id: parseInt(id),
      text: displayText,
      color,
      icon,
      count,
      unique_count,
      percent,
    })

    return acc
  }, {})

  return categories
}

const FunnelStats: ObservedFC<IFunnelStatsProps> = ({
  appStore: {
    account: { enums },
  },
  funnelData,
}) => {
  const groupedData = groupDataByCategory(funnelData, enums.emailing.funnel_group)
  const { emailing = [], newsroom = [] } = groupedData

  return (
    <StatsWrapper>
      <StatBlock title={t`Emailing`} stats={emailing} />
      <StatBlock title={t`Newsroom`} stats={newsroom} />
    </StatsWrapper>
  )
}

export default observer(FunnelStats)
