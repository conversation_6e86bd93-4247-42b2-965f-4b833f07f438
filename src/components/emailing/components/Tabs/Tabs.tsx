import { PropsWithChildren } from 'react'
import { css, styled } from 'styled-components'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

interface ITabsWrapperProps {
  bg?: string
}

export const TabsWrapper = styled.div<ITabsWrapperProps>`
  & .tab button,
  & .tab a {
    height: 34px;
    border-radius: 0;
    padding: 0 24px;
    transition: all 0.1s ease-in-out;
  }

  & .tab--active button,
  & .tab--active a {
    background: transparent;
    color: ${({ theme }) => theme.colors.heading};
  }

  & .tab i {
    color: ${({ theme }) => theme.colors.primary};
  }

  & .tab--inactive button,
  & .tab--inactive a {
    background: transparent;
    color: ${({ theme }) => theme.tabs.textColor};
  }

  & .tab--inactive:hover button,
  & .tab--inactive:hover a {
    background: ${({ theme }) => theme.tabs.hoverBackgroundColor} !important;
    color: ${({ theme }) => theme.colors.heading} !important;
  }

  & .tab--active button:after,
  & .tab--active a:after {
    content: '';
    width: 100%;
    height: 2px;
    background: ${({ theme }) => theme.colors.primary};
    position: absolute;
    bottom: 0px;
    box-shadow: 0 -2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  & .tab--inactive:hover {
    opacity: 1;
  }

  ${({ bg }) => {
    return (
      bg === 'transparent' &&
      css`
        position: relative;
        top: -2px;
      `
    )
  }}
`

interface ITabsProps extends PropsWithChildren {
  bg?: string
}

const Tabs = ({ children, bg }: ITabsProps) => {
  return <TabsWrapper bg={bg}>{children}</TabsWrapper>
}

export interface ITabsItemProps {
  key?: string
  label?: string
  icon?: string
  active?: boolean
  href?: string
  name?: string
  onClick?: () => void
}

Tabs.Item = ({ label, icon, active, href, onClick }: ITabsItemProps) => {
  return (
    <span className={active ? 'tab tab--active' : 'tab tab--inactive'}>
      <MntrButton
        label={label}
        icon={icon}
        iconColor={active ? 'primary' : 'mediumGrey'}
        iconFill={active}
        href={href}
        onClick={onClick}
        bg="transparentTertiary"
        active={active}
      />
    </span>
  )
}

export default Tabs
