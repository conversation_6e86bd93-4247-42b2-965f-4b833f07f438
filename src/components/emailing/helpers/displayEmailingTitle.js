import { t } from '@lingui/core/macro'
/**
 * Generates a display title for emailing purposes from the given item object.
 *
 * @function
 * @param {Object} itemObj - The item object
 * @param {string} [itemObj.name] - Name of the item, if available -> campaign usage
 * @param {string} [itemObj.title] - Title of the item, if available -> email usage
 * @param {string} [itemObj.subject] - Subject of the item, if available -> email usage
 * @param {boolean} [displayEmpty=false] - If true, it will return 'No title' when title is not available
 * @returns {string} Title to render
 *
 * @example
 * displayEmailingTitle({ name: 'Sample Name' }, true) // Returns 'Sample Name'
 * displayEmailingTitle({}, true) // Returns 'No title'
 */
const displayEmailingTitle = (itemObj, displayEmpty) => {
  if (!itemObj) return t`No title`
  const title = itemObj.name || itemObj.title || itemObj.subject

  if (displayEmpty && !title) return t`No title`
  return title
}

export default displayEmailingTitle
