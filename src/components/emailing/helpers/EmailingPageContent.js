import PageContent from '~/components/Content/Content'
import { observer } from '~/helpers/mst'

const EmailingPageContent = ({ children, appStore: { account, emailing } }) => {
  // skip if user doesn't have senders
  if (!emailing.settings.senders.isValid) {
    return null
  }

  // skip if user doesn't have permission
  if (!account.workspace?.permissions.newsroom_emailing.can_read) {
    return null
  }

  return <PageContent>{children}</PageContent>
}

export default observer(EmailingPageContent)
