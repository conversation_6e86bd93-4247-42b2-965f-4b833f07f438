import { t } from '@lingui/core/macro'
import { StyledCharAvatar } from '~/components/emailing/components/EmailingFeed/EmailingFeedItem'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import LoadMore from '~/components/monitoring/FeedList/LoadMore/LoadMore'
import { observer } from '~/helpers/msts'
import { ICampaignStore } from '~/store/models/emailing/campaigns/CampaignStore/CampaignStore'
import { ICampaignStoreArrItem } from '~/store/models/emailing/campaigns/CampaignStore/CampaignStoreArrItem'
import { ICampaignEmailStoreArrItem } from '~/store/models/emailing/campaigns/CampaignStore/emails/CampaignEmailsStore/CampaignEmailStoreArrItem'

const CampaignListSubmenu = observer<{
  closePopup: () => void
  item: ICampaignEmailStoreArrItem
  list: ICampaignStoreArrItem[]
}>(({ appStore: { notification }, closePopup, item, list }) => {
  // @ts-expect-error TODO refactor MntrMenu to tsx
  const menuItems = []

  if (!list.length) {
    return (
      <Flex centerY px={3} py={2}>
        <Text color="mediumGrey">{t`No results found`}.</Text>
      </Flex>
    )
  }

  list.forEach((campaign) => {
    menuItems.push({
      label: (
        <Flex centerY gap={2}>
          <Flex width="32px">
            <StyledCharAvatar center>{campaign.name.charAt(0)}</StyledCharAvatar>
          </Flex>
          <Flex flex={1}>{campaign.name}</Flex>
        </Flex>
      ),
      onClick: () => {
        closePopup()
        item.duplicate(campaign.id).then(() => {
          notification.add(t`Email copied to ${campaign.name}`, 'success')
        })
      },
    })
  })

  // @ts-expect-error TODO refactor MntrMenu to tsx
  return <MntrMenu menuItems={menuItems} />
})

const CampaignAutocompleteList = ({
  campaigns,
  closePopup,
  item,
}: {
  campaigns: ICampaignStore
  closePopup: () => void
  item: ICampaignEmailStoreArrItem
}) => {
  // remove current campaign from list
  const filteredCampaignsList = campaigns.results.filter(
    (campaign: ICampaignStoreArrItem) => campaign.id !== item.campaign,
  )

  return (
    <Flex gap={1} flex={1} maxHeight="400px" column minWidth="300px" maxWidth="375px">
      <Flex overflowY="auto" column>
        {!campaigns.isLoaded && (
          <Flex flex={1} centerX my={4}>
            <MntrCircularProgress />
          </Flex>
        )}
        {campaigns.isLoaded && (
          <Box width={1}>
            <CampaignListSubmenu item={item} list={filteredCampaignsList} closePopup={closePopup} />
          </Box>
        )}

        {campaigns.hasMore && (
          <LoadMore isLoading={campaigns.isLoadingMore} onLoadMore={campaigns.loadMore} />
        )}
      </Flex>
    </Flex>
  )
}

export default observer(CampaignAutocompleteList)
