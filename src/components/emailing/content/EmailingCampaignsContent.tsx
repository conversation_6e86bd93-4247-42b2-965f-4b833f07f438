import { t } from '@lingui/core/macro'
import PageContent from '~/components/Content/Content'
import CampaignFeedList from '~/components/emailing/modules/CampaignFeedList/CampaignFeedList'
import withModalAddCampaign from '~/components/emailing/modules/withModalAddCampaign'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { ObservedFC, observer } from '~/helpers/msts'

const EmailingCampaignsContent: ObservedFC = ({ appStore: { account, emailing } }) => {
  // Breadcrumb navigation
  if (emailing.campaigns.isLoaded)
    pushEvent(events.EMAILING_LOADED, {
      campaigns_count: emailing.campaigns.total_count,
    })

  const sendersWithoutVerification = emailing.settings.senders.activatedSendersWithoutVerification
  return (
    <PageContent>
      {/* Display warning box if some emails are not fully verified */}
      {sendersWithoutVerification.length > 0 &&
        account.workspace?.permissions.newsroom_emailing.can_write && (
          <MntrHint
            background="beigeSecondary"
            color="tertiary"
            heading={t`Your Emailing is not fully set up and verified`}
            text={
              <Flex flexDirection="column" gap={3}>
                <Text>
                  {t`Activated senders without verification:`}{' '}
                  <strong>{sendersWithoutVerification}</strong>
                </Text>
                <Box>
                  <MntrButton
                    icon="settings"
                    bg="tertiary"
                    href="/emailing/settings"
                    label={t`Settings`}
                  />
                </Box>
              </Flex>
            }
          />
        )}
      {/* Actions Bar */}
      <Flex justifyContent="space-between">
        <Heading>{t`Campaigns`}</Heading>
        {/* Deduplicate button on screen */}
        {!emailing.campaigns.isFeedEmpty &&
          account.workspace?.permissions.newsroom_emailing.can_write && (
            // @ts-expect-error TODO refactor MntrButton to tsx
            <MntrButton
              {...withModalAddCampaign({ emailing })}
              bg="secondary"
              modalBg="secondary"
              disabled={!emailing.campaigns.isLoaded}
              label={t`New Campaign`}
            />
          )}
      </Flex>
      {/* Campaigns List */}
      <CampaignFeedList />
    </PageContent>
  )
}

export default observer(EmailingCampaignsContent)
