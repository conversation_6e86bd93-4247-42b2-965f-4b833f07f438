import { t } from '@lingui/core/macro'
import { useEffect, useState } from 'react'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import { clone, observer } from '~/helpers/msts'
import { ICampaignEmailStoreArrItem } from '~/store/models/emailing/campaigns/CampaignStore/emails/CampaignEmailsStore/CampaignEmailStoreArrItem'
import CampaignAutocompleteList from './CampaignAutocompleteList'

const CampaignAutocomplete = observer<{
  closePopup: () => void
  item: ICampaignEmailStoreArrItem
}>(({ appStore: { emailing }, closePopup, item }) => {
  const [campaigns] = useState(clone(emailing.campaigns))

  useEffect(() => {
    campaigns.initAutocomplete()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const formCampaignFilterSchema: IFormSchemaItem[] = [
    {
      name: 'filter',
      autoFocus: true,
      placeholder: t`Filter`,
      autoComplete: 'off',
      icon: 'search',
    },
    {
      customComponent: ({ form }) =>
        form && !form.getState().values.filter ? null : (
          <Box position="absolute" top="4px" right={4}>
            <MntrButton icon="close" onClick={form?.reset} size="small" bg="flatError" />
          </Box>
        ),
    },
  ]

  return (
    <>
      <Box px={3} position="relative">
        <MntrForm
          formGap={0}
          autosubmit
          onSubmit={(spy) => {
            campaigns.setFilterQuery(spy)
          }}
          schema={formCampaignFilterSchema}
          visibleSubmitButton={false}
          subscription={{ values: true, dirty: true }}
        />
      </Box>
      <CampaignAutocompleteList campaigns={campaigns} closePopup={closePopup} item={item} />
    </>
  )
})

export default CampaignAutocomplete
