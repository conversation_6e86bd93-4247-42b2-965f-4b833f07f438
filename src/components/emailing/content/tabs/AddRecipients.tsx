import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useState } from 'react'
import { styled } from 'styled-components'
import Tabs from '~/components/emailing/components/Tabs/Tabs'
import FormEmailRecipients from '~/components/emailing/forms/FormEmailRecipients'
import FormEmailRecipientsBasket from '~/components/emailing/forms/FormEmailRecipientsBasket'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { ObservedFC, observer } from '~/helpers/msts'

interface IAddRecipients {
  emailBody: string
}

interface ITabNavigationItem {
  label: string
  id: string
  icon: string
}

const TextLabel = styled.div`
  font-size: 16px;
  line-height: 1;
  color: ${({ theme }) => theme.colors.mediumGrey};
  padding: 20px 0 10px 15px;
`

const WrapperAddSingle = styled(Flex)`
  flex-direction: column;
  padding: 10px 0;
  margin: 10px 0;
  border-top: 1px solid ${({ theme }) => theme.paper.border};
`

const TabWrapper = styled(Box)`
  border-bottom: 1px solid ${({ theme }) => theme.paper.border};
  border-top: 1px solid ${({ theme }) => theme.paper.border};
`

export const tabNavigationIds = {
  TAB_AUTHORS_LISTS: 'authors-lists',
  TAB_TAGS: 'tags',
}

const AddRecipients: ObservedFC<IAddRecipients> = ({
  appStore: { emailing, authors },
  emailBody,
}) => {
  const [tab, setTab] = useState('authors-lists')

  const generateTabNavigation = (tabsArr: ITabNavigationItem[]) => {
    const tabNavigation = tabsArr.map((tabInfo, index) => {
      return {
        label: tabInfo.label,
        active: Object.values(tabNavigationIds).indexOf(tab) === index,
        icon: tabInfo.icon,
        onClick: () => {
          setTab(tabInfo.id)
        },
      }
    })

    return tabNavigation
  }

  const tabNavigationArray: ITabNavigationItem[] = [
    { label: t`Authors lists`, id: 'authors-lists', icon: 'format_list_bulleted' },
    { label: t`Tags`, id: 'tags', icon: 'label' },
  ]

  const tabNavigation = generateTabNavigation(tabNavigationArray)
  const recipients = emailing.email_edit.recipients

  return (
    <MntrPaper mt={3} py={2}>
      <MntrMenuHeading label={t`Add recipients`} uppercase={false} icon="group_add" fontSize={18} />
      <TextLabel>
        <Trans>Choose authors list or tag:</Trans>
      </TextLabel>

      <TabWrapper bg="transparent">
        <Tabs>
          {tabNavigation.map((item, index) => {
            return <Tabs.Item key={String(index)} {...item} />
          })}
        </Tabs>
      </TabWrapper>

      {tab === tabNavigationIds.TAB_AUTHORS_LISTS && (
        <FormEmailRecipientsBasket
          list={authors.baskets.list}
          getSubItems={(id) => authors.getAuthorsFromBasketOrTag({ author_basket: id }, 'basket')}
          onLoadMore={(hash: string) => authors.loadMoreAuthorsForRecipients(hash)}
          onSubmitItem={(id) => {
            return recipients.addRecipient({ author_id: id }, emailBody)
          }}
          onSubmit={(id) => {
            return recipients.addRecipient({ author_basket_id: id }, emailBody)
          }}
        />
      )}

      {tab === tabNavigationIds.TAB_TAGS && (
        <FormEmailRecipientsBasket
          list={authors.tags.list}
          getSubItems={(id) => authors.getAuthorsFromBasketOrTag({ author_tags: [id] }, 'tag')}
          onLoadMore={(hash) => authors.loadMoreAuthorsForRecipients(hash)}
          icon="label"
          onSubmitItem={(id) => {
            return recipients.addRecipient({ author_id: id }, emailBody)
          }}
          onSubmit={(id) => {
            return recipients.addRecipient({ author_tag_id: id }, emailBody)
          }}
        />
      )}

      <WrapperAddSingle>
        <FormEmailRecipients
          onSubmit={(model: unknown) => {
            return recipients.addRecipient(model, emailBody)
          }}
        />
      </WrapperAddSingle>
    </MntrPaper>
  )
}

export default observer(AddRecipients)
