import { t } from '@lingui/core/macro'
import EmailRecipientsList from '~/components/emailing/components/EmailRecipientsList/EmailRecipientsList'
import EmailingRecipientsLimitWarning from '~/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning'
import { StyledContent } from '~/components/emailing/style/StyledEmailEdit'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { ObservedFC, observer } from '~/helpers/msts'
import AddRecipients from './AddRecipients'

interface IRecipientsTabProps {
  emailBody: string
  activeSenderId: number
}

const RecipientsTab: ObservedFC<IRecipientsTabProps> = ({
  appStore: { emailing },
  emailBody,
  activeSenderId,
}) => {
  return (
    <StyledContent>
      <MntrPaper py={2}>
        <MntrMenuHeading label={t`Recipients`} uppercase={false} icon="group" fontSize={18} />

        <EmailingRecipientsLimitWarning
          activeSenderId={activeSenderId}
          recipientsCount={emailing.email_edit.recipients.validRecipientsCount}
        />

        <EmailRecipientsList emailBody={emailBody} />
      </MntrPaper>

      <AddRecipients emailBody={emailBody} />
    </StyledContent>
  )
}

export default observer(RecipientsTab)
