import { useRouter } from 'next/router'
import RecipientsFeedContent from '~/components/emailing/components/RecipientsFeed/RecipientsFeedContent'
import { observer } from '~/helpers/mst'

const EmailDetailRecipientsContent = ({ appStore: { emailing } }) => {
  const router = useRouter()
  const campaignId = router.query.campaignId
  const recipientsStore = emailing.email_detail.result?.recipients || {}
  const emailId = emailing.email_detail.result?.id

  return (
    <RecipientsFeedContent
      url={`/emailing/campaign/${campaignId}/email/${emailId}/recipients`}
      recipientsStore={recipientsStore}
      allowFilters={['status', 'query']}
    />
  )
}

export default observer(EmailDetailRecipientsContent)
