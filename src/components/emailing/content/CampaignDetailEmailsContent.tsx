import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'

import EmailingFeedItem from '~/components/emailing/components/EmailingFeed/EmailingFeedItem'
import emailStatus from '~/components/emailing/constants/emailStatus.constants'
import NewEmailWizardButton from '~/components/emailing/content/NewEmailWizardButton'
import { Box, Flex, Heading, Skeleton, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import formatDate from '~/helpers/date/format'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { ObservedFC, observer } from '~/helpers/msts'
import { scrollToElement } from '~/helpers/scroll'
import { ICampaignEmailStoreArrItem } from '~/store/models/emailing/campaigns/CampaignStore/emails/CampaignEmailsStore/CampaignEmailStoreArrItem'
import CampaignAutocomplete from './CampaignAutocomplete'

const revealFlashElement = (id: number) => {
  const className = `email-feed-item-${id}`

  // scroll to element
  setTimeout(() => {
    scrollToElement(`.${className}`, '', 0, 250)
  }, 50)

  // flash animation
  setTimeout(() => {
    const email = document.getElementsByClassName(className)[0]
    email.classList.add('flash')
    setTimeout(() => {
      email.classList.remove('flash')
    }, 250)
  }, 250)
}

const CampaignDetailEmailsContent: ObservedFC = ({
  appStore: { account, emailing, notification },
}) => {
  const router = useRouter()
  // campaign -> emails tab -> emails list
  const emailsStore = emailing.campaign_detail.result?.emails || {}
  const emailList = emailsStore.results || []
  const { campaignId } = router.query

  return (
    <>
      {!emailsStore.isLoaded && <Skeleton height={'78px'} repeat={5} fullWidth />}
      {/* Emails feed */}
      {emailsStore.isVisibleEmailsFeed && emailsStore.isLoaded && (
        <Flex flexDirection="column" gap={2}>
          {emailList.map((item: ICampaignEmailStoreArrItem, index: string) => {
            const scheduledDateFormatted = `${formatDate(item.scheduled_at, 'd. M. yyyy HH:mm')}`
            const menuItems = item.sent_at
              ? [
                  {
                    label: t`Open`,
                    href: `/emailing/campaign/${campaignId}/email/${item.id}`,
                    leftIcon: 'open_in_full',
                  },
                ]
              : []

            if (account.workspace?.permissions.newsroom_emailing.can_write) {
              menuItems.push(
                // @ts-expect-error: refactor MntrMenu to TS
                !item.sent_at && {
                  label: t`Edit`,
                  href: `/emailing/campaign/${campaignId}/email/edit/${item.id}`,
                  leftIcon: 'edit',
                },
                {
                  leftIcon: 'content_copy',
                  label: t`Duplicate`,
                  onClick: () => {
                    item.duplicate(campaignId).then((res) => {
                      notification.add(t`Email successfully duplicated`, 'success')
                      emailing.campaign_detail.result.emails.addEmail(res)
                      revealFlashElement(res.id)
                    })
                  },
                },
                {
                  leftIcon: 'fork_left',
                  label: t`Copy to another campaign`,
                  subMenuItems: [
                    {
                      label: t`Select campaign`,
                    },
                    {
                      customComponent: (closePopup: () => void) => {
                        return <CampaignAutocomplete closePopup={closePopup} item={item} />
                      },
                    },
                  ],
                },
                {},
                !item.sent_at && {
                  label: t`Delete`,
                  ...withModalRemove({
                    message: item.scheduled_at
                      ? t`Scheduled to send at ${scheduledDateFormatted}, are you sure you want to delete this email?`
                      : t`Are you sure you want to delete this email?`,
                    onSubmit: item.remove,
                  }),
                  hoverVariant: 'error',
                  leftIcon: 'delete',
                },
              )
            }

            let href = `/emailing/campaign/${campaignId}/email/${item.id}`
            let as = `/emailing/campaign/${campaignId}/email/${item.id}`

            // redirect to email edit if is draft
            if (!item.sent_at && item.status.id !== emailStatus.SENDING) {
              href = `/emailing/campaign/${campaignId}/email/edit/${item.id}`
              as = `/emailing/campaign/${campaignId}/email/edit/${item.id}`
            }

            return (
              <EmailingFeedItem item={item} key={index} href={href} as={as} menuItems={menuItems} />
            )
          })}
        </Flex>
      )}
      {/* No emails */}
      {emailsStore.isFeedEmpty && !emailsStore.filterStatus && (
        <Box my={4} textAlign="center">
          <Heading color="heading" fontSize={4} mb={2}>{t`No emails yet`}</Heading>
          {account.workspace?.permissions.newsroom_emailing.can_write && (
            <>
              <Text
                mb={6}
                color="mediumGrey"
              >{t`You can create your first email by clicking the button below.`}</Text>

              {account.workspace?.permissions.newsroom_emailing.can_write && (
                <NewEmailWizardButton />
              )}
            </>
          )}
        </Box>
      )}
      {/* No emails with applied filter */}
      {emailsStore.isFeedEmpty && emailsStore.filterStatus && (
        <Box textAlign="center" my={4}>
          <Heading color="heading" fontSize={4} mb={2}>{t`No emails found`}</Heading>
          <Text
            mb={6}
            color="mediumGrey"
          >{t`You can reset your filter by clicking the button below.`}</Text>

          <MntrButton
            ml={2}
            label={t`Reset filter`}
            bg="secondary"
            href={`/emailing/campaign/${campaignId}`}
          />
        </Box>
      )}
    </>
  )
}

export default observer(CampaignDetailEmailsContent)
