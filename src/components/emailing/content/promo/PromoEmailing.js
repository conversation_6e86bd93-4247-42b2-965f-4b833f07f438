import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import PromoBox from '~/components/misc/PromoBox/PromoBox'
import { observer } from '~/helpers/mst'

const FEATURE_REQUEST_KEY = 'EMAILING_TRIAL'

const PromoEmailing = ({
  appStore: {
    appSettings: { sitePath },
  },
}) => {
  return (
    <PromoBox
      featureRequestKey={FEATURE_REQUEST_KEY}
      imagePath={`/static/${sitePath}/promo/emailing.png`}
      heading={<Trans>Emailing</Trans>}
      description={t`Platform for email communication with journalists.`}
      cards={[
        {
          icon: 'palette',
          title: t`Custom branding`,
          text: t`Simple setting of the appearance of the email template.`,
        },
        {
          icon: 'group',
          title: t`Mass mailing`,
          text: t`Send press releases to journalists with one click.`,
        },
        {
          icon: 'monitoring',
          title: t`Statistics`,
          text: t`Track delivery and opening statistics.`,
        },
      ]}
    />
  )
}

export default observer(PromoEmailing)
