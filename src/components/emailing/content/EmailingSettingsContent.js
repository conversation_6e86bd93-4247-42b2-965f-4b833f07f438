import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

import PageContent from '~/components/Content/Content'
import EmailingSendersList from '~/components/emailing/components/EmailingSettingsList/EmailingSendersList'
import FormEmailingSettingsSender from '~/components/emailing/forms/FormEmailingSettingsSender'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import { observer } from '~/helpers/mst'

const EmailingSettingsContent = ({ appStore: { emailing, notification } }) => {
  const [infoFromQueryString, setInfoFromQueryString] = useState({ text: '', type: '' })
  const sendersWithoutVerification = emailing.settings.senders.activatedSendersWithoutVerification

  const router = useRouter()
  const errorDefault = t`An error occurred while authorizing our application to use the external service.`

  useEffect(() => {
    if (router.query.oauth2_link_success === 'true') {
      setInfoFromQueryString({
        text: t`You have successfully authorized our application to use the external service.`,
        type: 'success',
      })
    } else if (router.query.oauth2_link_success === 'false') {
      if (router.query.oauth2_link_errorcode) {
        const errorMessages = {
          provider_scope_changed: t`We haven't been granted access to send emails on your behalf. Please try again and make sure to grant us access.`,
          provider_token_error: t`Unable to retrieve access token from the OAuth2 provider. This may be due to a network issue or provider outage. Please try again later.`,
          provider_userinfo_error: t`Failed to fetch your email address from the service provider. Please try again or contact support if the issue persists.`,
          nonce_mismatch: t`Linking with Google account timed out. Please, try again.`,
          sender_email_conflict: t`An email sender record with this address already exists. Please check your existing records or try again.`,
        }

        setInfoFromQueryString({
          text: errorMessages[router.query.oauth2_link_errorcode] || errorDefault,
          type: 'error',
        })
      } else {
        setInfoFromQueryString({ text: errorDefault, type: 'error' })
      }
    }
  }, [errorDefault, router.query.oauth2_link_success, router.query.oauth2_link_errorcode])

  const handleClose = () => {
    const currentPath = router.pathname
    router.replace(currentPath)
    setInfoFromQueryString({ text: '', type: '' })
  }

  return (
    <PageContent>
      <Box flex={1}>
        <Heading>{t`Settings`}</Heading>
      </Box>

      <Flex flexDirection="column" gap={3} maxWidth="650px" width={1} m="0 auto">
        {infoFromQueryString.text ? (
          <MntrHint
            background={infoFromQueryString.type === 'success' ? 'secondary' : 'error'}
            color="#ffffff"
            text={<Text color="#ffffff">{infoFromQueryString.text}</Text>}
            onClose={handleClose}
          />
        ) : null}
        {/* Display warning box if some emails are not fully verified */}
        {sendersWithoutVerification.length > 0 && (
          <MntrHint
            background="beigeSecondary"
            color="error"
            heading={t`Your Emailing is not fully set up and verified`}
            text={
              <Flex flexDirection="column" gap={3}>
                <Box>{t`Your Emailing is not fully set up and verified. This can decrease the trust level and deliverability. You can fully set up and verify your Emailing in the settings. If you need help, please contact our support.`}</Box>
                <Text>
                  {t`Activated senders without verification:`}{' '}
                  <strong>{sendersWithoutVerification}</strong>
                </Text>
              </Flex>
            }
          />
        )}

        {emailing.settings.senders.isLoaded && (
          <EmailingSendersList senders={emailing.settings.senders} />
        )}

        <FormEmailingSettingsSender
          onSubmit={(values) => {
            return emailing.settings.senders.add(values).then(() => {
              // send notification, that sender was added and verification email was sent
              notification.add(
                t`Verification email was sent to ${values.email}. Please check your inbox.`,
                'success',
              )
            })
          }}
        />
      </Flex>
    </PageContent>
  )
}

export default observer(EmailingSettingsContent)
