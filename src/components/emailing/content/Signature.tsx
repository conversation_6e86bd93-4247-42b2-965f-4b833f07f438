import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import noopFn from 'lodash/noop'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Heading } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalEditSignature from '~/components/misc/MntrEditor/modals/withModalEditSignature'
import HtmlCodeIframeView from '~/components/misc/MntrEditor/modules/HtmlCodeIframeView'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import SignaturePopup from './SignaturePopup'

interface Email {
  //TODO: temporary interface until we convert EmailingStore
  signature_name?: string
  signature_position?: string
  signature_email?: string
  signature_phone?: string
  footer?: string
}

interface ISignatureProps {
  email: Email
  disabled: boolean
  onSubmit: (model: object) => void
  onDelete: () => void
}

const Signature = ({ email, disabled, onSubmit, onDelete }: ISignatureProps) => {
  const menuItems = []
  const { signature_name, signature_position, signature_email, signature_phone, footer } = email

  const hasSignature = () => {
    return signature_name || signature_position || signature_phone || signature_email || footer
  }

  const initialValues = {
    signature_name,
    signature_position,
    signature_email,
    signature_phone,
    footer,
  }

  if (hasSignature() && footer) {
    menuItems.push({
      customComponent: () => {
        return (
          <Flex>
            <HtmlCodeIframeView value={footer} />
            <MntrButton
              bg="flat"
              icon="more_vert"
              popup={(closePopup) => {
                return (
                  <SignaturePopup
                    closePopup={closePopup}
                    onSubmit={onSubmit}
                    onDelete={onDelete}
                    initialValues={initialValues}
                  />
                )
              }}
            />
          </Flex>
        )
      },
    })
  }

  if (hasSignature() && signature_name && !footer) {
    menuItems.push({
      label: signature_name,
      secondaryText: signature_position,
      onClick: noopFn,
      buttonGroup: [
        {
          icon: 'more_vert',
          bg: 'flat',
          popup: (closePopup: () => void) => {
            return (
              <SignaturePopup
                closePopup={closePopup}
                onSubmit={onSubmit}
                onDelete={onDelete}
                initialValues={initialValues}
              />
            )
          },
        },
      ],
    })
  }

  const canAdd = menuItems.length === 0

  return (
    <Box id="signature" width={[1]}>
      <Flex justifyContent="space-between" alignContent="center" width={[1]}>
        <Flex gap={1} center>
          <Icon>stylus_note</Icon>
          <Heading fontSize={2}>
            <Trans>Signature</Trans>
          </Heading>
        </Flex>
        {canAdd && (
          // @ts-expect-error: MntrButton refactor
          <MntrButton
            rounded
            bg="flat"
            icon="add"
            label={t`Add signature`}
            {...withModalEditSignature({
              onSubmit,
              title: t`Add signature`,
            })}
            disabled={disabled}
          />
        )}
      </Flex>
      <>
        {menuItems?.length > 0 && (
          <MntrPaper mt={2}>
            {/* @ts-expect-error: nevim */}
            <MntrMenu menuItems={menuItems} />
          </MntrPaper>
        )}
      </>
    </Box>
  )
}

export default observer(Signature)
