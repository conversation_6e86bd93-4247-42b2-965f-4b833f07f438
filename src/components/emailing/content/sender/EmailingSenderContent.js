import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import FormEmailingSettingsSender from '~/components/emailing/forms/FormEmailingSettingsSender'
import { StyledContent, StyledWrapper } from '~/components/emailing/style/StyledSenderVerify'
import { Box, Heading, Text } from '~/components/misc/Mntr'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'

const EmailingSenderContent = ({ appStore: { emailing }, disabled }) => {
  return (
    <StyledWrapper>
      <StyledContent>
        {/* Text info about emailing */}
        <Heading as="h1" fontSize={5} fontWeight="bold" color="heading" textAlign="center" mb={3}>
          <Trans>Initial Emailing settings</Trans>
        </Heading>
        <Text color="black" textAlign="center" mb={5}>
          <Trans>
            You don't have Emailing set up yet. It only takes a few minutes to set it up.
          </Trans>
        </Text>

        {!emailing.settings.senders.isLoaded && (
          <Text textAlign="center">
            <MntrCircularProgress />
          </Text>
        )}

        {/* Form for adding new sender */}
        {emailing.settings.senders.isLoaded && (
          <FormEmailingSettingsSender
            description={t`Please add a sender address that will be used for sending emails.`}
            disabled={disabled}
            onSubmit={(values) => {
              return emailing.settings.senders.add(values).then(() => {
                routerPush('/emailing')
              })
            }}
          />
        )}

        <Box mt={5}>
          <MntrHint
            color="primary"
            icon="tips_and_updates"
            heading={t`What is Emailing used for?`}
            text={t`While our Emailing tool is designed to send press and PR messages to journalists, its functionality goes beyond that. You can use it for various types of communication, opening up possibilities beyond traditional media outreach.`}
          />
        </Box>
      </StyledContent>
    </StyledWrapper>
  )
}

export default observer(EmailingSenderContent)
