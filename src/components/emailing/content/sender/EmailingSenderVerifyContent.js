import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { Paper } from '~/components/emailing/style/StyledEmailing'
import { StyledContent, StyledWrapper } from '~/components/emailing/style/StyledSenderVerify'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { Box, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'

const EmailingSenderVerifyContent = ({ appStore: { emailing } }) => {
  return (
    <StyledWrapper>
      {emailing.settings.senders.results.map((item) => {
        return (
          <StyledContent key={item.id}>
            <Paper>
              <MntrPaper>
                <MntrPaperToolbar title={t`Settings complete`} bg="secondary" icon="verified" />
                <Box p={3}>
                  <Text color="black" mb={5}>
                    <Trans>Everything went well.</Trans>
                  </Text>
                  <MntrHint text={item.email} heading={item.name} icon="email" />

                  <Text color="black" my={3}>
                    <Trans>
                      We have sent you an activation link. To activate Emailing and to confirm your
                      email address please open the link.
                    </Trans>
                  </Text>
                </Box>

                <Box bg="highlight" py={3}>
                  <Text textAlign="center" color="black">
                    <Trans>Use another email address</Trans>
                  </Text>
                  <Text color="black" textAlign="center" mt={2}>
                    <MntrButton
                      onClick={() => {
                        item.remove().then(() => {
                          routerPush('/emailing')
                        })
                      }}
                      label={t`Change email`}
                      bg="tertiary"
                    />
                  </Text>
                </Box>
              </MntrPaper>
            </Paper>
          </StyledContent>
        )
      })}
    </StyledWrapper>
  )
}

export default observer(EmailingSenderVerifyContent)
