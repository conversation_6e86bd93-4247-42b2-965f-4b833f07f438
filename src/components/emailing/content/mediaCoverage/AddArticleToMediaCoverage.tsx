import { t } from '@lingui/core/macro'
import FormAddArticle from '~/components/ReusableFeed/FormAddArticle'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/msts'

const AddArticleToMediaCoverage = () => {
  return (
    <MntrButton
      ml={2}
      label={t`Add article`}
      bg="secondary"
      modalBg={'default'}
      modalTitle={t`Add article media coverage`}
      // @ts-expect-error TODO refactor MntrButton
      modal={(closeModal: () => void) => {
        return <FormAddArticle closeModal={closeModal} />
      }}
      rounded
      icon="add"
    />
  )
}

export default observer(AddArticleToMediaCoverage)
