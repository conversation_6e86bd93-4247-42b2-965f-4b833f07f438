import { t } from '@lingui/core/macro'
import arrayMutators from 'final-form-arrays'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { Field, Form } from 'react-final-form'
import { styled } from 'styled-components'

import forEach from 'lodash/forEach'
import identityFn from 'lodash/identity'
import FormSendEmail from '~/components/emailing/components/EmailToolbarActions/FormSendEmail'
import emailingLayout from '~/components/emailing/constants/emailingLayout.constants'
import emailStatus from '~/components/emailing/constants/emailStatus.constants'
import PreviewEmail from '~/components/emailing/modules/PreviewEmail/PreviewEmail'
import {
  StyledAttachmentsList,
  StyledContent,
  StyledContentFullHeight,
  StyledTextEditorWrapper,
  StyledToolbarWrapper,
} from '~/components/emailing/style/StyledEmailEdit'
import { StyledPaperToolbar } from '~/components/feed/InspectorToolbar/InspectorToolbar'
import MntrCmsEditorAdapter from '~/components/forms/adapters/MntrCmsEditorAdapter/MntrCmsEditorAdapter'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { ACTIONS } from '~/components/misc/MntrEditor/MntrEditorFloatingBar'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrStepper from '~/components/misc/MntrStepper/MntrStepper'
import AttachmentsList from '~/components/newsroom/content/post/AttachmentsList'
import withModalCustomAiRewrite from '~/components/newsroom/modals/withModalCustomAiRewrite'
import config from '~/config'
import events from '~/constants/gtm'
import formatDate from '~/helpers/date/format'
import { pushEvent } from '~/helpers/gtm'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'
import Signature from './Signature'
import RecipientsTab from './tabs/RecipientsTab'

const CustomQueryVisibleLg = styled(Flex)`
  max-width: 768px;

  @media (max-width: ${emailingLayout.TOOLBAR_BREAK_MEDIA_QUERY}px) {
    display: none;
  }
`

const CustomQueryVisibleSm = styled(Box)`
  @media (min-width: ${emailingLayout.TOOLBAR_BREAK_MEDIA_QUERY + 1}px) {
    display: none;
  }
`

const LockedEmailMessage = ({ email }) => {
  if (!email.is_locked || email.sent_at) {
    return null
  }

  const scheduledDate = `${formatDate(email.scheduled_at, 'd. M. yyyy HH:mm')}`

  return (
    <Box mb={5}>
      <MntrHint
        icon="lock"
        color="tertiary"
        heading={
          email.status.id === emailStatus.SENDING
            ? t`Email is sending`
            : email.status.id === emailStatus.SCHEDULED
              ? t`Email will be sent at: ${scheduledDate}`
              : ''
        }
        text={t`Email is locked and cannot be edited. If you want to edit the email, return it to the draft state.`}
      />
    </Box>
  )
}

const CreateEmailContent = ({ appStore: { account, emailing, newsroom, notification } }) => {
  const router = useRouter()
  const [emailBody, setEmailBody] = useState(emailing.email_edit?.result?.body)
  const [step, setStep] = useState(1) // for add recipient validation
  const { campaignId } = router.query
  const editorRef = useRef(null)

  const aiTools = newsroom.aiTools
  const email_edit = emailing.email_edit.toJSON()
  const selectedEmail = emailing.email_edit.result

  const defaultSender = selectedEmail?.sender
    ? selectedEmail.sender
    : emailing.settings.senders.primarySender.id

  const defaultSenderName = selectedEmail?.sender_name
    ? selectedEmail.sender_name
    : emailing.settings.senders.primarySender.name

  const [activeSenderId, setActiveSenderId] = useState(defaultSender)

  useEffect(() => {
    // redirect to email detail if the email is already sent
    if (email_edit && email_edit.result?.status === emailStatus.SENT) {
      const campaignId = email_edit.campaignId
      const emailId = email_edit.emailId
      routerPush(`/emailing/campaign/${campaignId}/email/${emailId}`)
    }
  }, [email_edit, selectedEmail])

  useEffect(() => {
    if (defaultSender) {
      setActiveSenderId(defaultSender)
    }
  }, [defaultSender])

  // handle submit
  const onSubmit = (values) => {
    // if is locked, set as draft
    if (values.is_locked) {
      return selectedEmail.setAsDraft().then(() => {
        notification.add(t`Email was set to draft`, 'success')
        pushEvent(events.EMAILING_EMAIL_DRAFT)
      })
    }

    // if is not locked, update email
    return selectedEmail.update(values).then(() => {
      // save only
      if (!values.send) {
        pushEvent(events.EMAILING_EMAIL_EDITED)
        if (values.notification) notification.add(t`Email was saved`, 'success')

        return null
      }

      // save and send
      if (values.send) {
        return selectedEmail
          .send(values)
          .then((res) => {
            if (res.sending_error) {
              notification.add(res.sending_error, 'error')
              return res
            }
            if (res.scheduled_at) {
              pushEvent(events.EMAILING_EMAIL_SCHEDULED)
              notification.add(t`Scheduled to send email`, 'success')
            } else {
              pushEvent(events.EMAILING_EMAIL_SENT)
              notification.add(t`Email was sent`, 'success')
            }

            return routerPush(`/emailing/campaign/${campaignId}`)
          })
          .catch((err) => {
            // if is scheduled_at, throw error for popup
            if (err.scheduled_at) {
              throw err
            }

            // if is err array, show errors[0]
            if (Array.isArray(err.errors)) {
              notification.add(err.errors[0], 'error')
              throw err
            }

            // switch tab
            setStep(Object.keys(err).length === 1 && err.recipients ? 2 : 1)
            return err
          })
      }
    })
  }

  // find active sender
  const senderItem = emailing.settings.senders.senderById(activeSenderId)

  if (!senderItem) {
    return null
  }

  // get email backend item
  const emailBackendItem = account.enums.emailing.email_backend.find(
    (item) => parseInt(item.id) === parseInt(senderItem.email_backend.id),
  )

  if (!emailBackendItem) {
    return null
  }

  const disableSubmit =
    emailing.email_edit.recipients.validRecipientsCount > senderItem.recipientsLimit

  const floatingMenuActions = [ACTIONS.ADD_IMAGE, ACTIONS.ADD_CTA, ACTIONS.ADD_HTML]

  if (account.workspace?.permissions.newsroom_blog.can_write) {
    floatingMenuActions.push(ACTIONS.ADD_ARTICLE)
  }

  const defaultSignatureItem = emailing.settings.senders.senderById(defaultSender)
  const signature = {
    footer: defaultSignatureItem.default_footer,
    signature_name: defaultSignatureItem.default_signature_name,
    signature_position: defaultSignatureItem.default_signature_position,
    signature_phone: defaultSignatureItem.default_signature_phone,
    signature_email: defaultSignatureItem.default_signature_email,
  }

  return (
    <div>
      {emailing.email_edit.isLoaded && (
        <Form
          onSubmit={onSubmit}
          mutators={{
            ...arrayMutators,
          }}
          initialValues={{
            ...selectedEmail,
            ...(!selectedEmail?.sender && signature),
            sender: defaultSender,
            sender_name: defaultSenderName,
            notification: true,
          }}
          render={({ handleSubmit, form, values, submitting }) => {
            const language = account.enums.app_language.find((lang) => lang.id === values.language)
            const validateRecipients = async () => {
              await emailing.email_edit.recipients.validateRecipients(values.body)
            }

            const updateSignatureAndSender = (model) => {
              const updatedSender = {
                ...emailing.settings.senders.senderById(values.sender),
                ...{
                  default_footer: model.footer,
                  default_signature_name: model.signature_name,
                  default_signature_position: model.signature_position,
                  default_signature_phone: model.signature_phone,
                  default_signature_email: model.signature_email,
                },
              }
              const updatedSenders = emailing.settings.senders.results.map((obj) =>
                obj.id === updatedSender.id ? { ...updatedSender } : obj,
              )

              emailing.settings.senders.updateResults(updatedSenders)

              forEach(model, (val, key) => {
                form.change(key, val)
              })

              selectedEmail.update({
                ...values,
                ...model,
              })
            }

            const steps = [
              {
                label: t`Email`,
                onClick: () => {
                  setStep(1)
                  !values.is_locked && emailing.email_edit.result.update(values)
                },
              },
              !values.is_locked && {
                label: t`Recipients`,
                onClick: () => {
                  !values.is_locked && emailing.email_edit.result.update(values)
                  setStep(2)
                  validateRecipients()
                },
              },

              {
                label: t`Preview`,
                onClick: async () => {
                  !values.is_locked && emailing.email_edit.result.update(values)
                  await validateRecipients()
                  setStep(values.is_locked ? 2 : 3)
                },
              },
            ].filter(Boolean)

            return (
              <>
                <form onSubmit={handleSubmit}>
                  <StyledToolbarWrapper>
                    <StyledPaperToolbar>
                      <Flex justifyContent="space-between">
                        <Box width="250px">
                          <MntrButton
                            icon="chevron_left"
                            label={t`Back`}
                            ml={1}
                            mt={0}
                            {...(step < 2
                              ? { href: `/emailing/campaign/${campaignId}` }
                              : {
                                  onClick: () => {
                                    setStep(step - 1)
                                    !values.is_locked && emailing.email_edit.result.update(values)
                                  },
                                })}
                            bg="flat"
                            rounded
                          />
                        </Box>
                        <CustomQueryVisibleLg flex={1} justifyContent="center">
                          {account.workspace?.permissions.newsroom_emailing.can_write && (
                            <MntrStepper active={step} steps={steps} />
                          )}
                        </CustomQueryVisibleLg>

                        {/* Save and Send buttons */}
                        {!values.is_locked &&
                          account.workspace?.permissions.newsroom_emailing.can_write && (
                            <Flex justifyContent="flex-end" alignItems="center" width="250px">
                              <MntrButton label={t`Save`} type="submit" mr={1} rounded />
                              <Flex>
                                {step === (values.is_locked ? 2 : 3) ? (
                                  <MntrButton
                                    label={t`Send`}
                                    icon="send"
                                    mr={1}
                                    rounded
                                    disabled={disableSubmit || submitting}
                                    bg="secondary"
                                    modalBg="default"
                                    modal={(closeModal) => {
                                      return (
                                        <FormSendEmail
                                          closeModal={closeModal}
                                          initialValues={values}
                                          onSubmit={(values) => {
                                            form.change(
                                              'scheduled_at',
                                              values.scheduled_at ? values.scheduled_at : null,
                                            )
                                            form.change('sender', values.sender)
                                            form.change('subject', values.subject)
                                            form.change('send', true)
                                            return handleSubmit()
                                          }}
                                          senders={emailing.settings.senders?.results}
                                        />
                                      )
                                    }}
                                  />
                                ) : (
                                  <MntrButton
                                    label={t`Continue`}
                                    rounded
                                    bg="secondary"
                                    endIconElement={<Icon>arrow_forward_ios</Icon>}
                                    onClick={() => {
                                      setStep(step + 1)
                                      !values.is_locked && emailing.email_edit.result.update(values)
                                    }}
                                  />
                                )}
                              </Flex>
                            </Flex>
                          )}
                        {/* Revert to draft */}
                        {values.is_locked &&
                          account.workspace?.permissions.newsroom_emailing.can_write && (
                            <Flex justifyContent="flex-end" alignItems="center" width="250px">
                              <MntrButton
                                label={t`Set as Draft`}
                                disabled={submitting}
                                type="submit"
                                mr={1}
                                rounded
                                bg="tertiary"
                              />
                            </Flex>
                          )}
                      </Flex>
                    </StyledPaperToolbar>

                    <CustomQueryVisibleSm>
                      <StyledPaperToolbar>
                        <Flex justifyContent="space-between">
                          <Flex flex={1} justifyContent="center">
                            <MntrStepper active={step} steps={steps} />
                          </Flex>
                        </Flex>
                      </StyledPaperToolbar>
                    </CustomQueryVisibleSm>
                  </StyledToolbarWrapper>

                  {/* Step 1 - Email + Attachments*/}
                  {step === 1 && (
                    <StyledContent>
                      <Flex my={3} flexDirection="column" gap={3}>
                        <MntrPaper>
                          <Box px={38} py={3}>
                            <LockedEmailMessage email={selectedEmail} />
                            <Flex gap={3} flexDirection="column">
                              <Box>
                                <Field
                                  label={t`Internal name of email`}
                                  name="title"
                                  component={MntrTextFieldAdapter}
                                  placeholder={`${t`Insert internal name of email`}...`}
                                  parse={identityFn}
                                  disabled={
                                    values.is_locked ||
                                    !account.workspace?.permissions.newsroom_emailing.can_write
                                  }
                                />
                              </Box>
                              <Box>
                                <Field
                                  label={t`Sender`}
                                  component={MntrSelectAdapter}
                                  items={emailing.settings.senders.selectorData}
                                  name="sender"
                                  onChange={(value) => {
                                    const senderObj = emailing.settings.senders.senderById(value)
                                    setActiveSenderId(senderObj.id)
                                    form.change('sender', senderObj.id)
                                    form.change('sender_name', senderObj.name)

                                    forEach(
                                      {
                                        footer: senderObj.default_footer,
                                        signature_name: senderObj.default_signature_name,
                                        signature_position: senderObj.default_signature_position,
                                        signature_phone: senderObj.default_signature_phone,
                                        signature_email: senderObj.default_signature_email,
                                      },
                                      (val, key) => {
                                        form.change(key, val)
                                      },
                                    )
                                  }}
                                  disabled={values.is_locked}
                                />
                              </Box>
                              <Flex gap={3} center>
                                <Field
                                  label={t`Subject`}
                                  name="subject"
                                  component={MntrTextFieldAdapter}
                                  placeholder={`${t`Insert subject`}...`}
                                  parse={identityFn}
                                  disabled={
                                    values.is_locked ||
                                    !account.workspace?.permissions.newsroom_emailing.can_write
                                  }
                                />
                                <Box mt={3}>
                                  <MntrButton
                                    isChip
                                    label={language.text}
                                    flagLanguage={values.language}
                                    popupPlacement={'bottom-start'}
                                    transformOrigin="0 0"
                                    popup={(closePopup) => {
                                      const menuItems = Object.entries(config.locales)
                                        .filter((item) => item[1].newsroom)
                                        .map(([key, val]) => {
                                          return {
                                            label: val.label,
                                            flag: key,
                                            rightIcon: values.language === key ? 'check' : null,
                                            onClick: () => {
                                              form.change('language', key)
                                              closePopup()
                                            },
                                          }
                                        })

                                      return (
                                        <MntrMenu menuItems={menuItems} closePopup={closePopup} />
                                      )
                                    }}
                                    disabled={
                                      values.is_locked ||
                                      !account.workspace?.permissions.newsroom_emailing.can_write
                                    }
                                  />
                                </Box>
                              </Flex>
                            </Flex>
                          </Box>
                        </MntrPaper>
                        <MntrPaper id="email-paper">
                          <Box px={38} py={3} center>
                            <StyledTextEditorWrapper>
                              <Field
                                ref={editorRef}
                                name="body"
                                transparentBg
                                parse={identityFn}
                                component={MntrCmsEditorAdapter}
                                isVisibleEmailBar
                                isVisibleBubbleMenu={false}
                                isVisibleAiRewriteMenu
                                setSelection={aiTools.modifyContent.setSelection}
                                customAiRewriteMenuItems={() => {
                                  const menuItems = []
                                  const getErrorMessage = (e) => {
                                    return Object.keys(e)
                                      .map((key) => `${key}: ${e[key].join(', ')}`)
                                      .join('\n')
                                  }

                                  // List of available AI rewrite operations
                                  account.enums.publishing.llm_operation_type.forEach((item) => {
                                    menuItems.push({
                                      label: item.text,
                                      leftIcon: 'chevron_right',
                                      onClick: () => {
                                        // get selected text
                                        return aiTools.modifyContent
                                          .aiGenericRewrite(values, item.text)
                                          .then((res) => {
                                            const editor = editorRef.current.getEditor()

                                            const selectedFrom = aiTools.modifyContent.selectionFrom
                                            const selectedTo = aiTools.modifyContent.selectionTo

                                            editor
                                              .chain()
                                              .focus()
                                              .deleteRange({ from: selectedFrom, to: selectedTo }) // Delete the selected range
                                              .insertContentAt(selectedFrom, res.selection) // Insert the new content at the start of the selection
                                              .setTextSelection({
                                                from: selectedFrom,
                                                to: selectedFrom + res.selection.length,
                                              })
                                              .run()
                                          })
                                          .catch((e) => {
                                            notification.add(getErrorMessage(e), 'error')
                                          })
                                      },
                                    })
                                  })

                                  // Custom AI rewrite operations
                                  menuItems.push({
                                    label: t`Custom`,
                                    leftIcon: 'chevron_right',
                                    ...withModalCustomAiRewrite({
                                      onSubmit: (model) => {
                                        return aiTools.modifyContent
                                          .aiGenericRewrite(values, model.instruction)
                                          .then((res) => {
                                            const editor = editorRef.current.getEditor()

                                            const selectedFrom = aiTools.modifyContent.selectionFrom
                                            const selectedTo = aiTools.modifyContent.selectionTo

                                            editor
                                              .chain()
                                              .focus()
                                              .deleteRange({ from: selectedFrom, to: selectedTo }) // Delete the selected range
                                              .insertContentAt(selectedFrom, res.selection) // Insert the new content at the start of the selection
                                              .setTextSelection({
                                                from: selectedFrom,
                                                to: selectedFrom + res.selection.length,
                                              })
                                              .run()
                                          })
                                          .catch((e) => {
                                            notification.add(getErrorMessage(e), 'error')
                                          })
                                      },
                                    }),
                                  })

                                  return menuItems
                                }}
                                isVisibleFloatingMenu
                                allowFloatingBarActions={floatingMenuActions}
                                multiline
                                spellcheck="false"
                                placeholder={t`Start typing or click + to add more content`}
                                allowActions={['merge-tags']}
                                onChange={(value) => {
                                  setEmailBody(value)
                                }}
                                mergeTags={account.enums.emailing.merge_tag.toJSON()}
                                disabled={
                                  values.is_locked ||
                                  !account.workspace?.permissions.newsroom_emailing.can_write
                                }
                                handleUploadMedia={(model, onUploadProgress, onUploadComplete) => {
                                  emailing.email_edit.attachments.uploadMedia(
                                    model,
                                    onUploadProgress,
                                    onUploadComplete,
                                    true,
                                  )
                                }}
                              />
                            </StyledTextEditorWrapper>
                          </Box>
                        </MntrPaper>
                        <div>
                          <Signature
                            email={values}
                            onSubmit={(model) => {
                              updateSignatureAndSender(model)
                            }}
                            onDelete={() => {
                              updateSignatureAndSender({
                                signature_name: '',
                                signature_position: '',
                                signature_email: '',
                                signature_phone: '',
                                footer: '',
                              })
                            }}
                          />
                        </div>
                        <StyledAttachmentsList>
                          <AttachmentsList
                            displayEmpty
                            attachments={emailing.email_edit.attachments}
                            handleUploadMedia={emailing.email_edit.attachments.uploadMedia}
                            disabled={values.is_locked}
                          />
                        </StyledAttachmentsList>
                      </Flex>
                    </StyledContent>
                  )}

                  {/* Step 3 - Preview */}
                  {step === (values.is_locked ? 2 : 3) && (
                    <StyledContentFullHeight>
                      <PreviewEmail
                        values={values}
                        emailing={emailing}
                        recipients={emailing.email_edit.recipients}
                        activeSenderId={activeSenderId}
                        recipientsCount={emailing.email_edit.recipients.validRecipientsCount}
                        goStep={(value) => setStep(value)}
                      />
                    </StyledContentFullHeight>
                  )}
                </form>
                {/* Step 2 - Recipients */}
                {!values.is_locked && step === 2 && (
                  <RecipientsTab emailBody={emailBody} activeSenderId={activeSenderId} />
                )}
              </>
            )
          }}
        />
      )}
    </div>
  )
}

export default observer(CreateEmailContent)
