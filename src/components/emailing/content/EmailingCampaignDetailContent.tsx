import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import { PropsWithChildren } from 'react'
import PageContent from '~/components/Content/Content'
import FunnelStats from '~/components/emailing/components/FunnelStats/FunnelStats'
import MentionedNewsroomPosts from '~/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts'
import Tabs from '~/components/emailing/components/Tabs/Tabs'
import NewEmailWizardButton from '~/components/emailing/content/NewEmailWizardButton'
import AddArticleToMediaCoverage from '~/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage'
import displayEmailingTitle from '~/components/emailing/helpers/displayEmailingTitle'
import withModalAddCampaign from '~/components/emailing/modules/withModalAddCampaign'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrBreadcrumbs from '~/components/misc/MntrBreadcrumbs/MntrBreadcrumbs'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import staticFeeds from '~/constants/staticFeeds'
import TABS_CONSTANTS from '~/constants/ui/tabs'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { ObservedFC, observer } from '~/helpers/msts'
import { routerPush } from '~/helpers/router'

interface IEmailingCampaignDetailContentProps extends PropsWithChildren {
  activeTab?: string
}

const EmailingCampaignDetailContent: ObservedFC<IEmailingCampaignDetailContentProps> = ({
  activeTab,
  children,
  appStore: {
    account,
    emailing,
    monitoring: { feedMap },
  },
}) => {
  const item = emailing.campaign_detail.result
  const funnel_data = item?.funnel_data.toJSON()
  const mentioned_articles = item?.mentioned_articles
  const router = useRouter()
  const { asPath } = useRouter()
  const emailsStore = emailing.campaign_detail.result?.emails || {}
  const canWriteNewsroomEmailing = account.workspace?.permissions.newsroom_emailing.can_write

  // skip render if campaign not found
  if (!item) return null

  const displayName = emailing.campaign_detail.isLoaded ? item.name : t`Loading...`
  const { campaignId } = router.query

  // skip render if campaign not found
  if (!item) return null

  // Breadcumb navigation
  const breadcrumbs = [
    {
      label: t`Campaigns`,
      href: '/emailing',
    },
    {
      label: displayName,
    },
  ]

  // Campaign card menu items
  // @ts-expect-error TODO refactor MntrMenu to tsx */
  const menuItems = []

  if (canWriteNewsroomEmailing) {
    menuItems.push(
      {
        label: t`New Email`,
        leftIcon: 'mail',
        href: `/emailing/campaign/${campaignId}/email/create`,
      },
      {
        label: t`Edit Campaign`,
        leftIcon: 'edit',
        ...withModalAddCampaign({
          modalTitle: t`Edit Campaign`,
          onSubmit: item?.update,
          emailing: emailing,
          initialValues: item,
        }),
      },
      {},
      {
        label: t`Remove Campaign`,
        leftIcon: 'delete',
        hoverVariant: 'error',
        ...withModalRemove({
          message: t`Campaign will be removed`,
          onSubmit: () => {
            item.remove().then(() => {
              routerPush('/emailing')
            })
          },
        }),
      },
    )
  }

  const tabNavigation = [
    {
      href: `/emailing/campaign/${campaignId}`,
      label: t`Emails`,
      icon: 'email',
      name: TABS_CONSTANTS.CAMPAIGN_DETAIL.EMAILS,
    },
    {
      href: `/emailing/campaign/${campaignId}/recipients`,
      label: t`Recipients`,
      icon: 'people',
      name: TABS_CONSTANTS.CAMPAIGN_DETAIL.RECIPIENTS,
    },
    ...(account.workspace?.permissions.newsroom_blog.can_read
      ? [
          {
            href: `/emailing/campaign/${campaignId}/media-coverage`,
            label: t`Media Coverage`,
            icon: 'bottom_sheets',
            name: TABS_CONSTANTS.CAMPAIGN_DETAIL.MEDIA_COVERAGE,
          },
        ]
      : []),
  ]

  const title = displayEmailingTitle(item, false)
  return (
    <PageContent>
      {/* Mobile navigation */}
      <Box display={['block', 'none']}>
        <Box position="absolute" left={0}>
          <MntrButton icon="chevron_left" href="/emailing" />
        </Box>
        <Heading textAlign="center">{t`Campaign`}</Heading>
      </Box>
      {/* Breadcumb */}
      <MntrBreadcrumbs items={breadcrumbs} />
      {/* Campaign Feed Item */}
      <MntrPaper p={3}>
        <Flex gap={4} column>
          <Flex justifyContent="space-between">
            {title ? (
              <Heading fontSize={[3, 5]} lineHeight={[1.22]} wordBreak="break-all">
                {title}
              </Heading>
            ) : (
              <Text color="mediumGrey" fontSize={5} fontStyle="italic">{`<${t`No title`}>`}</Text>
            )}

            <MntrButton
              icon="more_vert"
              bg="transparent"
              popupPlacement={'bottom-end'}
              transformOrigin="100% 0"
              popup={(closePopup) => {
                return (
                  // @ts-expect-error TODO refactor MntrMenu to tsx
                  <MntrMenu closePopup={closePopup} menuItems={menuItems} closePopupOnSubmenu />
                )
              }}
            />
          </Flex>

          {funnel_data ? <FunnelStats funnelData={funnel_data} /> : null}

          {/* Mentioned articles panel */}
          {mentioned_articles ? (
            <MentionedNewsroomPosts
              articles={mentioned_articles.results}
              isLoaded={mentioned_articles.isLoaded}
            />
          ) : null}
        </Flex>
      </MntrPaper>
      <Flex
        justifyContent="space-between"
        alignItems={['end', 'center']}
        flexDirection={['column', 'initial']}
      >
        <MntrPaper display={['none', 'block']} width={'fit-content'}>
          <Tabs>
            {tabNavigation.map((tab) => {
              return (
                <Tabs.Item
                  key={tab.href}
                  href={tab.href}
                  label={tab.label}
                  active={tab.name === activeTab}
                  icon={tab.icon}
                />
              )
            })}
          </Tabs>
        </MntrPaper>

        {canWriteNewsroomEmailing && (
          <Flex>
            {activeTab === TABS_CONSTANTS.CAMPAIGN_DETAIL.MEDIA_COVERAGE
              ? feedMap.get(staticFeeds.EMAILING_CAMPAIGN_FEED)?.feedStories?.length > 0 && (
                  <AddArticleToMediaCoverage />
                )
              : emailsStore.isVisibleActionsBar && <NewEmailWizardButton />}
          </Flex>
        )}
      </Flex>
      {/* Mobile tab navigation */}
      <Box display={['block', 'none']}>
        <MntrPaper>
          <Box px={3} py={1}>
            {/* @ts-expect-error: refactor MntrSelectAdapter to TS */}
            <MntrSelectAdapter
              name="tabNavigation"
              items={tabNavigation.map((tab) => {
                return {
                  label: tab.label,
                  value: tab.href,
                }
              })}
              meta={{}}
              transparentBg
              onChange={(value: string) => {
                routerPush(value)
              }}
              input={{ value: asPath }}
            />
          </Box>
        </MntrPaper>
      </Box>
      {children}
    </PageContent>
  )
}

export default observer(EmailingCampaignDetailContent)
