import { t } from '@lingui/core/macro'
import withModalEditSignature from '~/components/misc/MntrEditor/modals/withModalEditSignature'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalRemove from '~/helpers/modal/withModalRemove'

interface ISignaturePopupProps {
  onSubmit: (model: object) => void
  onDelete: () => void
  closePopup: () => void
  initialValues: Record<string, unknown>
}

const SignaturePopup = ({
  onSubmit,
  onDelete,
  initialValues,
  closePopup,
}: ISignaturePopupProps) => {
  const menuItems = [
    {
      bg: 'flat',
      label: t`Edit`,
      leftIcon: 'edit',
      ...withModalEditSignature({
        onSubmit,
        initialValues,
      }),
    },
    {
      bg: 'flat',
      label: t`Delete`,
      leftIcon: 'delete',
      ...withModalRemove({
        title: t`Delete signature`,
        onSubmit: onDelete,
        message: t`Are you sure you want to delete signature?`,
      }),
    },
  ]

  //@ts-expect-error: nevim
  return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
}

export default SignaturePopup
