import MntrCmsEditorAdapter from '~/components/forms/adapters/MntrCmsEditorAdapter/MntrCmsEditorAdapter'

interface IEmailPreviewInputProps {
  value: string
}

const EmailPreviewInput = ({ value }: IEmailPreviewInputProps) => {
  return (
    <MntrCmsEditorAdapter
      //@ts-expect-error: yes
      meta={{}}
      isPreview
      disabled
      input={{
        value,
      }}
    />
  )
}

export default EmailPreviewInput
