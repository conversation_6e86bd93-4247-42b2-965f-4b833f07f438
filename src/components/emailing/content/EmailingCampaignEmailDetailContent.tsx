import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import { PropsWithChildren } from 'react'

import PageContent from '~/components/Content/Content'
import FunnelStats from '~/components/emailing/components/FunnelStats/FunnelStats'
import MentionedNewsroomPosts from '~/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts'
import Tabs, { ITabsItemProps } from '~/components/emailing/components/Tabs/Tabs'
import displayEmailingTitle from '~/components/emailing/helpers/displayEmailingTitle'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Box, Flex, Heading, Skeleton, Text } from '~/components/misc/Mntr'
import MntrBreadcrumbs from '~/components/misc/MntrBreadcrumbs/MntrBreadcrumbs'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { ObservedFC, observer } from '~/helpers/msts'
import { routerPush } from '~/helpers/router'
import CampaignAutocomplete from './CampaignAutocomplete'

interface IEmailingCampaignEmailDetailContentProps extends PropsWithChildren {
  activeTab: string
}

const EmailingCampaignEmailDetailContent: ObservedFC<IEmailingCampaignEmailDetailContentProps> = ({
  appStore: { account, emailing, notification },
  activeTab,
  children,
}) => {
  const {
    asPath,
    query: { campaignId, emailId },
  } = useRouter()

  const item = emailing.email_detail.result
  const isLoaded = emailing.email_detail.isLoaded
  const funnel_data = item?.funnel_data.toJSON()
  const mentioned_articles = item?.mentioned_articles
  const campaignName = emailing.campaign_detail.result?.name
  const emailTitle = item ? displayEmailingTitle(item, true) : t`Loading...`

  // Breadcumb navigation
  const breadcrumbs = [
    { label: t`Campaigns`, href: '/emailing' },
    { label: campaignName, href: `/emailing/campaign/${campaignId}` },
    { label: emailTitle },
  ]

  const emailHref = `/emailing/campaign/${campaignId}/email/${emailId}`

  // Header - email detail menuItems
  // @ts-expect-error TODO refactor MntrButton to tsx */
  const menuItems = []

  if (account.workspace?.permissions.newsroom_emailing.can_write) {
    // Duplicate email
    menuItems.push({
      leftIcon: 'content_copy',
      label: t`Duplicate`,
      onClick: () => {
        item.duplicate().then(() => {
          notification.add(t`Email successfully duplicated`, 'success')
        })
      },
    })

    // Copy to another campaign
    menuItems.push({
      leftIcon: 'fork_left',
      label: t`Copy to another campaign`,
      subMenuItems: [
        {
          label: t`Select campaign`,
        },
        {
          customComponent: (closePopup: () => void) => {
            return <CampaignAutocomplete closePopup={closePopup} item={item} />
          },
        },
      ],
    })

    // Remove email
    menuItems.push({
      label: t`Delete`,
      ...withModalRemove({
        message: t`Are you sure you want to delete this email?`,
        onSubmit: () => {
          emailing.campaign_detail.result.emails.remove(campaignId, emailId)
          routerPush(`/emailing/campaign/${campaignId}`)
        },
      }),
      hoverVariant: 'error',
      leftIcon: 'delete',
    })
  }

  // Tabs
  const tabNavigation: ITabsItemProps[] = [
    { href: `${emailHref}/`, label: t`Email`, icon: 'email', name: 'email' },
    { href: `${emailHref}/recipients`, label: t`Recipients`, icon: 'people', name: 'recipients' },
  ]

  return (
    <PageContent>
      {/* Mobile navigation */}
      <Box display={['block', 'none']}>
        <Box position="absolute" left={0}>
          <MntrButton icon="chevron_left" href={`/emailing/campaign/${campaignId}`} />
        </Box>
        <Heading textAlign="center">{t`Email`}</Heading>
      </Box>
      <MntrBreadcrumbs items={breadcrumbs} />
      {emailing.campaign_detail.result && (
        <>
          <MntrPaper p={3}>
            <Flex gap={4} column>
              <Flex justifyContent="space-between">
                {isLoaded ? (
                  item ? (
                    <Heading fontSize={[3, 4]} lineHeight={[1.22]} wordBreak="break-all">
                      {item.subject}
                    </Heading>
                  ) : (
                    <Text
                      color="mediumGrey"
                      fontSize={4}
                      fontStyle="italic"
                    >{`<${t`No subject`}>`}</Text>
                  )
                ) : (
                  <Skeleton height={'32px'} />
                )}

                <MntrButton
                  icon="more_vert"
                  bg="transparent"
                  popupPlacement={'bottom-end'}
                  transformOrigin="100% 0"
                  popup={(closePopup) => {
                    return (
                      // @ts-expect-error TODO refactor MntrMenu to tsx
                      <MntrMenu closePopup={closePopup} menuItems={menuItems} closePopupOnSubmenu />
                    )
                  }}
                />
              </Flex>

              {isLoaded ? (
                funnel_data ? (
                  <FunnelStats funnelData={funnel_data} />
                ) : null
              ) : (
                <Skeleton height={'180px'} fullWidth />
              )}

              {mentioned_articles ? (
                <MentionedNewsroomPosts
                  articles={mentioned_articles.results}
                  isLoaded={mentioned_articles.isLoaded}
                />
              ) : null}
            </Flex>
          </MntrPaper>

          <Flex
            justifyContent="space-between"
            alignItems={['end', 'center']}
            flexDirection={['column', 'initial']}
          >
            <MntrPaper display={['none', 'block']} width={'fit-content'}>
              <Tabs>
                {tabNavigation.map((tab) => {
                  return (
                    <Tabs.Item
                      key={tab.href}
                      href={tab.href}
                      label={tab.label}
                      active={tab.name === activeTab}
                      icon={tab.icon}
                    />
                  )
                })}
              </Tabs>
            </MntrPaper>
          </Flex>

          {/* Mobile tab navigation */}
          <Box display={['block', 'none']}>
            <MntrPaper>
              <Box px={3} py={1}>
                {/* @ts-expect-error: refactor MntrSelectAdapter to TS */}
                <MntrSelectAdapter
                  name="tabNavigation"
                  items={tabNavigation.map((tab) => {
                    return {
                      label: tab.label,
                      value: tab.href,
                    }
                  })}
                  meta={{}}
                  input={{ value: asPath }}
                  transparentBg
                  onChange={(value: string) => {
                    routerPush(value)
                  }}
                />
              </Box>
            </MntrPaper>
          </Box>
          {children}
        </>
      )}
    </PageContent>
  )
}

export default observer(EmailingCampaignEmailDetailContent)
