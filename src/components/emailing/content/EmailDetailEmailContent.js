import { Trans } from '@lingui/react/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import { Flex, Heading, Text } from '~/components/misc/Mntr'
import AttachmentsList from '~/components/newsroom/content/post/AttachmentsList'

import { observer } from '~/helpers/mst'
import EmailPreviewInput from './EmailPreviewInput'

const EmailDetailEmailContent = ({ appStore: { emailing } }) => {
  const item = emailing.email_detail.result

  if (!item) return null

  return (
    <Flex maxWidth="648px" width={1} mx="auto" gap={2} flexDirection="column" mt={2}>
      <Heading fontSize={2} color="heading">
        <Trans>Sender</Trans>
      </Heading>

      <MntrPaper>
        <Text p={3} fontSize={2} color="heading">
          {item.sender_full_email}
        </Text>
      </MntrPaper>

      <Heading fontSize={2} color="heading">
        <Trans>Subject</Trans>
      </Heading>

      <MntrPaper>
        <Text p={3} fontSize={2} color="heading">
          {item.subject}
        </Text>
      </MntrPaper>

      <Heading fontSize={2} color="heading">
        <Trans>Email</Trans>
      </Heading>

      <MntrPaper>
        <Text p={3} py={0} fontSize={2} color="heading">
          <EmailPreviewInput value={item.body} />
        </Text>
      </MntrPaper>

      {item.attachments.length > 0 && (
        <AttachmentsList displayEmpty canAdd={false} attachments={{ results: item.attachments }} />
      )}
    </Flex>
  )
}

export default observer(EmailDetailEmailContent)
