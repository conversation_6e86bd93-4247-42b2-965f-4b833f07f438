import { useRouter } from 'next/router'
import RecipientsFeedContent from '~/components/emailing/components/RecipientsFeed/RecipientsFeedContent'
import { observer } from '~/helpers/mst'

const CampaignDetailRecipientsContent = ({ appStore: { emailing } }) => {
  const router = useRouter()
  const { campaignId } = router.query
  const recipientsStore = emailing.campaign_detail.result?.recipients || {}

  return (
    <RecipientsFeedContent
      url={`/emailing/campaign/${campaignId}/recipients`}
      recipientsStore={recipientsStore}
      allowFilters={['query']}
    />
  )
}

export default observer(CampaignDetailRecipientsContent)
