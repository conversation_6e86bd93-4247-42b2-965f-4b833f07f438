import { t } from '@lingui/core/macro'
import { useState } from 'react'

import WizardGenerateEmail from '~/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const NewEmailWizardButton = () => {
  const [modalWidth, setModalWidth] = useState(800)

  return (
    <MntrButton
      ml={2}
      label={t`New Email`}
      bg="secondary"
      modalTitle={t`Create an email`}
      modalWidth={modalWidth}
      // @ts-expect-error TODO refactor MntrButton
      modal={() => {
        return <WizardGenerateEmail setModalWidth={setModalWidth} />
      }}
      submitLabel={`Continue`}
      onClose={() => setModalWidth(800)}
      rounded
      icon="add"
    />
  )
}

export default NewEmailWizardButton
