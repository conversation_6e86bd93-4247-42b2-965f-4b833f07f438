import { styled } from 'styled-components'
import emailingLayout from '~/components/emailing/constants/emailingLayout.constants'
import { Box } from '~/components/misc/Mntr'

export const StyledTextEditorWrapper = styled.div`
  & .ProseMirror {
    min-height: 300px;
  }
`

export const StyledContent = styled(Box)`
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 812px;
  margin: 72px auto;
  padding-bottom: 30px;

  @media (max-width: ${emailingLayout.TOOLBAR_BREAK_MEDIA_QUERY}px) {
    margin-top: 120px;
  }
`

export const StyledContentPreview = styled(Box)`
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 912px;
  margin: 0px auto;
  padding-bottom: 30px;
  padding-left: 20px;
`

export const StyledContentFullHeight = styled(StyledContent)`
  height: calc(100vh - 200px);
  width: 100%;
  max-width: 1280px;
`

export const StyledToolbarWrapper = styled.div`
  position: fixed;
  width: 100%;
  top: 54px;
  z-index: 2;

  & div {
    box-shadow: none;
  }
  border-bottom: 1px solid ${({ theme }) => theme.colors.veryLightGrey};
`

export const StyledAttachmentsList = styled(Box)`
  & #attachments {
    max-width: 100%;
  }
`
