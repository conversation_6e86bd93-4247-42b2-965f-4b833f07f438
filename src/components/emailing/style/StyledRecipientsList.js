import color from 'color'
import { css, styled } from 'styled-components'
import { Box } from '~/components/misc/Mntr'

export const StyledRecipientsBox = styled.div`
  border: 1px dashed ${({ theme, bg }) => theme.colors[bg]};
  background-color: ${({ theme, bg }) => color(theme.colors[bg]).alpha(0.15).string()};
  border-radius: ${({ theme }) => theme.paper.borderRadius * 2}px;
  color: ${({ theme, bg }) => theme.colors[bg]};
  width: 100%;

  ${({ flex }) =>
    flex &&
    css`
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 16px;
    `}
`

export const StyledCounter = styled(Box)`
  padding: 4px 8px;
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  margin-left: 8px;
  background-color: ${({ theme, bg }) => color(theme.colors[bg]).alpha(0.35).string()};
`

export const StyledIconWrapper = styled.div`
  display: inline-block;
  position: relative;
  top: 3px;
  margin-right: 6px;
`

export const StyledMergeTagTooltip = styled.div`
  li {
    padding: 0;
    margin: 0 ${({ theme }) => theme.space[4]}px;
  }

  ul {
    padding: 0;
    margin: ${({ theme }) => theme.space[1]}px 0;
  }
`
