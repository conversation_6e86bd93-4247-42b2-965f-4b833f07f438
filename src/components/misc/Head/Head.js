import NextHead from 'next/head'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { sleep } from '~/helpers/async'
import { observer } from '~/helpers/mst'

const Head = ({
  appStore: {
    account: { user },
    appSettings: { appDomain, appName, og, twitter, themeColor },
    isMobileApp,
    theme,
  },
  description,
  title,
}) => {
  const { asPath } = useRouter()

  const composedTitle = title
    ? title.includes(appName)
      ? title
      : `${title} - ${appName}`
    : appName

  const [pageTitle, setPageTitle] = useState(composedTitle)

  useEffect(() => {
    async function setTitle(event) {
      if (event?.type === 'appinstalled') {
        await sleep(1000)
      }

      setPageTitle(window.matchMedia('(display-mode: standalone)').matches ? title : composedTitle)
    }

    window.addEventListener('appinstalled', setTitle)
    setTitle()

    return () => {
      window.removeEventListener('appinstalled', setTitle)
    }
  }, [composedTitle, title])

  return (
    <NextHead>
      <meta
        name="viewport"
        content={`width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=${
          isMobileApp ? 'cover' : 'auto'
        }`}
        key="viewport"
      />

      <title key="title">{pageTitle}</title>
      {description && <meta name="description" content={description} key="description" />}
      <meta name="theme-color" content={user.isActiveUser ? theme.colors.primary : themeColor} />

      {/* https://ogp.me/ */}
      <meta property="og:type" content="website" key="og:type" />
      <meta property="og:title" content={pageTitle} key="og:title" />
      {description && <meta property="og:description" content={description} key="og:description" />}
      <meta property="og:image" content={`https://${appDomain}${og.image}`} key="og:image" />
      <meta property="og:url" content={`https://${appDomain}${asPath}`} key="og:url" />
      <meta property="og:locale" content={og.locale} key="og:locale" />

      {/* https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/summary */}
      {/* https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/markup */}
      {twitter && (
        <>
          <meta name="twitter:card" content="summary" key="twitter:card" />
          <meta name="twitter:site" content={twitter.site} key="twitter:site" />
        </>
      )}
    </NextHead>
  )
}

export default observer(Head)
