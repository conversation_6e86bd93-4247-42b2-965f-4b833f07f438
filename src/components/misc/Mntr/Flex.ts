import { css, styled } from 'styled-components'
import { IStyleProps, ResponsiveStyleValue } from '.'
import parseStyles from './parseStyles'

const Flex = styled.div<
  IStyleProps & {
    center?: ResponsiveStyleValue<boolean>
    centerX?: ResponsiveStyleValue<boolean>
    centerY?: ResponsiveStyleValue<boolean>
    column?: ResponsiveStyleValue<boolean>
    onClick?: () => void
  }
>`
  display: flex;
  box-sizing: border-box;

  ${({ centerX }) => centerX && 'justify-content: center;'}
  ${({ centerY }) => centerY && 'align-items: center;'}
  ${({ center }) => center && 'justify-content: center; align-items: center;'}

  ${({ column }) =>
    column &&
    css`
      flex-direction: column;
    `}

  ${parseStyles}
`

export default Flex
