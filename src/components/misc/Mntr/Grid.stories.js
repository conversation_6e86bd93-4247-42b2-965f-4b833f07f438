import { Box, Col, Flex, Grid, Heading } from '~/components/misc/Mntr'

export default { title: 'Grid' }

export const Default = () => {
  return (
    <Flex flexDirection="column" gap={3}>
      <Heading>Basic</Heading>
      <Grid cols={[1, 2, 3, 4]}>
        <Box>A</Box>
        <Box>B</Box>
        <Box>C</Box>
        <Box>D</Box>
      </Grid>

      <Heading>With Col</Heading>
      <Grid cols={[1, 2, 4, 4, 4]} gap={3}>
        <Col colSize={[1, 1, 2, 4]} bg="beige">
          A
        </Col>
        <Box bg="error">B</Box>
        <Box bg="primary">C</Box>
        <Box bg="secondary">D</Box>
        <Box bg="tertiary">E</Box>
      </Grid>

      <Heading>Responsive Cols</Heading>
      <Grid cols={[12]} gap={3}>
        <Col colSize={[12, 12, 8, 8, 4]} bg="beige">
          Facebook
        </Col>

        <Col colSize={[12, 6, 4, 4, 2]} bg="beige">
          X.com
        </Col>

        <Col colSize={[12, 6, 4, 4, 2]} bg="beige">
          Instagram
        </Col>

        <Col colSize={[12, 6, 4, 4, 2]} bg="beige">
          TikTok
        </Col>

        <Col colSize={[12, 6, 4, 4, 2]} bg="beige">
          Youtube
        </Col>
      </Grid>
    </Flex>
  )
}
