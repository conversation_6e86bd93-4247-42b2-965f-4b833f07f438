import React from 'react'
import { css, styled } from 'styled-components'

const StyledButton = styled.button`
  position: relative;
  display: inline-flex;
  flex: 0 0 auto;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 12px;
  color: inherit;
  border: 0;
  cursor: pointer;
  outline: 0;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  appearance: none;
  overflow: visible;
  font-size: 1.5rem;
  text-align: center;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 50%;

  ${({ fullWidth }) => {
    return (
      fullWidth &&
      css`
        width: 100%;
      `
    )
  }}

  ${({ disabled }) => {
    return (
      disabled &&
      css`
        pointer-events: none;
      `
    )
  }}
`

const StyledLabel = styled.span`
  width: 100%;
  display: flex;
  align-items: inherit;
  justify-content: inherit;
`

const IconButton = React.forwardRef(function IconButton(props, ref) {
  const { children, component, ...other } = props
  const Tag = component ? component : props.href ? 'a' : 'button'

  return (
    <StyledButton as={Tag} ref={ref} {...other}>
      <StyledLabel>{children}</StyledLabel>
    </StyledButton>
  )
})

export default IconButton
