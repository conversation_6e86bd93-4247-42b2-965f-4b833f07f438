import { css, styled } from 'styled-components'
import { Box, IStyleProps } from '.'

interface ISkeletonProps extends IStyleProps {
  fullWidth?: boolean
  isCircle?: boolean
  repeat?: number
  size?: string
}

const StyledBox = styled(Box)<ISkeletonProps>`
  ${({ fullWidth, isCircle = false, theme, size }) => {
    const minWidth = 30 // Minimum width percentage
    const maxWidth = 90 // Maximum width percentage
    const borderRadius = isCircle ? '50%' : `${theme.radius}px`
    const width = isCircle
      ? size || 'auto'
      : fullWidth
        ? '100%'
        : `${Math.floor(Math.random() * (maxWidth - minWidth + 1)) + minWidth}%`

    return css`
      width: ${width};
      border-radius: ${borderRadius};
      ${isCircle && { height: size || 'auto' }}
    `
  }}

  background: linear-gradient(
    108deg,
    ${({ theme }) => theme.paper.backgroundHover} 20%,
    ${({ theme }) => theme.paper.background} 40%,
    ${({ theme }) => theme.paper.backgroundHover} 60%
  );
  background-size: 200% 100%;
  animation: shimmer 2.4s ease-in-out infinite;

  @keyframes shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
`
export const Skeleton = ({ fullWidth, size, isCircle, repeat = 1, ...props }: ISkeletonProps) => {
  return Array.from({ length: repeat }, (_, idx) => (
    <StyledBox fullWidth={fullWidth} size={size} isCircle={isCircle} key={idx} {...props} />
  ))
}

export default Skeleton
