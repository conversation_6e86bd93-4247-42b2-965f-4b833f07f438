import React from 'react'
import styled, { css } from 'styled-components'

import { IStyleProps } from '.'
import parseStyles from './parseStyles'

const GridWrapper = styled.div`
  container-type: inline-size;
`

interface IGridProps extends IStyleProps {
  children?: React.ReactNode
  cols?: number[]
}

const StyledGrid = styled.div<IGridProps>`
  display: grid;
  box-sizing: border-box;
  gap: ${({ gap }) => gap};

  /*
    Set the default grid-template-columns using the first index of cols
    or set the 'gridTemplateColumns' when available
  */
  grid-template-columns: ${({ gridTemplateColumns, cols }) =>
    gridTemplateColumns ? gridTemplateColumns : `repeat(${cols ? cols[0] : 1}, minmax(0, 1fr))`};

  /* Responsive grid-template-columns based on provided breakpoints */
  ${({ cols, theme }) =>
    cols &&
    cols.slice(1).map(
      (col, index) => css`
        @media (min-width: ${theme.breakpoints[index]}) {
          grid-template-columns: repeat(${col}, minmax(0, 1fr));
        }
      `,
    )}

  ${parseStyles}
`

function Grid({ children, cols, gridTemplateColumns, gap, ...props }: IGridProps) {
  return (
    <GridWrapper>
      <StyledGrid cols={cols} gridTemplateColumns={gridTemplateColumns} gap={gap} {...props}>
        {children}
      </StyledGrid>
    </GridWrapper>
  )
}

export default Grid
