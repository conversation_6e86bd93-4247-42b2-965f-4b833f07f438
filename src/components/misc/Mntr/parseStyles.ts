// TODO
/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/ban-ts-comment */
import get from 'lodash/get'
import { DefaultTheme } from 'styled-components'
import { IStyleProps } from '.'
import { aliases, multiples, scales } from './constants'

// Handle negative scale values
const positiveOrNegative = (scale: number | string, value: number | string) => {
  if (typeof value !== 'number' || value >= 0) {
    return get(scale, value, value)
  }
  const absolute = Math.abs(value)

  const n = get(scale, absolute, absolute)
  if (typeof n === 'string') return '-' + n
  return n * -1
}

// Props that are able to be negative
const transforms = [
  'margin',
  'marginTop',
  'marginRight',
  'marginBottom',
  'marginLeft',
  'marginX',
  'marginY',
  'top',
  'bottom',
  'left',
  'right',
].reduce(
  (acc, curr) => ({
    ...acc,
    [curr]: positiveOrNegative,
  }),
  {},
)

// Generate media query string if value is an array
// otherwise return the value
const responsive = (styles: any) => (theme: DefaultTheme) => {
  const next: any = {}
  const breakpoints = get(theme, 'breakpoints', undefined)
  // @ts-ignore
  const mediaQueries = [null, ...breakpoints.map((n) => `@media screen and (min-width: ${n})`)]

  for (const key in styles) {
    const value = styles[key]
    if (value === null) continue
    if (!Array.isArray(value)) {
      next[key] = value
      continue
    }
    for (let i = 0; i < value.slice(0, mediaQueries.length).length; i++) {
      const media = mediaQueries[i]
      if (!media) {
        next[key] = value[i]
        continue
      }
      next[media] = next[media] || {}
      if (value[i] === null) continue
      next[media][key] = value[i]
    }
  }

  return next
}

const css = (args: IStyleProps) => (theme: DefaultTheme) => {
  const result: any = {}
  // we do not accept functions as argument
  const obj = typeof args === 'function' ? null : args
  // recieves css rules as an object
  const styles = responsive(obj)(theme)

  // iterate through the rules and convert them to values from theme
  for (const key in styles) {
    const val = styles[key]

    if (val && typeof val === 'object') {
      result[key] = css(val)(theme)
      continue
    }

    // get prop name (e.g. backgroundColor, paddingX)
    const prop = get(aliases, key, key)
    // get scale name to be able to replace it with value from theme
    // (e.g. colors, fontSizes, space)
    const scaleName = get(scales, prop, undefined)
    // if scale name is not in rules list, skip this rule
    if (!scaleName) continue
    // get values from theme
    const scale = get(theme, scaleName, get(theme, prop, {}))
    // replace scale with values from theme
    const transform = get(transforms, prop, get)

    const value =
      scaleName === 'sizes'
        ? // convert numeric value to percentage for width and height (e.g 1 is 100%)
          // or pass value as string (e.g. 100px)
          typeof val === 'number'
          ? `${val * 100}%`
          : transform(scale, val, val)
        : scaleName === 'zIndices' || scaleName === 'lineHeights'
          ? // convert numeric value to string for z-index and line-height
            // to prevent appending px to the value
            `${val}`
          : transform(scale, val, val)

    // Handle mx, py, marginY or size multiples
    // @ts-ignore
    if (multiples[prop]) {
      // @ts-ignore
      const dirs = multiples[prop]

      for (let i = 0; i < dirs.length; i++) {
        result[dirs[i]] = value
      }
    } else {
      result[prop] = value
    }
  }

  // convert camelCase to kebab-case
  const convertedObj = Object.keys(result).reduce((acc, key) => {
    const newKey = key.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()

    return {
      ...acc,
      [newKey]: result[key],
    }
  }, {})

  return convertedObj
}

// get theme, all props and pass them to main function
const parseStyles = ({
  theme,
  children,
  ...props
}: IStyleProps & { theme: DefaultTheme; children?: React.ReactNode }) => {
  return css(props)(theme)
}

export default parseStyles
