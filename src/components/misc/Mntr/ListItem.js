import { styled } from 'styled-components'

const Wrapper = styled.div`
  display: flex;
  position: relative;
  width: 100%;
  justify-content: flex-start;
  text-align: left;
  text-decoration: none;
  padding: 0 16px;
  box-sizing: border-box;

  ${({ button, disabled, onClick }) =>
    button ||
    (onClick &&
      `
      &:hover {
        cursor: ${disabled ? 'default' : 'pointer'};
      }
    `)}
`

const ListItem = ({
  button,
  children,
  disabled,
  // TODO: add ripple effect and implement disableRipple prop
  // disableRipple,
  onClick,
  ...props
}) => {
  return (
    <Wrapper onClick={disabled ? null : onClick} {...props}>
      {children}
    </Wrapper>
  )
}

export default ListItem
