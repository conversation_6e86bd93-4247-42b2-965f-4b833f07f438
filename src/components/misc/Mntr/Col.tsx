import { styled } from 'styled-components'
import { IStyleProps } from '.'
import parseStyles from './parseStyles'

const Col = styled.div<
  IStyleProps & {
    colSize?: number[]
  }
>`
  box-sizing: border-box;
  display: grid;

  grid-column: span ${({ colSize }) => (colSize ? colSize[0] : 1)} / span
    ${({ colSize }) => (colSize ? colSize[0] : 1)};

  ${({ colSize, theme }) =>
    colSize &&
    colSize.map((item, index) => {
      if (index === 0) return
      return `@media screen and (min-width: ${theme.breakpoints[index - 1]}) {
          grid-column: span ${item}/span ${item};
        }`
    })}

  ${parseStyles}
`
export default Col
