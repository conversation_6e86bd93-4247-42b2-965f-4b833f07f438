import { css, styled } from 'styled-components'

const FormControlWrapper = styled.div`
  position: relative;
  display: inline-flex;
  flex-direction: column;
  vertical-align: top;
  box-sizing: border-box;

  ${({ fullWidth }) =>
    fullWidth &&
    css`
      width: 100%;
    `}
`

const FormControl = ({ children, fullWidth, props }) => {
  return (
    <FormControlWrapper fullWidth={fullWidth} {...props}>
      {children}
    </FormControlWrapper>
  )
}

export default FormControl
