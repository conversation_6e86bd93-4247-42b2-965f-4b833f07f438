import { useEffect, useState } from 'react'
import { Box } from '~/components/misc/Mntr'

interface TypingTextProps {
  html: string
  speed?: number
}

const TypingText = ({ html, speed = 5 }: TypingTextProps) => {
  const [displayedHtml, setDisplayedHtml] = useState<string>('')

  useEffect(() => {
    let currentIndex = 0
    let currentHtml = '' // Track the HTML string being built
    setDisplayedHtml('') // Clear the displayed HTML when the input HTML changes

    const intervalId = setInterval(() => {
      if (currentIndex < html.length) {
        currentHtml += html.charAt(currentIndex) // Append the next character
        setDisplayedHtml(currentHtml) // Update the displayed HTML
        currentIndex++
      } else {
        clearInterval(intervalId) // Clear interval when all characters are typed out
      }
    }, speed)

    return () => clearInterval(intervalId) // Cleanup the interval on unmount or when HTML changes
  }, [html, speed])

  return <Box dangerouslySetInnerHTML={{ __html: displayedHtml }} />
}

export default TypingText
