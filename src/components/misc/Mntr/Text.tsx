import React, { type JSX } from 'react'
import { styled } from 'styled-components'

import { IStyleProps } from '.'
import parseStyles from './parseStyles'

const StyledText = styled.div<IStyleProps>`
  box-sizing: border-box;
  margin: 0;
  color: ${({ theme }) => theme.colors.black};

  ${parseStyles}
`
interface ITextProps extends IStyleProps {
  as?: keyof JSX.IntrinsicElements | React.ComponentType<React.HTMLAttributes<HTMLElement>>
  children?: React.ReactNode
}

const Text = (props: ITextProps) => {
  const { as, children, ...other } = props
  const Component = as || 'div'

  return (
    <StyledText as={Component} {...other}>
      {children}
    </StyledText>
  )
}

export default Text
