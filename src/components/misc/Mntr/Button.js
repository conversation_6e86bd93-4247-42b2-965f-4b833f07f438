import React from 'react'
import { css, styled } from 'styled-components'

const StyledButton = styled.button`
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 6px 16px;
  border: 0;
  cursor: pointer;
  outline: 0;
  color: inherit;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  -webkit-tap-highlight-color: transparent;
  font-size: 0.875rem;
  min-width: 64px;
  transition:
    background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
    box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
    border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  text-transform: uppercase;
  box-sizing: border-box;

  ${({ fullWidth }) => {
    return (
      fullWidth &&
      css`
        width: 100%;
      `
    )
  }}

  ${({ disabled }) => {
    return (
      disabled &&
      css`
        pointer-events: none;
      `
    )
  }}
`

const StyledLabel = styled.span`
  width: 100%;
  display: inherit;
  align-items: inherit;
  justify-content: inherit;
`

const EndIcon = styled.span`
  display: inherit;
  margin-right: -4px;
  margin-left: 8px;
`

const StartIcon = styled.span`
  display: inherit;
  margin-left: -4px;
  margin-right: 8px;
`

const Button = React.forwardRef(function Button(props, ref) {
  const { children, component, endIcon, startIcon, ...other } = props
  const Tag = component ? component : props.href ? 'a' : 'button'

  return (
    <StyledButton as={Tag} ref={ref} {...other}>
      <StyledLabel>
        {startIcon ? <StartIcon>{startIcon}</StartIcon> : null}
        {children}
        {endIcon ? <EndIcon>{endIcon}</EndIcon> : null}
      </StyledLabel>
    </StyledButton>
  )
})

export default Button
