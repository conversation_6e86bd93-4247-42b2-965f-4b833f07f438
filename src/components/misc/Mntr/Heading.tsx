import React, { type JSX } from 'react'
import { styled } from 'styled-components'

import { IStyleProps } from '.'
import parseStyles from './parseStyles'

const StyledHeading = styled.h2<IStyleProps>`
  box-sizing: border-box;
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes[4]}px;
  color: ${({ theme }) => theme.colors.heading};

  ${parseStyles}
`

interface IHeadingProps extends IStyleProps, React.PropsWithChildren {
  id?: string
  as?: keyof JSX.IntrinsicElements | React.ComponentType<React.HTMLAttributes<HTMLElement>>
}

const Heading = (props: IHeadingProps) => {
  const { as, children, id, ...other } = props
  const Component = as || 'h2'

  return (
    <StyledHeading as={Component} id={id} {...other}>
      {children}
    </StyledHeading>
  )
}

export default Heading
