import { Box, Flex, <PERSON>ing, <PERSON> } from '~/components/misc/Mntr'

export default { title: 'Link' }

export const Default = () => {
  return (
    <Flex flexDirection="column" gap={3}>
      <Heading>Basic</Heading>
      <Box>
        <Link href="https://mediaboard.com">Basic link with href</Link>
      </Box>

      <Heading>Underlined on hover</Heading>
      <Box>
        <Link href="https://mediaboard.com" underline="hover">
          Underlined link on hover
        </Link>
      </Box>

      <Heading>Always Underlined</Heading>
      <Box>
        <Link href="https://mediaboard.com" underline="always">
          Underlined link
        </Link>
      </Box>

      <Heading>As a button</Heading>
      <Link as="button">As a button</Link>
    </Flex>
  )
}
