import { cloneElement, forwardRef } from 'react'
import { css, styled } from 'styled-components'

const RootStyled = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition:
    background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
    box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;

  ${({ color }) => {
    return (
      color &&
      css`
        color: ${({ theme }) => theme.colors[color]};
      `
    )
  }}

  ${({ disabled }) => {
    return (
      disabled &&
      css`
        pointer-events: none;
      `
    )
  }}

  ${({ clickable, onClick }) => {
    return (
      clickable ||
      (onClick &&
        css`
          cursor: pointer;
          user-select: none;
          -webkit-tap-highlight-color: transparent;
        `)
    )
  }}

  .avatar {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
    margin-left: 5px;
    margin-right: -6px;
  }

  .icon {
    margin-left: 5px;
    margin-right: -6px;
  }

  .iconSmall {
    width: 18px;
    height: 18px;
    margin-left: 4px;
    margin-right: -4px;
  }

  .deleteIcon {
    width: 22px;
    height: 22px;
    margin: 0px -1px 0px -3px;
    -webkit-tap-highlight-color: transparent;
    color: ${({ theme }) => theme.feed.chipDeleteIcon};
    cursor: pointer;
  }
`

const StyledLabel = styled.span`
  overflow: hidden;
  white-space: nowrap;
  padding-left: 12px;
  padding-right: 12px;
  text-overflow: ellipsis;
`

const Chip = forwardRef((props, ref) => {
  const {
    avatar,
    classes,
    clickable = true,
    color = 'default',
    component: Component = 'div',
    deleteIcon,
    disabled = false,
    icon,
    label,
    onClick,
    onDelete,
    skipFocusWhenDisabled = false,
    ...otherProps
  } = props
  const handleRootClick = (event) => {
    if (!disabled && clickable && onClick) {
      onClick(event)
    }
  }

  const handleDeleteClick = (event) => {
    event.stopPropagation() // Prevent the root click handler from being called
    if (onDelete) {
      onDelete(event)
    }
  }

  const rootProps = {
    onClick: handleRootClick,
    tabIndex: disabled && skipFocusWhenDisabled ? -1 : 0,
    ref,
    color,
    ...otherProps,
  }

  return (
    <RootStyled as={Component} {...rootProps}>
      {avatar && cloneElement(avatar, { className: 'avatar' })}
      {icon && cloneElement(icon, { className: 'icon' })}
      {label && <StyledLabel>{label}</StyledLabel>}
      {onDelete &&
        !disabled &&
        cloneElement(deleteIcon, { className: 'deleteIcon', onClick: handleDeleteClick })}
    </RootStyled>
  )
})

export default Chip
