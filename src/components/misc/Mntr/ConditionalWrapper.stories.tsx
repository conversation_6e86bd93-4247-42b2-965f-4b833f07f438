import type { Meta, StoryObj } from '@storybook/react'
import { ReactNode } from 'react'
import { Flex } from '~/components/misc/Mntr'
import ConditionalWrapper from './ConditionalWrapper'

function wrapper(children: ReactNode) {
  return (
    <Flex bg="green" color="white" p={3}>
      {children}
    </Flex>
  )
}

const meta = {
  title: 'ConditionalWrapper',
  component: ConditionalWrapper,
  args: {
    children: 'CHILDREN',
    wrapper,
  },
} satisfies Meta<typeof ConditionalWrapper>

export default meta

type Story = StoryObj<typeof meta>

export const True: Story = {
  args: {
    condition: true,
  },
}

export const False: Story = {
  args: {
    condition: false,
  },
}
