import React from 'react'
import { styled } from 'styled-components'

const StyledTag = styled.a`
  box-sizing: border-box;
  text-decoration: ${({ underline }) => (underline === 'always' ? 'underline' : 'none')};
  cursor: pointer;

  &:hover {
    text-decoration: ${({ underline }) =>
      underline === 'hover' || underline === 'always' ? 'underline' : 'none'};
  }
`

const Link = React.forwardRef(function Link(props, ref) {
  const { component, as = 'a', children, ...other } = props

  const Tag = component || as

  return (
    <StyledTag as={Tag} ref={ref} {...other}>
      {children}
    </StyledTag>
  )
})

export default Link
