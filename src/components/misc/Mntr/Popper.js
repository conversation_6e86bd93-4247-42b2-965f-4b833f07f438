import {
  autoUpdate,
  flip,
  FloatingPortal,
  limitShift,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
  useTransitionStyles,
} from '@floating-ui/react'
import { useEffect } from 'react'
import { HEIGHT_HEADER_PX } from '~/constants'

const defaultTransformOrigin = ({ side }) =>
  ({
    top: 'bottom',
    bottom: 'top',
    left: 'right',
    right: 'left',
  })[side]

const Popper = ({
  anchorEl = null,
  children,
  disablePortal,
  onClose,
  open,
  placement = 'bottom',
  style,
  transition = false,
  transformOrigin,
}) => {
  const { refs, floatingStyles, context } = useFloating({
    open: open,
    placement: placement,
    onOpenChange: onClose,
    middleware: [
      shift({
        limiter: limitShift(),
        padding: { top: HEIGHT_HEADER_PX },
      }),
      flip(),
    ],
    whileElementsMounted: autoUpdate,
  })
  const click = useClick(context)
  const dismiss = useDismiss(context)
  const role = useRole(context)

  const { getFloatingProps } = useInteractions([click, dismiss, role])

  const { isMounted, styles } = useTransitionStyles(context, {
    initial: {
      opacity: 0,
      transform: 'scale(0.97)',
    },
    common: ({ side }) => ({
      transitionDuration: '120ms',
      transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
      willChange: 'transform',
      transformOrigin: transformOrigin || defaultTransformOrigin(side),
    }),
  })

  useEffect(() => {
    if (anchorEl) refs.setReference(anchorEl)
  }, [anchorEl, refs])

  const renderedContent = isMounted ? (
    <div
      ref={refs.setFloating}
      style={{
        ...floatingStyles,
        ...style,
      }}
      {...getFloatingProps()}
    >
      <div
        style={{
          ...(transition ? styles : {}),
        }}
      >
        {children}
      </div>
    </div>
  ) : null

  return disablePortal ? renderedContent : <FloatingPortal>{renderedContent}</FloatingPortal>
}

export default Popper
