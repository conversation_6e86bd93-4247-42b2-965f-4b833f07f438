import type { Meta, StoryObj } from '@storybook/react'
import { Flex, Heading } from '~/components/misc/Mntr'

const meta = {
  title: 'Flex',
  component: Flex,
} satisfies Meta<typeof Flex>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: (
      <>
        <Flex>A</Flex>
        <Flex>B</Flex>
      </>
    ),
  },
}

export const Columns: Story = {
  args: {
    children: (
      <>
        <Heading>default gap</Heading>
        <Flex column>
          <Flex bg="beige">A</Flex>
          <Flex bg="beige">B</Flex>
        </Flex>
      </>
    ),
    column: true,
  },
}
