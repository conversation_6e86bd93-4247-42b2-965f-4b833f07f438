import React from 'react'
import { css, styled } from 'styled-components'

const StyledLabel = styled.label`
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  vertical-align: middle;
  --webkit-tap-highlight-color: transparent;

  ${({ disabled }) =>
    disabled &&
    css`
      cursor: default;
    `}
`
const StyledSpan = styled.span`
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`

const FormControlLabel = React.forwardRef(function FormControlLabel(props, ref) {
  const {
    checked,
    control,
    disabled: disabledProp,
    inputRef,
    label,
    name,
    onChange,
    value,
    ...other
  } = props

  const controlProps = {
    disabled: disabledProp ?? control?.props?.disabled,
  }

  const keysToCheck = ['checked', 'name', 'onChange', 'value', 'inputRef']

  keysToCheck.forEach((key) => {
    if (control.props[key] === undefined && props[key] !== undefined) {
      controlProps[key] = props[key]
    }
  })

  return (
    <StyledLabel ref={ref} {...other}>
      {React.cloneElement(control, controlProps)}
      <StyledSpan>{label}</StyledSpan>
    </StyledLabel>
  )
})

export default FormControlLabel
