// organize-imports-ignore
export { default as Box } from './Box'
export { default as <PERSON><PERSON> } from './Button'
export { default as Col } from './Col'
export { default as ConditionalWrapper } from './ConditionalWrapper'
export { default as Flex } from './Flex'
export { default as ButtonGroup } from './ButtonGroup'
export { default as FormControl } from './FormControl'
export { default as FormControlLabel } from './FormControlLabel'
export { default as Grid } from './Grid'
export { default as Heading } from './Heading'
export { default as IconButton } from './IconButton'
export { default as Link } from './Link'
export { default as ListItem } from './ListItem'
export { default as Modal } from './Modal'
export { ModalScrollingContent } from './Modal'
export { default as Popper } from './Popper'
export { default as Skeleton } from './Skeleton'
export { default as Text } from './Text'
export { default as TypingText } from './TypingText'

import { aliases, multiples, scales } from '~/components/misc/Mntr/constants'

// https://github.com/DefinitelyTyped/DefinitelyTyped/blob/eff7ed85c1a09706f9dcbb5bf3efcad5c50767b6/types/styled-system__css/index.d.ts#L20
export type ResponsiveStyleValue<T> = T | Array<T | null>

type AliasProps = {
  [K in keyof typeof aliases]?: ResponsiveStyleValue<number | string>
}

type MultipleProps = {
  [K in keyof typeof multiples]?: ResponsiveStyleValue<number | string>
}

type ScaleProps = {
  [K in keyof typeof scales]?: ResponsiveStyleValue<number | string>
}

export interface IStyleProps extends AliasProps, MultipleProps, ScaleProps {}
