import React from 'react'
import { Box, ButtonGroup, Flex, IStyleProps } from '~/components/misc/Mntr'
import { IButtonProps } from '~/components/misc/Mntr/ButtonGroup'

interface IModalScrollingContentProps extends IStyleProps, React.PropsWithChildren {
  children: React.ReactNode
}

export const ModalScrollingContent = (props: IModalScrollingContentProps) => {
  const { children, ...rest } = props
  return (
    <Box className="scroll-content-wrapper">
      <Box className="scroll-content" {...rest}>
        {children}
      </Box>
    </Box>
  )
}

interface IModalScrollingContentProps extends IStyleProps, React.PropsWithChildren {
  children: React.ReactNode
  contentPadding?: number
  gap?: number
  actions?: (IButtonProps | (() => void))[]
  actionsLeft?: IButtonProps[]
}

const Modal = (props: IModalScrollingContentProps) => {
  const { children, contentPadding = 0, gap = 3, actions, actionsLeft, ...rest } = props

  return (
    <>
      <ModalScrollingContent p={contentPadding} pb={0} {...rest}>
        {children}
      </ModalScrollingContent>
      {actions ? (
        <Flex column alignItems="end" p={contentPadding} pt={gap}>
          <ButtonGroup buttons={actions} buttonsLeft={actionsLeft} />
        </Flex>
      ) : null}
    </>
  )
}

export default Modal
