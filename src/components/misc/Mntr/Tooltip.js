import {
  arrow,
  autoUpdate,
  flip,
  FloatingArrow,
  FloatingPortal,
  offset,
  safePolygon,
  shift,
  useDismiss,
  useFloating,
  useFocus,
  useHover,
  useInteractions,
  useMergeRefs,
  useRole,
  useTransitionStatus,
  useTransitionStyles,
} from '@floating-ui/react'
import * as React from 'react'
import { useRef, useState } from 'react'

const Tooltip = ({
  arrow: withArrow,
  children,
  enterDelay = 0,
  enterTouchDelay = 0,
  interactive,
  offset: withOffset = 0,
  onOpenChange: setControlledOpen,
  open: controlledOpen,
  placement,
  style,
  title,
  zIndex = 9009,
}) => {
  const arrowRef = useRef(null)
  const [uncontrolledOpen, setUncontrolledOpen] = useState(false)
  const open = controlledOpen ?? uncontrolledOpen
  const setOpen = setControlledOpen ?? setUncontrolledOpen

  const { refs, floatingStyles, context, middlewareData } = useFloating({
    open: open,
    placement: placement,
    onOpenChange: setOpen,
    middleware: [
      withArrow ? arrow({ element: arrowRef }) : null,
      offset(withOffset),
      flip({
        fallbackAxisSideDirection: 'start',
      }),
      shift(),
    ],
    whileElementsMounted: autoUpdate,
  })

  const hover = useHover(context, {
    delay: {
      open: enterDelay || enterTouchDelay,
      close: 0,
    },
    ...(interactive ? { handleClose: safePolygon() } : {}),
    move: false,
  })
  const focus = useFocus(context)
  const dismiss = useDismiss(context)
  const role = useRole(context, { role: 'tooltip' })

  const { getFloatingProps, getReferenceProps } = useInteractions([hover, focus, dismiss, role])

  const ARROW_WIDTH = 30
  const ARROW_HEIGHT = 15
  const arrowX = middlewareData.arrow?.x ?? 0
  const arrowY = middlewareData.arrow?.y ?? 0
  const transformX = arrowX + ARROW_WIDTH / 2
  const transformY = arrowY + ARROW_HEIGHT

  const { isMounted, styles } = useTransitionStyles(context, {
    initial: {
      opacity: 0,
      transform: 'scale(0.9)',
    },
    common: ({ side }) => ({
      transitionDuration: '120ms',
      transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
      willChange: 'transform',
      transformOrigin: {
        top: `${transformX}px calc(100% + ${ARROW_HEIGHT}px)`,
        bottom: `${transformX}px ${-ARROW_HEIGHT}px`,
        left: `calc(100% + ${ARROW_HEIGHT}px) ${transformY}px`,
        right: `${-ARROW_HEIGHT}px ${transformY}px`,
      }[side],
    }),
  })

  const ref = useMergeRefs([refs.setReference])
  const { status } = useTransitionStatus(context)

  return (
    <>
      <FloatingPortal>
        {isMounted ? (
          <div
            ref={refs.setFloating}
            style={{
              ...floatingStyles,
              ...style,
              ...(status === 'close'
                ? {
                    pointerEvents: 'none',
                  }
                : {}),
              zIndex,
            }}
            {...getFloatingProps()}
          >
            <div style={{ ...styles }}>
              {title}
              {withArrow ? <FloatingArrow ref={arrowRef} context={context} /> : null}
            </div>
          </div>
        ) : null}
      </FloatingPortal>
      {React.isValidElement(children)
        ? React.cloneElement(React.Children.only(children), {
            ref: ref,
            ...getReferenceProps(),
          })
        : null}
    </>
  )
}

export default Tooltip
