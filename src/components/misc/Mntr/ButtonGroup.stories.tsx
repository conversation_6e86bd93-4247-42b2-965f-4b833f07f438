import type { Meta, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'
import { ButtonGroup, Flex } from '~/components/misc/Mntr'

const meta = {
  title: 'ButtonGroup',
  component: ButtonGroup,
  render({ buttons }) {
    return (
      <Flex>
        <ButtonGroup buttons={buttons} />
      </Flex>
    )
  },
} satisfies Meta<typeof ButtonGroup>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    buttons: [
      { label: 'Button 1', onClick: fn() },
      { label: 'Button 2', icon: 'close', onClick: fn() },
      { icon: 'close', onClick: fn() },
    ],
  },
}
