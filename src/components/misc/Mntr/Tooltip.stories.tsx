import type { Meta, StoryObj } from '@storybook/react'
import { Box } from '~/components/misc/Mntr'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'

const meta: Meta<typeof StyledTooltip> = {
  title: 'Tooltip',
  component: StyledTooltip,
  args: {
    tooltip: <Box>Tooltip content</Box>,
  },
  render(args) {
    return (
      <StyledTooltip {...args}>
        <button>Tooltip trigger</button>
      </StyledTooltip>
    )
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}
