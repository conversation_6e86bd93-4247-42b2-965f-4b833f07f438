import { t } from '@lingui/core/macro'
import { ConditionalWrapper, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

// TODO refactor MntrButton to tsx
export interface IButtonProps {
  bg?: string
  disabled?: boolean
  href?: string
  icon?: string
  label?: string | React.ReactNode
  onClick?: () => void
  rounded?: boolean
  target?: '_blank' | '_self' | '_parent' | '_top' | 'framename'
  type?: 'reset' | 'button' | 'submit'
  endIconElement?: React.ReactNode
}

interface IButtonGroupProps {
  buttons: (IButtonProps | (() => void) | null)[]
  buttonsLeft?: (IButtonProps | (() => void) | null)[]
  gap?: number
}

const ButtonGroup = ({ buttons = [], buttonsLeft, gap = 1 }: IButtonGroupProps) => {
  return (
    <ConditionalWrapper
      condition={!!buttonsLeft}
      wrapper={(children) => {
        return (
          <Flex justifyContent="space-between" centerY width={1}>
            <Flex gap={gap} flexWrap="wrap">
              {buttonsLeft?.map((button, index) => {
                return <MntrButton key={index.toString()} {...button} />
              })}
            </Flex>
            {children}
          </Flex>
        )
      }}
    >
      <Flex gap={gap} flexWrap="wrap">
        {buttons.map((button, index) => {
          if (typeof button === 'function') {
            return (
              <MntrButton
                key={index.toString()}
                {...{
                  bg: 'light',
                  rounded: true,
                  label: t`Close`,
                  onClick: button,
                }}
              />
            )
          } else {
            return <MntrButton key={index.toString()} {...button} />
          }
        })}
      </Flex>
    </ConditionalWrapper>
  )
}

export default ButtonGroup
