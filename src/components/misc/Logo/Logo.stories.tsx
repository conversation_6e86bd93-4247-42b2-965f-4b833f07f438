import type { Meta, StoryObj } from '@storybook/react'

import Logo from './Logo'

const meta = {
  title: 'Logo',
  component: Logo,
  render({ ...args }) {
    return (
      <div style={{ width: 300 }}>
        <Logo {...args} />
      </div>
    )
  },
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Logo>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}

export const Mono: Story = {
  args: {
    mono: true,
  },
  parameters: {
    backgrounds: {
      default: 'dark',
    },
  },
}
