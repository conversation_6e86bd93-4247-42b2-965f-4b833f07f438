import { cloneElement, CSSProperties, isValidElement, PropsWithChildren, ReactElement } from 'react'
import { css, styled } from 'styled-components'
import MediaIcon from '~/components/misc/MediaIcon/MediaIcon'
import { Box, Flex } from '~/components/misc/Mntr'

const Image = styled.img`
  position: absolute;
  height: 100%;
  width: 100%;
  inset: 0px;
  object-fit: cover;
  color: transparent;
`

type AvatarProps = {
  alt?: string
  className?: string
  color?: string
  iconColor?: string
  miniIcon?: ReactElement<React.ComponentProps<typeof MediaIcon>>
  shadow?: boolean
  size?: number
  src?: string
  style?: CSSProperties
}

const Wrapper = styled(Box)<AvatarProps>`
  position: relative;
  display: flex;
  flex-shrink: 0;
  border-radius: 50%;
  overflow: hidden;
  color: #fff;

  ${({ theme, color, size, shadow, iconColor }) => {
    return css`
      width: ${size}px;
      height: ${size}px;
      background-color: ${theme.colors[color as keyof typeof theme.colors] ||
      color ||
      theme.colors.defaultGrey};
      ${shadow ? 'box-shadow: 0 4px 5px rgba(0, 0, 0, 0.05);' : null}
      ${iconColor
        ? `color: ${theme.colors[iconColor as keyof typeof theme.colors]} !important`
        : null}
    `
  }}
`

function getInitials(alt: string) {
  return alt.split(' ').reduce((output, part) => output + part[0], '')
}

const MiniIconWrapper = styled.div`
  position: relative;
`

const MiniIconComponentWrapper = styled.div`
  position: absolute;
  right: -3px;
  bottom: -2px;
`

const MntrAvatar = ({
  alt = '',
  children,
  className,
  color,
  iconColor,
  miniIcon,
  shadow = false,
  size = 40,
  src,
  style,
  ...props
}: PropsWithChildren<AvatarProps>) => {
  return (
    <Box width={`${size}px`} height={`${size}px`}>
      <Wrapper className={className} color={color} shadow={shadow} size={size} style={style}>
        {src ? (
          <Image src={src} alt={alt} {...props} />
        ) : children ? (
          <Flex center width={1} height={1}>
            {children}
          </Flex>
        ) : alt ? (
          <Flex center width={1} height={1}>
            {getInitials(alt)}
          </Flex>
        ) : null}
      </Wrapper>
      <MiniIconWrapper>
        {miniIcon && isValidElement(miniIcon) && (
          <MiniIconComponentWrapper>
            {cloneElement(miniIcon, { size: 2 * Math.round(size / 4) })}
          </MiniIconComponentWrapper>
        )}
      </MiniIconWrapper>
    </Box>
  )
}

export default MntrAvatar
