import type { Meta, StoryObj } from '@storybook/react'
import MediaIcon from '~/components/misc/MediaIcon/MediaIcon'
import MntrAvatar from './MntrAvatar'

const meta = {
  title: 'MntrAvatar',
  component: MntrAvatar,
} satisfies Meta<typeof MntrAvatar>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    src: 'https://thispersondoesnotexist.com/',
  },
}

export const WithSize: Story = {
  args: {
    size: 22,
    src: 'https://thispersondoesnotexist.com/',
  },
}

export const WithShadow: Story = {
  args: {
    shadow: true,
    src: 'https://thispersondoesnotexist.com/',
  },
}

export const WithoutImageUsingAltText: Story = {
  args: {
    alt: 'Remy Sharp',
  },
}

export const WithoutImageUsingChildren: Story = {
  args: {
    children: 'K',
  },
}

export const WithoutMiniIcon: Story = {
  args: {
    miniIcon: <MediaIcon color="black" id={1} type="sentiment" />,
    src: 'https://thispersondoesnotexist.com/',
  },
}
