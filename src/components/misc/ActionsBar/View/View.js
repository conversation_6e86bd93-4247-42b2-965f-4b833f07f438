import { t } from '@lingui/core/macro'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'
import ViewMenu from './ViewMenu'

const View = ({
  appStore: {
    viewport: { isMobile },
  },
}) => {
  return (
    <MntrButton
      bg="flat"
      icon="view_stream"
      label={!isMobile && t`View`}
      popup={(closePopup) => {
        return (
          <ListScrollWrapper>
            <ViewMenu closePopup={closePopup} />
          </ListScrollWrapper>
        )
      }}
      popupPlacement="bottom-end"
      transformOrigin="100% 0"
    />
  )
}

export default observer(View)
