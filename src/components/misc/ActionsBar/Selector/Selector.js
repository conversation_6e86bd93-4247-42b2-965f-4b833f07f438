import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const selectCounts = [50, 100, 200]

const Selector = ({
  appStore: {
    monitoring: { feedMap },
    viewport: { isMobile },
  },
  feedId,
  items,
  selector: selectorFromProps,
  withSelectAll,
  withSelectCount,
}) => {
  const { pathname } = useRouter()
  const feed = feedMap.get(feedId)
  const { markVisible, markAll, mark, uncheckAll, isEmpty, visibleCount } =
    selectorFromProps || feed.selector
  const isTrash = pathname === '/trash'

  const menuItems = [
    isMobile && {
      label: t`Select`,
    },
    {
      label: (
        <>
          <Trans>Visible</Trans> {!isTrash && `(${visibleCount})`}
        </>
      ), // tbd fix trash
      onClick() {
        markVisible(items)
      },
    },
    withSelectCount &&
      selectCounts.map((count) => ({
        label: (
          <>
            <Trans id="selector.first">First</Trans> {count}
          </>
        ),
        onClick: () => mark(count, items),
      })),
    withSelectAll && {
      label: t`All`,
      onClick() {
        markAll(items)
      },
    },
    !isEmpty && {
      hoverVariant: 'error',
      label: t`None`,
      onClick() {
        uncheckAll()
      },
    },
  ]

  return (
    <MntrButton
      bg="flat"
      hoverable
      icon={isEmpty ? 'select_all' : 'indeterminate_check_box'}
      label={!isMobile && t`Select`}
      popup={(closePopup) => {
        return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
      }}
    />
  )
}

export default observer(Selector)
