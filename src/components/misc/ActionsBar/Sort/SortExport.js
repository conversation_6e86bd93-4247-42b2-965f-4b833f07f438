import { t } from '@lingui/core/macro'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import staticFeeds from '~/constants/staticFeeds'
import { observer } from '~/helpers/mst'

const SortExport = ({
  appStore: {
    monitoring: { feedMap },
    account,
    viewport: { isMobile },
  },
}) => {
  const feedMapItem = feedMap.get(staticFeeds.EXPORT_FEED)
  return (
    <MntrButton
      bg="flat"
      icon="sort"
      label={!isMobile && t`Sort`}
      popup={(closePopup) => {
        return (
          <MntrMenu
            menuItems={[
              {
                label: t`Sort List`,
              },
              ...(account.enums.export.export_order_by.map(({ id, text }) => {
                return {
                  label: text,
                  leftIcon: 'short_text',
                  onClick() {
                    feedMapItem.loadExportWithOrder(id)
                  },
                }
              }) || {}),
            ]}
            closePopup={closePopup}
          />
        )
      }}
      popupPlacement="bottom-end"
      transformOrigin="100% 0"
    />
  )
}

export default observer(SortExport)
