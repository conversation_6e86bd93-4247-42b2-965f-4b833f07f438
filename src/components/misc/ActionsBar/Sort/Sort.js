import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import MenuFilterOrderBy from '~/components/layout/MntrFiltersBar/modules/MenuFilterOrderBy'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'

const Sort = ({
  appStore: {
    account,
    viewport: { isMobile },
  },
  feedId,
}) => {
  const { pathname, query } = useRouter()

  return (
    <MntrButton
      bg="flat"
      icon="sort"
      label={!isMobile && t`Sort`}
      popup={(closePopup, forceOpen) => {
        if (feedId) {
          return (
            <MenuFilterOrderBy
              closePopup={closePopup}
              disableRedirect
              feedId={feedId}
              forceOpen={forceOpen}
            />
          )
        }

        return (
          <MntrMenu
            menuItems={[
              {
                label: t`Sort List`,
              },
              ...(account.enums.feed.order_by.map(({ id, text }) => {
                return {
                  href: { pathname, query: { ...query, order_by: id } },
                  label: text,
                  leftIcon: 'short_text',
                }
              }) || {}),
            ]}
            closePopup={closePopup}
          />
        )
      }}
      popupPlacement="bottom-end"
      transformOrigin="100% 0"
    />
  )
}

export default observer(Sort)
