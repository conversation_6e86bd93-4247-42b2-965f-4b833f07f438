import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import color from 'color'
import identityFn from 'lodash/identity'
import { useEffect } from 'react'
import { Field, Form } from 'react-final-form'
import { styled } from 'styled-components'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import { observer } from '~/helpers/mst'
import { Link } from '~/helpers/router'
import { getMessages } from './RefineArticles'

const Error = styled.p`
  color: ${({ theme }) => theme.colors.error};
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.66;
  margin: 0;
  margin-top: -0.5rem;
  padding: 0;
  text-align: left;
`

export const InputForm = ({
  closeModal,
  topics,
  onSubmit,
  appStore: {
    monitoring: {
      missingArticle,
      inspector: { forceToTopicMonitor },
    },
    appSettings: { appName, supportUrls },
  },
}) => {
  const items = [
    {
      id: null,
      label: '-',
      value: null,
    },
  ]

  useEffect(() => {
    missingArticle.reset()
  }, []) //eslint-disable-line

  topics.list.forEach((topic) => {
    items.push({
      activeBackground: color(topic.data.color).alpha(0.2).toString(),
      id: topic.id,
      hoverVariant: 'primary',
      label: topic.data.name,
      rightIconColor: 'check',
      rounded: false,
      topicId: topic.id,
      value: topic.id,
    })
  })

  return (
    <Form
      initialValues={{ articleUrl: '', topicMonitorId: null }}
      onSubmit={onSubmit}
      render={({ errors, handleSubmit, touched, values, submitErrors, form }) => {
        return (
          <form onSubmit={handleSubmit}>
            {missingArticle.currentStep === 0 && (
              <Flex flexWrap="nowrap" p={3} flexDirection="column">
                <Box mt="2" mb="3">
                  <Text fontSize="18px" textAlign="center" color="mediumGrey">
                    <Trans>
                      Can't find an article in your feed? Enter a link to the article you are
                      looking for and select a topic.
                    </Trans>
                  </Text>
                </Box>

                <Box mb={3} mt={4}>
                  <Field
                    autoFocus
                    autoComplete="off"
                    disabled={missingArticle.isLoading}
                    component={MntrTextFieldAdapter}
                    errorText={touched?.articleUrl ? errors?.articleUrl : null}
                    helperText={
                      <Trans>You can use an external link to the article or {appName} link.</Trans>
                    }
                    name="articleUrl"
                    parse={identityFn}
                    placeholder={t`Link to the article`}
                  />
                </Box>

                <div>
                  <Box>
                    <Text fontSize="12px" color="lightGrey">
                      <Trans>Search in topic</Trans>
                    </Text>
                  </Box>
                  <Box mb={1}>
                    <MntrSelectAdapter
                      disabled={missingArticle.isLoading}
                      meta={{}}
                      name="searchBarTopicSelector"
                      items={items.map((item) => ({
                        ...item,
                      }))}
                      onChange={(value) => {
                        const id = parseInt(value, 10) || null
                        form.change('topicMonitorId', id)
                        missingArticle.setTopicMonitorId(id)
                      }}
                      input={{ value: values.topicMonitorId }}
                    />
                  </Box>
                </div>
                {!values.topicMonitorId && submitErrors?.topicMonitorId && (
                  <Error>{submitErrors.topicMonitorId}</Error>
                )}

                {missingArticle.isLoading && (
                  <Flex mt={1} mb={1} center>
                    <MntrCircularProgress size={20} color="primary" />
                  </Flex>
                )}

                <MntrButton
                  bg="secondary"
                  mt={2}
                  disabled={!values.articleUrl || missingArticle.isLoading}
                  onClick={() => {
                    handleSubmit(values)
                  }}
                  label={<Trans>Search</Trans>}
                  type="submit"
                />

                {supportUrls && (
                  <Box mt={3}>
                    <Flex centerY flexWrap="nowrap" flexDirection="column">
                      <Text fontSize="12px" color="lightGrey">
                        <Trans>
                          Need help? Check our{' '}
                          <Link
                            style={{ color: 'inherit', textDecoration: 'underline' }}
                            href={supportUrls.howToUseUrl}
                            target="_blank"
                            color="lightGrey"
                          >
                            tutorial
                          </Link>{' '}
                          or contact us.
                        </Trans>
                      </Text>
                    </Flex>
                  </Box>
                )}
              </Flex>
            )}

            {missingArticle.currentStep === 1 && (
              <Flex flexWrap="nowrap" pb={3} flexDirection="column">
                <Flex centerY flexWrap="nowrap" p={(4, 2)} flexDirection="column">
                  {missingArticle.success ? (
                    <Trans>
                      We've added the article to the the topic "
                      {topics.getTopicNameById(missingArticle.topicMonitorId)}" and adjusted its
                      settings. The article will appear in your feed shortly.
                    </Trans>
                  ) : (
                    getMessages(missingArticle.message)
                  )}
                </Flex>

                {missingArticle.isLoading && (
                  <Flex mt={1} mb={1} center>
                    <MntrCircularProgress size={20} color="primary" />
                  </Flex>
                )}

                {missingArticle.automaticFixMessage && (
                  <Flex flexWrap="nowrap" p={3} pb={0} flexDirection="column">
                    <MntrButton
                      bg="secondary"
                      disabled={missingArticle.isLoading}
                      label={missingArticle.automaticFixMessage}
                      type="submit"
                    />
                  </Flex>
                )}

                {missingArticle.manualFixUrl && (
                  <Flex flexWrap="nowrap" p={3} pb={0} flexDirection="column">
                    <MntrButton
                      bg="secondary"
                      disabled={missingArticle.isLoading}
                      href={missingArticle.manualFixUrl}
                      label={
                        missingArticle.success ? (
                          <Trans>Display the article</Trans>
                        ) : (
                          missingArticle.manualFixMessage
                        )
                      }
                    />
                  </Flex>
                )}

                {missingArticle.showForceArticle && (
                  <Flex flexWrap="nowrap" p={3} pb={0} flexDirection="column">
                    <MntrButton
                      bg="secondary"
                      disabled={missingArticle.isLoading}
                      label={<Trans>Add article to topic</Trans>}
                      onClick={() => {
                        forceToTopicMonitor(
                          {
                            article_id: missingArticle.articleId,
                            token: missingArticle.articleToken,
                          },
                          missingArticle.topicMonitorId,
                        )
                        closeModal()
                      }}
                    />
                  </Flex>
                )}

                {missingArticle.showCreateArticle && (
                  <Flex flexWrap="nowrap" p={3} pb={0} flexDirection="column">
                    <MntrButton
                      bg="secondary"
                      disabled={missingArticle.isLoading}
                      href="/workspace-articles"
                      label={<Trans>Create Own Article</Trans>}
                    />
                  </Flex>
                )}
              </Flex>
            )}
          </form>
        )
      }}
    />
  )
}

export default observer(InputForm)
