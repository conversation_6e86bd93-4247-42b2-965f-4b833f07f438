import { Trans } from '@lingui/react/macro'
import { Box, Flex } from '~/components/misc/Mntr'
import Mntr<PERSON>utton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withWorkspaceArticleModal from '~/components/monitoring/WorkspaceArticles/withWorkspaceArticleModal'
import { observer } from '~/helpers/mst'
import InputForm from './InputForm'

export const getMessages = (messages) => {
  if (!messages || typeof messages !== 'string') return null
  const filtered = messages
    .split(/\n/)
    .map((i) => i.trim())
    .filter((i) => i.length)
  return filtered.map((message, index) => {
    return (
      <Box
        key={index.toString()}
        mb={index === filtered.length - 1 ? 0 : 3}
        style={{ textAlign: 'center' }}
      >
        {message}
      </Box>
    )
  })
}

const RefineArticles = ({
  appStore: {
    account,
    monitoring: { missingArticle },
    topics,
  },
}) => {
  return (
    <Flex centerY mr="1px">
      <MntrButton
        bg="flat"
        currentStep={missingArticle.currentStep}
        icon="more_vert"
        popupPlacement="bottom-end"
        popup={(closePopup) => {
          const menuItems = []

          if (account.workspace?.limits.allow_workspace_articles) {
            menuItems.push({
              leftIcon: 'post_add',
              label: <Trans>Create article</Trans>,
              ...withWorkspaceArticleModal(),
            })
          }

          menuItems.push({
            label: <Trans>Missing article</Trans>,
            leftIcon: 'find_in_page',
            modal: (closeModal) => {
              return (
                <Box>
                  <InputForm
                    closeModal={closeModal}
                    onSubmit={async ({ articleUrl, topicMonitorId }) => {
                      const errors = await missingArticle.solveMissingArticle(
                        { articleUrl, topicMonitorId },
                        missingArticle.currentStep === 0 ? false : true,
                        closeModal,
                      )
                      return errors // displays errors from BE
                    }}
                    topics={topics}
                  />
                </Box>
              )
            },
            modalTitle: <Trans>Missing article</Trans>,
            onClose: () => {
              missingArticle.reset()
            },
          })

          return (
            <MntrMenu
              menuItems={menuItems}
              closePopup={() => {
                missingArticle.reset()
                closePopup()
              }}
            />
          )
        }}
        size="default"
        transformOrigin="100% 0"
      />
    </Flex>
  )
}

export default observer(RefineArticles)
