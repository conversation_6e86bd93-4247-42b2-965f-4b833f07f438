import { styled } from 'styled-components'
import RefineArticles from '~/components/misc/ActionsBar/RefineArticles/RefineArticles'
import Selector from '~/components/misc/ActionsBar/Selector/Selector'
import Sort from '~/components/misc/ActionsBar/Sort/Sort'
import View from '~/components/misc/ActionsBar/View/View'
import { Box, Flex } from '~/components/misc/Mntr'
import { HEIGHT_HEADER_PX } from '~/constants'

const ActionsBarContainer = styled(Flex)`
  align-items: center;
  justify-content: space-between;
  height: ${HEIGHT_HEADER_PX}px;
  background: ${({ theme }) => theme.colors.background};
`

const ActionsBar = ({
  feedId,
  groups,
  items,
  selector,
  withSelectActions = true,
  withSelectAll = true,
  withSelectCount = true,
  withSort = true,
  withView = true,
  withRefineArticles = false,
}) => {
  return (
    <ActionsBarContainer px={['10px', '7px']}>
      <Flex>
        {withSelectActions && (
          <>
            {/* Selector Button */}
            <Box mr={1}>
              <Selector
                feedId={feedId}
                items={items}
                selector={selector}
                withSelectAll={withSelectAll}
                withSelectCount={withSelectCount}
              />
            </Box>

            {/* Selector - Multi Actions */}
            {groups.l?.filter(Boolean).map((group, index) => {
              return <Box key={`actions-bar-group-l-${index}`}>{group}</Box>
            })}
          </>
        )}
      </Flex>
      <Flex>
        {groups.r?.filter(Boolean).map((group, index) => {
          return <Box key={`actions-bar-group-r-${index}`}>{group}</Box>
        })}
        <Flex>
          {withSort && (
            <Box mr={1}>
              <Sort feedId={feedId} />
            </Box>
          )}
          {withView && <View />}
          {withRefineArticles && <RefineArticles />}
        </Flex>
      </Flex>
    </ActionsBarContainer>
  )
}

export default ActionsBar
