import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import { useRouter } from 'next/router'
import { Box, Heading } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

const MediaArchiveMessage = ({ appStore: { filter, account } }) => {
  const { pathname } = useRouter()

  return (
    <div style={{ textAlign: 'center' }}>
      <Box mt={1} mb={3} color="black">
        <Heading as="h1" fontSize={4} color="inherit">
          {!filter.isEmpty || pathname === '/crisis-communication' ? (
            <Trans>No results found</Trans>
          ) : (
            <Trans>Enter a word or phrase</Trans>
          )}
        </Heading>
      </Box>
      {(get(filter, 'data.query') || !account.workspace?.permissions.monitoring_feed.can_read) && (
        <MntrButton
          rounded
          bg="tertiary"
          href="/help/search"
          target="_blank"
          label={<Trans>Search help</Trans>}
        />
      )}
    </div>
  )
}

export default observer(MediaArchiveMessage)
