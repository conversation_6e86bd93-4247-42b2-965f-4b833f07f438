import { CSSProperties } from 'react'
import Icon from '~/components/misc/Icon/Icon'
import Avatar from '~/components/misc/MntrAvatar/MntrAvatar'
import SocialMediaIcon from '~/components/misc/SocialMediaIcon/SocialMediaIcon'
import socialNetwork from '~/constants/socialNetwork'

export function scaledSize(size: number) {
  return 2 * Math.floor(((size / 3) * 2) / 2)
}

const MediaIcon = ({
  avatarStyle: style,
  color,
  id,
  size = 40,
  type,
}: {
  avatarStyle?: CSSProperties
  color?: string
  id?: number
  size?: number
  type: 'tvr' | 'sentiment' | 'social_article_type' | 'social_news_source_category'
}) => {
  let icon

  switch (type) {
    case 'tvr':
      switch (id) {
        case 1:
          icon = 'radio'
          break

        case 2:
          icon = 'tv'
          break

        default:
          icon = 'label'
      }

      break

    case 'sentiment':
      switch (id) {
        case 1:
          icon = 'sentiment_very_satisfied'
          break

        case 2:
          icon = 'sentiment_very_dissatisfied'
          break

        case 3:
          icon = 'sentiment_satisfied'
          break

        case 4:
          icon = 'sentiment_satisfied'
          break

        default:
          icon = 'sentiment_dissatisfied'
      }

      break

    case 'social_article_type':
      switch (id) {
        case 300:
          icon = 'text_snippet'
          break

        case 301:
          icon = 'comment'
          break

        case 303:
          icon = 'share'
          break

        case 304:
          icon = 'forum'
          break

        case 305:
          icon = 'send'
          break
      }

      break

    case 'social_news_source_category':
      switch (id) {
        case socialNetwork.FACEBOOK:
          icon = 'facebook'
          break

        case socialNetwork.TWITTER:
          icon = 'twitter'
          break

        case socialNetwork.INSTAGRAM:
          icon = 'instagram'
          break

        case socialNetwork.YOUTUBE:
          icon = 'youtube'
          break

        case socialNetwork.LINKEDIN:
          icon = 'linkedin'
          break

        case socialNetwork.TIKTOK:
          icon = 'tiktok'
          break
        case socialNetwork.THREADS:
          icon = 'instagram'
          break
      }

      break
  }

  return (
    <Avatar color={color} size={size} style={{ ...style }}>
      {type === 'social_news_source_category' ? (
        <SocialMediaIcon icon={icon} size={scaledSize(size)} />
      ) : (
        <Icon size={scaledSize(size)}>{icon}</Icon>
      )}
    </Avatar>
  )
}

export default MediaIcon
