import type { Meta, StoryObj } from '@storybook/react'
import MediaIcon from './MediaIcon'

const meta = {
  title: 'MediaIcon',
  component: MediaIcon,
} satisfies Meta<typeof MediaIcon>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    type: 'tvr',
  },
}

export const Size: Story = {
  args: {
    size: 22,
    type: 'tvr',
  },
}

export const SocialArticleType: Story = {
  args: {
    color: 'green',
    id: 303,
    size: 22,
    type: 'social_article_type',
  },
}

export const SocialNewsSourceCategory: Story = {
  args: {
    color: 'blue',
    id: 400,
    size: 22,
    type: 'social_news_source_category',
  },
}
