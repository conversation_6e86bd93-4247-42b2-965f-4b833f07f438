import { css, styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { IStyleProps } from '~/components/misc/Mntr'

const basePaddingX = 16

interface IStyledMntrMenuHeadingProps extends IStyleProps {
  uppercase: boolean
  nestedLevel?: number
  color?: string
  paddingX?: number
}

const StyledMntrMenuHeading = styled.div<IStyledMntrMenuHeadingProps>`
  display: flex;
  align-items: center;
  padding: 4px ${({ paddingX }) => paddingX}px;
  font-size: ${({ fontSize }) => fontSize}px;
  text-align: left;
  min-height: 24px;
  gap: 5px;

  ${({ theme, color }) => {
    return (
      color &&
      css`
        color: ${theme.colors[color as keyof typeof theme.colors]};
      `
    )
  }}

  ${(props) =>
    props.uppercase
      ? css`
          text-transform: uppercase;
        `
      : null};

  ${({ nestedLevel, paddingX }) => {
    return (
      nestedLevel &&
      css`
        padding-left: ${paddingX ? paddingX : 0 + basePaddingX * nestedLevel}px;
      `
    )
  }}
`

const TruncatedText = styled.div`
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`

const MntrMenuHeading = ({
  fontSize = 14,
  label = '',
  noPadding = false,
  uppercase = true,
  color = 'mediumGrey',
  icon = '',
  ...otherProps
}: {
  fontSize?: number
  label?: string | React.ReactNode
  noPadding?: boolean
  uppercase?: boolean
  color?: string
  icon?: string
}) => {
  return (
    <StyledMntrMenuHeading
      className="menu-heading"
      fontSize={fontSize}
      paddingX={noPadding ? 0 : basePaddingX}
      uppercase={uppercase}
      color={color}
      {...otherProps}
    >
      {icon && <Icon size={26}>{icon}</Icon>}
      <TruncatedText>{label}</TruncatedText>
    </StyledMntrMenuHeading>
  )
}

export default MntrMenuHeading
