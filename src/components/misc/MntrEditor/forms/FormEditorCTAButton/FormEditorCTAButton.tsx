import { t } from '@lingui/core/macro'
import { FormApi } from 'final-form'
import { Field, FormProps } from 'react-final-form'
import { styled, useTheme } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import ColorPicker from '~/components/misc/ColorPicker/ColorPicker'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import isUrl, { addUrlScheme } from '~/helpers/isUrl'
import { observer } from '~/helpers/mst'

interface IPreviewWrapperProps {
  previewMobileDevice?: boolean
}

export const PreviewWrapper = styled(Box)<IPreviewWrapperProps>`
  position: relative;
  height: auto;
  flex: 1;
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  display: flex;
  border: 3px dashed ${({ theme }) => theme.paper.border};
  width: ${({ previewMobileDevice }) => (previewMobileDevice ? '375px' : '100%')};
  margin: 0 auto;

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    max-height: 90%;
    overflow-y: scroll;
  }
`

interface IStyledPreviewButtonProps {
  bg: string
}

export const StyledPreviewButton = styled.div<IStyledPreviewButtonProps>`
  display: inline-block;
  background: ${({ bg }) => bg};
  color: ${({ color }) => color};
  font-size: 26px;
  font-weight: 500;
  padding: 12px 38px;
  line-height: 1.22;
  border-radius: 9999px;
`

const FormEditorCTAButton = ({ initialValues, onSubmit }: IMntrFormProps) => {
  const theme = useTheme()
  // add protocol to url
  const handleSubmitWrapper = (
    values: FormProps['FormValues'],
    form: FormApi<FormProps['FormValues']>,
  ) => {
    const errors = validate(values)

    if (Object.keys(errors).length > 0) {
      return errors
    }

    if (values.url) {
      values.url = addUrlScheme(values.url)
    }

    return onSubmit(values, form)
  }

  interface IErrorsProps {
    label?: string
    url?: string
  }
  // form validation
  const validate = (values: FormProps['FormValues']) => {
    const errors: IErrorsProps = {}

    // validate empty label
    if (!values.label) {
      errors.label = t`This field is required.`
    }

    // validate empty url
    if (!values.url) {
      errors.url = t`This field is required.`
    }

    // validate malformed url
    if (values.url && !isUrl(values.url, { skipUrlScheme: true })) {
      errors.url = t`Malformed URL`
    }

    return errors
  }

  const formSchema: IFormSchemaItem[] = [
    {
      customComponent: ({ values, form }) => {
        return (
          <Flex flexDirection="column" gap={3}>
            <Box mb={3}>
              <Flex mt={2} gap={4}>
                {/* Left col - color pickers */}
                <Flex flexDirection="column" gap={3}>
                  {/* @ts-expect-error: refactor ColorPicker to TS */}
                  <ColorPicker
                    color={values.bg}
                    onColorChange={(color: string) => form?.change('bg', color)}
                    label={t`Background Color`}
                  />
                  {/* @ts-expect-error: refactor ColorPicker to TS */}
                  <ColorPicker
                    color={values.color}
                    onColorChange={(color: string) => form?.change('color', color)}
                    label={t`Text Color`}
                  />
                </Flex>
                {/* Right col - inputs */}
                <Flex flexDirection="column" gap={3} flex={1}>
                  <Field
                    autoFocus
                    // @ts-expect-error TODO refactor adapters to tsx
                    component={MntrTextFieldAdapter}
                    label={t`Label`}
                    name="label"
                  />
                  <Field
                    onBlur={() => {
                      form?.change('url', addUrlScheme(values.url))
                    }}
                    // @ts-expect-error TODO refactor adapters to tsx
                    component={MntrTextFieldAdapter}
                    label={t`Link`}
                    name="url"
                  />
                </Flex>
              </Flex>
            </Box>

            {/* Preview */}
            <Box my={2}>
              <MntrMenuHeading label={t`Preview`} />
              <PreviewWrapper>
                <Flex fontSize="14px" fontWeight="bold" width={1}>
                  {values.label && (
                    <Flex p={3} center width={1}>
                      <StyledPreviewButton bg={values.bg} color={values.color}>
                        {values.label}
                      </StyledPreviewButton>
                    </Flex>
                  )}

                  {!values.label && (
                    <Flex p={3} center width={1}>
                      <Text color="mediumGrey">
                        {'< '}
                        {t`Insert button label to view preview`}
                        {' >'}
                      </Text>
                    </Flex>
                  )}
                </Flex>
              </PreviewWrapper>
            </Box>
          </Flex>
        )
      },
    },
    {
      actions: () => [
        {
          label: t`Save`,
          type: 'submit',
          rounded: true,
          bg: 'secondary',
        },
      ],
    },
  ]

  return (
    <MntrForm
      contentPadding={3}
      schema={formSchema}
      onSubmit={handleSubmitWrapper}
      initialValues={{ bg: theme.colors.primary, color: '#fff', ...initialValues }}
    />
  )
}

export default observer(FormEditorCTAButton)
