import { t } from '@lingui/core/macro'
import { Field } from 'react-final-form'
import { styled, useTheme } from 'styled-components'

import MntrCmsEditorAdapter from '~/components/forms/adapters/MntrCmsEditorAdapter/MntrCmsEditorAdapter'
import ColorPicker from '~/components/misc/ColorPicker/ColorPicker'
import { Flex } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import { observer } from '~/helpers/mst'

interface IStyledEditorAdapterProps {
  bg: string
}

const StyledEditorAdapter = styled.div<IStyledEditorAdapterProps>`
  .editor-content {
    margin-top: 20px;
    background-color: ${({ bg }) => bg};
    border-radius: 10px;
    padding: 1px 20px;
    font-size: 20px;
    color: ${({ color }) => color};

    & a {
      color: ${({ color }) => color} !important;
      &:hover {
        text-decoration: none;
      }
    }
  }

  & .is-empty::before {
    color: rgba(255, 255, 255, 0.6) !important;
  }
`

const FormPromoBoxEditor = ({ initialValues, onSubmit }: IMntrFormProps) => {
  const theme = useTheme()
  const formSchema: IFormSchemaItem[] = [
    {
      customComponent: ({ values }) => {
        return (
          <StyledEditorAdapter color={values.color_text} bg={values.color_background}>
            <Field
              // @ts-expect-error forward ref
              component={MntrCmsEditorAdapter}
              autoFocus
              isVisibleMenuBar
              isVisibleBubbleMenu={false}
              transparent
              allowActions={['fontSize', 'textAlign']}
              name="content"
              placeholder={t`Enter text here...`}
            />
          </StyledEditorAdapter>
        )
      },
    },
    {
      customComponent: ({ form, values }) => {
        return (
          <Flex centerX gap={4}>
            <Flex>
              {/* @ts-expect-error: refactor ColorPicker to TS */}
              <ColorPicker
                color={values.color_background}
                onColorChange={(color: string) => form?.change('color_background', color)}
                label={t`Background Color`}
              />
            </Flex>
            <Flex>
              {/* @ts-expect-error: refactor ColorPicker to TS */}
              <ColorPicker
                color={values.color_text}
                onColorChange={(color: string) => form?.change('color_text', color)}
                label={t`Text Color`}
              />
            </Flex>
          </Flex>
        )
      },
    },
    {
      actions: () => [
        {
          label: t`Save`,
          type: 'submit',
          rounded: true,
          bg: 'secondary',
        },
      ],
    },
  ]
  return (
    <MntrForm
      contentPadding={3}
      schema={formSchema}
      onSubmit={onSubmit}
      initialValues={{
        color_text: '#fff',
        color_background: theme.colors.primary,
        ...initialValues,
      }}
    />
  )
}

export default observer(FormPromoBoxEditor)
