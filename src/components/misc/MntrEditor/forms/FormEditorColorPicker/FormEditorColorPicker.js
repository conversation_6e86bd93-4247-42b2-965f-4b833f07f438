import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'
import ColorPicker from '~/components/misc/ColorPicker/ColorPicker'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

const DEFAULT_COLORS = [
  '#0c60ff',
  '#04edb5',
  '#1f2f4e',
  '#2c3979',
  '#e57066',
  '#055a5b',
  '#776ea7',
  '#4f3a4b',
]

const StyledColorPickBox = styled(Box)`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin: 2px;
  cursor: pointer;
`

const StyledColorPickBoxWrapper = styled(Box)`
  border-radius: 50%;
  border: 2px solid ${({ theme, selected }) => (selected ? '#f0cf61' : theme.colors.veryLightGrey)};
`

const FormEditorColorPicker = ({ onSubmit, editor }) => {
  return (
    <Box pt={1} pb={2}>
      {/* Popup Heading */}
      <MntrMenuHeading label={t`Text Color`} />

      <Flex mx={3} flexDirection="column" gap={2}>
        {/* List of Default colors */}
        <Flex gap={1}>
          <MntrButton
            rounded
            icon="format_color_reset"
            tooltip={t`Clear Color`}
            onClick={() => onSubmit(null)}
          />

          {DEFAULT_COLORS.map((color) => {
            return (
              <StyledColorPickBoxWrapper
                key={color}
                selected={editor.getAttributes('textStyle').color === color}
              >
                <StyledColorPickBox bg={color} onClick={() => onSubmit(color)} />
              </StyledColorPickBoxWrapper>
            )
          })}
        </Flex>

        {/* Custom Color */}
        <Flex>
          <ColorPicker
            color={'#0f0'}
            onColorChangeComplete={(color) => onSubmit(color)}
            customElement={<MntrButton rounded icon="style" label={t`Custom color`} />}
          />
        </Flex>
      </Flex>
    </Box>
  )
}

export default FormEditorColorPicker
