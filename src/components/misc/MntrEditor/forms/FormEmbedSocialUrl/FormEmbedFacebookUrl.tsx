import { t } from '@lingui/core/macro'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import EmbedFacebook from '~/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook'

interface FormEmbedFacebookUrlProps {
  placeholder: string
  onSubmit: (values: { url: string }) => void
  onCancel: () => void
  initialValues: { url: string }
}

interface FormValues {
  url: string
}

interface ValidationErrors {
  [key: string]: string
}

const FormEmbedFacebookUrl: React.FC<FormEmbedFacebookUrlProps> = ({
  placeholder,
  onSubmit,
  onCancel,
  initialValues,
}) => {
  const formSchema: IFormSchemaItem[] = [
    {
      name: 'url',
      label: t`Facebook Post URL`,
      placeholder: placeholder,
    },
    {
      customComponent: ({ values }) => {
        return <EmbedFacebook item={{ url: values.url }} viewportWidth={580} />
      },
    },
  ]

  const validate = (values: FormValues): Promise<void> => {
    return new Promise((resolve, reject) => {
      const errors: ValidationErrors = {}

      if (!values.url) {
        errors.url = t`Malformed URL`
      }

      if (Object.keys(errors).length > 0) {
        reject(errors)
      } else {
        resolve()
      }
    })
  }

  const handleValidation = (values: FormValues): Promise<void> => {
    return validate(values)
      .then((res) => res)
      .catch((validationErrors) => {
        return Promise.reject(validationErrors)
      })
  }

  const handleSubmit = async (values: FormValues): Promise<void> => {
    try {
      await handleValidation(values)
      onSubmit(values)
    } catch (errors) {
      return errors
    }
  }

  return (
    <MntrForm
      contentPadding={3}
      schema={formSchema}
      initialValues={initialValues}
      subscription={{ values: true }}
      onSubmit={handleSubmit}
      onCancel={onCancel}
    />
  )
}

export default FormEmbedFacebookUrl
