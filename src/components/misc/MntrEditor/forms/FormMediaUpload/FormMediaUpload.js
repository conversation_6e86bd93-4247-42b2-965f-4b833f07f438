import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useEffect, useState } from 'react'
import { Field, Form } from 'react-final-form'
import { styled } from 'styled-components'
import MntrFileAdapter from '~/components/forms/adapters/MntrFileAdapter/MntrFileAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, ButtonGroup, Flex, Text } from '~/components/misc/Mntr'
import isUrl, { addUrlScheme } from '~/helpers/isUrl'
import { observer } from '~/helpers/mst'
import { scrollTo } from '~/helpers/scroll'
import UploadProgress from './UploadProgress'

const StyledImg = styled.img`
  max-width: 100%;

  border-radius: 10px;
  overflow: hidden;
`

const StyledVideo = styled.video`
  max-width: 100%;
`

const StyledImgWrapper = styled(Box)`
  text-align: center;
`

const FormMediaUpload = ({
  appStore: { newsroom, account },
  initialValues,
  editor,
  errors,
  closeModal,
  closePopup,
  isEdit,
  type, // 'image' || 'video'
  placeholderTitle,
  isAttachment,
  handleUploadMedia,
}) => {
  const [progress, setProgress] = useState(0)
  const [isImageEmpty, setImageEmpty] = useState(false)
  const [previewImage, setPreviewImage] = useState(null)

  // if edit, set preview image from initialValues
  useEffect(() => {
    if (isEdit && initialValues.src) {
      setPreviewImage(initialValues.src)
    }
  }, [isEdit, initialValues.src])

  // data to progress bar from upload response
  const onUploadProgress = (progressEvent) => {
    setProgress(Math.round((progressEvent.loaded * 100) / progressEvent.total))
  }

  // handle submit
  const onSubmit = (model) => {
    if (!isEdit && !model.payload) {
      setImageEmpty(true)
      return false
    }

    const data = {
      alt: model.image_alt,
      url: addUrlScheme(model.image_url),
    }

    if (isEdit && !model.payload) {
      editor.chain().focus().updateMedia(data).run()
      closeModal()
      if (typeof closePopup === 'function') {
        closePopup()
      }
      return false
    }

    const onUploadComplete = (response) => {
      const fileTypeId = response.file_type

      const fileType = account.enums.publishing.blog_media_file_type.find(
        (fileType) => fileType.id === fileTypeId,
      )

      // set preview to editor
      if (editor) {
        if (isEdit) {
          editor
            .chain()
            .focus()
            .updateMedia({
              src: response.url,
              type: fileType.type,
              content_type: fileType.content_type,
              ...data,
            })
            .run()
        } else {
          editor
            .chain()
            .focus()
            .setMedia({
              src: response.url,
              type: fileType.type,
              content_type: fileType.content_type,
              ...data,
            })
            .run()
        }
      }
      closeModal()
      if (typeof closePopup === 'function') {
        closePopup()
      }
    }

    const onUploadCompleteAttachment = (response) => {
      // add new attachment to store
      newsroom.selected.posts.attachments.add(response)
      scrollTo('#attachments')
      closeModal()
      if (typeof closePopup === 'function') {
        closePopup()
      }
    }

    if (typeof handleUploadMedia === 'function') {
      return handleUploadMedia(model, onUploadProgress, onUploadComplete)
    }

    newsroom.selected.uploadMedia(
      model,
      onUploadProgress,
      isAttachment ? onUploadCompleteAttachment : onUploadComplete,
      isAttachment,
    )
  }

  let placeholderIcon = type === 'image' ? 'image_search' : 'videocam'
  if (isAttachment) {
    placeholderIcon = 'attach_file'
  }

  return (
    <>
      <UploadProgress progress={progress} />

      {progress === 0 && (
        <Form
          onSubmit={onSubmit}
          validate={(values) => {
            const errors = {}

            if (values.image_url && !isUrl(values.image_url, { skipUrlScheme: true })) {
              errors.image_url = t`Malformed URL`
            }

            return errors
          }}
          initialValues={initialValues}
          render={({ handleSubmit, form }) => {
            return (
              <form onSubmit={handleSubmit} className="scroll-content-wrapper">
                <Box p={2} pb={0} className="scroll-content">
                  {isImageEmpty && (
                    <Text color="error">
                      <Trans>Please select Image</Trans>
                    </Text>
                  )}

                  <label htmlFor="file-input">
                    {previewImage && type === 'image' && (
                      <StyledImgWrapper>
                        <StyledImg src={previewImage} alt="preview-image" />
                      </StyledImgWrapper>
                    )}
                    {previewImage && type === 'video' && (
                      <StyledImgWrapper>
                        <StyledVideo src={previewImage} alt="preview-image" controls />
                      </StyledImgWrapper>
                    )}
                  </label>

                  <Field
                    placeholderIcon={placeholderIcon}
                    placeholder={placeholderTitle}
                    placeholderExtensions={account.enums.publishing.getExtensionsByType(type)}
                    accept={account.enums.publishing.getContentTypeByType(type)}
                    component={MntrFileAdapter}
                    errorText={errors?.payload}
                    renderLabel={!previewImage}
                    disable={previewImage}
                    name="file"
                    onChange={(event) => {
                      form.change('payload', event.target.value ? event.target.files[0] : null)

                      // load image to preview
                      var reader = new FileReader()
                      reader.onload = function (e) {
                        setPreviewImage(e.target.result)
                        if (isAttachment) {
                          handleSubmit()
                        }
                      }
                      reader.readAsDataURL(event.target.files[0])

                      setTimeout(() => {
                        const input = document.querySelector('input[name="image_alt"]')

                        if (input) {
                          input.focus()
                        }
                      }, 20)
                    }}
                    type="file"
                  />

                  {!isAttachment && (
                    <Flex gap={3} mt={2} flexDirection="column">
                      <Field
                        component={MntrTextFieldAdapter}
                        label={t`Description`}
                        name="image_alt"
                      />

                      {type === 'image' && (
                        <Field
                          component={MntrTextFieldAdapter}
                          label={`URL (${t`optional`})`}
                          name="image_url"
                        />
                      )}
                    </Flex>
                  )}
                </Box>
                <Flex column alignItems="end" p={3} pt={2}>
                  {/* TODO: refactor to MntrForm  */}
                  <ButtonGroup
                    buttons={[
                      {
                        label: t`Save`,
                        type: 'submit',
                        rounded: true,
                        bg: 'secondary',
                      },
                    ]}
                  />
                </Flex>
              </form>
            )
          }}
        />
      )}
    </>
  )
}

export default observer(FormMediaUpload)
