import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import { Box, Flex } from '~/components/misc/Mntr'
import {
  Label,
  ProgressBg,
  ProgressBox,
  ProgressValue,
} from '~/components/monitoring/Inspector/DemographicsData/modules/ProgressList/ProgressListContent'

const StyledProgressBox = styled(Box)`
  height: ${({ height }) => height}px !important;
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
`

const BoxFullWidth = styled(Box)`
  width: 100%;
`

const UploadProgress = ({ progress, height = 200 }) => {
  return (
    <>
      {progress > 0 && (
        <StyledProgressBox height={height} mb={2} display="flex" centerX centerY>
          <BoxFullWidth px={5} display="flex" flexDirection="column">
            <Flex flexWrap="nowrap" mb={1}>
              <Label width={[1 / 2]} fontSize={1}>
                <Trans>Loading...</Trans>
              </Label>
              <Label width={[1 / 2]} fontSize={1} style={{ textAlign: 'right' }}>
                {progress}%
              </Label>
            </Flex>
            <ProgressBox>
              <ProgressBg>
                <ProgressValue value={progress} aboveAverage={1} />
              </ProgressBg>
            </ProgressBox>
          </BoxFullWidth>
        </StyledProgressBox>
      )}
    </>
  )
}

export default UploadProgress
