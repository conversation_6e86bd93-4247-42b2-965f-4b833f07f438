import { t } from '@lingui/core/macro'
import { ValidationErrors } from 'final-form'
import { useState } from 'react'
import { Field, FormProps } from 'react-final-form'
import { styled, useTheme } from 'styled-components'

import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { PreviewWrapper } from '~/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton'
import HtmlCodeIframeView from '~/components/misc/MntrEditor/modules/HtmlCodeIframeView'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { ObservedFC, observer } from '~/helpers/msts'
import { IEmailingMergeTagStoreArrItem } from '~/store/models/account/enums/emailing/EmailingMergeTagStoreArrItem'

const StyledTextAreaFont = styled(Box)`
  height: 100%;

  & *:not(.error-message) {
    height: 100% !important;
  }

  & textarea {
    overflow-y: scroll;

    font-family:
      Menlo,
      Consolas,
      DejaVu Sans Mono,
      Monaco,
      Courier New,
      monospace;
  }
`

const insertTagAtCursor = (tag: string) => {
  const textarea = document.getElementById('code-input') as HTMLTextAreaElement
  if (!textarea) return

  textarea.focus()

  const cursorPosition = textarea.selectionStart
  const textBeforeCursor = textarea.value.substring(0, cursorPosition)
  const textAfterCursor = textarea.value.substring(cursorPosition)
  textarea.value = textBeforeCursor + tag + textAfterCursor

  // Move the cursor to the end of the inserted tag
  textarea.selectionStart = textarea.selectionEnd = cursorPosition + tag.length

  return textarea.value
}

interface IFormEditorHtmlCodeProps extends IMntrFormProps {
  mergeTags: IEmailingMergeTagStoreArrItem[] // reference to enums.emailing.merge_tag
}

const FormEditorHtmlCode: ObservedFC<IFormEditorHtmlCodeProps> = ({
  appStore: { account },
  initialValues,
  onSubmit,
  mergeTags, // reference to enums.emailing.merge_tag
}) => {
  const theme = useTheme()
  const [previewMobileDevice, setPreviewMobileDevice] = useState(false)
  const allowMergeTags = mergeTags.length > 0

  // form validation
  const validate = (values: FormProps['FormValues']) => {
    const errors: ValidationErrors = {}

    // validate empty field
    if (!values?.code) {
      errors.code = t`This field is required.`
    }

    return errors
  }

  const formSchema: IFormSchemaItem[] = [
    {
      customComponent: ({ values, form }) => {
        return (
          <Flex gap={3} height="100vh" flexDirection={['column', 'column', 'row']}>
            <Box width={1} height={1} maxHeight={[0.5, 0.5, 1]}>
              {/* Left column */}
              <Flex height={1} column>
                {/* Toolbar */}
                <Flex justifyContent="space-between" mb={3} centerY height="48px">
                  <MntrMenuHeading label={t`HTML`} noPadding />

                  {allowMergeTags && (
                    <div>
                      <MntrButton
                        label={t`Merge Tags`}
                        isChip={true}
                        size="default"
                        bg="default"
                        popupPlacement="bottom-end"
                        zIndex={6000}
                        popup={(closePopup) => {
                          const mergeTags = account.enums.emailing.merge_tag.toJSON()
                          const menuItems = mergeTags.map((item: IEmailingMergeTagStoreArrItem) => {
                            return {
                              leftIcon: 'short_text',
                              label: item.text,
                              onClick: () => {
                                closePopup()
                                form?.change('code', insertTagAtCursor(`${item.tag}`))
                              },
                            }
                          })

                          // @ts-expect-error TODO refactor MntrMenu to tsx */
                          return <MntrMenu menuItems={[...menuItems]} />
                        }}
                      />
                    </div>
                  )}
                </Flex>
                <StyledTextAreaFont>
                  <Field
                    autoFocus
                    multiline
                    // @ts-expect-error TODO refactor adapters to tsx
                    component={MntrTextFieldAdapter}
                    useReactTextareaAutosize={false}
                    minRows={10}
                    name="code"
                  />
                </StyledTextAreaFont>
              </Flex>
            </Box>
            {/* Right column */}
            <Flex width={1} maxHeight={[0.5, 0.5, 1]}>
              <Flex column height={1} width={1}>
                {/* Toolbar */}
                <Flex justifyContent="space-between" mb={3} centerY height="46px">
                  <MntrMenuHeading label={t`Preview`} noPadding />
                  <div>
                    <Box display={['none', 'block']}>
                      <Flex gap={1} p={1} centerX>
                        <MntrButton
                          icon="computer"
                          bg={!previewMobileDevice ? 'tertiary' : undefined}
                          iconColor={previewMobileDevice ? 'grey' : undefined}
                          onClick={() => setPreviewMobileDevice(false)}
                        />
                        <MntrButton
                          icon="smartphone"
                          bg={previewMobileDevice ? 'tertiary' : undefined}
                          iconColor={!previewMobileDevice ? 'grey' : undefined}
                          onClick={() => setPreviewMobileDevice(true)}
                        />
                      </Flex>
                    </Box>
                  </div>
                </Flex>
                <PreviewWrapper
                  height={1}
                  overflowY="scroll"
                  previewMobileDevice={previewMobileDevice}
                >
                  {!values.code && (
                    <Flex flex={1} fontSize="14px" fontWeight="bold" center>
                      <Text color="mediumGrey">
                        {'< '}
                        {t`Insert HTML code to view preview`}
                        {' >'}
                      </Text>
                    </Flex>
                  )}

                  {values.code && (
                    <Flex flex={1} fontSize="14px" fontWeight="bold">
                      <HtmlCodeIframeView value={values.code} />
                    </Flex>
                  )}
                </PreviewWrapper>
              </Flex>
            </Flex>
          </Flex>
        )
      },
    },
    {
      actions: () => {
        return [
          {
            label: t`Save`,
            type: 'submit',
            rounded: true,
            bg: 'secondary',
          },
        ]
      },
    },
  ]

  return (
    <MntrForm
      contentPadding={2}
      formGap={2}
      schema={formSchema}
      onSubmit={onSubmit}
      validate={validate}
      initialValues={{ bg: theme.colors.primary, color: '#fff', ...initialValues }}
      style={{ height: '100%' }}
    />
  )
}

export default observer(FormEditorHtmlCode)
