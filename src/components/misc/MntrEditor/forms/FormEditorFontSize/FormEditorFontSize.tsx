import { t } from '@lingui/core/macro'
import { Editor } from '@tiptap/core'
import { useCallback, useState } from 'react'

import { Box, ButtonGroup, Flex, Text } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import MntrSlider from '~/components/misc/MntrSlider/MntrSlider'

const MIN_FONT_SIZE = 8
const MAX_FONT_SIZE = 144
const DEFAULT_FONT_SIZE = 20

interface IEditorProps {
  editor: Editor
  onSubmit: () => void
}

const useFontSize = (editor: Editor, onSubmit: (value: number) => void) => {
  const initialSize = parseInt(editor.getAttributes('textStyle').fontSize) || DEFAULT_FONT_SIZE
  const [fontSize, setFontSize] = useState(initialSize)

  const resetFontSize = useCallback(() => {
    setFontSize(DEFAULT_FONT_SIZE)
    onSubmit(DEFAULT_FONT_SIZE)
  }, [onSubmit])

  const saveFontSize = useCallback(() => {
    onSubmit(fontSize)
  }, [fontSize, onSubmit])

  const actions = [
    ...(fontSize !== DEFAULT_FONT_SIZE
      ? [{ label: t`Reset`, bg: 'flat', onClick: resetFontSize }]
      : []),
    { label: t`Save`, bg: 'secondary', onClick: saveFontSize },
  ]

  return { fontSize, setFontSize, actions }
}

const FormEditorFontSize = ({ onSubmit, editor }: IEditorProps) => {
  const { fontSize, setFontSize, actions } = useFontSize(editor, onSubmit)
  const handleOnChange = (value: number | number[]) => {
    if (typeof value === 'number') {
      setFontSize(value)
    } else if (Array.isArray(value) && value.length > 0) {
      setFontSize(value[0])
    }
  }

  return (
    <Box pt={1} pb={0} width={'250px'}>
      <MntrMenuHeading label={t`Font Size`} />

      <Flex mx={3} flexDirection="column" gap={2}>
        <Flex gap={1} centerY>
          <Flex width="60px" justifyContent="flex-end" mr={2} position="relative" top="6px">
            <Text>
              <strong>{fontSize}</strong>px
            </Text>
          </Flex>
          <MntrSlider
            min={MIN_FONT_SIZE}
            max={MAX_FONT_SIZE}
            value={fontSize}
            onChange={handleOnChange}
          />
        </Flex>
      </Flex>
      <Flex column alignItems="end" p={3} pt={2}>
        <ButtonGroup buttons={actions} />
      </Flex>
    </Box>
  )
}

export default FormEditorFontSize
