import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { Editor } from '@tiptap/core'
import { useEffect, useState } from 'react'
import { Field } from 'react-final-form'
import { Box, Text } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'

import { ObservedFC, observer } from '~/helpers/msts'
import ImageGalleryAdapter from './ImageGalleryAdapter'
import ImageGalleryList from './ImageGalleryList'

interface IFormImageGalleryUploadProps {
  initialValues?: Record<string, unknown>
  closeModal: () => void
  closePopup?: () => void
  editor?: Editor
  gallery?: string[]
  isEdit?: boolean
}

const FormImageGalleryUpload: ObservedFC<IFormImageGalleryUploadProps> = ({
  appStore: { newsroom, account },
  initialValues,
  editor,
  closeModal,
  closePopup,
  isEdit,
  gallery,
}) => {
  const [isImageEmpty, setImageEmpty] = useState(false)
  const [disabledSubmit, setDisabledSubmit] = useState(false)

  useEffect(() => {
    newsroom.selected.extensionPreviewGallery.init(gallery)
  }, [gallery, newsroom.selected.extensionPreviewGallery])

  // handle submit
  const onSubmit = async () => {
    if (newsroom.selected.extensionPreviewGallery.fileSummaries.length === 0) {
      setImageEmpty(true)
      return // Return 'undefined' to indicate that the form submission failed
    }

    // Set preview to editor
    if (editor) {
      if (isEdit) {
        await editor
          .chain()
          .focus()
          // @ts-expect-error TODO refactor ExtensionImageGallery to tsx
          .updateImageGallery({
            gallery: JSON.stringify(newsroom.selected.extensionPreviewGallery.fileSummaries),
          })
          .run()
      } else {
        await editor
          .chain()
          .focus()
          // @ts-expect-error TODO refactor ExtensionImageGallery to tsx
          .setImageGallery({
            gallery: JSON.stringify(newsroom.selected.extensionPreviewGallery.fileSummaries),
          })
          .run()
      }
    }

    closeModal()
    if (typeof closePopup === 'function') {
      closePopup()
    }
  }

  const formSchema: IFormSchemaItem[] = [
    {
      customComponent: ({ form }) => {
        return (
          <Box>
            {isImageEmpty && (
              <Text color="error">
                <Trans>Please select Image</Trans>
              </Text>
            )}
            <Box>
              <Field
                placeholderExtensions={account.enums.publishing.getExtensionsByType('image')}
                accept={account.enums.publishing.getContentTypeByType('image')}
                // @ts-expect-error TODO refactor adapters to tsx
                component={ImageGalleryAdapter}
                name="file"
                height={newsroom.selected.extensionPreviewGallery.imagesList.length > 0 ? 100 : 200}
                form={form}
                onChange={(files: string[]) => {
                  setDisabledSubmit(true)
                  newsroom.selected.extensionPreviewGallery.uploadFiles(files).then(() => {
                    setDisabledSubmit(false)
                  })
                }}
              />
            </Box>
            <ImageGalleryList />
          </Box>
        )
      },
    },
    {
      actions: () => [
        {
          bg: 'light',
          rounded: true,
          label: t`Close`,
          onClick: () => closeModal(),
          disabled: disabledSubmit,
        },
        {
          label: t`Save`,
          type: 'submit',
          rounded: true,
          bg: 'secondary',
          disabled: disabledSubmit,
        },
      ],
    },
  ]

  return (
    <MntrForm
      contentPadding={2}
      formGap={2}
      schema={formSchema}
      onSubmit={onSubmit}
      initialValues={initialValues}
    />
  )
}

export default observer(FormImageGalleryUpload)
