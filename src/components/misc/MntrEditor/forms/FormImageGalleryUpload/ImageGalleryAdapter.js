import { Trans } from '@lingui/react/macro'
import { useEffect, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Text } from '~/components/misc/Mntr'

const StyledBox = styled(Box)`
  width: 100%;
  padding: 2px;
  position: relative;

  & .dropzone {
    width: 100%;
    height: ${({ height }) => height}px;
    background: ${({ theme }) => theme.paper.background};
    color: ${({ theme }) => theme.colors.black};
    justify-content: center;
    align-items: center;
    border-radius: 5px !important;
    border: 3px dashed ${({ theme }) => theme.paper.border};
    & span {
      font-size: 15px;
    }
  }
`

const ImageGalleryAdapter = ({ height = 200, onChange, placeholderExtensions }) => {
  const [files, setFiles] = useState([])
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/*': [],
    },
    onDrop: (acceptedFiles) => {
      const newFiles = acceptedFiles.map((file) =>
        Object.assign(file, {
          preview: URL.createObjectURL(file),
        }),
      )
      setFiles((prevFiles) => [...prevFiles, ...newFiles])
      onChange(newFiles)
    },
  })

  useEffect(() => {
    return () => files.forEach((file) => URL.revokeObjectURL(file.preview))
  }, [files])

  return (
    <StyledBox height={height}>
      <div {...getRootProps()}>
        <div className="dropzone">
          <input {...getInputProps()} />

          <Flex gap={1}>
            <Icon>image_search</Icon>

            <Text color="mediumGray" fontSize={1}>
              <Trans>Drag 'n' drop some images here, or click to select files</Trans>
            </Text>
          </Flex>

          {placeholderExtensions && (
            <Box mt={1}>
              <Text color="mediumGrey">[{placeholderExtensions.toUpperCase()}]</Text>
            </Box>
          )}
        </div>
      </div>
    </StyledBox>
  )
}

export default ImageGalleryAdapter
