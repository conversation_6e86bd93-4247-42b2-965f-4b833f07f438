import { t } from '@lingui/core/macro'
import { useRef, useState } from 'react'
import { styled } from 'styled-components'
import { Box, Flex, Heading } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import useThumbnailWheelScroll from '~/components/misc/MntrEditor/helpers/useThumbnailWheelScroll'
import {
  PreviewImage,
  ThumbnailImage,
  ThumbnailImageWrapper,
} from '~/components/misc/MntrEditor/styles/StyledExtensionImageGallery'
import MntrForm from '~/components/misc/MntrForm/MntrForm'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import { ObservedFC, observer } from '~/helpers/msts'
import viewTransition from '~/helpers/viewTransition'
import { IExtensionPreviewGalleryFileStoreArrItem } from '~/store/models/newsroom/blogs/extensions/ExtensionPreviewGalleryStore/ExtensionPreviewGalleryFileStoreArrItem'

const StyledPreviewImage = styled.div`
  position: relative;
  background: ${({ theme }) => theme.form.selectActiveBackground};
  display: flex;
  flex: 1;
  flex-direction: column;
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  border: 1px solid ${({ theme }) => theme.paper.border};
  padding: ${({ theme }) => theme.space[2]}px;
  gap: ${({ theme }) => theme.space[4]}px;

  & input {
    background: ${({ theme }) => theme.paper.background};
  }
`

const FormDropzoneGalleryList: ObservedFC = ({ appStore: { newsroom } }) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const thumbnailContainerRef = useRef(null)
  const imagesList = newsroom.selected.extensionPreviewGallery.imagesList

  useThumbnailWheelScroll(thumbnailContainerRef)

  const activeItem = imagesList[activeIndex]

  const formSchema = [{ name: 'image_description', label: t`Description` }]

  const moveItem = (index: number, direction: number) => {
    const newIndex = (index + direction + imagesList.length) % imagesList.length
    newsroom.selected.extensionPreviewGallery.moveItem(index, newIndex)
    setActiveIndex(newIndex)
  }

  const handleRemove = () => {
    const newIndex = activeIndex > 0 ? activeIndex - 1 : 0
    setActiveIndex(newIndex)
  }

  return (
    <>
      {activeItem && (
        <>
          <Box mb={1} mt={2}>
            <Heading fontSize={2}>{t`Images`}</Heading>
          </Box>
          <StyledPreviewImage>
            <Flex centerX>
              <PreviewImage
                src={activeItem.preview}
                alt={activeIndex.toString()}
                style={{ width: 'auto', maxHeight: '300px' }}
              />
            </Flex>
            {activeItem.id && (
              <Box position="absolute" top={0} right={2}>
                <MntrButton
                  icon="delete"
                  tooltip={t`Delete`}
                  onClick={() => {
                    activeItem.remove().then(() => {
                      handleRemove()
                    })
                  }}
                  bg="error"
                  mt={2}
                />
              </Box>
            )}
            <MntrForm
              autosubmit
              schema={formSchema}
              subscription={{ values: true }}
              initialValues={activeItem}
              onSubmit={(spy) => {
                activeItem.update(spy)
              }}
              visibleSubmitButton={false}
            />
          </StyledPreviewImage>
        </>
      )}
      {imagesList.length > 0 ? (
        <Box width="100%" overflowX="auto" mt={4} ref={thumbnailContainerRef}>
          {imagesList.map((item: IExtensionPreviewGalleryFileStoreArrItem, index: number) => {
            const imageSrc = item.file?.preview || item.image_src

            return (
              <Box key={index.toString()} display="inline-block" position="relative" pb={1}>
                <ThumbnailImageWrapper active={index === activeIndex} className="widget">
                  <ThumbnailImage
                    height={80}
                    width={130}
                    src={imageSrc}
                    alt={index.toString()}
                    onClick={() => setActiveIndex(index)}
                    complete={Boolean(item.id)}
                  />

                  {imagesList.length > 1 && (
                    <Flex
                      position="absolute"
                      bottom={2}
                      left={0}
                      right={0}
                      mx={1}
                      justifyContent="space-between"
                      className="visible-on-hover"
                    >
                      <MntrButton
                        size="small"
                        icon="chevron_left"
                        onClick={() =>
                          viewTransition(() => {
                            moveItem(index, -1)
                          })
                        }
                      />
                      <MntrButton
                        size="small"
                        icon="chevron_right"
                        onClick={() =>
                          viewTransition(() => {
                            moveItem(index, 1)
                          })
                        }
                      />
                    </Flex>
                  )}

                  {!item.id && (
                    <Flex position="absolute" top={0} left={0} right={0} bottom={0} center>
                      <MntrCircularProgress color="black" />
                    </Flex>
                  )}
                </ThumbnailImageWrapper>
              </Box>
            )
          })}
        </Box>
      ) : null}
    </>
  )
}

export default observer(FormDropzoneGalleryList)
