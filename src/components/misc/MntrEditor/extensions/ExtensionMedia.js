import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { styled } from 'styled-components'
import { Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalMediaUpload from '~/components/misc/MntrEditor/modals/withModalMediaUpload'
import { Paper, Wrapper } from '~/components/misc/MntrEditor/styles/StyledEmbed'

const CUSTOM_TAG = 'component-embed-media'

const StyledImg = styled.img`
  max-width: 100%;
  border-radius: 10px;
  overflow: hidden;
`
const StyledVideo = styled.video`
  max-width: 100%;
`

const StyledImgWrapper = styled.div`
  text-align: center;
`

const StyledAlt = styled(Text)`
  font-style: italic;
`

const Component = ({ node, editor, deleteNode, updateAttributes }) => {
  const { type, url } = node.attrs
  const { isEditable } = editor

  const toggleVisibleAlt = () => {
    const currentVisibleAlt = node.attrs.visible_alt
    updateAttributes({ visible_alt: !currentVisibleAlt })
  }

  return (
    <NodeViewWrapper className="embed-image">
      <div style={{ position: 'relative' }}>
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          {isEditable && (
            <>
              <div className="label">{type === 'image' && <Trans>Image</Trans>}</div>
              <div className="label">{type === 'video' && <Trans>Video</Trans>}</div>
              <div className="buttongroup">
                <MntrButton
                  tooltip={t`Edit`}
                  {...withModalMediaUpload({
                    editor: editor,
                    src: node.attrs.src,
                    url: node.attrs.url,
                    alt: node.attrs.alt,
                    type,
                    isEdit: true,
                  })}
                />
                <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
              </div>
            </>
          )}

          <div className="background">
            <Wrapper>
              {/* Image Preview */}
              {type === 'image' && (
                <StyledImgWrapper>
                  {url && (
                    <a href={url} target="_blank">
                      <StyledImg src={node.attrs?.src} alt={node.attrs?.alt} />
                    </a>
                  )}
                  {!url && <StyledImg src={node.attrs?.src} alt={node.attrs?.alt} />}
                </StyledImgWrapper>
              )}
              {/* Video Preview */}
              {type === 'video' && (
                <StyledImgWrapper>
                  <StyledVideo src={node.attrs?.src} alt={node.attrs?.alt} controls />
                </StyledImgWrapper>
              )}

              {node.attrs.alt && (
                <Flex>
                  <Flex width={'60px'}></Flex>
                  <Flex flex={1} centerX textAlign="center">
                    {node.attrs.visible_alt && (
                      <StyledAlt fontSize={3} color="mediumGrey">
                        {node.attrs.alt}
                      </StyledAlt>
                    )}
                  </Flex>

                  <Flex width={'60px'} justifyContent="flex-end">
                    <MntrButton
                      ml={1}
                      onClick={toggleVisibleAlt}
                      icon={node.attrs.visible_alt ? 'subtitles' : 'subtitles_off'}
                      bg="transparent"
                    />
                  </Flex>
                </Flex>
              )}
            </Wrapper>
          </div>
        </Paper>
      </div>
    </NodeViewWrapper>
  )
}

const ExtensionMedia = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  atom: true,

  draggable: true,

  addAttributes() {
    return {
      src: {
        default: '',
      },
      alt: {
        default: '',
      },
      visible_alt: {
        default: true,
      },
      url: {
        default: '',
      },
      content_type: '',
      type: '',
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setMedia:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
      updateMedia:
        (options) =>
        ({ commands }) => {
          return commands.updateAttributes(this.name, options)
        },
    }
  },
})

export default ExtensionMedia
