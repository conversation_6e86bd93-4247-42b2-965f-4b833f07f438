import { Extension } from '@tiptap/core'
import '@tiptap/extension-text-style'

const ExtensionFontSize = Extension.create({
  name: 'fontSize',

  addOptions() {
    return {
      types: ['textStyle'],
      getStyle: (fontSize) => {
        return `font-size: ${fontSize}`
      },
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontSize: {
            default: null,
            parseHTML: (element) => {
              if (element.style.fontSize) {
                element.style.fontSize.replace(/['"]+/g, '')
              }
              return null
            },
            renderHTML: (attributes) => {
              if (!attributes.fontSize) {
                return {}
              }

              return {
                style: this.options.getStyle(attributes.fontSize),
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      setFontSize:
        (fontSize) =>
        ({ chain }) => {
          return chain().setMark('textStyle', { fontSize }).run()
        },
      unsetFontSize:
        () =>
        ({ chain }) => {
          return chain().setMark('textStyle', { fontSize: null }).removeEmptyTextStyle().run()
        },
    }
  },
})

export default ExtensionFontSize
