import { Trans } from '@lingui/react/macro'
import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalHtmlCode from '~/components/misc/MntrEditor/modals/withModalHtmlCode'
import HtmlCodeIframeView from '~/components/misc/MntrEditor/modules/HtmlCodeIframeView'
import { Paper } from '~/components/misc/MntrEditor/styles/StyledEmbed'

const CUSTOM_TAG = 'component-html-code'

const Component = ({ editor, node, deleteNode }) => {
  const { isEditable } = editor
  return (
    <NodeViewWrapper>
      <Box position="relative">
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          {isEditable && (
            <>
              <div className="label">
                <Trans>Custom HTML Code</Trans>
              </div>
              <div className="buttongroup">
                <MntrButton
                  {...withModalHtmlCode({
                    editor,
                    initialValues: node.attrs,
                    mergeTags: editor.options.mergeTags,
                  })}
                />
                <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
              </div>
            </>
          )}

          <div className="background">
            <HtmlCodeIframeView value={node.attrs.code} />
          </div>
        </Paper>
      </Box>
    </NodeViewWrapper>
  )
}

const ExtensionHtmlCode = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  draggable: true,

  atom: true,

  addAttributes() {
    return {
      code: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setHtmlCode:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionHtmlCode
