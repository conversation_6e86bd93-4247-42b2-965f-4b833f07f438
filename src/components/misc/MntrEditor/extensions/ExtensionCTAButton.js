import { Trans } from '@lingui/react/macro'
import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { StyledPreviewButton } from '~/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton'
import withModalCTAButton from '~/components/misc/MntrEditor/modals/withModalCTAButton'
import { Paper } from '~/components/misc/MntrEditor/styles/StyledEmbed'

const CUSTOM_TAG = 'component-cta-button'

const Component = ({ editor, node, deleteNode }) => {
  const { isEditable } = editor
  return (
    <NodeViewWrapper>
      <Box position="relative">
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          {isEditable && (
            <>
              <div className="label">
                <Trans>CTA Button</Trans>
              </div>
              <div className="buttongroup">
                <MntrButton
                  {...withModalCTAButton({
                    editor,
                    color: node.attrs.color_background,
                    initialValues: node.attrs,
                  })}
                />
                <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
              </div>
            </>
          )}

          <div className="background">
            <Box bg={node.attrs.color_background}>
              <div dangerouslySetInnerHTML={{ __html: node.attrs.content }} />
              {node.attrs.label && node.attrs.url && (
                <Flex centerX className="promo-button">
                  <a href={node.attrs.url} target="_blank">
                    <StyledPreviewButton bg={node.attrs.bg} color={node.attrs.color}>
                      {node.attrs.label}
                    </StyledPreviewButton>
                  </a>
                </Flex>
              )}
            </Box>
          </div>
        </Paper>
      </Box>
    </NodeViewWrapper>
  )
}

const ExtensionCTAButton = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  atom: true,

  draggable: true,

  addAttributes() {
    return {
      url: {
        default: '',
      },
      bg: {
        default: '',
      },
      label: {
        default: '',
      },
      color: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setCTAButton:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionCTAButton
