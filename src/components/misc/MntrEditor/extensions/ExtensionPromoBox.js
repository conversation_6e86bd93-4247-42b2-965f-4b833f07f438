import { Trans } from '@lingui/react/macro'
import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { styled } from 'styled-components'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalPromoBox from '~/components/misc/MntrEditor/modals/withModalPromoBox'
import { Paper } from '~/components/misc/MntrEditor/styles/StyledEmbed'

const StyledPromoBox = styled(Box)`
  border-radius: 10px;
  padding: 1px 20px;
  font-size: 20px;

  & a {
    color: ${({ color }) => color} !important;
    &:hover {
      text-decoration: none;
    }
  }
`

const CUSTOM_TAG = 'component-promo-box'

const Component = ({ editor, node, deleteNode }) => {
  return (
    <NodeViewWrapper>
      <Box position="relative">
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          <div className="label">
            <Trans>Promo Box</Trans>
          </div>
          <div className="buttongroup">
            <MntrButton
              {...withModalPromoBox({
                editor,
                color: node.attrs.color_background,
                initialValues: node.attrs,
              })}
            />
            <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
          </div>

          <div className="background">
            <StyledPromoBox color={node.attrs.color_text} bg={node.attrs.color_background}>
              <div dangerouslySetInnerHTML={{ __html: node.attrs.content }} />
            </StyledPromoBox>
          </div>
        </Paper>
      </Box>
    </NodeViewWrapper>
  )
}

const ExtensionPromoBox = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  draggable: true,

  atom: true,

  addAttributes() {
    return {
      color_background: {
        default: '',
      },
      color_text: {
        default: '#fff',
      },
      content: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setPromoBox:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionPromoBox
