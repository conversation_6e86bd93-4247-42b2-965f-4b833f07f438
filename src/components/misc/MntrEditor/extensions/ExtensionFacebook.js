import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalEmbedFacebook from '~/components/misc/MntrEditor/modals/withModalEmbedFacebook'
import { Paper } from '~/components/misc/MntrEditor/styles/StyledEmbed'
import EmbedFacebook from '~/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook'

const CUSTOM_TAG = 'component-embed-facebook'

const Component = ({ editor, node, deleteNode }) => {
  return (
    <NodeViewWrapper className="embed-facebook">
      <div style={{ position: 'relative' }}>
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          <div className="label">Facebook</div>
          <div className="buttongroup">
            <MntrButton
              {...withModalEmbedFacebook({
                editor,
                url: node.attrs.url,
                isEdit: true,
              })}
            />
            <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
          </div>

          <div className="background">
            <EmbedFacebook item={{ url: node.attrs.url }} viewportWidth={600} />
          </div>
        </Paper>
      </div>
    </NodeViewWrapper>
  )
}

const ExtensionFacebook = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  atom: true,

  addAttributes() {
    return {
      url: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setFacebook:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionFacebook
