import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalEmbedYoutube from '~/components/misc/MntrEditor/modals/withModalEmbedYoutube'
import { Paper, Wrapper } from '~/components/misc/MntrEditor/styles/StyledEmbed'
import { convertYoutubeUrlToEmbedUrl } from '~/helpers/convertYoutubeUrl'

const Component = ({ node, editor, deleteNode }) => {
  return (
    <NodeViewWrapper className="embed-youtube">
      <Paper>
        <div className="label">Youtube</div>
        <div className="buttongroup">
          <MntrButton
            {...withModalEmbedYoutube({
              editor,
              url: node.attrs.url,
              isEdit: true,
              onUpdate: () => {
                deleteNode()
              },
            })}
          />
          <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
        </div>

        <div className="background">
          <Wrapper>
            <iframe
              src={convertYoutubeUrlToEmbedUrl(node.attrs.url)}
              frameBorder="0"
              allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </Wrapper>
        </div>
      </Paper>
    </NodeViewWrapper>
  )
}

const CUSTOM_TAG = 'component-embed-youtube'

const ExtensionYoutube = Node.create({
  name: CUSTOM_TAG,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  group: 'block',

  content: 'block+',

  atom: true,

  parseHTML() {
    return [{ tag: CUSTOM_TAG }]
  },

  addAttributes() {
    return {
      url: {
        default: null,
      },
    }
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setYoutube:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionYoutube
