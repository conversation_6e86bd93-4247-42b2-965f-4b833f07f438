import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { useRef, useState } from 'react'
import { styled } from 'styled-components'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import useThumbnailWheelScroll from '~/components/misc/MntrEditor/helpers/useThumbnailWheelScroll'
import withModalImageGallery from '~/components/misc/MntrEditor/modals/withModalImageGallery'
import { Paper, Wrapper } from '~/components/misc/MntrEditor/styles/StyledEmbed'
import {
  PreviewImage,
  ThumbnailImage,
  ThumbnailImageWrapper,
} from '~/components/misc/MntrEditor/styles/StyledExtensionImageGallery'

const CUSTOM_TAG = 'component-embed-image-gallery'

const StyledImageDescription = styled(Flex)`
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  position: absolute;
  bottom: 0;
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
`

const Component = ({ node, editor, deleteNode }) => {
  const gallery = JSON.parse(node.attrs.gallery)
  const imagesList = gallery.filter((item) => item.image_src)

  const [activeIndex, setActiveIndex] = useState(0)
  const thumbnailContainerRef = useRef(null)
  useThumbnailWheelScroll(thumbnailContainerRef)

  const activeItem = imagesList[activeIndex]

  const changeActiveIndex = (direction) => {
    setActiveIndex((prevIndex) => (prevIndex + direction + imagesList.length) % imagesList.length)
  }

  return (
    <NodeViewWrapper className="embed-image-gallery">
      <div style={{ position: 'relative' }}>
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          <div className="label">
            <Trans>Image</Trans>
          </div>
          <div className="buttongroup" style={{ zIndex: 1 }}>
            <MntrButton
              tooltip={t`Edit`}
              {...withModalImageGallery({
                editor: editor,
                gallery: imagesList,
                isEdit: true,
              })}
            />
            <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
          </div>

          <div className="background">
            <Wrapper className="widget">
              {activeItem && (
                <Flex center position="relative">
                  <PreviewImage
                    src={activeItem.file?.preview || activeItem.image_src}
                    alt={activeIndex.toString()}
                    style={{
                      width: 'auto',
                      maxWidth: '100%',
                      height: '400px',
                      objectFit: 'contain',
                    }}
                  />

                  {imagesList.length > 1 && (
                    <div className="visible-on-hover">
                      <Box position="absolute" left="0">
                        <MntrButton icon="chevron_left" onClick={() => changeActiveIndex(-1)} />
                      </Box>

                      <Box position="absolute" right="0">
                        <MntrButton icon="chevron_right" onClick={() => changeActiveIndex(1)} />
                      </Box>
                    </div>
                  )}

                  {activeItem.image_description && (
                    <StyledImageDescription px={2} py={1} mb={1}>
                      {activeItem.image_description}
                    </StyledImageDescription>
                  )}
                </Flex>
              )}

              {imagesList.length > 1 && (
                <Box
                  overflowX="auto"
                  mt={1}
                  ref={thumbnailContainerRef}
                  textWrap="nowrap"
                  width={['300px', '560px', '100%']}
                >
                  {imagesList.map((item, index) => {
                    const imageSrc = item.file?.preview || item.image_src

                    return (
                      <Box
                        key={index.toString()}
                        gap={1}
                        position="relative"
                        pb={2}
                        display="inline-block"
                      >
                        <ThumbnailImageWrapper active={index === activeIndex}>
                          <ThumbnailImage
                            height={80}
                            width={130}
                            src={imageSrc}
                            alt={index.toString()}
                            onClick={() => setActiveIndex(index)}
                            complete={Boolean(item.id)}
                          />
                        </ThumbnailImageWrapper>
                      </Box>
                    )
                  })}
                </Box>
              )}
            </Wrapper>
          </div>
        </Paper>
      </div>
    </NodeViewWrapper>
  )
}

const ExtensionImageGallery = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  atom: true,

  draggable: true,

  addAttributes() {
    return {
      gallery: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setImageGallery:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
      updateImageGallery:
        (options) =>
        ({ commands }) => {
          return commands.updateAttributes(this.name, options)
        },
    }
  },
})

export default ExtensionImageGallery
