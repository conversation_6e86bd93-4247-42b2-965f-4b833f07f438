import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { styled } from 'styled-components'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalEmbedVimeo from '~/components/misc/MntrEditor/modals/withModalEmbedVimeo'
import { Paper } from '~/components/misc/MntrEditor/styles/StyledEmbed'
import { convertVimeoUrlToEmbedUrl } from '~/helpers/convertVimeoUrl'

const VimeoContainer = styled(Box)`
  padding: 56.25% 0 0 0;
  position: relative;
`

const VimeoIframe = styled.iframe`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`

const Component = ({ editor, node, deleteNode }) => {
  return (
    <NodeViewWrapper className="embed-vimeo">
      <Paper>
        <div className="label">Vimeo</div>
        <div className="buttongroup">
          <MntrButton
            {...withModalEmbedVimeo({
              editor,
              url: node.attrs.url,
              isEdit: true,
              onUpdate: deleteNode,
            })}
          />
          <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
        </div>

        <div className="background">
          <VimeoContainer>
            <VimeoIframe
              src={convertVimeoUrlToEmbedUrl(node.attrs.url)}
              frameBorder="0"
              allow="autoplay; fullscreen; picture-in-picture"
              allowFullScreen
            ></VimeoIframe>
          </VimeoContainer>
        </div>
      </Paper>
    </NodeViewWrapper>
  )
}

const CUSTOM_TAG = 'component-embed-vimeo'

const ExtensionYoutube = Node.create({
  name: CUSTOM_TAG,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  group: 'block',

  content: 'block+',

  atom: true,

  parseHTML() {
    return [{ tag: CUSTOM_TAG }]
  },

  addAttributes() {
    return {
      url: {
        default: null,
      },
    }
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setVimeo:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionYoutube
