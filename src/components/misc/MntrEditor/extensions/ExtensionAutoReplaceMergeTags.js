import { Extension } from '@tiptap/core'

const ExtensionAutoReplaceMergeTags = Extension.create({
  name: 'autoReplaceMergeTags',

  addOptions() {
    return {
      mergeTags: [],
    }
  },
  onTransaction({ editor }) {
    const { mergeTags } = this.options
    let content = editor.getHTML()
    let isContentChanged = false

    // Define a function to generate the replacement string for a given tag
    const replacementString = (tag) =>
      `<span class="merge-tag" data-type="merge-tag" data-label=" ${tag} " contenteditable="false"></span>`

    // Preprocess to escape potential conflict characters in merge tags
    const escapeRegex = (string) => string.replace(/[-\\^$*+?.()|[\]{}]/g, '\\$&')

    // Iterate through the mergeTags to find and replace them in the editor content
    mergeTags.forEach((tagItem) => {
      const tagRegex = escapeRegex(tagItem.tag)
      const regex = new RegExp(`\\*\\|${tagRegex}\\|\\*`, 'g')

      // Apply replacement only if not inside <component-html-code ... code="...">
      if (!content.includes(`<component-html-code code="...${tagItem.tag}...`)) {
        content = content.replace(regex, () => {
          isContentChanged = true
          return replacementString(tagItem.tag)
        })
      }
    })

    // Set the new content only if changes were made
    if (isContentChanged) {
      editor.commands.setContent(content, { trigger: false }) // Prevent infinite loop
    }
  },
})

export default ExtensionAutoReplaceMergeTags
