import { Trans } from '@lingui/react/macro'
import { CommandProps, mergeAttributes, Node, NodeViewProps } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import Box from '~/components/misc/Mntr/Box'
import Flex from '~/components/misc/Mntr/Flex'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { Paper } from '~/components/misc/MntrEditor/styles/StyledEmbed'
import { WIDGET_TYPES } from '~/helpers/modal/withModalAddArticle/ModalAddArticle'
import {
  ArticleLink,
  ClickableBox,
  Perex,
  StyledImage,
  Title,
} from '~/helpers/modal/withModalAddArticle/styles/styles'

const CUSTOM_TAG = 'component-blog-post-widget'

const Component = ({ editor, node, deleteNode }: NodeViewProps) => {
  const { isEditable } = editor
  const { attrs } = node
  const { cover_image_url, title, perex, type } = attrs

  return (
    <NodeViewWrapper>
      <Box position="relative">
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          {isEditable && (
            <>
              <div className="label">
                <Trans>Article</Trans>
              </div>
              <div className="buttongroup">
                <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
              </div>
            </>
          )}

          <Box px={50}>
            <ClickableBox>
              <>
                {cover_image_url && <StyledImage src={cover_image_url} alt="Article" />}
                <Flex p={1} flexDirection="column">
                  {title && type !== WIDGET_TYPES.LINK && <Title>{title}</Title>}
                  {perex && <Perex>{perex}</Perex>}
                </Flex>
                {type === WIDGET_TYPES.LINK && <ArticleLink>{title}</ArticleLink>}
              </>
            </ClickableBox>
          </Box>
        </Paper>
      </Box>
    </NodeViewWrapper>
  )
}

const ExtensionArticle = Node.create({
  name: CUSTOM_TAG,
  group: 'block',
  content: 'block+',
  atom: true,
  draggable: true,
  addAttributes() {
    return {
      url: {
        default: '',
      },
      cover_image_url: {
        default: '',
      },
      title: {
        default: '',
      },
      perex: {
        default: '',
      },
      type: {
        default: '',
      },
    }
  },
  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },
  //@ts-expect-error: stfu
  addCommands() {
    return {
      setAddArticle:
        //@ts-expect-error: stfu

        (options) =>
          ({ commands, tr }: CommandProps) => {
            commands.wrapIn(this.name, options)

            // get position
            const { doc, selection } = tr
            const position = doc.resolve(selection.to + 1).end()

            return commands.setTextSelection(position)
          },
    }
  },
})

export default ExtensionArticle
