import { mergeAttributes, Node } from '@tiptap/core'

const ExtensionMergeTags = Node.create({
  name: 'merge-tag',

  addOptions() {
    return {
      HTMLAttributes: {},
      renderLabel({ node }) {
        return `${node.attrs.label ?? node.attrs.id}`
      },
    }
  },

  group: 'inline',

  inline: true,

  selectable: false,

  atom: true,

  addAttributes() {
    return {
      id: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-id'),
        renderHTML: (attributes) => {
          if (!attributes.id) {
            return {}
          }

          return {
            'data-id': attributes.id,
          }
        },
      },

      label: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-label'),
        renderHTML: (attributes) => {
          if (!attributes.label) {
            return {}
          }

          return {
            'data-label': attributes.label,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: `span[data-type="${this.name}"]`,
      },
    ]
  },

  renderHTML({ node, HTMLAttributes }) {
    return [
      'span',
      mergeAttributes({ 'data-type': this.name }, this.options.HTMLAttributes, HTMLAttributes),
      this.options.renderLabel({
        options: this.options,
        node,
      }),
    ]
  },

  renderText({ node }) {
    return this.options.renderLabel({
      options: this.options,
      node,
    })
  },

  addCommands() {
    return {
      // Example usage: editor.commands.insertMergeTag({ tag: item.tag })
      insertMergeTag:
        (item) =>
        ({ commands, chain }) => {
          // Insert merge tag
          commands.insertContent(
            `<span class="merge-tag" data-type="merge-tag" data-label="${item.tag}" contenteditable="false" />`,
          )

          // Focus the editor
          chain().focus().run()
        },
    }
  },
})

export default ExtensionMergeTags
