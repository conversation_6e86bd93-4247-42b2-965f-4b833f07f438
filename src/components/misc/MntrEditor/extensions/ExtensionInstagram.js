import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalEmbedInstagram from '~/components/misc/MntrEditor/modals/withModalEmbedInstagram'
import { Paper, Wrapper } from '~/components/misc/MntrEditor/styles/StyledEmbed'
import EmbedInstagram from '~/components/monitoring/Inspector/InspectorSource/MediaView/EmbedInstagram/EmbedInstagram'

const CUSTOM_TAG = 'component-embed-instagram'

const Component = ({ editor, node, deleteNode }) => {
  return (
    <NodeViewWrapper className="embed-instagram">
      <div style={{ position: 'relative' }}>
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          <div className="label">Instagram</div>
          <div className="buttongroup">
            <MntrButton
              {...withModalEmbedInstagram({
                editor,
                url: node.attrs.url,
                isEdit: true,
              })}
            />
            <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
          </div>

          <div className="background">
            <Wrapper>
              <EmbedInstagram item={{ url: node.attrs.url }} width={600} />
            </Wrapper>
          </div>
        </Paper>
      </div>
    </NodeViewWrapper>
  )
}

const ExtensionInstagram = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  atom: true,

  addAttributes() {
    return {
      url: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setInstagram:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionInstagram
