import { mergeAttributes, Node } from '@tiptap/core'
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalEmbedTwitter from '~/components/misc/MntrEditor/modals/withModalEmbedTwitter'
import { Paper, Wrapper } from '~/components/misc/MntrEditor/styles/StyledEmbed'
import EmbedTwitter from '~/components/monitoring/Inspector/InspectorSource/MediaView/EmbedTwitter/EmbedTwitter'

const CUSTOM_TAG = 'component-embed-twitter'

const Component = ({ node, editor, deleteNode }) => {
  return (
    <NodeViewWrapper className="embed-twitter">
      <div style={{ position: 'relative' }}>
        <div className="drag-handle" contentEditable="false" draggable="true" data-drag-handle />

        <Paper contentEditable="false">
          <div className="label">X.com</div>
          <div className="buttongroup">
            <MntrButton
              {...withModalEmbedTwitter({
                editor: editor,
                url: node.attrs.url,
                isEdit: true,
              })}
            />
            <MntrButton ml={1} onClick={deleteNode} icon="close" bg="error" />
          </div>

          <div className="background">
            <Wrapper>
              <EmbedTwitter item={{ url: node.attrs.url }} />
            </Wrapper>
          </div>
        </Paper>
      </div>
    </NodeViewWrapper>
  )
}

const ExtensionTwitter = Node.create({
  name: CUSTOM_TAG,

  group: 'block',

  content: 'block+',

  atom: true,

  addAttributes() {
    return {
      url: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: CUSTOM_TAG,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [CUSTOM_TAG, mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(Component)
  },

  addCommands() {
    return {
      setTwitter:
        (options) =>
        ({ commands, tr }) => {
          commands.wrapIn(this.name, options)

          // get position
          const { doc, selection } = tr
          const position = doc.resolve(selection.to + 1).end()

          return commands.setTextSelection(position)
        },
    }
  },
})

export default ExtensionTwitter
