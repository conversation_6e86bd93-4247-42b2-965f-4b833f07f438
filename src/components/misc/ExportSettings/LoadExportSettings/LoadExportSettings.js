import { Trans } from '@lingui/react/macro'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'

const LoadExportSettings = ({
  appStore: {
    viewport: { isMobile },
  },
  exportList,
}) => {
  const { exportSettings } = exportList
  return (
    <>
      {exportSettings.list && (
        <MntrButton
          bg="flat"
          label={!isMobile && <Trans>Load</Trans>}
          icon={'restore_page'}
          popup={(closePopup) => {
            const menuItems = []

            exportSettings.list.map((settingsItem, index) => {
              menuItems.push({
                label: settingsItem.name,
                onClick: () => {
                  exportSettings.applySettings(index)
                  closePopup()
                },
                actions: () => [
                  {
                    leftIcon: 'delete',
                    label: <Trans>Delete</Trans>,
                    onClick: () => {
                      exportSettings.deleteById(settingsItem.id)
                      closePopup()
                    },
                  },
                ],
              })
            })

            return (
              <Box width={1} minWidth="200px">
                <Box pt={1}>
                  <MntrMenuHeading label={<Trans>Load settings</Trans>} />
                </Box>
                {exportSettings.list && exportSettings.list.length === 0 && (
                  <Box px={3} pb={2}>
                    <Trans>You have not saved any settings</Trans>
                  </Box>
                )}
                {menuItems.length > 0 && (
                  <Box maxHeight="500px" overflowY="auto" overflowX="hidden">
                    <MntrMenu menuItems={menuItems} />
                  </Box>
                )}
              </Box>
            )
          }}
        />
      )}
    </>
  )
}

export default observer(LoadExportSettings)
