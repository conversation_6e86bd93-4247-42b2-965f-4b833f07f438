import { Trans } from '@lingui/react/macro'
import { Box, Flex } from '~/components/misc/Mntr'
import Mntr<PERSON>utton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import { observer } from '~/helpers/mst'
import FormSaveExportSettings from './FormSaveExportSettings'

const SaveExportSettings = ({
  appStore: {
    viewport: { isMobile },
  },
  exportList,
}) => {
  const { exportSettings } = exportList
  return (
    <>
      {exportSettings.list && (
        <MntrButton
          bg="flat"
          label={!isMobile && <Trans>Save</Trans>}
          icon={'save'}
          popup={(closePopup) => {
            const menuItems = []

            exportSettings.list.map((settingsItem) => {
              menuItems.push({
                id: settingsItem.id,
                label: settingsItem.name,
                onClick: () => {
                  exportSettings.saveAs(settingsItem.id)
                  closePopup()
                },
                actions: () => [
                  {
                    leftIcon: 'delete',
                    label: <Trans>Delete</Trans>,
                    onClick: () => {
                      exportSettings.deleteById(settingsItem.id)
                      closePopup()
                    },
                  },
                ],
              })
            })

            return (
              <>
                <Box width={1} minWidth="200px">
                  <Box pt={1}>
                    <MntrMenuHeading label={<Trans>Save settings</Trans>} />
                  </Box>
                  <FormSaveExportSettings
                    onSubmit={(model) => {
                      exportSettings.save(model)
                      closePopup()
                    }}
                  />
                  {exportSettings.list && Boolean(exportSettings.list.length) && (
                    <Flex flexDirection="column">
                      <MntrMenuHeading label={<Trans>Save as</Trans>} />
                      {menuItems.length > 0 && (
                        <Box maxHeight="500px" overflowY="auto" overflowX="hidden">
                          <MntrMenu menuItems={menuItems} />
                        </Box>
                      )}
                    </Flex>
                  )}
                </Box>
              </>
            )
          }}
        />
      )}
    </>
  )
}

export default observer(SaveExportSettings)
