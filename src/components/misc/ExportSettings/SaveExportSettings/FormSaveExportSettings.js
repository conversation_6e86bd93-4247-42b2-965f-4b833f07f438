import { t } from '@lingui/core/macro'
import { Field, Form } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const FormSaveExportSettings = ({ onSubmit }) => {
  return (
    <Form
      onSubmit={onSubmit}
      render={({ handleSubmit, pristine, invalid }) => {
        return (
          <form onSubmit={handleSubmit}>
            <Flex centerY mt="-20px" pb={2} justifyContent="space-between" pl={3} pr="10px">
              <Box width={1}>
                <Field
                  autoFocus
                  autoComplete="off"
                  name="name"
                  component={MntrTextFieldAdapter}
                  placeholder={t`Label`}
                />
              </Box>
              <Box>
                <MntrButton
                  icon={'add'}
                  mt={2}
                  type="submit"
                  size="default"
                  disabled={pristine || invalid}
                />
              </Box>
            </Flex>
          </form>
        )
      }}
    />
  )
}

export default FormSaveExportSettings
