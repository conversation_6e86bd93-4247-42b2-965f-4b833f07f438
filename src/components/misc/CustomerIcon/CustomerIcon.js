import Avatar from '~/components/misc/MntrAvatar/MntrAvatar'
import { observer } from '~/helpers/mst'

const hashCode = (str) => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }

  return hash
}

const intToRGB = (i) => {
  const c = (i & 0x00ffffff).toString(16).toUpperCase()

  return '00000'.substring(0, 6 - c.length) + c
}

const CustomerIcon = ({ customer, size = 40 }) => {
  const char = customer.name.charAt(0).toUpperCase()
  const background = (() => `#${intToRGB(`${hashCode(customer.name)}${customer.id}`)}`)()

  if (customer.logo_image_url) {
    return (
      <img
        src={customer.logo_image_url}
        width={size}
        height={size}
        style={{ borderRadius: '50%' }}
        alt=""
      />
    )
  }

  return (
    <Avatar
      color={background}
      size={size}
      style={{
        fontSize: size / 2.2,
      }}
    >
      {char}
    </Avatar>
  )
}

export default observer(CustomerIcon)
