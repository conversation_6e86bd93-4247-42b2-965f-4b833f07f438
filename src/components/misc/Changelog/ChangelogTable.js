import { Trans } from '@lingui/react/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import ChangelogTableRow from '~/components/misc/Changelog/ChangelogTableRow'
import { Flex } from '~/components/misc/Mntr'
import { Table, TableBody, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import ResponsiveTable from '~/components/misc/ResponsiveTable/ResponsiveTable'
import { observer } from '~/helpers/mst'

const ChangelogTable = ({
  appStore: {
    account: {
      user: { isAdmin },
    },
    viewport,
  },
  data,
}) => {
  return (
    <ResponsiveTable active={viewport.isTablet}>
      <MntrPaper>
        <Table
          light
          style={{
            ...(viewport.isTablet ? { tableLayout: 'auto' } : {}),
          }}
        >
          <TableBody>
            <Tr style={{ height: 55 }}>
              <Th style={{ width: 45 }}>
                <Trans>Date & User</Trans>
              </Th>
              <Th style={{ width: 60 }}>
                <Trans>Object</Trans>
              </Th>
              <Th style={{ width: 180 }}>
                <Trans>Change Type</Trans>
              </Th>
              <Th pr={isAdmin ? '0' : '12'} style={{ width: 40 }}>
                <Flex flexDirection="column" justifyContent="flex-end" alignItems="end">
                  <Trans>Revert</Trans>
                </Flex>
              </Th>
              {isAdmin && <Th style={{ width: 20 }}></Th>}
            </Tr>
            {data.results.map((item, key) => (
              <ChangelogTableRow item={item} key={key} />
            ))}
          </TableBody>
        </Table>
      </MntrPaper>
    </ResponsiveTable>
  )
}

export default observer(ChangelogTable)
