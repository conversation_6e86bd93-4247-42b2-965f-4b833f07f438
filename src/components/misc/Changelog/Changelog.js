import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import ChangelogTable from '~/components/misc/Changelog/ChangelogTable'
import ListHeading from '~/components/misc/ListHeading/ListHeading'
import Loader from '~/components/misc/Loader/Loader'
import { Box } from '~/components/misc/Mntr'
import Pagination from '~/components/misc/Pagination/Pagination'
import * as format from '~/helpers/formatNumber'
import { getConfigTagsTopicsDisabled } from '~/helpers/getActiveFiltersConfig'
import { observer } from '~/helpers/mst'

const Changelog = ({
  appStore: {
    changelog: { data, isLoading },
  },
  backButton,
  paginationOnChange,
}) => {
  const isEmpty = data?.results?.length === 0

  return (
    <>
      <MntrFiltersBar filters={['Date', 'ChangeType']} backButton={backButton} />
      <MntrActiveFilters config={getConfigTagsTopicsDisabled()} />
      {!isLoading && (
        <>
          <ListHeading text={format.formatChanges(data?.total_count)} results={data?.results} />
          {data && !isEmpty && (
            <>
              <ChangelogTable data={data} />
              <Box textAlign="center">
                <Pagination
                  activePage={data.current_page}
                  itemsCountPerPage={data.page_size}
                  totalItemsCount={data.total_count}
                  onChange={paginationOnChange}
                />
              </Box>
            </>
          )}
        </>
      )}
      {isLoading && <Loader />}
    </>
  )
}

export default observer(Changelog)
