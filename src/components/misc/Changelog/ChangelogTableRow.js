import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { isBefore, isThisMonth, isValid, parse, startOfDay } from 'date-fns'
import { useEffect, useRef, useState } from 'react'
import { DayPicker } from 'react-day-picker'
import { css, styled } from 'styled-components'

import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { Td, Tr } from '~/components/misc/MntrTable/MntrTable'
import { WEEK_STARTS_ON } from '~/constants'
import changelogChangeTypes from '~/constants/changelogChangeTypes'
import formatDate from '~/helpers/date/format'
import { observer } from '~/helpers/mst'

export const DATE_FORMAT = 'yyyy-MM-dd'

const today = new Date()

const StyledDiv = styled.div`
  ${({ expanded }) => {
    return css`
      white-space: ${expanded ? 'normal' : 'nowrap'};
      text-overflow: ellipsis;
      overflow: hidden;
    `
  }}
`

export const StyledRow = styled(Tr)`
  height: ${({ height }) => `${height}px`};
  padding-top: 5px;
  padding-bottom: 5px;
  &:hover {
    cursor: pointer;
    background: ${({ theme }) => theme.paper.backgroundHover};
  }
`

const ClickableArea = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
`

const StyledTd = styled(Td)`
  padding-top: 15px;
  padding-bottom: 15px;
  max-width: ${({ isTablet }) => (isTablet ? '50vw' : 'unset')};
`

const ChangelogTableRow = ({
  appStore: {
    account: {
      user: { isAdmin },
    },
    viewport,
  },
  item,
}) => {
  const {
    created,
    changed_by,
    change_type,
    model,
    object_str,
    fieldname,
    old_value,
    new_value,
    is_salesman,
    diff_count,
    can_revert,
    revert_on,
    is_revert_completed,
    revert_requested_by,
    revert,
  } = item
  const ref = useRef()
  const [isExpanded, setIsExpanded] = useState(false)
  const [selected, setSelected] = useState()
  const [month, setMonth] = useState(today)
  const ROW_HEIGHT = 70

  const changedText = (() => {
    let text = `${old_value ?? ''}
    ${change_type.id === changelogChangeTypes.CHANGED_VALUE ? '→' : ''} ${new_value ?? ''}`

    return text
  })()

  useEffect(() => {
    const parsedDate = parse(revert_on, DATE_FORMAT, today)
    setSelected(parsedDate)
    isValid(parsedDate) && !isThisMonth(parsedDate) && setMonth(parsedDate)
  }, [revert_on])

  const toggleExpand = () => setIsExpanded(!isExpanded)

  return (
    <StyledRow hover height={ROW_HEIGHT}>
      <Td onClick={toggleExpand}>
        <Flex flexDirection="column">
          <Text fontWeight="bold">{formatDate(created, 'dd. MM. yyyy HH:mm')}</Text>
          <Flex centerY>
            <Text mr="2px" color="lightGrey">
              {changed_by}
            </Text>
            {is_salesman && (
              <StyledTooltip tooltip={t`Account manager`}>
                <Flex centerY>
                  <Icon size={15} color="lightGrey">
                    support_agent
                  </Icon>
                </Flex>
              </StyledTooltip>
            )}
          </Flex>
        </Flex>
      </Td>

      <Td onClick={toggleExpand}>
        <Flex flexDirection="column">
          <Text fontWeight="bold">{model}</Text>
          <Text color="lightGrey">{object_str}</Text>
        </Flex>
      </Td>

      <StyledTd onClick={toggleExpand} isTablet={viewport.isTablet}>
        <Flex flexDirection="column">
          <Flex flexDirection="row">
            <Text fontWeight="bold">{fieldname}</Text>
            <Text ml={1} color={Math.sign(diff_count) > 0 ? 'success' : 'error'}>
              {Math.sign(diff_count) > 0 ? `+${diff_count}` : diff_count}
            </Text>
          </Flex>
          <Box>
            <StyledDiv expanded={isExpanded} color="lightGrey" ref={ref}>
              <Text fontWeight="bold" color={change_type.color}>
                {change_type.text}
              </Text>
              {changedText}
            </StyledDiv>
          </Box>
        </Flex>
      </StyledTd>

      <Td pr={isAdmin ? '0' : '12'} onClick={toggleExpand}>
        {/* Irreversible: */}
        {!can_revert && (
          <Flex justifyContent="flex-end" alignItems="end">
            <Trans>Irreversible</Trans>
          </Flex>
        )}

        {/* Reverted on: */}
        {can_revert && is_revert_completed && (
          <Flex flexDirection="column" justifyContent="flex-end" alignItems="end">
            <Trans>Reverted on:</Trans>
            <Text fontWeight="bold">{formatDate(revert_on, 'dd. MM. yyyy')}</Text>
            <Text mr="2px" color="lightGrey">
              {revert_requested_by}
            </Text>
          </Flex>
        )}
        {can_revert && !is_revert_completed && revert_on && (
          <Flex flexDirection="column" justifyContent="flex-end" alignItems="end">
            <Trans>Scheduled on:</Trans>
            <Text fontWeight="bold">{formatDate(revert_on, 'dd. MM. yyyy')}</Text>
            <Text mr="2px" color="lightGrey">
              {revert_requested_by}
            </Text>
          </Flex>
        )}
      </Td>

      {isAdmin && (
        <Td position="relative">
          {/* Revert scheduled: */}
          <ClickableArea onClick={toggleExpand} />
          {can_revert && !is_revert_completed && (
            <MntrButton
              ml={1}
              icon="more_vert"
              bg="transparent"
              popupPlacement="bottom-end"
              {...(!viewport.isMobile && {
                tooltip: t`Revert actions`,
              })}
              popupWidth={249}
              popup={(closePopup) => {
                const setRevert = (date, updateSelected) => {
                  if (updateSelected) setSelected(date)
                  revert(date)
                  closePopup()
                }

                const actions = [
                  {
                    leftIcon: 'restore',
                    onClick: () => {
                      const date = formatDate(startOfDay(today), DATE_FORMAT)
                      setRevert(date)
                    },
                    label: t`Revert now`,
                  },
                  {
                    label: t`Schedule revert`,
                    leftIcon: 'date_range',
                    placement: 'bottom-start',
                    subMenuItems: [
                      {
                        component: (
                          <DayPicker
                            mode="single"
                            firstDayOfWeek={WEEK_STARTS_ON}
                            month={month}
                            onMonthChange={(month) => setMonth(month)}
                            onDayClick={(date) => {
                              const dateFormated = formatDate(startOfDay(date), DATE_FORMAT)
                              const isPast = isBefore(startOfDay(date), startOfDay(today))
                              !isPast && setRevert(dateFormated, true)
                            }}
                            selected={selected}
                            modifiers={{
                              disabled: {
                                before: startOfDay(today),
                              },
                            }}
                          />
                        ),
                      },
                    ],
                  },
                ]

                if (revert_on) {
                  actions.unshift({
                    leftIcon: 'close',
                    onClick: () => {
                      setRevert(null)
                    },
                    label: t`Cancel revert`,
                  })
                }
                return <MntrMenu menuItems={actions} closePopup={closePopup} />
              }}
            />
          )}
        </Td>
      )}
    </StyledRow>
  )
}

export default observer(ChangelogTableRow)
