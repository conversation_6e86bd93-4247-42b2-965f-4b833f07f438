import { css, styled } from 'styled-components'

const Span = styled.span`
  font-weight: bold;

  ${(props) =>
    props.type === ''
      ? css`
          display: inline-block;
          padding: 3px 7px;
          font-weight: bold;
          border: 1px solid #e3e3e3;
          border-radius: 3px;
          box-shadow: 1px 1px 0 rgba(0, 0, 0, 0.3);
        `
      : null};

  ${(props) =>
    props.type === 'primary'
      ? css`
          color: #5daced;
        `
      : null};

  ${(props) =>
    props.type === 'secondary'
      ? css`
          color: #b94a48;
        `
      : null};
`

const Highlight = ({ children, type = '' }) => <Span type={type}>{children}</Span>

export default Highlight
