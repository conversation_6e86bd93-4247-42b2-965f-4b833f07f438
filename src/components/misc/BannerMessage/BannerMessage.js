import { useState } from 'react'
import { styled } from 'styled-components'
import Mntr<PERSON>aper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { Box, Text } from '~/components/misc/Mntr'

const StyledBox = styled(Box)`
  color: ${({ theme, bg }) => theme.colors[theme.buttons[bg + 'Color']]};
`

const BannerMessage = ({ bg, collapse, opened = false, icon, text, textComponent, title }) => {
  const [isOpen, toggle] = useState(opened)

  return (
    <MntrPaper bg={bg}>
      <MntrPaperToolbar
        bg={bg}
        flat
        icon={icon || 'announcement'}
        title={title}
        {...(collapse
          ? {
              actions: [
                {
                  icon: isOpen ? 'expand_less' : 'expand_more',
                  onClick() {
                    toggle(!isOpen)
                  },
                },
              ],
              onClick() {
                toggle(!isOpen)
              },
            }
          : {})}
      />
      {(!collapse || (collapse && isOpen)) && text && (
        <StyledBox p={3} pt={0} bg={bg}>
          {textComponent || (
            <Text my={0} mt={0} color="inherit">
              {text}
            </Text>
          )}
        </StyledBox>
      )}
    </MntrPaper>
  )
}

export default BannerMessage
