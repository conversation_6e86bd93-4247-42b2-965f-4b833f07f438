import Script from 'next/script'

type GTMType = {
  containerId: string
}

export function GTMNoScript({ containerId }: GTMType) {
  return (
    <noscript>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${containerId}`}
        height={0}
        width={0}
        style={{ display: 'none', visibility: 'hidden' }}
      />
    </noscript>
  )
}

interface IGTM extends GTMType {
  withNoScript?: boolean
}

export function GTM({ containerId, withNoScript }: IGTM) {
  return (
    <>
      <Script
        id={containerId}
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${containerId}');
          `,
        }}
      />
      {withNoScript && <GTMNoScript containerId={containerId} />}
    </>
  )
}
