import Icon from '~/components/misc/Icon/Icon'
import { scaledSize } from '~/components/misc/MediaIcon/MediaIcon'
import MntrAvatar from '~/components/misc/MntrAvatar/MntrAvatar'

// TODO: CategoryTypeStoreArrItem
interface ICategoryType {
  categoryType: {
    color: string
    icon: string
  }
  size?: number
}

const CategoryTypeIcon = ({ categoryType, size = 26 }: ICategoryType) => {
  return (
    <MntrAvatar color={categoryType.color} size={size}>
      <Icon size={scaledSize(size)}>{categoryType.icon}</Icon>
    </MntrAvatar>
  )
}

export default CategoryTypeIcon
