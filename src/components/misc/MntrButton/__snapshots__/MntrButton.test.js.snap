// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MntrButton matches snapshot when counter is 1 1`] = `
<DocumentFragment>
  .c0 {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 6px 16px;
  border: 0;
  cursor: pointer;
  outline: 0;
  color: inherit;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  -webkit-tap-highlight-color: transparent;
  font-size: 0.875rem;
  min-width: 64px;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  text-transform: uppercase;
  box-sizing: border-box;
}

.c2 {
  width: 100%;
  display: inherit;
  align-items: inherit;
  justify-content: inherit;
}

.c1 {
  user-select: auto;
  color: !important;
  height: 32px;
  max-width: 100%;
  min-width: auto!important;
  line-height: 24px;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 12px 2px 12px;
  font-weight: 400;
  font-size: 14px;
  border-radius: 5px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c1.hovered,
.c1:hover {
  color: !important;
  text-decoration: none;
}

.c1:disabled {
  color: !important;
}

.c1:disabled:hover {
  box-shadow: none;
  text-decoration: none;
}

<button
    class="c0 c1"
    type="button"
  >
    <span
      class="c2"
    />
  </button>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when hoverable 1`] = `
<DocumentFragment>
  .c5 {
  font-size: 22px!important;
  font-variation-settings: 'FILL' 0;
  width: 22px;
  height: 22px;
  overflow: hidden;
}

.c1 {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 6px 16px;
  border: 0;
  cursor: pointer;
  outline: 0;
  color: inherit;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  -webkit-tap-highlight-color: transparent;
  font-size: 0.875rem;
  min-width: 64px;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  text-transform: uppercase;
  box-sizing: border-box;
}

.c3 {
  width: 100%;
  display: inherit;
  align-items: inherit;
  justify-content: inherit;
}

.c4 {
  display: inherit;
  margin-left: -4px;
  margin-right: 8px;
}

.c0 {
  display: inline-block;
}

.c2 {
  user-select: auto;
  color: !important;
  height: 32px;
  max-width: 100%;
  min-width: auto!important;
  line-height: 24px;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 12px 2px 12px;
  font-weight: 400;
  font-size: 14px;
  border-radius: 5px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c2.hovered,
.c2:hover {
  color: !important;
  text-decoration: none;
}

.c2:disabled {
  color: !important;
}

.c2:disabled:hover {
  box-shadow: none;
  text-decoration: none;
}

<div
    class="c0"
  >
    <button
      class="c1 c2"
      type="button"
    >
      <span
        class="c3"
      >
        <span
          class="c4"
        >
          <i
            class="c5 material-symbols-outlined"
            size="22"
          />
        </span>
      </span>
    </button>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip 1`] = `
<DocumentFragment>
  .c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 2px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  />
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip with avatar 1`] = `
<DocumentFragment>
  .c4 {
  font-size: 18px!important;
  font-variation-settings: 'FILL' 1;
  width: 18px;
  height: 18px;
  overflow: hidden;
}

.c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 20px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c2 {
  display: inline-block;
  color: #fff!important;
  background: rgb(65, 186, 226);
  text-align: center;
  width: 26px!important;
  height: 26px!important;
  border-radius: 50%;
  position: absolute;
  left: -5px;
  top: 0px;
}

.c3 {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  left: 0px;
  top: 0px;
  height: 100%;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  >
    <div
      class="c2 avatar"
      size="26"
    >
      <div
        class="c3"
      >
        <i
          class="c4 material-symbols-outlined"
          size="18"
        >
          all
        </i>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip with favicon 1`] = `
<DocumentFragment>
  .c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 18px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c3 {
  display: inline-block;
  width: 16px;
  height: 16px;
  position: relative;
  top: 0px;
  left: 1px;
  overflow: hidden;
  background: url(true);
  background-size: contain;
  margin: 4px;
}

.c2 {
  display: inline-block;
  position: absolute;
  left: -5px;
  top: 1px;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  >
    <div
      class="c2 avatar"
      size="26"
    >
      <div
        class="c3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip with flagCountry 1`] = `
<DocumentFragment>
  .c3 {
  position: relative;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  border: 1px solid;
  overflow: hidden;
  border: 0px;
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 20px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c2 {
  display: inline-block;
  color: #fff!important;
  background: rgb(65, 186, 226);
  text-align: center;
  width: 26px!important;
  height: 26px!important;
  border-radius: 50%;
  position: absolute;
  left: -5px;
  top: 0px;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  >
    <div
      class="c2 avatar"
      size="26"
    >
      <div
        class="c3"
        size="26"
      >
        <img
          alt="true"
          class="c4"
          height="26"
          src="/static/flags/true.png"
          width="26"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip with flagLanguage 1`] = `
<DocumentFragment>
  .c3 {
  position: relative;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  border: 1px solid;
  overflow: hidden;
  border: 0px;
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 20px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c2 {
  display: inline-block;
  color: #fff!important;
  background: rgb(65, 186, 226);
  text-align: center;
  width: 26px!important;
  height: 26px!important;
  border-radius: 50%;
  position: absolute;
  left: -5px;
  top: 0px;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  >
    <div
      class="c2 avatar"
      size="26"
    >
      <div
        class="c3"
        size="26"
      >
        <img
          alt="true"
          class="c4"
          height="26"
          src="/static/flags/xx.png"
          width="26"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip with image 1`] = `
<DocumentFragment>
  .c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 20px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c3 {
  display: inline-block;
  width: 24px;
  height: 24px;
  position: relative;
  top: 0px;
  left: 0px;
  overflow: hidden;
  background: url(true);
  border-radius: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position-x: center;
}

.c2 {
  display: inline-block;
  position: absolute;
  left: -5px;
  top: 1px;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  >
    <div
      class="c2 avatar"
      size="26"
    >
      <div
        class="c3"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip with non-zero counter 1`] = `
<DocumentFragment>
  .c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 20px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c2 {
  display: inline-block;
  color: #fff!important;
  background: default!important;
  font-size: 14px!important;
  text-align: center;
  width: 26px!important;
  height: 26px!important;
  line-height: 24px!important;
  border-radius: 50%;
  position: absolute;
  left: -5px;
  top: 0px;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  >
    <div
      class="c2 avatar"
    >
      1
    </div>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip with topicId 1`] = `
<DocumentFragment>
  .c3 {
  position: relative;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  border: 1px solid;
  overflow: hidden;
  border: 0px;
}

.c4 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 20px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c2 {
  display: inline-block;
  color: #fff!important;
  background: rgb(65, 186, 226);
  text-align: center;
  width: 26px!important;
  height: 26px!important;
  border-radius: 50%;
  position: absolute;
  left: -5px;
  top: 0px;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  >
    <div
      class="c2 avatar"
      size="26"
    >
      <div
        class="c3"
        size="26"
      >
        <img
          alt="true"
          class="c4"
          height="26"
          src="/static/flags/true.png"
          width="26"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isChip without avatar 1`] = `
<DocumentFragment>
  .c0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  box-sizing: border-box;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 16px;
  white-space: nowrap;
  vertical-align: middle;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.c0 .avatar {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .icon {
  margin-left: 5px;
  margin-right: -6px;
}

.c0 .iconSmall {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: -4px;
}

.c0 .deleteIcon {
  width: 22px;
  height: 22px;
  margin: 0px -1px 0px -3px;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}

.c1 {
  user-select: auto;
  position: relative;
  height: 26px;
  max-width: 100%;
  width: auto;
  justify-content: center;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 2px 2px 2px;
  font-weight: 400;
  font-size: 14px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

<div
    class="c0 c1"
    color="default"
    data-chip="true"
    tabindex="0"
  />
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isCta 1`] = `
<DocumentFragment>
  .c0 {
  padding: 10px 30px;
  cursor: pointer;
  border: 0;
  font-weight: 500;
  font-size: 15px;
  text-transform: uppercase;
  position: relative;
  border-radius: 50px;
  color: #fff;
  width: 100%;
  transition: background-color 0.2s,color 0.2s;
}

.c0:hover {
  background: linear-gradient(45deg, rgb(50, 182, 183) 30%, rgb(4, 237, 181) 90%);
  transition: background-color 0.1s 0.2s,color 0.1s 0.2s;
  color: #fff;
  background-color: #3f51b5;
  animation: liYkqT 0.2s forwards;
}

.c0:hover::before {
  animation: kLyDvu 0.2s 0.2s forwards;
}

.c0::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -20px;
  bottom: -10px;
  right: -20px;
  background: inherit;
  border-radius: 50px;
  z-index: -1;
  opacity: 0.4;
  transform: scale3d(0.8, 0.5, 1);
}

<button
    class="c0"
    type="button"
  />
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isMainMenu 1`] = `
<DocumentFragment>
  .c4 {
  font-size: 22px!important;
  font-variation-settings: 'FILL' 0;
  width: 22px;
  height: 22px;
  overflow: hidden;
}

.c0 {
  position: relative;
  display: inline-flex;
  flex: 0 0 auto;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 12px;
  color: inherit;
  border: 0;
  cursor: pointer;
  outline: 0;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  appearance: none;
  overflow: visible;
  font-size: 1.5rem;
  text-align: center;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 50%;
}

.c3 {
  width: 100%;
  display: flex;
  align-items: inherit;
  justify-content: inherit;
}

.c1 {
  padding: 0;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  height: 32px;
  width: 32px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c1:hover {
  text-decoration: none;
}

.c1:disabled {
  color: !important;
}

.c1:disabled:hover {
  box-shadow: none;
  text-decoration: none;
}

.c2 {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 0;
  background: transparent;
  color: #f5f7f9;
}

.c2:hover {
  box-shadow: none;
}

.c2:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-left: 2px solid transparent;
}

<button
    class="c0 c1 c2"
    type="button"
  >
    <span
      class="c3"
    >
      <i
        class="c4 material-symbols-outlined"
        size="22"
      />
    </span>
  </button>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isSelectButton 1`] = `
<DocumentFragment>
  .c0 {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 6px 16px;
  border: 0;
  cursor: pointer;
  outline: 0;
  color: inherit;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  -webkit-tap-highlight-color: transparent;
  font-size: 0.875rem;
  min-width: 64px;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  text-transform: uppercase;
  box-sizing: border-box;
}

.c2 {
  width: 100%;
  display: inherit;
  align-items: inherit;
  justify-content: inherit;
}

.c1 {
  box-sizing: border-box;
  line-height: 1;
  font-size: 16px;
  padding: px;
  border: none;
  border-radius: 5px 5px 0px 0px;
  transition: all 0.15s ease-in-out;
  width: 100%;
  border-bottom: 1px solid;
  width: 100%;
  justify-content: space-between;
  text-transform: none;
  height: 36px;
  padding-right: 24px;
  padding-top: 6px;
}

.c1:before {
  border-bottom-color: !important;
}

.c1:disabled {
  color: !important;
}

.c1:focus {
  border-bottom-color: !important;
}

.c1::placeholder {
  color: #adadad;
  opacity: 0.9;
}

<button
    class="c0 c1"
    font-size="16"
    type="button"
  >
    <span
      class="c2"
    />
  </button>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when isSidebar 1`] = `
<DocumentFragment>
  .c4 {
  font-size: 22px!important;
  font-variation-settings: 'FILL' 0;
  width: 22px;
  height: 22px;
  overflow: hidden;
}

.c0 {
  position: relative;
  display: inline-flex;
  flex: 0 0 auto;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 12px;
  color: inherit;
  border: 0;
  cursor: pointer;
  outline: 0;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  appearance: none;
  overflow: visible;
  font-size: 1.5rem;
  text-align: center;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 50%;
}

.c3 {
  width: 100%;
  display: flex;
  align-items: inherit;
  justify-content: inherit;
}

.c1 {
  padding: 0;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  height: 32px;
  width: 32px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c1:hover {
  text-decoration: none;
}

.c1:disabled {
  color: !important;
}

.c1:disabled:hover {
  box-shadow: none;
  text-decoration: none;
}

.c2 {
  border-radius: 0;
  width: 100%;
  height: 45px;
}

.c2:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-left: 2px solid transparent;
}

.c2:hover {
  color: !important;
}

<button
    class="c0 c1 c2"
    data-iconbutton="true"
  >
    <span
      class="c3"
    >
      <i
        class="c4 material-symbols-outlined"
        size="22"
      />
    </span>
  </button>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot when tooltip 1`] = `
<DocumentFragment>
  .c0 {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 6px 16px;
  border: 0;
  cursor: pointer;
  outline: 0;
  color: inherit;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  -webkit-tap-highlight-color: transparent;
  font-size: 0.875rem;
  min-width: 64px;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  text-transform: uppercase;
  box-sizing: border-box;
}

.c2 {
  width: 100%;
  display: inherit;
  align-items: inherit;
  justify-content: inherit;
}

.c1 {
  user-select: auto;
  color: !important;
  height: 32px;
  max-width: 100%;
  min-width: auto!important;
  line-height: 24px;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 12px 2px 12px;
  font-weight: 400;
  font-size: 14px;
  border-radius: 5px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c1.hovered,
.c1:hover {
  color: !important;
  text-decoration: none;
}

.c1:disabled {
  color: !important;
}

.c1:disabled:hover {
  box-shadow: none;
  text-decoration: none;
}

<button
    class="c0 c1"
    type="button"
  >
    <span
      class="c2"
    />
  </button>
</DocumentFragment>
`;

exports[`MntrButton matches snapshot without props 1`] = `
<DocumentFragment>
  .c0 {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 6px 16px;
  border: 0;
  cursor: pointer;
  outline: 0;
  color: inherit;
  user-select: none;
  text-decoration: none;
  background-color: transparent;
  -webkit-tap-highlight-color: transparent;
  font-size: 0.875rem;
  min-width: 64px;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  text-transform: uppercase;
  box-sizing: border-box;
}

.c2 {
  width: 100%;
  display: inherit;
  align-items: inherit;
  justify-content: inherit;
}

.c1 {
  user-select: auto;
  color: !important;
  height: 32px;
  max-width: 100%;
  min-width: auto!important;
  line-height: 24px;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 2px 12px 2px 12px;
  font-weight: 400;
  font-size: 14px;
  border-radius: 5px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.c1.hovered,
.c1:hover {
  color: !important;
  text-decoration: none;
}

.c1:disabled {
  color: !important;
}

.c1:disabled:hover {
  box-shadow: none;
  text-decoration: none;
}

<button
    class="c0 c1"
    type="button"
  >
    <span
      class="c2"
    />
  </button>
</DocumentFragment>
`;
