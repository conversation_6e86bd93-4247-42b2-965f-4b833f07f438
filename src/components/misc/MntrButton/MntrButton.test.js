/**
 * @jest-environment jsdom
 */

import { render } from '@testing-library/react'
import 'jest-styled-components'
import noopFn from 'lodash/noop'
import { Providers as ThemeProvider } from '~/components/misc/MntrProviders/StyleProvider'
import MntrButton from './MntrButton'

const theme = {
  buttons: {
    iconColor: 'white',
  },
  colors: {},
  feed: {},
  flag: {},
  form: {},
  sidebar: {},
  space: {},
  textfield: {},
  timepicker: {},
}

describe('MntrButton', () => {
  it('renders', () => {
    render(
      <ThemeProvider theme={theme}>
        <MntrButton />
      </ThemeProvider>,
    )
  })

  it('matches snapshot without props', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip with avatar', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip icon="all" iconFill="false" />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip without avatar', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip with flagLanguage', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip flagLanguage />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when counter is 1', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton counter={1} />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip with flagCountry', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip flagCountry />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  // TODO skipped for now because it depends on asppStore
  it.skip('matches snapshot when isChip with topicId', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip topicId={1} />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip with favicon', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip favicon />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip with image', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip image />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isChip with non-zero counter', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isChip counter={1} />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isSelectButton', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isSelectButton />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isCta', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isCta />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isMainMenu', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isMainMenu />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when isSidebar', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton isSidebar />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when hoverable', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton hoverable popup={noopFn} />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  // TODO skipped for now because of trouble with mounting to portal
  it.skip('matches snapshot when popup but not disabled', () => {
    const portal = document.createElement('div')
    portal.id = 'portal-overlay'
    // document.append(portal)

    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton disabled={false} popup={noopFn} />
      </ThemeProvider>,
    )

    expect(asFragment()).toMatchSnapshot()
  })

  // TODO skipped for now because of trouble with mounting to portal
  it.skip('matches snapshot when modal', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton modal />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })

  it('matches snapshot when tooltip', () => {
    const { asFragment } = render(
      <ThemeProvider theme={theme}>
        <MntrButton tooltip />
      </ThemeProvider>,
    )
    expect(asFragment()).toMatchSnapshot()
  })
})
