import type { JSX } from 'react'
import { isValidElement, ReactNode } from 'react'
import Flag from '~/components/Flag/Flag'
import Icon from '~/components/misc/Icon/Icon'
import Chip from '~/components/misc/Mntr/Chip'
import TopicIcon from '~/components/misc/TopicIcon/TopicIcon'
import ButtonHover from './modules/ButtonHover'
import ButtonModal from './modules/ButtonModal'
import ButtonPopup from './modules/ButtonPopup'
import ButtonRequestFeature from './modules/ButtonRequestFeature'
import ButtonTooltip from './modules/ButtonTooltip'
import StyledButton from './style/StyledButton'
import StyledChip from './style/StyledChip'
import StyledChipAvatar from './style/StyledChipAvatar'
import StyledCounter from './style/StyledCounter'
import StyledCtaButton from './style/StyledCtaButton'
import { StyledFavicon, StyledFaviconWrapper } from './style/StyledFavicon'
import StyledIconButton from './style/StyledIconButton'
import StyledIconOffset from './style/StyledIconOffset'
import { StyledImage, StyledImageWrapper } from './style/StyledImage'
import StyledMainMenuButton from './style/StyledMainMenuButton'
import StyledNavButton from './style/StyledNavButton'
import { StyledSelectButton } from './style/StyledSelectButton'
import StyledTopicIcon from './style/StyledTopicIcon'

export interface MntrButtonProps {
  'data-e2e'?: string
  ml?: number
  mr?: number
  mt?: number
  mb?: number
  label?: string | JSX.Element | ReactNode
  onClick?: () => void
  bg?: string
  rounded?: boolean
  disabled?: boolean
  fullWidth?: boolean
  isChip?: boolean
  isCta?: boolean
  isMainMenu?: boolean
  icon?: ReactNode | string
  endIcon?: ReactNode | string
  endIconElement?: ReactNode
  counter?: number
  iconBg?: string
  iconOffsetX?: number
  iconOffsetY?: number
  iconSize?: number
  image?: string
  iconColor?: string
  iconColorHover?: string
  iconFill?: boolean
  favicon?: string
  fullHeight?: boolean
  popup?: (closePopup: () => void) => JSX.Element | ReactNode
  popupWidth?: number | string
  noPadding?: boolean
  modal?: ReactNode | JSX.Element
  popupPlacement?: 'top' | 'bottom' | 'left' | 'right' | 'bottom-end' | 'bottom-start'
  popupBg?: string
  hoverable?: boolean
  hoverDelay?: number
  tooltip?: JSX.Element | string
  type?: 'button' | 'submit' | 'reset'
  size?: 'default' | 'small'
  onDelete?: () => void
  modalTitle?: string
  modalClass?: string | null
  modalBg?: string
  modalIcon?: ReactNode | string
  modalWidth?: number | string
  modalMarginTop?: number | string
  modalButtonGroup?: ReactNode
  modalZIndex?: number
  isStackedModal?: boolean
  keepScrollLock?: boolean
  flagLanguage?: string
  flagCountry?: string
  onOpen?: () => void
  onClose?: () => void
  beforeOpen?: () => void
  active?: boolean
  placement?: 'top' | 'bottom' | 'left' | 'right'
  requestId?: string
  forceRequest?: boolean
  transformOrigin?: string
  href?: string
  topicId?: string
  target?: string
  height?: number | string
  zIndex?: number
  isSidebar?: boolean
  customElement?: ReactNode
  selected?: boolean
  submitLabel?: string
  badge?: number
  isSelectButton?: boolean
  transparentBg?: boolean
  fontSize?: number
  captureIgnore?: string
}

const MntrButton = ({
  'data-e2e': dataE2E,
  ml = 0,
  mr = 0,
  mt = 0,
  mb = 0,
  label,
  onClick,
  bg = 'default',
  rounded = false,
  disabled = false,
  fullWidth = false,
  isChip = false,
  isCta = false,
  isMainMenu = false,
  icon = '',
  endIcon,
  endIconElement = '',
  counter,
  iconBg,
  iconOffsetX = 0,
  iconOffsetY = 0,
  iconSize,
  image,
  iconColor = '',
  iconColorHover,
  iconFill = false,
  favicon,
  fullHeight,
  popup,
  popupWidth,
  noPadding,
  modal,
  popupPlacement,
  popupBg,
  hoverable,
  hoverDelay,
  tooltip,
  type = 'button',
  size = 'default',
  onDelete,
  modalTitle = '',
  modalClass = null,
  modalBg,
  modalIcon,
  modalWidth,
  modalMarginTop,
  modalButtonGroup,
  modalZIndex,
  isStackedModal,
  keepScrollLock,
  flagLanguage,
  flagCountry,
  onOpen,
  onClose,
  beforeOpen,
  active,
  placement,
  requestId,
  forceRequest,
  transformOrigin,
  href,
  topicId,
  target,
  height,
  zIndex,
  isSidebar = false,
  customElement,
  selected,
  submitLabel,
  badge = 0,
  isSelectButton = false,
  transparentBg = false,
  fontSize = 16,
  captureIgnore,
}: MntrButtonProps) => {
  if (requestId) {
    return (
      <ButtonRequestFeature
        isChip={isChip}
        icon={icon}
        label={label}
        requestId={requestId}
        modal={modal}
        forceRequest={forceRequest}
        title={modalTitle}
        submitLabel={submitLabel}
        fullWidth={fullWidth}
      />
    )
  }

  let element: ReactNode =
    !label && icon ? (
      // @ts-expect-error TODO refactor StyledIconButton to TS
      <StyledIconButton
        data-e2e={dataE2E}
        data-html2canvas-ignore={captureIgnore}
        data-iconbutton
        mt={mt}
        mb={mb}
        ml={ml}
        mr={mr}
        bg={bg}
        badge={badge}
        onClick={onClick}
        type={type}
        iconcolor={iconColor}
        iconcolorhover={iconColorHover}
        iconBg={iconBg}
        href={href}
        target={target}
        buttonsize={size}
        disabled={disabled}
        selected={selected ? 1 : 0}
        hasEndIcon={endIcon ? 1 : 0}
      >
        <Icon size={size === 'small' ? 20 : 22} fill={iconFill}>
          {icon}
        </Icon>
        {endIcon && (
          <Icon size={size === 'small' ? 20 : 22} fill={iconFill}>
            {endIcon}
          </Icon>
        )}
      </StyledIconButton>
    ) : (
      // @ts-expect-error TODO refactor StyledButton to TS
      <StyledButton
        onClick={onClick}
        bg={bg}
        fullWidth={fullWidth}
        rounded={rounded ? 1 : 0}
        disabled={disabled}
        ml={ml}
        mr={mr}
        mt={mt}
        mb={mb}
        type={type}
        startIcon={
          icon &&
          (isValidElement(icon) ? (
            icon
          ) : (
            <Icon size={22} color={iconColor} fill={iconFill}>
              {icon}
            </Icon>
          ))
        }
        endIcon={endIconElement}
        href={href}
        target={target}
        height={height}
        buttonsize={size}
        active={active ? 1 : 0}
      >
        {label}
      </StyledButton>
    )

  if (isChip) {
    let fontIconSize = size === 'default' ? 18 : 20
    if (iconSize) {
      fontIconSize = iconSize
    }
    let avatar: ReactNode = icon ? (
      <StyledChipAvatar
        // @ts-expect-error TODO refactor StyledChipAvatar to TS
        iconColor={iconColor}
        iconBg={iconBg}
        bg={disabled || bg === 'transparent' ? 'transparent' : null}
        size={size === 'default' ? 26 : 32}
      >
        {/* @ts-expect-error TODO refactor StyledIconOffset to TS */}
        <StyledIconOffset iconOffsetX={iconOffsetX} iconOffsetY={iconOffsetY}>
          <Icon size={fontIconSize} fill={iconFill}>
            {icon}
          </Icon>
        </StyledIconOffset>
      </StyledChipAvatar>
    ) : null

    if (flagLanguage) {
      avatar = (
        // @ts-expect-error TODO refactor StyledChipAvatar to TS
        <StyledChipAvatar size={size === 'default' ? 26 : 32}>
          <Flag language={flagLanguage} size={26} disableBorder noTooltip />
        </StyledChipAvatar>
      )
    }

    if (flagCountry) {
      avatar = (
        // @ts-expect-error TODO refactor StyledChipAvatar to TS
        <StyledChipAvatar size={size === 'default' ? 26 : 32}>
          <Flag country={flagCountry} size={26} disableBorder noTooltip />
        </StyledChipAvatar>
      )
    }

    if (topicId) {
      avatar = (
        <StyledTopicIcon>
          <TopicIcon id={topicId} size={24} />
        </StyledTopicIcon>
      )
    }

    if (favicon) {
      avatar = (
        // @ts-expect-error TODO refactor StyledFaviconWrapper to TS
        <StyledFaviconWrapper size={size === 'default' ? 26 : 32}>
          {/* @ts-expect-error TODO refactor StyledFavicon to TS */}
          <StyledFavicon favicon={favicon} />
        </StyledFaviconWrapper>
      )
    }

    if (image) {
      avatar = (
        // @ts-expect-error TODO refactor StyledImageWrapper to TS
        <StyledImageWrapper size={size === 'default' ? 26 : 32}>
          {/* @ts-expect-error TODO refactor StyledImage to TS */}
          <StyledImage image={image} />
        </StyledImageWrapper>
      )
    }

    if (counter && !isNaN(counter) && counter > 0) {
      // @ts-expect-error TODO refactor StyledCounter to TS
      avatar = <StyledCounter bg={bg}>{counter}</StyledCounter>
    }

    element = (
      <StyledChip
        // @ts-expect-error TODO refactor StyledChip to TD
        component={Chip}
        onDelete={onDelete}
        hasicon={
          avatar ||
          icon ||
          topicId ||
          flagLanguage ||
          flagCountry ||
          (counter && !isNaN(counter) && counter > 0)
            ? 1
            : 0
        }
        href={href}
        hasfavicon={favicon ? 1 : 0}
        nopadding={noPadding ? 1 : 0}
        data-chip
        avatar={avatar}
        label={label}
        onClick={onClick}
        deleteIcon={
          <span>
            <Icon fill>cancel</Icon>
          </span>
        }
        bg={bg}
        clickable={disabled ? 0 : 1}
        iconBg={iconBg}
        height={height}
        mr={mr}
        ml={ml}
        mt={mt}
        mb={mb}
        fullWidth={fullWidth}
      />
    )
  }

  if (isSelectButton) {
    element = (
      // @ts-expect-error TODO refactor StyledSelectButton to TD
      <StyledSelectButton
        transparentBg={transparentBg}
        fontSize={fontSize}
        disabled={disabled}
        type={type}
        onClick={onClick}
        endIcon={endIconElement}
        icon={(!!icon).toString()}
      >
        {label}
      </StyledSelectButton>
    )
  }

  if (isCta) {
    element = (
      // @ts-expect-error TODO refactor StyledCtaButton to TD
      <StyledCtaButton bg={bg} disabled={disabled} type={type} onClick={onClick}>
        {label}
      </StyledCtaButton>
    )
  }

  if (isMainMenu) {
    element = (
      // @ts-expect-error TODO refactor StyledMainButton to TD
      <StyledMainMenuButton active={active} href={href} onClick={onClick} type={type}>
        <Icon size={22} fill={active}>
          {icon}
        </Icon>
      </StyledMainMenuButton>
    )
  }

  if (isSidebar) {
    element = (
      // @ts-expect-error TODO refactor StyledNavButton to TD
      <StyledNavButton
        data-iconbutton
        active={active ? 1 : 0}
        bg={bg}
        onClick={onClick}
        href={href}
        target={target}
        size={size}
        disabled={disabled}
      >
        <Icon size={22} iconOffsetX={iconOffsetX} fill={active}>
          {icon}
        </Icon>
      </StyledNavButton>
    )
  }

  if (hoverable) {
    return (
      <ButtonHover
        popup={popup}
        popupBg={popupBg}
        popupWidth={popupWidth}
        popupPlacement={popupPlacement}
        button={element}
        zIndex={zIndex}
        hoverDelay={hoverDelay}
      />
    )
  }

  if (popup && !disabled) {
    return (
      <ButtonPopup
        element={customElement || element}
        placement={popupPlacement}
        transformOrigin={transformOrigin}
        popup={popup}
        popupWidth={popupWidth}
        onOpen={onOpen}
        onClose={onClose}
        tooltip={tooltip}
        tooltipPlacement={placement}
        zIndex={zIndex}
      />
    )
  }

  if (modal) {
    return (
      <ButtonModal
        element={customElement || element}
        modalIcon={modalIcon}
        modalTitle={modalTitle || label}
        modalClass={modalClass}
        modalBg={modalBg}
        modal={modal}
        modalZIndex={modalZIndex}
        modalButtonGroup={modalButtonGroup}
        tooltip={tooltip}
        tooltipPlacement={placement}
        fullHeight={fullHeight}
        modalWidth={modalWidth}
        modalMarginTop={modalMarginTop}
        onClose={onClose}
        beforeOpen={beforeOpen}
        isStackedModal={isStackedModal}
        keepScrollLock={keepScrollLock}
        zIndex={zIndex}
      />
    )
  }

  if (tooltip) {
    return (
      <ButtonTooltip button={element} tooltip={tooltip} placement={placement} zIndex={zIndex} />
    )
  }

  return element
}

export default MntrButton
