import { styled } from 'styled-components'

export const StyledImage = styled.div`
  display: inline-block;
  width: 24px;
  height: 24px;
  position: relative;
  top: 0px;
  left: 0px;
  overflow: hidden;
  background: url(${({ image }) => image});
  border-radius: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position-x: center;
`

export const StyledImageWrapper = styled.div`
  display: inline-block;
  position: absolute;
  left: -5px;
  top: 1px;
`
