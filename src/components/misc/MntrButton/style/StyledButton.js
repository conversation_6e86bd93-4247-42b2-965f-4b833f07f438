import { css, styled } from 'styled-components'
import Button from '~/components/misc/MntrButton/modules/Button'

const StyledButton = styled(Button)`
  user-select: auto;
  background: ${({ theme, bg }) => theme.buttons[bg]};
  color: ${({ theme, bg }) => theme.colors[theme.buttons[bg + 'Color']]} !important;
  height: ${({ height }) => height || '32px'};
  max-width: 100%;
  min-width: auto !important;
  line-height: 24px;
  margin-right: ${({ theme, mr }) => theme.space[mr] || 0}px;
  margin-left: ${({ theme, ml }) => theme.space[ml] || 0}px;
  margin-top: ${({ theme, mt }) => theme.space[mt] || 0}px;
  margin-bottom: ${({ theme, mb }) => theme.space[mb] || 0}px;
  padding: 2px 12px 2px 12px;
  font-weight: 400;
  font-size: 14px;
  border-radius: ${({ rounded }) => (rounded ? 16 : 5)}px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

  &.hovered,
  &:hover {
    color: ${({ theme, bg }) => theme.colors[theme.buttons[bg + 'HoverColor']]} !important;
    background: ${({ theme, bg }) => theme.buttons[bg + 'Hover']};
    box-shadow: ${({ theme, bg }) => theme.buttons[bg + 'BoxShadow']};
    text-decoration: none;
  }

  &:disabled {
    background: ${({ theme }) => theme.buttons['disabled']};
    color: ${({ theme }) => theme.buttons['disabledColor']} !important;
  }

  &:disabled:hover {
    background: ${({ theme }) => theme.buttons['disabledHover']};
    color: ${({ theme, bg }) => theme.colors[theme.buttons[bg + 'Color']]};
    box-shadow: none;
    text-decoration: none;
  }

  ${({ buttonsize }) =>
    buttonsize === 'small' &&
    css`
      height: 26px !important;
      width: 26px !important;
    `}

  ${({ bg }) =>
    bg === 'timestamp' &&
    css`
      text-decoration: underline;
    `}
`

export default StyledButton
