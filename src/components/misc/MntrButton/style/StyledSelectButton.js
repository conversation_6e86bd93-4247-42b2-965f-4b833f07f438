import { css, styled } from 'styled-components'
import { baseInputStyle } from '~/components/forms/adapters/shared/StyledInput'
import Button from '~/components/misc/MntrButton/modules/Button'

const baseSelectStyle = css`
  ${baseInputStyle}
  width: 100%;
  justify-content: space-between;
  text-transform: none;
  height: 36px;
`

export const StyledSelectButton = styled(Button)`
  ${baseSelectStyle}
  padding-right: 24px;
  padding-top: 6px;
`

export const StyledSelect = styled.select`
  ${baseSelectStyle}
  padding-top: 0px;
  padding-bottom: 0px;
`
