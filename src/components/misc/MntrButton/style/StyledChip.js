import { css, styled } from 'styled-components'
import Button from '~/components/misc/MntrButton/modules/Button'

const StyledChip = styled(Button)`
  user-select: auto;
  position: relative;
  background: ${({ theme, bg }) => theme.buttons[bg]};
  color: ${({ theme, bg }) => theme.colors[theme.buttons[bg + 'Color']]};
  height: ${({ height }) => height || '26px'};
  max-width: 100%;
  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};
  justify-content: ${({ fullWidth }) => (fullWidth ? 'flex-end' : 'center')};
  margin-right: ${({ theme, mr }) => theme.space[mr] || 0}px;
  margin-left: ${({ theme, ml }) => theme.space[ml] || 0}px;
  margin-top: ${({ theme, mt }) => theme.space[mt] || 0}px;
  margin-bottom: ${({ theme, mb }) => theme.space[mb] || 0}px;
  padding: 2px 2px 2px
    ${({ hasicon, hasfavicon, nopadding }) => {
      if (nopadding) {
        return 0
      }
      if (hasfavicon) {
        return 18
      }
      return !hasicon ? 2 : 20
    }}px;
  font-weight: 400;
  font-size: 14px;

  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

  ${({ nopadding }) =>
    nopadding
      ? css`
          & span {
            padding-left: 3px;
            padding-right: 3px;
          }
        `
      : null}

  &:focus {
    background: ${({ theme, bg }) => theme.buttons[bg]};
  }
  &.hovered,
  &:hover {
    color: ${({ theme, bg, clickable }) => {
      if (!clickable) {
        return theme.colors[theme.buttons[bg + 'Color']]
      }
      return theme.colors[theme.buttons[bg + 'HoverColor']]
    }};
    background: ${({ theme, bg, clickable }) =>
      clickable ? theme.buttons[bg + 'Hover'] : theme.buttons[bg]};
    box-shadow: ${({ theme, bg, clickable }) =>
      clickable ? theme.buttons[bg + 'BoxShadow'] : 'none'};

    & .material-symbols-outlined {
      color: ${({ iconColoHover }) => iconColoHover};
    }
  }
`

export default StyledChip
