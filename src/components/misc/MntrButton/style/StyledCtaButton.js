import { css, keyframes, styled } from 'styled-components'

const anim1 = keyframes`
  60% {
    transform: scale3d(0.98, 1, 1);
  }

  85% {
    transform: scale3d(1.03, 1, 1);
  }

  100% {
    transform: scale3d(1, 1, 1);
  }
`

const anim2 = keyframes`
  to {
    transform: scale3d(1, 1, 1);
    opacity: 0;
  }
`

const StyledCtaButton = styled.button`
  background: ${({ theme, bg }) => {
    if (bg === 'default') {
      /* TODO: Why you no work?!?!? */
      return theme.buttons.secondary
    }
    /* TODO: Why you work?!?!? */
    return theme.buttons[bg]
      ? theme.buttons[bg]
      : 'linear-gradient(45deg, rgb(50, 182, 122) 30%, rgb(55, 186, 127) 90%)'
  }};
  padding: 10px 30px;
  cursor: pointer;
  border: 0;
  font-weight: 500;
  font-size: 15px;
  text-transform: uppercase;
  position: relative;
  border-radius: 50px;
  box-shadow: ${({ theme }) => theme.buttons.ctaBoxShadow};
  color: #fff;
  width: 100%;
  transition:
    background-color 0.2s,
    color 0.2s;

  &:hover {
    background: linear-gradient(45deg, rgb(50, 182, 183) 30%, rgb(4, 237, 181) 90%);
    transition:
      background-color 0.1s 0.2s,
      color 0.1s 0.2s;
    color: #fff;
    background-color: #3f51b5;
    animation: ${anim1} 0.2s forwards;
  }

  &:hover::before {
    animation: ${anim2} 0.2s 0.2s forwards;
  }

  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -20px;
    bottom: -10px;
    right: -20px;
    background: inherit;
    border-radius: 50px;
    z-index: -1;
    opacity: 0.4;
    transform: scale3d(0.8, 0.5, 1);
  }

  ${({ disabled }) =>
    Boolean(disabled) &&
    css`
      box-shadow: none;
      background: rgb(229, 229, 229);
      background-color: rgb(229, 229, 229);
      animation: none;
      &:hover {
        background: rgb(229, 229, 229);
        transition: none;
        color: #fff;
        background-color: rgb(229, 229, 229);

        animation: none;
      }
      &:hover::before {
        animation: none;
      }
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        opacity: 0.4;
        transform: none;
      }
    `}
`

export default StyledCtaButton
