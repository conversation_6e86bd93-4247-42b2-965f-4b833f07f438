/**
 * @jest-environment jsdom
 */

import { render } from '@testing-library/react'
import 'jest-styled-components'
import { ThemeProvider } from 'styled-components'
import StyledButton from './StyledButton'

const theme = {
  bg: 'white',
  buttons: {
    whiteColor: 'red',
  },
  colors: {
    whiteColor: 'red',
  },
  space: {},
}

describe('StyledButton', () => {
  it('has style from theme', () => {
    const { container: button } = render(
      <ThemeProvider theme={theme}>
        <StyledButton />
      </ThemeProvider>,
    )
    expect(button.firstChild).toHaveStyleRule('background-color', 'transparent')
  })
})
