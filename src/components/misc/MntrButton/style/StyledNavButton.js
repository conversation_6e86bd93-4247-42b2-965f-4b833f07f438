import { styled } from 'styled-components'
import StyledIconButton from './StyledIconButton'

const StyledNavButton = styled(StyledIconButton)`
  border-radius: 0;
  width: 100%;
  height: 45px;
  ${({ theme, bg, active }) =>
    bg &&
    `color: ${
      active ? theme.sidebar.activeColor : theme.colors[theme.buttons[bg + 'Color']]
    } !important`}
  box-shadow: ${({ theme, active }) =>
    active ? theme.sidebar.boxShadowActive : theme.sidebar.boxShadowPasive};

  &:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-left: 2px solid
      ${({ active, theme }) => (active ? theme.sidebar.activeBorder : 'transparent')};
  }

  &:hover {
    color: ${({ theme, bg }) => theme.colors[theme.buttons[bg + 'HoverColor']]} !important;
  }
`

export default StyledNavButton
