import { styled } from 'styled-components'

const StyledChipAvatar = styled.div`
  display: inline-block;
  color: ${({ bg, iconColor, iconBg }) => {
    if (iconBg === 'social') {
      return '#fff'
    }
    if (iconColor) {
      return iconColor
    }
    return bg === 'transparent' ? 'currentColor' : '#fff'
  }} !important;
  background: ${({ bg, iconBg, theme }) => {
    if (iconBg && theme.colors[iconBg]) {
      return theme.colors[iconBg]
    }

    if (iconBg) {
      return theme.buttons[iconBg] ? theme.buttons[iconBg] : iconBg
    }
    return bg || 'rgb(65, 186, 226)'
  }};
  text-align: center;
  width: ${({ size }) => size}px !important;
  height: ${({ size }) => size}px !important;
  border-radius: 50%;
  position: absolute;
  left: -5px;
  top: 0px;
`

export default StyledChipAvatar
