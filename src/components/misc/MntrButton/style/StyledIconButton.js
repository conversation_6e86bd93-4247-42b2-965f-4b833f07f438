import { css, styled } from 'styled-components'
import { IconButton } from '~/components/misc/MntrButton/modules/Button'

const StyledIconButton = styled(IconButton)`
  background: ${({ theme, bg, iconBg }) => {
    if (iconBg) {
      return iconBg
    }
    return theme.buttons[bg]
  }};
  color: ${({ theme, bg, iconcolor }) => {
    if (iconcolor) {
      return theme.colors[iconcolor] ? theme.colors[iconcolor] : iconcolor
    }
    return theme.colors[theme.buttons[bg + 'Color']]
  }};
  padding: 0;
  margin-right: ${({ theme, mr }) => theme.space[mr] || 0}px;
  margin-left: ${({ theme, ml }) => theme.space[ml] || 0}px;
  margin-top: ${({ theme, mt }) => theme.space[mt] || 0}px;
  margin-bottom: ${({ theme, mb }) => theme.space[mb] || 0}px;
  height: ${({ buttonsize }) => (buttonsize === 'small' ? 26 : 32)}px;
  width: ${({ buttonsize }) => (buttonsize === 'small' ? 26 : 32)}px;
  transition: box-shadow 50ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

  &:hover {
    color: ${({ theme, bg, iconcolorhover }) => {
      if (iconcolorhover) {
        return theme.colors[iconcolorhover] ? theme.colors[iconcolorhover] : iconcolorhover
      }
      return theme.colors[theme.buttons[bg + 'HoverColor']]
    }};
    background: ${({ theme, bg }) => theme.buttons[bg + 'Hover']};
    box-shadow: ${({ theme, bg }) => theme.buttons[bg + 'BoxShadow']};
    text-decoration: none;
  }

  &:disabled {
    background: ${({ theme }) => theme.buttons['disabled']};
    color: ${({ theme }) => theme.buttons['disabledColor']} !important;
  }

  &:disabled:hover {
    background: ${({ theme }) => theme.buttons['disabledHover']};
    color: ${({ theme, bg }) => theme.colors[theme.buttons[bg + 'Color']]};
    box-shadow: none;
    text-decoration: none;
  }

  ${({ selected }) =>
    Boolean(selected) &&
    css`
      background: ${({ theme, bg }) => theme.buttons[bg + 'Hover']};
    `}

  ${({ badge }) =>
    Boolean(badge) &&
    css`
      position: relative;
      &:after {
        content: '${badge}';
        display: inline-block;
        background: #ff8b8b;
        color: #fff;
        padding: 1px 5px;
        font-size: 12px;
        border-radius: 9px;
        position: absolute;
        right: 0px;
        bottom: 0px;
        z-index: 10;
        white-space: nowrap;
      }
    `}


  ${({ hasEndIcon }) => {
    return (
      hasEndIcon &&
      css`
        border-radius: 4px !important;
        width: 40px !important;
      `
    )
  }}
`

export default StyledIconButton
