import { styled } from 'styled-components'

const StyledCounter = styled.div`
  display: inline-block;
  color: ${({ bg, iconColor }) => {
    if (iconColor) {
      return iconColor
    }
    return bg === 'transparent' ? 'currentColor' : '#fff'
  }} !important;
  background: ${({ bg, iconBg, theme }) => {
    if (theme.buttons[bg]) {
      return theme.buttons[bg + 'Counter']
    }

    if (iconBg) {
      return iconBg
    }
    return bg || 'rgb(188, 188, 188)'
  }} !important;
  font-size: 14px !important;
  text-align: center;
  width: 26px !important;
  height: 26px !important;
  line-height: 24px !important;
  border-radius: 50%;
  position: absolute;
  left: -5px;
  top: 0px;
`

export default StyledCounter
