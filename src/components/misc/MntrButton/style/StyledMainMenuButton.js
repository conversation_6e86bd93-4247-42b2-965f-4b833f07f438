import { styled } from 'styled-components'
import StyledIconButton from './StyledIconButton'

const StyledMainMenuButton = styled(StyledIconButton)`
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 0;
  background: transparent;
  color: #f5f7f9;

  &:hover {
    background: ${({ theme }) => theme.buttons.defaultHover};
    color: ${({ theme }) => theme.colors.white};
    box-shadow: none;
  }

  &:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-left: 2px solid ${({ active }) => (active ? '#f5f7f9' : 'transparent')};
  }
`

export default StyledMainMenuButton
