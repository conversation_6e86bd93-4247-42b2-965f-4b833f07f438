import { ReactNode } from 'react'
import { styled } from 'styled-components'
import Tooltip from '~/components/misc/Mntr/Tooltip'

const LightTooltip = styled.div`
  max-width: 300px;
  padding: 4px 8px;
  color: #fff;
  background-color: rgb(60, 72, 94);
  font-size: 14px;
  box-shadow:
    0px 2px 4px -1px rgba(0, 0, 0, 0.2),
    0px 4px 5px 0px rgba(0, 0, 0, 0.14),
    0px 1px 10px 0px rgba(0, 0, 0, 0.12);
  word-wrap: break-word;
  border-radius: 4px;
  font-weight: 500;

  & ~ svg {
    fill: rgb(60, 72, 94);
  }
`

interface StyledTooltipProps {
  children: ReactNode
  enterDelay?: number
  enterTouchDelay?: number
  interactive?: boolean
  offset?: number
  onOpenChange?: (open: boolean) => void
  open?: boolean
  placement?: 'top' | 'bottom' | 'left' | 'right'
  tooltip: ReactNode
  zIndex?: number
  style?: React.CSSProperties
}

const StyledTooltip = ({
  children,
  enterDelay = 0,
  enterTouchDelay = 0,
  interactive,
  offset = 15,
  onOpenChange,
  open,
  placement,
  tooltip,
  zIndex,
  style,
}: StyledTooltipProps) => {
  if (!tooltip) return <>{children}</>

  return (
    <Tooltip
      arrow
      interactive={interactive}
      title={<LightTooltip>{tooltip}</LightTooltip>}
      placement={placement}
      enterDelay={enterDelay}
      enterTouchDelay={enterTouchDelay}
      offset={offset}
      open={open}
      onOpenChange={onOpenChange}
      zIndex={zIndex}
      style={style} // Ensure this is passed
    >
      {children}
    </Tooltip>
  )
}

export default StyledTooltip
