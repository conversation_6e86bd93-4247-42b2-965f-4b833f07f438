import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { cloneElement } from 'react'
import { createPortal } from 'react-dom'
import { styled } from 'styled-components'
import usePortalContainer from '~/app/lib/use-portal-container'
import Icon from '~/components/misc/Icon/Icon'
import Popper from '~/components/misc/Mntr/Popper'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import HandleEscapeKey from '~/helpers/hooks/modules/HandleEscapeKey'

const PopperContent = styled.div`
  background: ${({ theme }) => theme.popper.background};
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  box-shadow: ${({ theme }) => theme.popper.boxShadow};
  min-width: ${({ popupWidth }) => popupWidth || 160}px;

  & .menu,
  & .menu-heading {
    color: ${({ theme }) => theme.popper.heading};
  }

  & .form-label {
    color: ${({ theme }) => theme.popper.formLabel};
  }

  & textarea,
  & input {
    color: ${({ theme }) => theme.popper.formLabel};
    &::placeholder {
      color: ${({ theme }) => theme.popper.formLabel};
      opacity: 0.8;
    }
  }
`

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: ${({ zIndex }) => zIndex};
`

const OverlayPortal = ({ close, isOpen, zIndex }) => {
  //TODO add use client boundary
  const portalContainer = usePortalContainer()

  if (!portalContainer || !isOpen) return null

  return createPortal(<Overlay onClick={close} zIndex={zIndex} />, portalContainer)
}

const ButtonPopup = ({
  element,
  popup,
  placement = 'bottom',
  transformOrigin,
  onOpen,
  onClose,
  popupWidth,
  tooltip,
  tooltipPlacement,
  zIndex = 3001,
}) => {
  return (
    <PopupState variant="popover" popupId="button-popup">
      {(popupState) => {
        let props = {}

        if (popupState.isOpen) {
          props.bg = 'primary'
        }

        if (popupState.isOpen && element.props.startIcon) {
          props.startIcon = <Icon>close</Icon>
        }

        if (element.props['data-iconbutton'] && popupState.isOpen) {
          props.children = <Icon size={22}>close</Icon>
        }

        if (element.props['data-chip'] && popupState.isOpen) {
          props.className = `${element.props.className} hovered`
        }

        const handleClose = () => {
          if (typeof onClose === 'function') {
            onClose()
          }
          popupState.close()
        }

        const handleOpen = () => {
          if (typeof onOpen === 'function') {
            onOpen()
          }
        }

        return (
          <>
            <OverlayPortal close={handleClose} isOpen={popupState.isOpen} zIndex={zIndex} />

            {popupState.isOpen && <HandleEscapeKey onCancel={popupState.close} />}

            {tooltip ? (
              <StyledTooltip tooltip={tooltip} placement={tooltipPlacement} zIndex={zIndex}>
                {cloneElement(element, {
                  ...props,
                  ...bindTrigger(popupState),
                  onMouseDown: handleOpen,
                })}
              </StyledTooltip>
            ) : (
              cloneElement(element, {
                ...props,
                ...bindTrigger(popupState),
                onMouseDown: handleOpen,
              })
            )}

            {popupState.isOpen && (
              <Popper
                disablePortal={false}
                {...bindPopover(popupState)}
                onClose={handleClose}
                transition
                placement={placement}
                style={{ zIndex: zIndex }}
                transformOrigin={transformOrigin}
              >
                <PopperContent popupWidth={popupWidth} className="popper-menu">
                  {popup(popupState.close, onClose)}
                </PopperContent>
              </Popper>
            )}
          </>
        )
      }}
    </PopupState>
  )
}

export default ButtonPopup
