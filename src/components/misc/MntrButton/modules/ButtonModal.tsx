import PopupState, { bindTrigger } from 'material-ui-popup-state'
import { cloneElement } from 'react'
import { createPortal } from 'react-dom'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import ModalPortal from '~/components/misc/Modal/ModalPortal'
import Handle<PERSON>capeKey from '~/helpers/hooks/modules/HandleEscapeKey'
import { observer } from '~/helpers/mst'

interface IButtonModalProps {
  beforeOpen?: () => void
  closePopup?: () => void
  disableClose?: boolean
  element: React.ReactElement
  fullHeight?: boolean
  modal: (close: () => void, ref: React.RefObject<HTMLDivElement>) => React.ReactNode
  modalBg?: 'flat' | 'default' | string
  modalClass?: string
  modalIcon?: string
  modalTitle?: string
  modalZIndex?: number
  onClose?: () => void
  onOpen?: () => void
  modalButtonGroup?: (close: () => void) => unknown[]
  tooltip?: string
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  modalWidth?: number
  isStackedModal?: boolean
  keepScrollLock?: boolean
  zIndex?: number
}

const ButtonModal = ({
  beforeOpen,
  closePopup,
  disableClose,
  element,
  fullHeight,
  modal,
  modalBg,
  modalClass,
  modalIcon,
  modalTitle,
  modalZIndex,
  onClose,
  onOpen,
  modalButtonGroup,
  tooltip,
  tooltipPlacement,
  modalWidth = 600,
  isStackedModal = false,
  keepScrollLock = false,
  zIndex,
}: IButtonModalProps) => {
  const portalModalContainer = document.getElementById('portal-modal')

  return (
    <PopupState variant="popover" popupId="button-popup">
      {(popupState) => {
        const popupStateClose = () => {
          if (!disableClose) {
            popupState.close()
            closePopup?.()
            onClose?.()
          }
        }

        const popupStateOpen = (
          event: React.MouseEvent<HTMLElement> | React.TouchEvent<HTMLElement>,
        ) => {
          beforeOpen?.()
          popupState.open(event)
        }

        const triggerProps = {
          ...bindTrigger(popupState),
          onClick: popupStateOpen,
          onMouseDown: () => onOpen?.(),
        }

        return (
          <>
            {tooltip ? (
              <StyledTooltip tooltip={tooltip} placement={tooltipPlacement} zIndex={zIndex}>
                {cloneElement(element, triggerProps)}
              </StyledTooltip>
            ) : (
              cloneElement(element, triggerProps)
            )}

            {popupState.isOpen && <HandleEscapeKey onCancel={popupStateClose} />}

            {popupState.isOpen &&
              portalModalContainer &&
              createPortal(
                <ModalPortal
                  isOpen={popupState.isOpen}
                  close={popupStateClose}
                  modal={modal}
                  fullHeight={fullHeight}
                  modalWidth={modalWidth}
                  icon={modalIcon}
                  modalBg={modalBg}
                  modalZIndex={modalZIndex}
                  modalTitle={modalTitle}
                  buttonGroup={modalButtonGroup ? modalButtonGroup(popupStateClose) : undefined}
                  isStackedModal={isStackedModal}
                  modalClass={modalClass}
                  keepScrollLock={keepScrollLock}
                />,
                portalModalContainer,
              )}
          </>
        )
      }}
    </PopupState>
  )
}

export default observer(ButtonModal)
