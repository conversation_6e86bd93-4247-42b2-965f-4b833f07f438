import { createElement, forwardRef } from 'react'

import MntrButton from '~/components/misc/Mntr/Button'
import MntrIconButton from '~/components/misc/Mntr/IconButton'
import { Link } from '~/helpers/router'

const Button = forwardRef(function Button(
  { component = MntrButton, children, href, target, onClick, ...props },
  ref,
) {
  if (href && !target) {
    return createElement(
      component,
      {
        component: Link,
        naked: true,
        href,
        onClick(event) {
          if (event.ctrlKey || event.metaKey) {
            return false
          }

          if (onClick) {
            event.preventDefault()
            onClick()
          }
        },
        ...props,
        ref,
      },
      children,
    )
  }

  return createElement(component, { onClick, ...props, href, target, ref }, children)
})

export default Button

export const IconButton = forwardRef(function IconButton(props, ref) {
  // we need this spread to unify forwardRef components to codemod to work
  const { ...other } = props
  return <Button component={MntrIconButton} {...other} ref={ref} />
})
