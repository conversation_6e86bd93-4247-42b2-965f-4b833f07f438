import { cloneElement } from 'react'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'

const ButtonTooltip = ({
  tooltip,
  button,
  placement,
  enterDelay = 0,
  enterTouchDelay = 0,
  zIndex,
}) => {
  return (
    <StyledTooltip
      enterDelay={enterDelay}
      enterTouchDelay={enterTouchDelay}
      tooltip={tooltip}
      placement={placement}
      zIndex={zIndex}
    >
      {cloneElement(button, {})}
    </StyledTooltip>
  )
}

export default ButtonTooltip
