import { cloneElement, useCallback, useEffect, useState } from 'react'
import { css, styled } from 'styled-components'

import Icon from '~/components/misc/Icon/Icon'
import Tooltip from '~/components/misc/Mntr/Tooltip'

const PopperWrapper = styled.div`
  background: ${({ popupBg, theme }) => popupBg || theme.popper.background};
  ${({ popupBg }) =>
    popupBg !== 'transparent' &&
    css`
      border-radius: ${({ theme }) => theme.paper.borderRadius}px;
      box-shadow: ${({ theme }) => theme.popper.boxShadow};
    `}

  ${({ popupwidth }) =>
    popupwidth > 0 &&
    css`
      width: ${popupwidth}px;
    `}

  & .menu-heading {
    color: ${({ theme }) => theme.popper.heading};
  }

  & .form-label {
    color: ${({ theme }) => theme.popper.formLabel};
  }

  & textarea,
  & input {
    color: ${({ theme }) => theme.popper.formLabel};
    &::placeholder {
      color: ${({ theme }) => theme.popper.formLabel};
      opacity: 0.8;
    }
  }
`
const ButtonWrapper = styled.div`
  display: inline-block;
`
const LightTooltip = styled(Tooltip)`
  background: transparent;
  max-width: none;
  color: rgba(0, 0, 0, 0.87);
  border-radius: 5px;
  margin-top: 0;
  padding: 0;
`

const ButtonHover = ({
  popup,
  button,
  popupBg,
  popupWidth,
  hoverDelay = 150,
  popupPlacement = 'bottom-start',
  zIndex = 4500,
}) => {
  const [isOpen, setOpen] = useState(false)
  const [isForceOpen, setForceOpen] = useState(false)
  const [hovered, sethovered] = useState(false)
  const [leaved, setleaved] = useState(false)

  const TIMEOUT_MOUSE_ENTER = hoverDelay
  const TIMEOUT_MOUSE_LEAVE = 0

  const onTimeoutEnter = useCallback(() => {
    setOpen(true)
    sethovered(false)
  }, [])

  const onTimeoutLeave = useCallback(() => {
    setOpen(false)
    setleaved(false)
    setForceOpen(false)
  }, [])

  useEffect(() => {
    const timer = hovered && setTimeout(onTimeoutEnter, TIMEOUT_MOUSE_ENTER)
    return () => {
      timer && clearTimeout(timer)
    }
  }, [hovered, onTimeoutEnter, TIMEOUT_MOUSE_ENTER])

  useEffect(() => {
    const timer = leaved && setTimeout(onTimeoutLeave, TIMEOUT_MOUSE_LEAVE)
    return () => {
      timer && clearTimeout(timer)
    }
  }, [leaved, onTimeoutLeave])

  const mouseEnterHandler = () => {
    isOpen ? setleaved(false) : sethovered(true)
  }
  const mouseLeaveHandler = () => {
    isOpen && !isForceOpen ? setleaved(true) : sethovered(false)
  }

  function handleButtonMouseDown(event) {
    event.stopPropagation()
    setForceOpen(true)
    onTimeoutEnter()
  }

  let buttonClassName = ''

  if (isOpen) {
    buttonClassName = 'hovered'
  }

  if (isForceOpen) {
    buttonClassName = 'active'
  }

  return (
    <ButtonWrapper onMouseEnter={mouseEnterHandler} onMouseLeave={mouseLeaveHandler}>
      {isForceOpen && (
        <div
          onClick={() => onTimeoutLeave()}
          style={{
            background: 'transparent',
            position: 'fixed',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            zIndex: 1701,
          }}
        />
      )}
      <LightTooltip
        open={isOpen || isForceOpen}
        placement={popupPlacement}
        interactive
        enterDelay={isOpen ? 120 : 0}
        title={
          <PopperWrapper popupwidth={popupWidth} className="popper-menu" popupBg={popupBg}>
            {popup(
              () => onTimeoutLeave(),
              () => setForceOpen(true),
            )}
          </PopperWrapper>
        }
        zIndex={zIndex}
      >
        {cloneElement(button, {
          ...(!button.props.onClick && { onMouseDown: handleButtonMouseDown }),
          bg: isForceOpen ? 'primary' : button.props.bg,
          startIcon: isForceOpen ? (
            <Icon size={22}>close</Icon>
          ) : (
            <Icon size={22}>{button.props.startIcon}</Icon>
          ),
          className: buttonClassName,
        })}
      </LightTooltip>
    </ButtonWrapper>
  )
}

export default ButtonHover
