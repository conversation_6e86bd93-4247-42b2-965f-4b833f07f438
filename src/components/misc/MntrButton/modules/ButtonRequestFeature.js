import { Trans } from '@lingui/react/macro'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalRequestFeature from '~/helpers/modal/withModalRequestFeature'
import { observer } from '~/helpers/mst'

const ButtonRequestFeature = ({
  appStore: { featureRequest },
  label,
  icon,
  size,
  requestId,
  forceRequest,
  modal,
  title,
  submitLabel,
  fullWidth,
  isChip = true,
  bg = 'secondary',
}) => {
  if (featureRequest.hasRequested(requestId) && !forceRequest) {
    return (
      <MntrButton
        size={size || 'default'}
        disabled
        label={<Trans id="featureRequest.Requested">Requested</Trans>}
        rounded
        isChip={isChip}
      />
    )
  }
  return (
    <MntrButton
      bg={bg}
      size={size || 'default'}
      icon={icon}
      label={label || <Trans>Request Access?</Trans>}
      rounded
      isChip={isChip}
      fullWidth={fullWidth}
      {...withModalRequestFeature({
        title,
        modal,
        submitLabel,
        onSubmit: () => {
          featureRequest.sendFeatureRequest(requestId)
        },
      })}
    />
  )
}

export default observer(ButtonRequestFeature)
