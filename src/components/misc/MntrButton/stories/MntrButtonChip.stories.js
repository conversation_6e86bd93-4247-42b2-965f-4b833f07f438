import { action } from '@storybook/addon-actions'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'

export default { title: 'Chip' }

export const Default = () => {
  return (
    <>
      <Box>
        <MntrButton isChip label="Disabled" mr={1} disabled />
        <MntrButton isChip label="Text only" mr={1} />
        <MntrButton isChip onClick={action('clicked')} label="Default" mr={1} />
        <MntrButton isChip onClick={action('clicked')} bg="primary" label="Primary" mr={1} />
        <MntrButton
          isChip
          onClick={action('clicked')}
          bg="primary"
          label="Delete"
          onDelete={action('delete')}
          mr={1}
        />
        <MntrButton isChip onClick={action('clicked')} bg="keyword" label="Keyword" mr={1} />
      </Box>
    </>
  )
}

export const WithIcons = () => {
  return (
    <>
      <Box>
        <MntrButton isChip label="Text only" mr={1} icon="person" mb={1} />
        <MntrButton
          isChip
          onClick={action('clicked')}
          icon="date_range"
          label="Default"
          mr={1}
          mb={1}
        />
        <MntrButton
          isChip
          onClick={action('clicked')}
          bg="primary"
          icon="date_range"
          label="Primary"
          mr={1}
          mb={1}
        />
        <MntrButton
          isChip
          onClick={action('clicked')}
          bg="primary"
          icon="date_range"
          label="Delete"
          onDelete={action('delete')}
          mr={1}
          mb={1}
        />
      </Box>
      <Box>
        <MntrButton isChip onClick={action('clicked')} flagLanguage="cs" label="Flag" mr={1} />
        <MntrButton
          isChip
          onClick={action('clicked')}
          bg="primary"
          icon="date_range"
          label="Primary"
          mr={1}
        />
        <MntrButton
          isChip
          bg="primary"
          onClick={action('clicked')}
          icon="date_range"
          label="Delete"
          onDelete={action('delete')}
          mr={1}
        />
      </Box>
    </>
  )
}

const menuItemsLanguages = [
  {
    label: 'Filtrovat podle země',
  },
  {
    label: 'Česká media',
    flag: 'cs',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Slovenská média',
    flag: 'sk',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Zahraniční média',
    flag: 'en',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Filtrovat podle země',
  },
  {
    label: 'Čeština',
    flag: 'cs',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Slovenština',
    flag: 'sk',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Angličtina',
    flag: 'en',
    onClick: () => {
      action('clicked')('cs')
    },
  },
]

export const WithPopup = () => {
  return (
    <>
      <MntrButton
        isChip
        popup={(closePopup) => {
          return <MntrMenu menuItems={menuItemsLanguages} closePopup={closePopup} />
        }}
        label="Primary"
        mr={1}
      />
      <MntrButton
        icon="add"
        isChip
        popup={(closePopup) => {
          return <MntrMenu menuItems={menuItemsLanguages} closePopup={closePopup} />
        }}
        label="secondary"
        mr={1}
      />
      <MntrButton
        isChip
        bg="primary"
        onClick={action('clicked')}
        icon="date_range"
        label="Delete"
        onDelete={action('delete')}
        mr={1}
        popup={(closePopup) => {
          return <MntrMenu menuItems={menuItemsLanguages} closePopup={closePopup} />
        }}
      />
    </>
  )
}
