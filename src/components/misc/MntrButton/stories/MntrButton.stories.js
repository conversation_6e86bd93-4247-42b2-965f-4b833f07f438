import { action } from '@storybook/addon-actions'
import noopFn from 'lodash/noop'
import { Box, Flex } from '~/components/misc/Mntr'
import ButtonGroup from '~/components/misc/Mntr/ButtonGroup'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import ButtonOAuth from '~/components/misc/MntrButton/modules/ButtonOAuth'
import MntrForm from '~/components/misc/MntrForm/MntrForm'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalRemove from '~/helpers/modal/withModalRemove'
import defaultTheme from '~/styles/theme/defaultTheme'

export default { title: 'Button' }

export const Default = () => {
  const buttonGroup = Object.keys(defaultTheme.buttons).filter(
    (item) =>
      item.indexOf('Hover') === -1 &&
      item.indexOf('BoxShadow') === -1 &&
      item.indexOf('Color') === -1,
  )
  return (
    <>
      <Box mb={2}>
        {buttonGroup.map((button, index) => {
          return <MntrButton key={index.toString()} bg={button} label={button} mr={1} />
        })}
      </Box>
      <Box>
        {buttonGroup.map((button, index) => {
          return <MntrButton rounded key={index.toString()} bg={button} label={button} mr={1} />
        })}
      </Box>
    </>
  )
}

export const WithIcons = () => {
  return (
    <>
      <Box>
        <MntrButton bg="flat" icon="edit" onClick={action('clicked')} label="Flat" mr={1} />
        <MntrButton icon="edit" onClick={action('clicked')} label="Default" mr={1} />
        <MntrButton icon="add" bg="primary" onClick={action('clicked')} label="Primary" mr={1} />
        <MntrButton
          icon="download"
          bg="secondary"
          onClick={action('clicked')}
          label="Secondary"
          mr={1}
        />
        <MntrButton
          icon="open_in_browser"
          bg="neutral"
          onClick={action('clicked')}
          label="Neutral"
          mr={1}
        />
        <MntrButton icon="close" bg="error" onClick={action('clicked')} label="Error" />
      </Box>

      <Box mt={2}>
        <MntrButton bg="flat" rounded icon="edit" onClick={action('clicked')} label="Flat" mr={1} />
        <MntrButton rounded icon="edit" onClick={action('clicked')} label="Default" mr={1} />
        <MntrButton
          rounded
          icon="add"
          bg="primary"
          onClick={action('clicked')}
          label="Primary"
          mr={1}
        />
        <MntrButton
          rounded
          icon="download"
          bg="secondary"
          onClick={action('clicked')}
          label="Secondary"
          mr={1}
        />
        <MntrButton
          rounded
          icon="open_in_browser"
          bg="neutral"
          onClick={action('clicked')}
          label="Neutral"
          mr={1}
        />
        <MntrButton rounded icon="close" bg="error" onClick={action('clicked')} label="Error" />
      </Box>
      <Box mt={2}>
        <MntrButton bg="flat" icon="edit" onClick={action('clicked')} mr={1} />
        <MntrButton icon="edit" onClick={action('clicked')} mr={1} />
        <MntrButton icon="add" bg="primary" onClick={action('clicked')} mr={1} />
        <MntrButton icon="download" bg="secondary" onClick={action('clicked')} mr={1} />
        <MntrButton icon="send" bg="tertiary" onClick={action('clicked')} mr={1} />
        <MntrButton icon="open_in_browser" bg="neutral" onClick={action('clicked')} mr={1} />
        <MntrButton icon="close" bg="error" onClick={action('clicked')} />
      </Box>
    </>
  )
}

const menuItems = [
  {
    label: 'Open',
    leftIcon: 'open_in_full',
    onClick: () => {
      action('clicked')('Open')
    },
  },
  {
    label: 'Open on hover',
    leftIcon: 'download',
    hover: true,
    subMenuItems: [
      {
        label: 'Rychlý export',
      },
      {
        label: 'HTML',
        hover: true,
        subMenuItems: [
          {
            label: 'Recursive shit',
          },
          {
            label: 'HTML',
            onClick: () => {
              action('clicked')('HTML')
            },
          },
          {
            label: 'RTF',
            onClick: () => {
              action('clicked')('RTF')
            },
          },
          {
            label: 'PDF',
            onClick: () => {
              action('clicked')('PDF')
            },
          },
        ],
      },
      {
        label: 'RTF',
        onClick: () => {
          action('clicked')('RTF')
        },
      },
      {
        label: 'PDF',
        onClick: () => {
          action('clicked')('PDF')
        },
      },
    ],
  },
  {
    label: 'Open on click',
    leftIcon: 'send',
    onClick: () => {
      action('clicked')('Download')
    },
    subMenuItems: [
      {
        label: 'Rychlý export',
      },
      {
        label: 'HTML',
        onClick: () => {
          action('clicked')('HTML')
        },
        subMenuItems: [
          {
            label: 'Recursive shit',
          },
          {
            label: 'HTML',
            onClick: () => {
              action('clicked')('HTML')
            },
          },
          {
            label: 'RTF',
            onClick: () => {
              action('clicked')('RTF')
            },
          },
          {
            label: 'PDF',
            onClick: () => {
              action('clicked')('PDF')
            },
          },
        ],
      },
      {
        label: 'RTF',
        onClick: () => {
          action('clicked')('RTF')
        },
      },
      {
        label: 'PDF',
        onClick: () => {
          action('clicked')('PDF')
        },
      },
    ],
  },
  {
    label: 'Přidat do exportu',
    leftIcon: 'move_to_inbox',
    onClick: noopFn,
  },
  {
    label: 'Vyhledat podobné články',
    leftIcon: 'compare',
    onClick: noopFn,
  },
  {},
  {
    label: 'Nahlásit problém',
    leftIcon: 'support_agent',
    hoverVariant: 'error',
    onClick: noopFn,
  },
  {
    label: 'Neodebírat',
    leftIcon: 'report_off',
    hoverVariant: 'error',
    onClick: noopFn,
  },
  {
    label: 'Odstranit článek',
    leftIcon: 'delete',
    hoverVariant: 'error',
    ...withModalRemove({
      onSubmit: () => action('clicked')('submit remove'),
      message: 'Prejete si odstranit..',
    }),
  },
  {},
  {
    label: 'Admin',
  },
  {
    label: 'Edit',
    leftIcon: 'edit',
    hoverVariant: 'secondary',
    onClick: noopFn,
  },
]

const menuItemsLanguages = [
  {
    label: 'Filtrovat podle země',
  },
  {
    label: 'Česká media',
    flag: 'cs',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Slovenská média',
    flag: 'sk',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Zahraniční média',
    flag: 'en',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Filtrovat podle země',
  },
  {
    label: 'Čeština',
    flag: 'cs',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Slovenština',
    flag: 'sk',
    onClick: () => {
      action('clicked')('cs')
    },
  },
  {
    label: 'Angličtina',
    flag: 'en',
    onClick: () => {
      action('clicked')('cs')
    },
  },
]

export const WithPopup = () => {
  return (
    <>
      <Box style={{ textAlign: 'right' }}>
        <MntrButton
          bg="primary"
          popup={(closePopup) => {
            return <MntrMenu menuItems={menuItemsLanguages} closePopup={closePopup} />
          }}
          label="With tooltip"
          tooltip="tooltip"
          mr={1}
        />
        <MntrButton
          icon="add"
          bg="secondary"
          popup={(closePopup) => {
            return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
          }}
          label="secondary"
          mr={1}
        />
        <MntrButton
          rounded
          icon="more_vert"
          popup={(closePopup) => {
            return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
          }}
          tooltip="menu"
          mr={1}
        />
        <MntrButton
          rounded
          icon="more_vert"
          popup={(closePopup) => {
            return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
          }}
          mr={1}
        />
        <MntrButton
          rounded
          icon="add"
          popup={(closePopup) => {
            return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
          }}
          label="Default"
        />
      </Box>
    </>
  )
}

export const WithModal = () => {
  return (
    <>
      <Box>
        <MntrButton
          bg="primary"
          modal={(closeModal) => {
            return (
              <div>
                <button onClick={closeModal}>custom close</button>
              </div>
            )
          }}
          label="Tooltip"
          tooltip="Tooltip"
          mr={1}
        />
        <MntrButton
          icon="add"
          bg="secondary"
          modal={(closeModal) => {
            return (
              <div>
                <button onClick={closeModal}>custom close</button>
              </div>
            )
          }}
          label="Přidat téma"
          mr={1}
        />
        <MntrButton
          rounded
          icon="more_vert"
          modalTitle="Custom Title + Bg + Icon + max height"
          modalIcon="support_agent"
          modalBg="tertiary"
          modalWidth={1024}
          modal={(closeModal) => {
            return (
              <div>
                <button onClick={closeModal}>custom close</button>
              </div>
            )
          }}
          mr={1}
        />
        <MntrButton
          rounded
          icon="add"
          modal={(closeModal) => {
            return (
              <div>
                <Box>
                  <ul>
                    <li>Nunc nec ante sed lacus congue molestie.</li>
                    <li>Donec interdum felis a tellus tincidunt porttitor ac id odio.</li>
                    <li>Aliquam vel nisl at augue consectetur lacinia.</li>
                    <li>Nullam dapibus ipsum in purus gravida, vel pulvinar turpis vestibulum.</li>
                    <li>Pellentesque sodales ipsum eu urna pharetra suscipit.</li>
                    <li>Morbi porta enim eget erat mattis, vel mattis risus efficitur.</li>
                    <li>Aliquam sodales nisi vel vestibulum convallis.</li>
                    <li>Mauris interdum turpis in congue imperdiet.</li>
                    <li>Nunc ac nulla sed mauris laoreet porttitor.</li>
                    <li>Pellentesque at tellus malesuada, tempor leo at, vestibulum erat.</li>
                    <li>Etiam quis felis in justo ultricies facilisis eu sit amet ex.</li>
                    <li>Donec vehicula magna vehicula, tincidunt metus nec, tempor diam.</li>
                    <li>Praesent et dolor gravida lorem pellentesque malesuada.</li>
                    <li>Duis nec quam tristique, congue purus et, viverra diam.</li>
                    <li>Aliquam rhoncus arcu et porttitor dignissim.</li>
                    <li>Vivamus accumsan velit quis blandit egestas.</li>
                    <li>Morbi faucibus lorem ut neque ultrices, quis auctor ligula dictum.</li>
                    <li>Praesent rutrum est a justo ultrices dictum.</li>
                    <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
                    <li>Suspendisse commodo orci vitae hendrerit aliquet.</li>
                    <li>Curabitur eu mauris a purus porta euismod vitae ut ipsum.</li>
                    <li>Nunc nec ante sed lacus congue molestie.</li>
                    <li>Donec interdum felis a tellus tincidunt porttitor ac id odio.</li>
                    <li>Aliquam vel nisl at augue consectetur lacinia.</li>
                    <li>Nullam dapibus ipsum in purus gravida, vel pulvinar turpis vestibulum.</li>
                    <li>Pellentesque sodales ipsum eu urna pharetra suscipit.</li>
                    <li>Morbi porta enim eget erat mattis, vel mattis risus efficitur.</li>
                    <li>Aliquam sodales nisi vel vestibulum convallis.</li>
                    <li>Mauris interdum turpis in congue imperdiet.</li>
                    <li>Nunc ac nulla sed mauris laoreet porttitor.</li>
                    <li>Pellentesque at tellus malesuada, tempor leo at, vestibulum erat.</li>
                    <li>Etiam quis felis in justo ultricies facilisis eu sit amet ex.</li>
                    <li>Donec vehicula magna vehicula, tincidunt metus nec, tempor diam.</li>
                    <li>Praesent et dolor gravida lorem pellentesque malesuada.</li>
                    <li>Duis nec quam tristique, congue purus et, viverra diam.</li>
                    <li>Aliquam rhoncus arcu et porttitor dignissim.</li>
                    <li>Vivamus accumsan velit quis blandit egestas.</li>
                    <li>Morbi faucibus lorem ut neque ultrices, quis auctor ligula dictum.</li>
                  </ul>
                </Box>
                <Flex column alignItems="end" px={4} py={3}>
                  <ButtonGroup
                    buttons={[
                      { label: 'Button 1', onClick: () => action('Button 1 click') },
                      { label: 'Button 2', icon: 'close', onClick: () => action('Button 2 click') },
                      { icon: 'close', onClick: closeModal },
                    ]}
                    gap={4}
                  ></ButtonGroup>
                </Flex>
              </div>
            )
          }}
          modalBg="flat"
          label="Flat"
          mr={1}
        />
        <MntrButton
          rounded
          icon="remove"
          label="With MntrForm"
          modal={(closeModal) => {
            return (
              <MntrForm
                schema={[
                  { label: 'length > 3 validation', name: 'name' },
                  {
                    name: 'multiline',
                    label: 'Multiline',
                    multiline: true,
                    minRows: 3,
                  },
                  {
                    name: 'email',
                    label: 'Email',
                    autoComplete: 'username email',
                  },
                  {
                    name: 'test',
                    label: 'with placeholder',
                    placeholder: 'placeholder test',
                  },
                  {
                    name: 'with_tooltip',
                    label: 'with tooltip info',
                    tooltip: 'Tooltip example of additional info',
                  },
                  {
                    name: 'with_description',
                    label: 'with descripoon info',
                    description: 'Description example of additional content for the field',
                  },
                  {
                    name: 'address',
                    label: 'Address',
                  },
                  {
                    name: 'type',
                    adapter: 'select',
                    label: 'Selector',
                    items: [
                      { value: '1', label: 'Type 1' },
                      { value: '2', label: 'Type 2' },
                    ],
                  },
                  {
                    actions: () => {
                      return [
                        {
                          bg: 'transparent',
                          rounded: true,
                          label: 'Close',
                          onClick: closeModal,
                        },
                        {
                          bg: 'secondary',
                          rounded: true,
                          label: 'Submit',
                          type: 'submit',
                        },
                      ]
                    },
                  },
                ]}
                validate={(values) => {
                  const errors = {}

                  if (values.name?.length <= 3) {
                    errors.name = 'Length must be greater then 3'
                  }

                  return errors
                }}
                onSubmit={closeModal}
              />
            )
          }}
        />

        <MntrButton
          rounded
          icon="remove"
          label="withModalRemove"
          {...withModalRemove({
            onSubmit: () => action('clicked')('submit remove'),
            message: 'Prejete si odstranit..',
          })}
        />
      </Box>
    </>
  )
}

export const WithTooltip = () => {
  return (
    <>
      <Box>
        <MntrButton bg="primary" tooltip="Přeposlat" label="Primary" mr={1} />
        <MntrButton icon="send" bg="secondary" tooltip="Přeposlat" label="secondary" mr={1} />
        <MntrButton rounded icon="send" tooltip="Přeposlat" mr={1} />
        <MntrButton rounded icon="send" tooltip="Přeposlat" label="Default" />
      </Box>
    </>
  )
}

export const WithHoverPopup = () => {
  return (
    <>
      <Box>
        <MntrButton
          bg="primary"
          icon="date_range"
          hoverable
          popup={(closePopup) => {
            return <MntrMenu menuItems={menuItemsLanguages} closePopup={closePopup} />
          }}
          label="Date"
        />
        <MntrButton
          bg="primary"
          icon="input"
          hoverable
          popup={(closePopup, forceOpen) => {
            return (
              <div>
                <MntrButton
                  bg="flat"
                  onClick={() => {
                    closePopup()
                    action('clicked')('Popup MntrButton')
                  }}
                  label="Click and Close"
                />
                <MntrButton
                  bg="flat"
                  onClick={() => {
                    forceOpen()
                    action('clicked')('Force Open')
                  }}
                  label="Force Open"
                />
              </div>
            )
          }}
          label="Source"
        />
      </Box>
    </>
  )
}

export function WithLink() {
  return (
    <Box m={-1}>
      <Flex py={1}>
        <Box mx={1}>
          <MntrButton href="/some/place" label="href" />
        </Box>
        <Box mx={1}>
          <MntrButton
            href="/some/place"
            label="href + onClick"
            onClick={() => {
              alert('doing things onClick')
            }}
          />
        </Box>
        <Box mx={1}>
          <MntrButton href="/some/place" icon="link" />
        </Box>
      </Flex>
      <Flex py={1}>
        <Box mx={1}>
          <MntrButton
            label="onClick"
            onClick={() => {
              alert('doing things onClick')
            }}
          />
        </Box>
        <Box mx={1}>
          <a href="https://example.com/" style={{ textDecoration: 'none' }} target="_blank">
            <MntrButton icon="open_in_new" />
          </a>
        </Box>
      </Flex>
    </Box>
  )
}

export function ForMainMenu() {
  return (
    <Box
      style={{
        position: 'fixed',
        top: 0,
        bottom: 0,
        left: 0,
        paddingTop: 10,
        paddingBottom: 10,
        width: 48,
        background: '#3c485e',
      }}
    >
      <Flex style={{ height: '100%' }} flexDirection="column" justifyContent="space-between">
        <Box>
          <MntrButton
            active
            href="/some/place"
            icon="language"
            isMainMenu
            placement="right"
            tooltip="Monitoring"
          />
          <MntrButton icon="dashboard" isMainMenu placement="right" tooltip="Dashboard" />
        </Box>
        <Box>
          <MntrButton
            icon="help"
            isMainMenu
            popup={(closePopup) => {
              return <MntrMenu menuItems={menuItemsLanguages} closePopup={closePopup} />
            }}
          />
        </Box>
      </Flex>
    </Box>
  )
}

export function GoogleAuth() {
  return (
    <Box>
      <ButtonOAuth type="google" label="Sign in with Google" url="https://google.com" />
    </Box>
  )
}

export function MSAuth() {
  return (
    <Box>
      <ButtonOAuth type="microsoft" label="Sign in with Microsoft" url="https://microsoft.com" />
    </Box>
  )
}
