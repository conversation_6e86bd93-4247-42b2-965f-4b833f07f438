import isEqual from 'lodash/isEqual'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useState } from 'react'
import {
  FALLBACK_FAKE_ID,
  INTERCOM_BLACKLISTED_PATHNAMES,
  INTERCOM_HIDDEN_PATHNAMES,
} from '~/constants/gtm'
import { ObservedFC, observer } from '~/helpers/msts'

const Intercom: ObservedFC = ({
  appStore: {
    account: { intercom, user },
    appLanguage,
    appSettings: { gtmProductId, enableIntercom },
    monitoring,
    theme,
    viewport: { isMobile, isTablet },
  },
}) => {
  const { pathname } = useRouter()
  const makeIntercomSettings = useCallback(() => {
    return {
      app_id: window?.APP_ID,
      ...intercom,
      ...(intercom?.email
        ? {
            action_color: theme.colors.primary,
            background_color: theme.colors.primary,
          }
        : {
            action_color: null,
            background_color: null,
          }),
      'moni-domain': location.hostname,
      hide_default_launcher:
        isMobile ||
        (isTablet && monitoring.isOpenPortable) ||
        INTERCOM_HIDDEN_PATHNAMES.includes(pathname),
      language_override: appLanguage,
    }
  }, [
    appLanguage,
    intercom,
    isMobile,
    isTablet,
    monitoring.isOpenPortable,
    pathname,
    theme.colors.primary,
  ])

  const newIntercomSettings = makeIntercomSettings()
  const [intercomSettings, setIntercomSettings] = useState(newIntercomSettings)
  const [productId, setProductId] = useState(gtmProductId)

  const [isInstalled, setInstallState] = useState(!!window?.Intercom)
  const [isBooted, setBootState] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      if (!isInstalled && !!window.Intercom) {
        clearInterval(interval)
        setInstallState(true)
      }
    }, 100)
  }, [isInstalled])

  useEffect(() => {
    const intercomSettingsDidUpdate = !isEqual(intercomSettings, newIntercomSettings)

    if (intercomSettingsDidUpdate) {
      setIntercomSettings(newIntercomSettings)
    }

    if (productId !== gtmProductId) {
      setProductId(gtmProductId)
    }

    if (isInstalled) {
      if (!enableIntercom || user.isImpersonating || window.APP_ID === FALLBACK_FAKE_ID) {
        if (isBooted) {
          console.log('[Intercom] shutdown')
          window.Intercom('shutdown')
          setBootState(false)
        }

        return
      }

      if (intercomSettingsDidUpdate) {
        if (isBooted) {
          if (newIntercomSettings.email) {
            console.log('[Intercom] update')
            window.Intercom('update', newIntercomSettings)
          } else {
            console.log('[Intercom] reboot')
            window.Intercom('shutdown')
            window.Intercom('boot', newIntercomSettings)
          }
        } else if (enableIntercom && !INTERCOM_BLACKLISTED_PATHNAMES.includes(pathname)) {
          console.log('[Intercom] boot')
          window.Intercom('boot', newIntercomSettings)
          setBootState(true)
        }
      }
    }
  }, [
    //
    enableIntercom,
    gtmProductId,
    intercomSettings,
    isBooted,
    user.isImpersonating,
    isInstalled,
    newIntercomSettings,
    pathname,
    productId,
  ])

  return null
}

export default observer(Intercom)
