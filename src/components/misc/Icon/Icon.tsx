import { isValidElement, ReactNode } from 'react'
import { css, DefaultTheme, styled } from 'styled-components'

// Typ pro klíče v theme.colors
type ThemeColor = keyof DefaultTheme['colors']

interface StyledIconProps {
  size: number
  color?: ThemeColor | string // Barva může být buď klíč theme.colors, nebo custom string
  isFilled: number | boolean
  iconOffsetX: number
  children: ReactNode
  role?: string
}

const StyledIcon = styled.i<StyledIconProps>`
  font-size: ${({ size }) => size}px !important;

  ${({ color, theme }) => {
    if (color && theme.colors[color as ThemeColor]) {
      return css`
        color: ${theme.colors[color as ThemeColor]} !important;
      `
    }
    if (color) {
      return css`
        color: ${color} !important;
      `
    }
    return null // Return null if no conditions are met
  }}

  font-variation-settings: 'FILL' ${({ isFilled }) => (isFilled ? 1 : 0)};

  ${({ children, size }) => {
    if (!isValidElement(children)) {
      return css`
        width: ${size}px;
        height: ${size}px;
        overflow: hidden;
      `
    }
    return null // Return null if children is a valid React element
  }}

  ${({ iconOffsetX }) =>
    iconOffsetX > 0 &&
    css`
      position: relative;
      left: ${iconOffsetX}px;
    `}
`

interface IconProps {
  color?: ThemeColor | string
  children: ReactNode
  role?: string
  iconOffsetX?: number
  size?: number
  fill?: number | boolean
}

const Icon = ({ color, children, role, iconOffsetX = 0, size = 22, fill = 0 }: IconProps) => {
  return (
    <StyledIcon
      size={size}
      className="material-symbols-outlined"
      color={color}
      isFilled={fill}
      iconOffsetX={iconOffsetX}
      role={role}
    >
      {children}
    </StyledIcon>
  )
}

export default Icon
