import { styled } from 'styled-components'

const ListWrapper = styled.div`
  max-height: ${({ maxheight }) => maxheight}px;
  max-width: ${({ maxWidth }) => maxWidth}px;
  overflow: auto;
`

const ListScrollWrapper = ({ delta = 200, children, maxWidth = '375' }) => {
  const maxheight =
    Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0) - delta

  return (
    <ListWrapper maxheight={Math.min(maxheight, 1000)} maxWidth={maxWidth}>
      {children}
    </ListWrapper>
  )
}

export default ListScrollWrapper
