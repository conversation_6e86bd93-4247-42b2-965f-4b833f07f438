import { Trans } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import noopFn from 'lodash/noop'
import { Field } from 'react-final-form'
import { styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box, Flex } from '~/components/misc/Mntr'
import ColorPickerSelector from './ColorPickerSelector'

const StyledColorField = styled(Field)`
  margin: 4px;
  height: 30px;
  width: 44px;
  cursor: pointer;
`

const StyledColorBox = styled(Box)`
  width: 54px;
  height: 38px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
`

const ColorPicker = ({
  color,
  label,
  colorPalette = [],
  isTouchDevice,
  onColorChange,
  onColorChangeComplete,
  customElement,
  setIsOpenPalette = noopFn,
  replaceColorPalette = false,
}) => {
  return (
    <div>
      {!isTouchDevice && (
        <Flex mt={customElement ? 0 : 2}>
          <ColorPickerSelector
            label={label}
            color={color}
            colorPalette={colorPalette}
            onChangeColor={onColorChange}
            onChangeColorComplete={onColorChangeComplete}
            replaceColorPalette={replaceColorPalette}
            customElement={customElement}
          />
        </Flex>
      )}

      {isTouchDevice && (
        <Flex mt={2}>
          <StyledColorBox onClick={setIsOpenPalette}>
            <StyledColorField
              parse={identityFn}
              name="color"
              type="color"
              id="color"
              component={MntrTextFieldAdapter}
              placeholder={label || <Trans>Select color</Trans>}
              disableUnderline
            />
          </StyledColorBox>
          <Box py={8} px={2}>
            <label onClick={setIsOpenPalette} htmlFor="color" style={{ cursor: 'pointer' }}>
              {label || <Trans>Select color</Trans>}
            </label>
          </Box>
        </Flex>
      )}
    </div>
  )
}

export default ColorPicker
