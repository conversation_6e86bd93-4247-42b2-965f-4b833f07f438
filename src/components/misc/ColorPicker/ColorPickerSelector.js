import { Trans } from '@lingui/react/macro'
import colorFn from 'color'
import { useEffect, useState } from 'react'
import { SketchPicker } from 'react-color'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { topicColors } from '~/constants/colors'

const makeColorState = (colorString) => {
  const [r, g, b] = colorFn(colorString).rgb().color

  return {
    r: r.toString(),
    g: g.toString(),
    b: b.toString(),
    a: '1',
  }
}

const ColorPickerSelector = ({
  color,
  label,
  customElement,
  onChangeColor,
  onChangeColorComplete,
  colorPalette,
  replaceColorPalette,
}) => {
  const [displayColorPicker, toggleColorPicker] = useState(false)
  const [colorObj, setColor] = useState(makeColorState(color))
  const styles = {
    color: {
      width: '36px',
      height: '20px',
      borderRadius: '2px',
      background: `rgba(${colorObj.r}, ${colorObj.g}, ${colorObj.b}, ${colorObj.a})`,
    },
    swatch: {
      padding: '5px',
      background: '#fff',
      borderRadius: '1px',
      boxShadow: '0 0 0 1px rgba(0,0,0,.1)',
      display: 'inline-block',
      cursor: 'pointer',
    },
    label: {
      display: 'inline-block',
      cursor: 'pointer',
      position: 'relative',
      top: -8,
      left: 8,
    },
    popover: {
      position: 'fixed',
      zIndex: '5000',
      marginTop: -260,
    },
    cover: {
      position: 'fixed',
      top: '0px',
      right: '0px',
      bottom: '0px',
      left: '0px',
    },
  }

  useEffect(() => {
    setColor(makeColorState(color))
  }, [color])

  const handleClick = () => toggleColorPicker(!displayColorPicker)

  const handleChange = (newColor) => {
    setColor(newColor.rgb)

    if (typeof onChangeColor === 'function') {
      onChangeColor(
        colorFn(`rgb(${newColor.rgb.r}, ${newColor.rgb.g}, ${newColor.rgb.b})`).hex().toString(),
      )
    }
  }

  const handleChangeComplete = (newColor) => {
    if (typeof onChangeColorComplete === 'function') {
      onChangeColorComplete(
        colorFn(`rgb(${newColor.rgb.r}, ${newColor.rgb.g}, ${newColor.rgb.b})`).hex().toString(),
      )
    }
  }

  return (
    <MntrButton
      customElement={
        customElement || (
          <div>
            <span style={styles.swatch} onClick={handleClick}>
              <div style={styles.color} />
            </span>
            <span style={styles.label} onClick={handleClick}>
              {label || <Trans>Select color</Trans>}
            </span>
          </div>
        )
      }
      popup={() => {
        return (
          <Box py="10px">
            <SketchPicker
              width={204}
              disableAlpha
              color={colorObj}
              onChange={handleChange}
              onChangeComplete={handleChangeComplete}
              presetColors={replaceColorPalette ? colorPalette : topicColors.concat(colorPalette)}
            />
          </Box>
        )
      }}
      popupPlacement="bottom-start"
      zIndex={6001}
    />
  )
}

export default ColorPickerSelector
