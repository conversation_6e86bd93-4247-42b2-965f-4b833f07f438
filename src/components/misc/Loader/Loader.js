import { styled } from 'styled-components'
import { Box } from '~/components/misc/Mntr'

const LoadingWrapper = styled.div`
  width: 100%;
  height: 48px;
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  border: 1px solid ${({ theme }) => theme.paper.background};
  background: ${({ theme }) => theme.paper.background};
  margin-top: 6px;
  overflow: hidden;
`

const Loader = () => {
  const placeholderArray = Array(20).fill(1)

  return (
    <Box mt={2}>
      {placeholderArray.map((item, index) => (
        <LoadingWrapper key={index.toString()}></LoadingWrapper>
      ))}
    </Box>
  )
}

export default Loader
