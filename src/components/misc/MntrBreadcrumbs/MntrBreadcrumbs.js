import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const Wrapper = styled(Flex)`
  @media (max-width: ${({ theme }) => theme.breakpoints[1]}) {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    width: 100%;
    -webkit-overflow-scrolling: touch;
  }
`

const MntrBreadcrumbs = ({ items }) => {
  // generate breadcrumbs items
  const breadcrumbsItems = items
    .map((item, index) => {
      // label only - used for last item
      if (!item.href) {
        return (
          <Text
            fontSize={['14px', 1]}
            fontWeight="bold"
            px={2}
            key={index.toString()}
            position="relative"
            top={[0, '-2px']}
            color="black"
            whiteSpace="nowrap"
          >
            {item.label}
          </Text>
        )
      }

      // icon only - used for first item
      if (!item.label && item.icon) {
        return <MntrButton {...item} key={index.toString()} bg="transparent" mr={1} />
      }

      return <MntrButton {...item} key={index.toString()} bg="transparent" isChip />
    })
    .reduce((prev, curr) => [
      prev,
      <Icon key={prev.toString()} color={curr ? 'mediumGrey' : 'black'}>
        chevron_right
      </Icon>,
      curr,
    ])

  return (
    <Wrapper className="ios-scroll">
      <Flex flexWrap="nowrap" centerY>
        {breadcrumbsItems}
      </Flex>
    </Wrapper>
  )
}

export default MntrBreadcrumbs
