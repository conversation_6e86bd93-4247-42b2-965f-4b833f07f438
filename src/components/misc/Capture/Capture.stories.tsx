import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { styled } from 'styled-components'
import { Flex } from '~/components/misc/Mntr'
import Capture from './Capture'

const Pre = styled.pre`
  padding: 1em;
  border-radius: 0.33em;

  &:nth-child(odd) {
    background: #7549fb;
    color: #2fffc5;
  }

  &:nth-child(even) {
    background: #2fffc5;
    color: #7549fb;
  }
`

const meta = {
  title: 'Capture',
  component: Capture,
  render({ ...args }) {
    return (
      <>
        <Flex alignItems="center" justifyContent="space-between">
          <h2>My deck</h2>
          <Capture {...args} />
        </Flex>
        <h3>Some boxes with things to ignore</h3>
        <div className="capture-bundle">
          <Pre className="capture-single">
            FOO <span data-html2canvas-ignore>ignore</span>
          </Pre>
          <Pre className="capture-single">
            BAR <span data-html2canvas-ignore>ignore</span>
          </Pre>
          <Pre className="capture-single">
            BAZ <span data-html2canvas-ignore>ignore</span>
          </Pre>
        </div>
        <h3>Images</h3>
        <div className="capture-bundle">
          <Flex alignItems="center" justifyContent="space-evenly">
            <img
              className="capture-single"
              src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/author/p/3294/ia1nKrtxoXhiYtHN/2/`}
              alt="ČTK"
              width={44}
            />
            <img
              className="capture-single"
              src="/static/social-interactions/fb_love.png"
              alt="emoji"
              width={44}
            />
            <img
              className="capture-single"
              src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/media/topic_monitor/noInXxnU8SfZ.png`}
              alt="Newton"
              width={44}
            />
            <img
              className="capture-single"
              src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/media/widget_image/xeS0aDJEuXmL.jpe`}
              alt="Mimi"
              width={44}
            />
            <img
              className="capture-single"
              src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/media/widget_image/xeS0aDJEuXmL.jpe`}
              alt="Mimi"
              width={44}
            />
            <img
              className="capture-single"
              src="https://graph.facebook.com/102389958091735/picture"
              alt="El Presidente"
              width={44}
            />
          </Flex>
        </div>
      </>
    )
  },
} satisfies Meta<typeof Capture>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}
