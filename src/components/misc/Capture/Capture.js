import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import color from 'color'
import PProgress from 'p-progress'
import { useEffect, useState } from 'react'
import { styled } from 'styled-components'
import Logo from '~/components/misc/Logo/Logo'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrLinearProgress, {
  VARIANT_DETERMINATE,
  VARIANT_INDETERMINATE,
} from '~/components/misc/MntrProgress/MntrLinearProgress'
import formatDate from '~/helpers/date/format'
import getCommonDOMNodeAncestor from '~/helpers/getCommonDOMNodeAncestor'
import { pushEvent } from '~/helpers/gtm'
import { useFirstMountState } from '~/helpers/hooks/useFirstMountState'
import { observer } from '~/helpers/mst'
import pCanvasToBlob from '~/helpers/pCanvasToBlob'

const CapturingOverlay = styled.div`
  background: ${({ background }) => background};
  color: ${({ color }) => color};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  text-align: center;

  h2 {
    padding-top: 60px;
  }

  .description {
    opacity: 0.7;
    width: 300px;
    margin: 0 auto;
  }

  svg {
    opacity: 0.8;

    path {
      fill: ${({ color }) => color};
    }
  }
`

const LogoWrapper = styled.div`
  margin: 30px auto;
  width: 120px;
`

const EXPORT_FILE_FORMAT_ID_PNG = 0

const Capture = ({
  appStore: {
    account,
    appSettings: { sitePath },
    capture: {
      exportInFormat,
      isCapturing,
      overlayBackground,
      overlayColor,
      startCapturing,
      stopCapturing,
    },
    filter,
    viewport: { isTouchDevice },
  },
  disabled,
  exportFilenameLabel = 'export',
  exportFilenameSuffix = filter.isEmpty
    ? formatDate(new Date(), 'd-M-yyyy-HHmmss')
    : `${formatDate(filter.data.lower_date, 'd-M-yyyy')}-${formatDate(
        filter.data.upper_date,
        'd-M-yyyy',
      )}`,
  imageFilenamePrefix = 'image',
  ...props
}) => {
  const isFirstRender = useFirstMountState()
  const scale = 3
  const [[, isCanvasSizeLimited], setCanvasSize] = useState([])

  useEffect(() => {
    async function getCanvasSize() {
      const captureItems = [...document.querySelectorAll('.capture-bundle, .capture-single')]
      const captureTarget = getCommonDOMNodeAncestor(...captureItems)
      const canvasSize = await import('canvas-size').then((m) => m.default)

      // https://jhildenbiddle.github.io/canvas-size/#/?id=test-results
      //
      // Based on our testing, these are the `canvas-size` methods we need to
      // call. Looks weird, but it is what it is...
      const { height: maxCanvasHeight } = isTouchDevice
        ? await canvasSize.maxArea()
        : await canvasSize.maxHeight()

      setCanvasSize([maxCanvasHeight, maxCanvasHeight < captureTarget.scrollHeight * scale])
    }

    getCanvasSize()
  }, [isTouchDevice])

  const [[fileFormatId, selectors, captureItems, captureTarget], setCaptureData] = useState([])
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    async function capture() {
      const html2canvas = await import('html2canvas').then((m) => m.default)
      const cropData = []

      const canvas = await html2canvas(captureTarget, {
        onclone(documentClone, targetClone) {
          const wrapperRect = targetClone.getBoundingClientRect()

          Array.from(documentClone.querySelectorAll(selectors)).forEach((element) => {
            const elementRect = element.getBoundingClientRect()

            cropData.push({
              x: elementRect.left * scale - wrapperRect.left * scale,
              y: elementRect.top * scale - wrapperRect.top * scale,
              w: elementRect.width * scale,
              h: elementRect.height * scale,
            })
          })
        },
        scale,
        useCORS: true,
      })

      const blobsInProgress = PProgress.all(
        cropData.map(({ x, y, w, h }) => {
          return async () => {
            const newCanvas = document.createElement('canvas')
            newCanvas.width = w
            newCanvas.height = h

            const newContext = newCanvas.getContext('2d')
            newContext.drawImage(canvas, x, y, w, h, 0, 0, w, h)

            return pCanvasToBlob(newCanvas)
          }
        }),
        {
          // (Un)setting concurrency must be tested thoroughly because currently
          // the canvas operations we perform happen on the main browser thread,
          // blocking UI rendering. This should be revisited with future updates
          // of `html2canvas` or experiments with
          // https://developer.mozilla.org/en-US/docs/Web/API/OffscreenCanvas
          concurrency: 1,
        },
      ).then((b) => b.filter(Boolean))

      blobsInProgress.onProgress(setProgress)

      const blobs = await blobsInProgress
      const filenameParts = [sitePath, exportFilenameLabel, exportFilenameSuffix]

      if (fileFormatId !== EXPORT_FILE_FORMAT_ID_PNG) {
        const urls = await exportInFormat(
          fileFormatId,
          blobs,
          filenameParts.filter(Boolean).join('-'),
          imageFilenamePrefix,
        )

        if (urls) {
          urls.forEach((href) => {
            const link = document.createElement('a')
            link.download = true
            link.href = href
            link.click()
          })
        }
      } else {
        // TODO: Pass blobs to buttons same as the planned `urls` above in
        // https://github.com/monitora-media/monitora-frontend/issues/3311
        blobs.forEach((blob, index) => {
          const link = document.createElement('a')

          link.download = `${[
            sitePath,
            exportFilenameLabel,
            captureItems[index].dataset.captureLabel,
            exportFilenameSuffix,
            captureItems.length > 1 && index + 1,
          ]
            .filter(Boolean)
            .join('-')}.png`

          link.href = URL.createObjectURL(blob)

          link.click()
          URL.revokeObjectURL(link.href)
        })
      }

      stopCapturing()
      pushEvent(`custom:${exportFilenameLabel} exported`)
    }

    if (isCapturing) {
      capture()
    } else {
      setProgress(0)
    }
  }, [
    captureItems,
    captureTarget,
    exportFilenameLabel,
    exportFilenameSuffix,
    exportInFormat,
    fileFormatId,
    imageFilenamePrefix,
    isCapturing,
    selectors,
    sitePath,
    stopCapturing,
  ])

  const withProgress = fileFormatId !== EXPORT_FILE_FORMAT_ID_PNG

  if (isFirstRender || isCanvasSizeLimited) {
    return null
  }

  return (
    <>
      <MntrButton
        bg="flat"
        hoverable={!disabled}
        icon="get_app"
        label={t`Save`}
        popup={(closePopup) => {
          return (
            <MntrMenu
              closePopup={closePopup}
              menuItems={[
                {
                  label: t`Save in format`,
                },
                ...account.enums.analytics.export_charts_file_format.map(
                  ({ icon, id, is_individual_element, text }) => {
                    return {
                      label: text,
                      leftIcon: icon,
                      onClick() {
                        const selectors = is_individual_element
                          ? '.capture-single'
                          : '.capture-bundle'

                        const captureItems = [...document.querySelectorAll(selectors)]
                        const captureTarget = getCommonDOMNodeAncestor(...captureItems)

                        setCaptureData([id, selectors, captureItems, captureTarget])
                        startCapturing()
                      },
                    }
                  },
                ),
              ]}
            />
          )
        }}
        disabled={disabled}
        {...props}
      />
      {isCapturing && (
        <CapturingOverlay
          background={overlayBackground}
          color={overlayColor}
          data-html2canvas-ignore
        >
          <h2>
            <LogoWrapper>
              <Logo mono />
            </LogoWrapper>
            <Trans>Preparing export...</Trans>
          </h2>

          <div className="description">
            {withProgress && (
              <Box my={3} borderRadius={2} overflow="hidden">
                <MntrLinearProgress
                  background={color(overlayColor).alpha(0.1).toString()}
                  color={overlayColor}
                  max={1}
                  value={progress}
                  variant={progress === 1 ? VARIANT_INDETERMINATE : VARIANT_DETERMINATE}
                />
              </Box>
            )}

            {!withProgress || !progress ? (
              <Trans>this should take only a couple of seconds</Trans>
            ) : progress < 1 ? (
              `${Math.round(progress * 100)}%`
            ) : (
              <Trans>
                finalizing{' '}
                {
                  account.enums.analytics.export_charts_file_format.find(
                    ({ id }) => id === fileFormatId,
                  ).text
                }{' '}
                file for download
              </Trans>
            )}
          </div>
        </CapturingOverlay>
      )}
    </>
  )
}

export default observer(Capture)
