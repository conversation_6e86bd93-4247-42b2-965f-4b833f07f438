import { useEffect, useRef, useState } from 'react'
import H5AudioPlayer, { RHAP_UI } from 'react-h5-audio-player'
import { css, styled } from 'styled-components'

import FormMediaEditor from '~/components/forms/inspector/FormMediaEditor'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrSlider from '~/components/misc/MntrSlider/MntrSlider'
import RangeSlider from '~/components/misc/RangeSlider/RangeSlider'
import Marker from '~/components/misc/VideoPlayer/Marker'
import { formatDuration } from '~/helpers/date/format'
import { observer } from '~/helpers/mst'

const StyledSlider = styled(MntrSlider)`
  cursor: pointer;
  margin-top: 0px !important;
  & .rc-slider-step {
    background: rgba(255, 255, 255, 0.3);
    .rc-slider-dot {
      border: solid 2px ${({ theme }) => theme.colors.highlight};
    }
  }
  & .rc-slider-track,
  & .rc-slider-step {
    ${({ isEditing }) =>
      isEditing
        ? css`
            background: transparent !important;
          `
        : ''}
  }
  ${({ isEditing }) =>
    isEditing
      ? css`
          height: 50px !important;
          & .rc-slider-track,
          & .rc-slider-step {
            background: transparent !important;
          }
          & .rc-slider-rail {
            top: 4px !important;
            height: 42px !important;
            border-radius: 0 !important;
            background: transparent !important;
          }
          & .rc-slider-step {
            .rc-slider-dot {
              border: solid 2px ${({ theme }) => theme.colors.highlight};
            }
          }
          & .rc-slider {
            padding: 0;
          }
          & .rc-slider-handle {
            top: 2px;
            width: 5px;
            height: 46px;
            margin-top: 0;
            border: 0px;
            border-radius: 2px;
            background: ${({ theme }) => theme.mediaEditor.handle};
            z-index: 1;
          }
        `
      : ''}
`
const StyledFlex = styled(Flex)`
  background: ${({ theme }) => theme.paper.background};
  max-width: 500px;
  margin: 0 auto;
`
const MarkersBox = styled(Box)`
  ${({ isEditing }) =>
    isEditing &&
    css`
      position: absolute;
      top: 32px;
      width: 100%;
    `}
`

const AudioPlayer = ({
  appStore: {
    audioPlayer: {
      offset: newOffset,
      init,
      currentTime,
      durationTime,
      setDurationTime,
      setCurrentTime,
      setSeeked,
      setIsLoaded,
      moveTo,
      isLoaded,
    },
    viewport: { isMobile },
    monitoring: { inspector },
  },
  markers,
  src,
  autoPlay = false,
  isTvr = false,
}) => {
  const audioPlayerEl = useRef()

  const [volume, setVolume] = useState(80)
  const [offset, setOffset] = useState(0)
  const [time, setTime] = useState(0)
  const [crop, setCrop] = useState([0, 0])

  useEffect(() => {
    init()
  }, []) //eslint-disable-line

  useEffect(() => {
    if (isLoaded && audioPlayerEl.current.audio.current.duration) {
      setDurationTime(audioPlayerEl.current.audio.current.duration)
      if (markers.length > 0) {
        seek(markers[0].time)
      }
    }
  }, [isLoaded]) //eslint-disable-line

  useEffect(() => {
    setTime(currentTime)
  }, [currentTime])

  useEffect(() => {
    if (offset !== newOffset) {
      setOffset(newOffset)
      audioPlayerEl.current.audio.current.currentTime = newOffset
    }
  }, [newOffset]) //eslint-disable-line

  useEffect(() => {
    audioPlayerEl.current.audio.current.volume = volume / 100
  }, [volume])

  useEffect(() => {
    setCrop([0, durationTime])

    return () => {
      setCrop([0, 0])
    }
  }, [durationTime])

  const onListen = (event) => {
    const currentPlayerTime = Math.floor(event.currentTarget.currentTime)

    if (currentPlayerTime !== currentTime) {
      setCurrentTime(currentPlayerTime)
    }
  }

  const onLoadedData = () => {
    setIsLoaded(true)
  }

  const seek = (offset) => {
    moveTo(offset)
  }

  const changeVolume = (volume) => {
    setVolume(volume)
  }

  const handleChange = ([newMinValue, newMaxValue]) => {
    const [oldMinValue, oldMaxValue] = crop
    const isMinChanged = newMinValue !== oldMinValue
    const isMaxChanged = newMaxValue !== oldMaxValue
    if (!isMinChanged && !isMaxChanged) return

    const timeToSeek = isMinChanged ? newMinValue : isMaxChanged ? newMaxValue : time
    setTime(timeToSeek)
    seek(timeToSeek)

    if (isMinChanged || newMaxValue !== oldMaxValue) {
      setCrop([newMinValue, newMaxValue])
    }
  }

  const customProgressBarSection = [
    RHAP_UI.CURRENT_TIME,
    <Box key="slider" mx={5} flex="1 0 auto" position="relative">
      {inspector?.mediaEditor.isEditing ? (
        <RangeSlider durationTime={durationTime} crop={crop} onChange={handleChange} />
      ) : null}
      <StyledSlider
        min={0}
        max={durationTime}
        onAfterChange={seek}
        onChange={(value) => setTime(value)}
        value={time}
        isEditing={inspector?.mediaEditor.isEditing}
      />

      {markers && (
        <MarkersBox isEditing={inspector?.mediaEditor.isEditing}>
          {markers.map((marker, index) => {
            return (
              <Marker
                key={index}
                marker={marker}
                duration={durationTime}
                onClick={() => seek(marker.time)}
              />
            )
          })}
        </MarkersBox>
      )}
    </Box>,
    RHAP_UI.DURATION,
  ]

  const customVolumeControls = [
    <Flex key="volume" centerY flex="0 1 100px">
      <Box pr={1}>
        <MntrButton
          noPadding
          bg="clear"
          icon={(() => {
            if (volume === 0) return 'volume_off'
            if (volume < 50) return 'volume_down'
            return 'volume_up'
          })()}
          {...(!isMobile && {
            onClick: () => {
              changeVolume(volume === 0 ? 30 : 0)
            },
          })}
        />
      </Box>

      <Flex centerY width={1}>
        <StyledSlider min={0} max={100} value={volume} onChange={changeVolume} />
      </Flex>
    </Flex>,
  ]

  return (
    <>
      <H5AudioPlayer
        ref={audioPlayerEl}
        src={src}
        autoPlay={autoPlay}
        autoPlayAfterSrcChange={autoPlay}
        customProgressBarSection={customProgressBarSection}
        customVolumeControls={customVolumeControls}
        onLoadedData={onLoadedData}
        onSeeked={() => setSeeked(true)}
        onListen={onListen}
      />
      {inspector?.mediaEditor.isEditing ? (
        <StyledFlex flexDirection="row" centerY p={2}>
          <FormMediaEditor
            initialValues={{
              startTime: formatDuration(crop[0]),
              endTime: formatDuration(crop[1]),
              cutLength: formatDuration(crop[1] - crop[0]),
            }}
            durationTime={durationTime}
            mediaEditor={inspector.mediaEditor}
            onChange={handleChange}
            isVideo={false}
            isTvr={isTvr}
          />
        </StyledFlex>
      ) : null}
    </>
  )
}

export default observer(AudioPlayer)
