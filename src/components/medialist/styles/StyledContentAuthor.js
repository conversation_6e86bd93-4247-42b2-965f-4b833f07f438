import { styled } from 'styled-components'
import { medialistSettings } from '~/components/medialist/constants/medialist.settings'

export const Wrapper = styled.div`
  display: grid;
  gap: 10px;
  grid-auto-rows: minmax(100px, auto);
  grid-template-columns: 500px 1fr;
  position: relative;
  z-index: 1;

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    margin-left: 0px;
    grid-auto-rows: auto;
    grid-template-columns: none;
    display: block;
  }
`

export const StyledContent = styled.div`
  position: relative;
  grid-column: 2 / 4;
  grid-row: 2;

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    display: ${({ hide }) => (hide ? 'none' : 'block')};
  }

  opacity: ${({ disabled }) => (disabled ? medialistSettings.DISABLED_CONTENT_OPACITY : 1)};
  height: calc(100vh - 180px);
  overflow-y: auto;
`

export const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  display: ${({ disabled }) => (disabled ? 'block' : 'none')};
`

export const TabsWrapper = styled.div`
  opacity: ${({ disabled }) => (disabled ? medialistSettings.DISABLED_CONTENT_OPACITY : 1)};
`
