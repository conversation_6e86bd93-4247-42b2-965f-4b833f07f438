import { css, styled } from 'styled-components'
import { Flex } from '~/components/misc/Mntr'

export const StyledHeader = styled.div`
  position: sticky;
  top: 54px;
  z-index: 1;
  grid-column: 1/4;
  grid-row: 1;
  background: ${({ theme }) => theme.paper.background};
  border-bottom: 1px solid ${({ theme }) => theme.paper.border};
  height: 150px;

  @media (max-width: ${({ theme }) => theme.breakpoints[1]}) {
    top: 0px;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    height: 140px;
  }
`

export const StyledSidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  grid-column: 1 / 2;
  grid-row: 2;
  padding-right: 20px;
  position: sticky;
  top: 230px;
  height: calc(100vh - 230px);

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    height: calc(100vh - 250px);
  }
  overflow-y: scroll;
  z-index: 10;

  ${({ disable }) => {
    if (disable) {
      return css`
        @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
          display: none;
        }
      `
    }
  }}

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    padding: ${({ theme }) => theme.space[3]}px;
    padding-right: 8px;
  }

  @media (min-width: ${({ theme }) => theme.breakpoints[2]}) {
    padding-left: 24px;
  }

  @media (min-width: ${({ theme }) => theme.breakpoints[4]}) {
    padding-left: 80px;
  }
`

export const FlagWrapper = styled.div`
  position: absolute;
  bottom: 0;
  right: 0;
`

// Submit button at the end of the form, position fixed for desktop and bottom for mobile if is editing
export const FloatingPanel = styled.div`
  position: fixed;
  top: 70px;
  right: 0;
  height: 120px;
  display: flex;
  align-items: center;
  margin-right: 18px;
  z-index: 100;

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    top: 60px;
  }

  ${({ bottom }) => {
    if (bottom) {
      return css`
        @media (max-width: ${({ theme }) => theme.breakpoints[1]}) {
          background: ${({ theme }) => theme.paper.background};
          border-top: 1px solid ${({ theme }) => theme.paper.border};
          position: fixed;
          bottom: 0;
          top: auto;
          height: auto;
          padding: 12px;
          text-align: right;
          left: 0;
          right: 0;
          margin: 0;
          justify-content: flex-end;
        }
      `
    }
  }}
`

export const StyledProfileImg = styled.img`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;

  @media (max-width: ${({ theme }) => theme.breakpoints[1]}) {
    width: 60px;
    height: 60px;
  }
`

export const VisibleMd = styled.div`
  @media (max-width: ${({ theme }) => theme.breakpoints[1]}) {
    display: none;
  }
`

export const VisibleSm = styled.div`
  @media (min-width: calc(${({ theme }) => theme.breakpoints[1]} + 1px)) {
    display: none;
  }
`

export const RelativeBox = styled.div`
  position: relative;
`

export const DropzoneWrapper = styled.div`
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: ${({ theme }) => theme.colors.background};
  border: 3px dashed ${({ theme }) => theme.paper.border};

  & .dropzone {
    border: 0 !important;
    background: transparent !important;
  }
`

export const LoadingOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ theme }) => theme.modal.overlay};
  z-index: 10000;
`

export const LoaderSpinnerWrapper = styled(Flex)`
  align-items: center;
  justify-content: center;
  height: 100%;
  z-index: 10001;
`
