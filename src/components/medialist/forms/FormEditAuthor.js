import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import copy from 'copy-html-to-clipboard'
import arrayMutators from 'final-form-arrays'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { Form } from 'react-final-form'
import Flag from '~/components/Flag/Flag'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import AuthorInfoDetail from '~/components/medialist/content/AuthorInfoDetail'
import AuthorPhoto from '~/components/medialist/content/FeedMedialist/AuthorPhoto/AuthorPhoto'
import AuthorAddress from '~/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress'
import AddAuthorToBasket from '~/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket'
import AuthorFiles from '~/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles'
import AuthorMediaData from '~/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData'
import AuthorSocialIcons from '~/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorSocialIcons/AuthorSocialIcons'
import AddAuthorTag from '~/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag'
import {
  FlagWrapper,
  FloatingPanel,
  LoaderSpinnerWrapper,
  LoadingOverlay,
  RelativeBox,
  StyledHeader,
  StyledSidebar,
  VisibleMd,
  VisibleSm,
} from '~/components/medialist/styles/StyledFormEditAuthor'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import authorTypes from '~/constants/authorTypes'
import withModalRemove from '~/helpers/modal/withModalRemove'
import withModalReportProblem from '~/helpers/modal/withModalReportProblem'
import withModalResetAuthor from '~/helpers/modal/withModalResetAuthor'
import { observer } from '~/helpers/mst'
import { transformCountries, transformDataToSelectItems } from '~/helpers/objectToSelectItems'
import { back, routerPush, routerReplace } from '~/helpers/router'
import AccordionPaper from './modules/AccordionPaper'
import FixedField from './modules/FixedField'
import FormArray from './modules/FormArray'
import FormFieldUploadPhoto from './modules/FormFieldUploadPhoto'
import MedialistMobileNavigation from './modules/MedialistMobileNavigation'
import MetaDataAuthorsShortnames from './modules/MetaDataAuthorsShortnames'
import MetaDataContacts from './modules/MetaDataContacts'
import MetaDataJobs from './modules/MetaDataJobs'
import MetaDataParagraph from './modules/MetaDataParagraph'
import MetaDataStatus from './modules/MetaDataStatus'
import MetaDataStatusList from './modules/MetaDataStatusList'

const FormEditAuthor = ({
  appStore: {
    account,
    authors: {
      editAuthor,
      showValue,
      removeAuthorFromBasket,
      removeTagFromAuthor,
      exportAuthor,
      baskets,
      addAuthorToBasket,
      addTagToAuthor,
      tags,
      reportProblem,
      saveNote,
      deleteAuthor,
      resetAuthor,
    },
    notification,
    viewport,
  },
  onSubmit,
  initialValues,
  readOnly,
  setReadOnly,
  authorId,
  activeTab,
  isCreate,
  mobilePreferSidebar,
}) => {
  const router = useRouter()
  const [canEditNote, setEditNote] = useState(false)
  const [previewImage, setPreviewImage] = useState(null)
  const canEdit = account.workspace?.permissions.authors_database.can_write
  const canDelete = canEdit && initialValues.is_own_author
  const canRestore = canEdit && !initialValues.is_own_author && initialValues.is_edited
  const authorTags = initialValues.author_tags || []
  const hasSocial =
    initialValues.facebook_url ||
    initialValues.linkedin_url ||
    initialValues.twitter_url ||
    initialValues.instagram_url
  const hasContacts = initialValues.phones.length > 0 || initialValues.emails.length > 0
  const exportLimit = account.workspace?.limits.authors_export_limit || 0
  const exportLimitUsed = account.workspace?.limits.authors_export_limit_used || 0
  const newsSourceId = initialValues.news_source_data?.id
  const publisherId = initialValues.publisher_data?.id

  useEffect(() => {
    if (readOnly && router.asPath.split('#')[1] === 'edit') {
      routerReplace(`/author/${authorId}`)
    }
    if ((!readOnly || canEditNote) && canEdit && !isCreate) {
      editAuthor()
    }
  }, [readOnly, editAuthor, authorId, router, canEdit, isCreate, canEditNote])

  const showAndCopy = (show_value_url, text, index, type) => {
    if (!show_value_url) {
      copy(text)
      return notification.add(t`Copied to the clipboard.`, 'success')
    }
    showValue(show_value_url, index, type, (res) => {
      copy(res.text)
      notification.add(t`Copied to the clipboard.`, 'success')
    })
  }

  return (
    <Form
      onSubmit={(model) => {
        return onSubmit(model)
          .then(() => {
            setReadOnly(true)
          })
          .catch((err) => {
            return err
          })
      }}
      initialValues={initialValues}
      mutators={{
        ...arrayMutators,
      }}
      render={({ form, values, pristine, submitting, handleSubmit }) => {
        const country = account.enums.media_country.find(
          (country) => country.id === parseInt(values.country),
        )
        const resetForm = (form) => {
          form.reset()
          if (values?.photo_url?.large) {
            setPreviewImage(values?.photo_url?.large)
          } else {
            setPreviewImage(null)
          }
        }

        return (
          <>
            {submitting && (
              <LoadingOverlay>
                <LoaderSpinnerWrapper m={3}>
                  <MntrCircularProgress size={40} />
                </LoaderSpinnerWrapper>
              </LoadingOverlay>
            )}
            <StyledHeader>
              <Flex centerY height={1}>
                <Box ml={[2, 2, 4]} mr={[2, 2, 4]}>
                  <MntrButton
                    icon="navigate_before"
                    onClick={() => {
                      back('/authors')
                    }}
                  />
                </Box>
                <Box mr={[2, 2, 4]}>
                  <RelativeBox>
                    {/* TODO: dropzone -> upload profile image */}
                    {readOnly && (
                      <AuthorPhoto
                        authorType={values.author_type}
                        size={viewport.isMobile ? 50 : 100}
                        image={values.photo_url.large}
                      />
                    )}

                    {!readOnly && (
                      <FormFieldUploadPhoto
                        values={values}
                        form={form}
                        isEdit={!readOnly}
                        change={form.change}
                        setPreviewImage={setPreviewImage}
                        previewImage={previewImage}
                      />
                    )}

                    <FlagWrapper>
                      <Flag country={country?.code} size={viewport.isMobile ? 20 : 30} />
                    </FlagWrapper>
                  </RelativeBox>
                </Box>
                <Box
                  flex={['1', 'initial']}
                  width={['auto', '320px']}
                  mr={[readOnly ? '40px' : 3, 3]}
                >
                  <Flex>
                    <Box flex={1} color="black">
                      {readOnly && <Heading color="inherit">{values.name}</Heading>}
                      {!readOnly && (
                        <FixedField
                          name="name"
                          fullWidth
                          component={MntrTextFieldAdapter}
                          placeholder={t`Name`}
                          label={t`Name`}
                          readOnly={readOnly}
                        />
                      )}
                    </Box>
                  </Flex>
                  {readOnly && (
                    <Flex color="black">
                      <AuthorInfoDetail
                        author={initialValues}
                        fontSize={viewport.isMobile ? 14 : 15}
                        opacity={0.7}
                      />
                    </Flex>
                  )}
                  {!readOnly && (
                    <Flex mt={1}>
                      <Box mr={2} flex={1}>
                        <FixedField
                          name="author_type"
                          component={MntrSelectAdapter}
                          label={t`Author type`}
                          items={transformDataToSelectItems(
                            account.enums.authors.author_type.filter(
                              (item) => item.is_medialist_filter,
                            ),
                          )}
                          readOnly={readOnly}
                        />
                      </Box>
                      <Box flex={1}>
                        <FixedField
                          name="country"
                          component={MntrSelectAdapter}
                          label={t`Country`}
                          items={transformCountries({
                            object: account.enums.mediaCountryIsActive,
                            countryKey: 'code',
                          })}
                          readOnly={readOnly}
                        />
                      </Box>
                    </Flex>
                  )}
                </Box>
              </Flex>
            </StyledHeader>

            {/* Mobile Navigation */}
            {!isCreate && readOnly && (
              <MedialistMobileNavigation
                isOwnAuthor={values.is_own_author}
                authorId={authorId}
                activeTab={activeTab}
              />
            )}

            {/* Floating Panel - Header Right ButtonGroup */}
            <FloatingPanel bottom={!readOnly}>
              {readOnly && (
                <VisibleSm>
                  <MntrButton
                    icon="more_vert"
                    popupPlacement="bottom-end"
                    popup={(closePopup) => {
                      const menuItems = []

                      if (canEdit) {
                        // Edit profile
                        menuItems.push({
                          label: t`Edit profile`,
                          leftIcon: 'edit',
                          onClick: () => {
                            if (router.pathname.endsWith('/articles')) {
                              routerPush(`/author/${values.id}#edit`)
                            }
                            setReadOnly(false)
                          },
                        })
                      }

                      if (canRestore) {
                        menuItems.push({
                          label: t`Reset author profile`,
                          leftIcon: 'history',
                          ...withModalResetAuthor({
                            keepScrollLock: true,
                            message: `${t`The author's profile will be reset to its original values.`} ${t`Do you really want to continue?`}`,
                            submitLabel: t`Continue`,
                            onSubmit: () => {
                              resetAuthor(values.id)
                            },
                          }),
                        })
                      }

                      if (canDelete) {
                        menuItems.push({
                          hoverVariant: 'error',
                          label: t`Delete author`,
                          leftIcon: 'delete',
                          ...withModalRemove({
                            keepScrollLock: true,
                            modalIcon: 'warning',
                            message: `${t`Author will be deleted.`} ${t`Do you really want to continue?`}`,
                            modalTitle: t`Delete author`,
                            submitLabel: t`Continue`,
                            onSubmit: () => {
                              resetForm(form)
                              deleteAuthor(values.id)
                            },
                          }),
                        })
                      }

                      // // Resend author
                      // menuItems.push({
                      //   label: t`Resend`,
                      //   leftIcon: 'send',
                      //   onClick: () => {
                      //     // TODO: resend author
                      //   },
                      // })

                      if (menuItems.length > 0) {
                        menuItems.push({})
                      }

                      if (newsSourceId) {
                        menuItems.push({
                          leftIcon: 'groups',
                          label: t`Show authors`,
                          href: `/authors?author_type=${authorTypes.JOURNALIST}&news_source=${newsSourceId}`,
                        })
                      }

                      if (publisherId) {
                        menuItems.push({
                          leftIcon: 'groups',
                          label: t`Show authors`,
                          href: `/authors?author_type=${authorTypes.JOURNALIST}&publisher=${publisherId}`,
                        })
                        menuItems.push({
                          leftIcon: 'newspaper',
                          label: t`Show newsrooms`,
                          href: `/authors?author_type=${authorTypes.NEWSROOM}&publisher=${publisherId}`,
                        })
                      }

                      menuItems.push({
                        label: t`Export XLSX`,
                        leftIcon: 'download',
                        onClick: () => {
                          exportAuthor(values.id)
                        },
                      })

                      if (values.google_search_url) {
                        menuItems.push({
                          label: t`Search on Google`,
                          leftIcon: 'person_search',
                          target: '_blank',
                          href: values.google_search_url,
                        })
                      }

                      menuItems.push(
                        {},
                        {
                          label: <Trans>Report problem</Trans>,
                          leftIcon: 'support_agent',
                          hoverVariant: 'error',
                          ...withModalReportProblem({
                            keepScrollLock: true,
                            onSubmit: reportProblem,
                            title: t`Report problem`,
                          }),
                        },
                      )

                      if (values.admin_url) {
                        menuItems.push({
                          hoverVariant: 'secondary',
                          leftIcon: 'build',
                          label: t`Admin`,
                          href: values.admin_url,
                          target: '_blank',
                        })
                      }

                      return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
                    }}
                  />
                </VisibleSm>
              )}

              {/* Desktop */}
              {readOnly && (
                <VisibleMd>
                  {/* <MntrButton
      mr={2}
      tooltip={t`Resend`}
      icon="send"
      onClick={() => {
        // TODO: resend author
      }}
    /> */}

                  {newsSourceId && (
                    <MntrButton
                      mr={2}
                      icon="groups"
                      tooltip={t`Show authors`}
                      href={`/authors?author_type=${authorTypes.JOURNALIST}&news_source=${newsSourceId}`}
                    />
                  )}
                  {publisherId && (
                    <>
                      <MntrButton
                        mr={2}
                        icon="groups"
                        tooltip={t`Show authors`}
                        href={`/authors?author_type=${authorTypes.JOURNALIST}&publisher=${publisherId}`}
                      />
                      <MntrButton
                        mr={2}
                        icon="newspaper"
                        tooltip={t`Show newsrooms`}
                        href={`/authors?author_type=${authorTypes.NEWSROOM}&publisher=${publisherId}`}
                      />
                    </>
                  )}

                  {canEdit && (
                    <MntrButton
                      mr={2}
                      tooltip={t`Edit profile`}
                      icon={'edit'}
                      onClick={() => {
                        setReadOnly(false)
                      }}
                    />
                  )}

                  {canRestore && (
                    <MntrButton
                      mr={2}
                      tooltip={t`Reset author profile`}
                      icon={'history'}
                      {...withModalResetAuthor({
                        keepScrollLock: true,
                        message: `${t`The author's profile will be reset to its original values.`} ${t`Do you really want to continue?`}`,
                        submitLabel: t`Continue`,
                        onSubmit: () => {
                          resetAuthor(values.id)
                        },
                      })}
                    />
                  )}

                  {canDelete && (
                    <MntrButton
                      mr={2}
                      bg="error"
                      tooltip={t`Delete author`}
                      icon={'delete'}
                      {...withModalRemove({
                        keepScrollLock: true,
                        modalIcon: 'warning',
                        message: `${t`Author will be deleted.`} ${t`Do you really want to continue?`}`,
                        modalTitle: t`Delete author`,
                        submitLabel: t`Continue`,
                        onSubmit: () => {
                          resetForm(form)
                          deleteAuthor(values.id)
                        },
                      })}
                    />
                  )}

                  {account.workspace?.permissions.authors_export.can_write && (
                    <MntrButton
                      mr={2}
                      icon="download"
                      disabled={exportLimit === exportLimitUsed}
                      onClick={() => {
                        exportAuthor(values.id)
                      }}
                      tooltip={
                        <Flex alignContent="center" center flexDirection="column">
                          <Text color="inherit">{t`Export XLSX`}</Text>
                          <Text color="inherit">
                            ({t`limit`} {exportLimitUsed}/{exportLimit})
                          </Text>
                        </Flex>
                      }
                    />
                  )}

                  {values.google_search_url && (
                    <MntrButton
                      mr={2}
                      tooltip={t`Search on Google`}
                      icon="person_search"
                      href={values.google_search_url}
                      target="_blank"
                    />
                  )}

                  <MntrButton
                    icon="more_vert"
                    popupPlacement="bottom-end"
                    popup={(closePopup) => {
                      const menuItems = []

                      menuItems.push({
                        label: <Trans>Report problem</Trans>,
                        leftIcon: 'support_agent',
                        hoverVariant: 'error',
                        ...withModalReportProblem({
                          keepScrollLock: true,
                          onSubmit: reportProblem,
                          title: t`Report problem`,
                        }),
                      })

                      if (values.admin_url) {
                        menuItems.push({
                          hoverVariant: 'secondary',
                          leftIcon: 'build',
                          label: t`Admin`,
                          href: values.admin_url,
                          target: '_blank',
                        })
                      }

                      return <MntrMenu menuItems={menuItems} closePopup={closePopup} />
                    }}
                  />
                </VisibleMd>
              )}
              {!readOnly && (
                <Box>
                  {/* Form is not changed - can be set to read only */}
                  {!isCreate && pristine && (
                    <MntrButton
                      icon="close"
                      label={t`Cancel`}
                      ml={1}
                      onClick={() => {
                        setReadOnly(true)
                      }}
                    />
                  )}

                  {/* Form is changed - show warning modal with unsaved changes */}
                  {!isCreate && !pristine && (
                    <MntrButton
                      icon="close"
                      label={t`Cancel`}
                      ml={1}
                      {...withModalRemove({
                        keepScrollLock: true,
                        modalIcon: 'warning',
                        message: t`All unsaved changes will be lost. Do you really want to cancel the changes?`,
                        modalTitle: t`Unsaved changes`,
                        submitLabel: t`Continue`,
                        onSubmit: () => {
                          setReadOnly(true)
                          resetForm(form)
                        },
                      })}
                    />
                  )}

                  <MntrButton
                    icon="undo"
                    disabled={pristine}
                    type="reset"
                    label={t`Reset`}
                    onClick={() => resetForm(form)}
                    ml={1}
                  />
                  <MntrButton
                    icon="save"
                    disabled={submitting || pristine}
                    bg="secondary"
                    type="text"
                    onClick={() => handleSubmit()}
                    label={isCreate ? t`Create` : t`Save`}
                    ml={1}
                  />
                </Box>
              )}
            </FloatingPanel>

            <StyledSidebar disable={!mobilePreferSidebar}>
              {/* Accordion Paper - Designation */}
              <AccordionPaper label={t`Tags, lists and note`} id="designation" disabled={!readOnly}>
                <Box mx="-4px">
                  <Flex mb={1} justifyContent="space-between" centerY>
                    <MntrMenuHeading
                      label={
                        <>
                          {t`Tags`} ({values.author_tags?.length ?? 0})
                        </>
                      }
                    />
                    {canEdit && (
                      <Box mr={3}>
                        <MntrButton
                          bg="flat"
                          size="small"
                          icon="new_label"
                          tooltip={t`Assign tag to author`}
                          popup={(closePopup) => {
                            return (
                              <AddAuthorTag
                                setTag={(tag) => addTagToAuthor(tag, values.id)}
                                closePopup={closePopup}
                                tags={tags}
                                authorTags={authorTags}
                                canEditTags={canEdit}
                              />
                            )
                          }}
                        />
                      </Box>
                    )}
                  </Flex>

                  <Box mb={2}>
                    <FormArray
                      name="author_tags"
                      values={values}
                      change={form.change}
                      {...(canEdit && {
                        onDelete: (index) => {
                          removeTagFromAuthor(values.author_tags[index].id, values.id)
                        },
                      })}
                    />
                  </Box>

                  <Flex mb={1} justifyContent="space-between" centerY>
                    <MntrMenuHeading
                      label={
                        <>
                          {t`Lists`} ({values.author_basket_entries?.length ?? 0})
                        </>
                      }
                    />
                    {canEdit && (
                      <Box mr={3}>
                        <MntrButton
                          bg="flat"
                          size="small"
                          icon="playlist_add"
                          tooltip={t`Add to list`}
                          popup={(closePopup) => {
                            return (
                              <AddAuthorToBasket
                                addAuthorToBasket={(basket) => addAuthorToBasket(basket, values.id)}
                                closePopup={closePopup}
                                baskets={baskets}
                                authorBaskets={initialValues.author_basket_entries}
                                canEditBaskets={canEdit}
                              />
                            )
                          }}
                        />
                      </Box>
                    )}
                  </Flex>

                  <FormArray
                    name="author_basket_entries"
                    values={values}
                    change={form.change}
                    {...(canEdit && {
                      onDelete: (index) => {
                        removeAuthorFromBasket(
                          values.author_basket_entries[index].id,
                          values.id,
                          values.author_basket_entries[index].author_basket.id,
                        )
                      },
                    })}
                  />
                  <Box className="wrapper-touch-visibility">
                    <Flex mb={1} justifyContent="space-between" mt={2} centerY>
                      <MntrMenuHeading label={t`Note`} />
                      <Box mr={3}>
                        {!canEditNote && canEdit && (
                          <MntrButton
                            bg="flat"
                            size="small"
                            icon={values.notes ? 'edit' : 'new_label'}
                            tooltip={values.notes ? t`Edit note` : t`Add note`}
                            onClick={() => setEditNote(true)}
                          />
                        )}
                        {canEditNote && (
                          <MntrButton
                            bg="flat"
                            size="small"
                            icon="close"
                            onClick={() => {
                              resetForm(form)
                              setEditNote(false)
                            }}
                          />
                        )}
                      </Box>
                    </Flex>
                    {canEditNote && (
                      <Box px={3}>
                        <Box mt={-2}>
                          <FixedField
                            name="notes"
                            component={MntrTextFieldAdapter}
                            multiline
                            minRows={5}
                            maxRows={5}
                            placeholder={t`Insert text...`}
                          />
                        </Box>
                        <Box mt={2} style={{ textAlign: 'right' }}>
                          <MntrButton
                            label={t`Save`}
                            bg="secondary"
                            onClick={() => saveNote(values.notes, setEditNote(false))}
                            disabled={pristine || submitting}
                          />
                        </Box>
                      </Box>
                    )}
                    {values.notes && !canEditNote && <Text px={3}>{values.notes}</Text>}
                  </Box>
                </Box>
              </AccordionPaper>

              {/* Accordion Paper - About Author */}
              <AccordionPaper label={t`About Author`} id="about" forceOpen={!readOnly} mb={[5, 4]}>
                {/* About Author - Read Only State */}
                {readOnly && (
                  <Box mx="-4px">
                    {hasContacts && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Contacts`} />
                        {values.phones?.length > 0 && (
                          <Box>
                            <MetaDataContacts
                              list={values.phones}
                              label={t`Phone`}
                              actions={({ show_value_url, text }, index) => {
                                return [
                                  {
                                    icon: show_value_url ? 'visibility' : 'content_copy',
                                    tooltip: show_value_url
                                      ? t`Show and copy to clipboard`
                                      : t`Copy to clipboard`,
                                    size: 'small',
                                    onClick: () => {
                                      showAndCopy(show_value_url, text, index, 'phones')
                                    },
                                  },
                                ]
                              }}
                              handleContactClick={({ show_value_url, text }, index) => {
                                showAndCopy(show_value_url, text, index, 'phones')
                              }}
                            />
                          </Box>
                        )}

                        {values.emails?.length > 0 && (
                          <Box>
                            <MetaDataContacts
                              type="emails"
                              list={values.emails}
                              label={t`Email`}
                              actions={({ show_value_url, text }, index) => {
                                return [
                                  {
                                    icon: show_value_url ? 'visibility' : 'content_copy',
                                    size: 'small',
                                    tooltip: show_value_url
                                      ? t`Show and copy to clipboard`
                                      : t`Copy to clipboard`,
                                    onClick: () => {
                                      showAndCopy(show_value_url, text, index, 'emails')
                                    },
                                  },
                                ]
                              }}
                              handleContactClick={({ show_value_url, text }, index) => {
                                showAndCopy(show_value_url, text, index, 'emails')
                              }}
                            />
                          </Box>
                        )}
                      </Box>
                    )}

                    {hasSocial && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Social Media`} />
                        <AuthorSocialIcons item={values} />
                      </Box>
                    )}

                    {values.publisher_data && <AuthorAddress data={values.publisher_data} />}
                    {values.news_source_data && (
                      <>
                        <AuthorMediaData data={values.news_source_data} />
                        <AuthorFiles data={values.news_source_data} />
                      </>
                    )}

                    {values.shortnames?.length > 0 && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Author's shortnames`} />
                        <MetaDataAuthorsShortnames items={values.shortnames} />
                      </Box>
                    )}

                    {values.websites?.length > 0 && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Personal Website`} />
                        <MetaDataStatusList list={values.websites} label={t`Website`} />
                      </Box>
                    )}

                    {values.jobs?.length > 0 && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Editorial offices and positions`} />
                        <MetaDataJobs list={values.jobs} />
                      </Box>
                    )}

                    {values.description?.length > 0 && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Bio`} />
                        <MetaDataParagraph text={values.description} />
                      </Box>
                    )}

                    {values.salutation && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Salutation`} />
                        <MetaDataStatus
                          value={values.salutation}
                          actions={[
                            {
                              size: 'small',
                              icon: 'content_copy',
                              tooltip: t`Copy to clipboard`,
                              onClick: () => {
                                copy(values.salutation)
                                notification.add(t`Copied to the clipboard.`, 'success')
                              },
                            },
                          ]}
                        />
                      </Box>
                    )}
                  </Box>
                )}

                {/* About Author - Read Only State */}
                {!readOnly && (
                  <Box>
                    <Box px={2}>
                      <MntrHint
                        background="beige"
                        borderColor="#4c4c4c"
                        color="error"
                        text={
                          <Text color="#4c4c4c">
                            <Trans>
                              Only you can see all the data you entered and the changes made.
                            </Trans>
                          </Text>
                        }
                        icon="privacy_tip"
                      />
                    </Box>
                    <MntrMenuHeading label={t`Phone`} />
                    <FormArray
                      change={form.change}
                      display="input"
                      name="phones"
                      defaultCountry={country?.code}
                      placeholder={t`Phone`}
                      push={form.mutators.push}
                      type="tel"
                      values={values}
                    />
                    <MntrMenuHeading label={t`Email`} />
                    <FormArray
                      change={form.change}
                      display="input"
                      name="emails"
                      placeholder={t`Email`}
                      push={form.mutators.push}
                      type="text"
                      values={values}
                    />
                    <Flex flexDirection="column">
                      <MntrMenuHeading label={t`Social Media`} />
                      <Flex px={3} mt={2} pb={2} alignItems="center">
                        <Box mr={1} mt={1}>
                          <StyledTooltip tooltip={'Facebook'}>
                            <img
                              src={`/static/social/facebook.png`}
                              width={22}
                              height={22}
                              alt={'facebook'}
                            />
                          </StyledTooltip>
                        </Box>
                        <FixedField
                          name="facebook_url"
                          component={MntrTextFieldAdapter}
                          placeholder={'Facebook'}
                        />
                      </Flex>
                      <Flex px={3} pb={2} alignItems="center">
                        <Box mr={1} mt={1}>
                          <StyledTooltip tooltip={'X.com'}>
                            <img
                              src={`/static/social/twitter.png`}
                              width={22}
                              height={22}
                              alt={'X.com'}
                            />
                          </StyledTooltip>
                        </Box>
                        <FixedField
                          name="twitter_url"
                          component={MntrTextFieldAdapter}
                          placeholder={'X.com'}
                        />
                      </Flex>
                      <Flex px={3} pb={2} alignItems="center">
                        <Box mr={1} mt={1}>
                          <StyledTooltip tooltip={'Instagram'}>
                            <img
                              src={`/static/social/instagram.png`}
                              width={22}
                              height={22}
                              alt={'instagram'}
                            />
                          </StyledTooltip>
                        </Box>
                        <FixedField
                          name="instagram_url"
                          component={MntrTextFieldAdapter}
                          placeholder={'Instagram'}
                        />
                      </Flex>
                      <Flex px={3} pb={2} alignItems="center">
                        <Box mr={1} mt={1}>
                          <StyledTooltip tooltip={'LinkedIn'}>
                            <img
                              src={`/static/social/linkedin.png`}
                              width={22}
                              height={22}
                              alt={'linkedin'}
                            />
                          </StyledTooltip>
                        </Box>
                        <FixedField
                          name="linkedin_url"
                          component={MntrTextFieldAdapter}
                          placeholder={'LinkedIn'}
                        />
                      </Flex>
                    </Flex>
                    <MntrMenuHeading label={t`Author's shortnames`} />
                    <FormArray
                      change={form.change}
                      display="input"
                      name="shortnames"
                      placeholder={t`Author's shortname`}
                      push={form.mutators.push}
                      type="text"
                      values={values}
                    />
                    <MntrMenuHeading label={t`Personal Website`} />
                    <FormArray
                      change={form.change}
                      display="input"
                      name="websites"
                      placeholder={t`Personal Website`}
                      push={form.mutators.push}
                      type="text"
                      values={values}
                    />
                    <MntrMenuHeading label={t`Editorial offices and positions`} />
                    <Box>
                      <FormArray
                        change={form.change}
                        display="input"
                        name="jobs"
                        push={form.mutators.push}
                        type="text"
                        values={values}
                      />
                    </Box>
                    <MntrMenuHeading label={t`Bio`} />
                    <Box px={3} pb={2} mt={1}>
                      <Box>
                        <FixedField
                          minRows={10}
                          maxRows={10}
                          name="description"
                          component={MntrTextFieldAdapter}
                          placeholder={t`Insert text...`}
                          multiline
                        />
                      </Box>
                    </Box>
                    {values.salutation && (
                      <Box mb={3}>
                        <MntrMenuHeading label={t`Salutation`} />
                        <MetaDataStatus
                          value={values.salutation}
                          actions={[
                            {
                              size: 'small',
                              icon: 'content_copy',
                              tooltip: t`Copy to clipboard`,
                              onClick: () => {
                                copy(values.salutation)
                                notification.add(t`Copied to the clipboard.`, 'success')
                              },
                            },
                          ]}
                        />
                      </Box>
                    )}
                  </Box>
                )}
              </AccordionPaper>

              {/* Accordion Paper - Communication */}
              {/* <AccordionPaper label={t`Communication`} id="communication" disabled={!readOnly}>
<MntrMenuHeading label={t`Status`} />
<MetaDataParagraph text={values.status} />

<MntrMenuHeading label={t`Statistics`} />
<MetaDataStatus label={t`Campaigns`} value={values.campaigns} />
<MetaDataStatus label={t`Emails sent`} value={values.emails_sent} />
<MetaDataStatus
  label={t`Delivered / rate`}
  value={`${values.delivered_emails} / ${values.delivered_emails_percent}%`}
/>
<MetaDataStatus
  label={t`Opened / rate`}
  value={`${values.opened_emails} / ${values.opened_emails_percent}%`}
/>
<MetaDataStatus label={t`Related articles`} value={values.related_articles} />
</AccordionPaper> */}
            </StyledSidebar>
          </>
        )
      }}
    />
  )
}

export default observer(FormEditAuthor)
