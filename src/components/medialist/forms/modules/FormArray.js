import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import color from 'color'
import { FieldArray } from 'react-final-form-arrays'
import { styled } from 'styled-components'
import MntrAutocompleteAdapter from '~/components/forms/adapters/MntrAutocompleteAdapter/MntrAutocompleteAdapter'
import MntrPhoneAdapter from '~/components/forms/adapters/MntrPhoneAdapter/MntrPhoneAdapter'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import BasketIcon from '~/components/layout/Sidebar/modules/SidebarBaskets/BasketIcon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'
import FixedField from './FixedField'
import MainEmailHelperText from './MainEmailHelperText'

const AbsoluteBox = styled(Box)`
  position: absolute;
  top: 0px;
  left: 0px;
`

// TODO: this component is not very nice - we can do better
const FormArray = ({
  appStore: {
    authors: { getAuthorTitles, getNewsrooms, newsrooms, authorTitles },
  },
  values,
  name,
  disabled,
  readOnly,
  display,
  defaultCountry,
  placeholder,
  type = 'text',
  push,
  onDelete,
  onAdd,
  change,
}) => {
  return (
    <Box px={3} mb={1}>
      <Flex flexWrap={'wrap'}>
        <FieldArray name={name}>
          {({ fields }) =>
            fields.map((valuesArrPath, index) => {
              const item = values[name][index]
              let icon = ''
              let label = ''
              let iconBg = ''
              let iconColor = ''

              if (name === 'author_tags') {
                icon = 'label'
                label = item.author_tag_definition.label
                iconBg = item.author_tag_definition.color
                iconColor = color(iconBg).darken(0.6).toString()
              }

              if (name === 'author_basket_entries') {
                icon = (
                  <AbsoluteBox>
                    <BasketIcon
                      size={26}
                      fontSize={13}
                      label={item.author_basket.label}
                      bg={color(item.author_basket.color).darken(0.3).toString()}
                    />
                  </AbsoluteBox>
                )
                label = item.author_basket.label
                iconBg = item.author_basket.color
                iconColor = color(iconBg).darken(0.4).toString()
              }

              const onFieldDelete = () => {
                onDelete?.(index)
                if (values[name].length === 1) {
                  // fields.remove() with one item delets the item completely
                  return change(name, [])
                }
                fields.remove(index)
              }

              // Display Input as field
              if (display === 'input') {
                if (name === 'jobs') {
                  return (
                    <Flex key={index.toString()} width={1} alignItems="self-end" mb={1}>
                      <Flex flexDirection="column">
                        <Flex flexDirection="row" alignItems="self-end" width={1} gap={2} mt={3}>
                          <Box>
                            <FixedField
                              name={`${valuesArrPath}.newsroom_name`}
                              placeholder={`${t`Editorial Office`} #${index + 1}`}
                              component={MntrAutocompleteAdapter}
                              options={newsrooms.slice() || []}
                              onInputChange={(value) => {
                                if (value !== item?.newsroom_name) {
                                  getNewsrooms(value)
                                }
                              }}
                            />
                          </Box>
                          <Box>
                            <FixedField
                              name={`${valuesArrPath}.title`}
                              placeholder={`${t`Job Position`} #${index + 1}`}
                              component={MntrAutocompleteAdapter}
                              options={authorTitles.slice() || []}
                              onInputChange={(value) => {
                                if (value !== item?.title) {
                                  getAuthorTitles(value)
                                }
                              }}
                            />
                          </Box>
                          <Box>
                            <MntrButton
                              tooltip={t`Delete`}
                              onClick={() => onFieldDelete()}
                              variant="icon"
                              icon="delete"
                            />
                          </Box>
                        </Flex>
                        <Box mr={'44px'} mt={2}>
                          <FixedField
                            name={`${valuesArrPath}.url`}
                            component={MntrTextFieldAdapter}
                            type={type}
                            placeholder={`${t`Url`} #${index + 1}`}
                          />
                        </Box>
                      </Flex>
                    </Flex>
                  )
                }

                const fieldName = (() => {
                  if (name === 'shortnames') {
                    return valuesArrPath
                  }

                  return `${valuesArrPath}.text`
                })()

                const hasHelperText = name === 'emails' && index === 0

                return (
                  <Flex key={index.toString()} width={1} alignItems="self-end">
                    <Box flex="1" mt={2}>
                      <FixedField
                        name={fieldName}
                        component={type === 'tel' ? MntrPhoneAdapter : MntrTextFieldAdapter}
                        defaultCountry={defaultCountry}
                        type={type}
                        placeholder={`${placeholder} #${index + 1}`}
                        {...(hasHelperText && {
                          helperText: <MainEmailHelperText />,
                        })}
                      />
                    </Box>
                    <Box mb={hasHelperText ? 4 : '2px'} ml={2}>
                      <MntrButton
                        tooltip={t`Delete`}
                        onClick={() => onFieldDelete()}
                        variant="icon"
                        icon="delete"
                      />
                    </Box>
                  </Flex>
                )
              }

              // Display Chip as field
              return (
                <MntrButton
                  disabled
                  isChip
                  icon={icon}
                  iconColor={iconColor}
                  iconBg={iconBg}
                  key={name}
                  label={label}
                  {...(onDelete && { onDelete: onFieldDelete })}
                  mr={1}
                  mb={1}
                />
              )
            })
          }
        </FieldArray>

        {typeof push === 'function' && (
          <Box py={2} width={1}>
            <MntrButton
              isChip
              label={t`Add`}
              onClick={() => push(name, undefined)}
              bg="secondary"
              icon="add"
              iconBg="transparent"
            />
          </Box>
        )}

        {/* TODO: add button */}
        {display !== 'input' &&
          (name === 'author_tags' || name === 'author_basket_entries') &&
          !disabled &&
          !readOnly &&
          onAdd && (
            <MntrButton
              mt={-3}
              isChip
              rounded
              bg="secondary"
              iconBg="transparent"
              icon={'add'}
              label={<Trans>Add</Trans>}
              zIndex={9000}
              {...onAdd()}
            />
          )}
      </Flex>
    </Box>
  )
}

export default observer(FormArray)
