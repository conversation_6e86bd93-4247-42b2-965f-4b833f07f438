import { Box, Flex, Text } from '~/components/misc/Mntr'
import Mntr<PERSON>utton from '~/components/misc/MntrButton/MntrButton'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'

const MetaDataStatus = ({ label, value, url, tooltip, actions = [] }) => {
  return (
    <Flex pl={3} pr="4px" mb={1} color="black">
      {label && (
        <Flex width={['145px', '185px']} centerY>
          <Text fontWeight="700">{label}</Text>
        </Flex>
      )}
      <Box flex="1" mt={1}>
        {url && (
          <a href={url} target="_blank">
            <MntrButton
              icon="open_in_new"
              isChip
              label={value}
              mr={1}
              mb={1}
              bg="defaultPrimary"
              tooltip={tooltip}
              tooltipPlacement="top-start"
            />
          </a>
        )}

        {!url && (
          <Box style={{ display: 'inline-flex' }}>
            <StyledTooltip tooltip={tooltip} placement="top-start">
              <Text fontSize="14px">{value}</Text>
            </StyledTooltip>
          </Box>
        )}
      </Box>

      <Box width="36px" className="action">
        {actions.map((action, index) => {
          return <MntrButton key={index.toString()} {...action} />
        })}
      </Box>
    </Flex>
  )
}

export default MetaDataStatus
