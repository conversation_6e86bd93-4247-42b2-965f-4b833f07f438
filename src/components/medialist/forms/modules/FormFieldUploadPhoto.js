import { Trans } from '@lingui/react/macro'
import { useEffect } from 'react'
import Dropzone from 'react-dropzone'
import { Field } from 'react-final-form'
import { styled } from 'styled-components'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { DropzoneWrapper } from '~/components/medialist/styles/StyledFormEditAuthor'
import Icon from '~/components/misc/Icon/Icon'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { ACCEPTED_FILE_TYPES } from '~/constants'
import { observer } from '~/helpers/mst'

const StyledImg = styled.img`
  max-width: 100%;
  border-radius: 10px;
  overflow: hidden;
`

const StyledImgWrapper = styled(Box)`
  cursor: pointer;
  text-align: center;
  & img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
`

const RemoveButtonWrapper = styled(Box)`
  z-index: 10;
  position: absolute;
  top: 0;
  right: 0;
`

const FormFieldUploadPhoto = ({ values, isEdit, change, previewImage, setPreviewImage }) => {
  // if edit, set preview image from initialValues
  useEffect(() => {
    if (isEdit && values?.photo_url?.large) {
      setPreviewImage(values?.photo_url?.large)
    }
  }, [isEdit, values?.photo_url?.large, setPreviewImage])

  return (
    <>
      <RemoveButtonWrapper>
        {previewImage && (
          <MntrButton
            tooltip={<Trans>Remove Photo</Trans>}
            icon="close"
            onClick={() => {
              setPreviewImage(null)
              change('photo', null)
              change('photo_url.small', '')
              change('photo_url.large', '')
            }}
          />
        )}
      </RemoveButtonWrapper>
      <DropzoneWrapper>
        <Box p={2} pb={0}>
          <Dropzone
            accept={ACCEPTED_FILE_TYPES}
            onDrop={(files) => {
              const reader = new FileReader()
              reader.readAsDataURL(files[0])
              reader.addEventListener('load', (res) => {
                change('photo', res.target.result)
                setPreviewImage(res.target.result)
              })
            }}
          >
            {({ getRootProps, getInputProps }) => {
              return (
                <div {...getRootProps({ className: 'dropzone' })}>
                  <input {...getInputProps()} />
                  <label htmlFor="file-input">
                    {previewImage ? (
                      <>
                        <StyledImgWrapper>
                          <StyledImg src={previewImage} alt="preview-image" />
                        </StyledImgWrapper>
                      </>
                    ) : (
                      <StyledImgWrapper>
                        <Box mb={1}>
                          <Icon color="black">add_a_photo</Icon>
                        </Box>
                      </StyledImgWrapper>
                    )}
                  </label>
                </div>
              )
            }}
          </Dropzone>
          <div style={{ display: 'none' }}>
            <Field name="photo" component={MntrTextFieldAdapter} />
          </div>
        </Box>
      </DropzoneWrapper>
    </>
  )
}

export default observer(FormFieldUploadPhoto)
