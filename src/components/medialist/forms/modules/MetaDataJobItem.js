import { styled } from 'styled-components'
import CategoryTypeIcon from '~/components/misc/CategoryTypeIcon/CategoryTypeIcon'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import { observer } from '~/helpers/mst'

const Image = styled.img`
  width: 18px;
  height: 18px;
`

const IconWrapper = styled(Box)`
  position: relative;
  top: 3px;
`

const MetaDataJobItem = ({
  appStore: {
    account: { enums },
  },
  newsroom,
  title,
  url,
  tooltip,
  faviconUrl,
  categoryType,
}) => {
  const categoryTypeObj = enums.category_type.find(
    (entry) => parseInt(entry.id) === parseInt(categoryType),
  )

  return (
    <Flex pl={3} pr={'4px'} mb={1} hasnewsroom={0} color="black">
      <Flex mr="8px" alignItems="top">
        <IconWrapper>
          {categoryTypeObj && !faviconUrl && (
            <CategoryTypeIcon categoryType={categoryTypeObj} size={18} />
          )}
          {faviconUrl && <Image alt={`favicon-${newsroom}`} src={faviconUrl} />}
        </IconWrapper>
      </Flex>

      {url && (
        <a href={url} target="_blank">
          <Box width={['120px', '160px']}>
            <Text fontWeight="700">{newsroom}</Text>
          </Box>
        </a>
      )}

      {!url && (
        <Box width={['120px', '160px']}>
          <Text fontWeight="700">{newsroom}</Text>
        </Box>
      )}

      <Box flex="1">
        <Box display="inline-flex">
          <StyledTooltip tooltip={tooltip} placement="top-start">
            <Text fontSize="14px">{title}</Text>
          </StyledTooltip>
        </Box>
      </Box>
    </Flex>
  )
}

export default observer(MetaDataJobItem)
