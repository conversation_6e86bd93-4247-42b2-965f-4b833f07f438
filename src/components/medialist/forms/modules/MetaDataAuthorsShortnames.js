import { styled } from 'styled-components'
import { Box, Text } from '~/components/misc/Mntr'

const ArrItem = styled.div`
  display: inline-flex;
  margin-right: 4px;
`

const MetaDataAuthorsShortnames = ({ items = [] }) => {
  return (
    <Box px={3}>
      {items.map((item, index) => {
        return (
          <>
            {item && (
              <ArrItem>
                <Text>
                  {item}
                  {index < items.length - 1 && ', '}
                </Text>
              </ArrItem>
            )}
          </>
        )
      })}
    </Box>
  )
}

export default MetaDataAuthorsShortnames
