import { observer } from '~/helpers/mst'
import MetaDataStatus from './MetaDataStatus'

const MetaDataStatusList = ({ list = [], label, handleClick, icon = 'visibility' }) => {
  return (
    <>
      {list.map((item, index) => {
        const counter = index + 1
        return (
          <>
            {item && (
              <MetaDataStatus
                key={index.toString()}
                label={list.length > 1 ? `${label} #${counter}` : label}
                value={item.text}
                url={item.uri}
                {...(handleClick && {
                  actions: [
                    {
                      icon,
                      size: 'small',
                      onClick: () => {
                        handleClick(item.show_value_url, index)
                      },
                    },
                  ],
                })}
              />
            )}
          </>
        )
      })}
    </>
  )
}

export default observer(MetaDataStatusList)
