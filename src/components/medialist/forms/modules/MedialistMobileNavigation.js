import identityFn from 'lodash/identity'
import noopFn from 'lodash/noop'
import { Field, Form } from 'react-final-form'
import { styled } from 'styled-components'
import MntrSelectAdapter from '~/components/forms/adapters/MntrSelectAdapter/MntrSelectAdapter'
import tabNavigation from '~/components/medialist/constants/medialist.tabNavigation'
import { Box, Flex } from '~/components/misc/Mntr'
import { routerPush } from '~/helpers/router'

const Wrapper = styled(Box)`
  display: none;
  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    display: block;
  }
`

const MedialistMobileNavigation = ({ isOwnAuthor, activeTab, authorId }) => {
  // redirect on change
  const redirect = (url) => {
    routerPush(url)
  }

  // Read navigation items from constants
  const navigation = tabNavigation(isOwnAuthor, authorId, activeTab).map((item) => {
    return {
      value: item.href,
      label: item.label,
    }
  })

  return (
    <Wrapper mx={3} pb={1}>
      <Form
        onSubmit={noopFn}
        initialValues={{
          url: location.pathname,
        }}
        render={({ handleSubmit }) => {
          return (
            <form onSubmit={handleSubmit}>
              <Flex flexWrap="wrap">
                <Box width={1}>
                  <Field
                    parse={identityFn}
                    name="url"
                    component={MntrSelectAdapter}
                    items={navigation}
                    autoComplete="off"
                    onChange={(value) => redirect(value)}
                  />
                </Box>
              </Flex>
            </form>
          )
        }}
      />
    </Wrapper>
  )
}

export default MedialistMobileNavigation
