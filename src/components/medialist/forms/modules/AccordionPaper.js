import { useEffect, useState } from 'react'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import { medialistSettings } from '~/components/medialist/constants/medialist.settings'
import { Box } from '~/components/misc/Mntr'

const AccordionPaper = ({ label, children, id, disabled = false, forceOpen = false, mb }) => {
  const [expanded, setExpanded] = useState(true)

  // initial state
  useEffect(() => {
    const expanded = JSON.parse(localStorage.getItem(`accordion-${id}`))
    setExpanded(expanded === null ? true : expanded)
  }, [id])

  // toggle and save state
  const handleChange = () => {
    localStorage.setItem(`accordion-${id}`, JSON.stringify(!expanded))
    setExpanded(!expanded)
  }

  // right menu
  const actions = [
    {
      icon: expanded ? 'expand_less' : 'expand_more',
      bg: 'light',
      onClick: handleChange,
    },
  ]

  if (disabled) {
    return (
      <Box style={{ opacity: medialistSettings.DISABLED_CONTENT_OPACITY }}>
        <MntrPaper height="auto" mb={mb}>
          <MntrPaperToolbar title={label} />
        </MntrPaper>
      </Box>
    )
  }

  if (forceOpen) {
    return (
      <MntrPaper height="auto" mb={mb}>
        <MntrPaperToolbar title={label} />
        <Box pb={2}>{children}</Box>
      </MntrPaper>
    )
  }

  return (
    <MntrPaper height="auto" mb={mb}>
      <MntrPaperToolbar title={label} actions={actions} />
      {expanded && <Box pb={2}>{children}</Box>}
    </MntrPaper>
  )
}

export default AccordionPaper
