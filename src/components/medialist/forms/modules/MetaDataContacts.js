import StyledHelperText from '~/components/forms/adapters/shared/StyledHelperText'
import { Box } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'
import MainEmailHelperText from './MainEmailHelperText'
import MetaDataContactItem from './MetaDataContactItem'

const MetaDataContacts = ({ list = [], label, actions, handleContactClick, type }) => {
  return (
    <>
      {list.map((item, index) => {
        const counter = index + 1

        return (
          <>
            {item && (
              <>
                <MetaDataContactItem
                  key={index.toString()}
                  label={list.length > 1 ? `${label} #${counter}` : label}
                  value={item.text}
                  url={item.uri}
                  onContactClick={() => handleContactClick(item, index)}
                  actions={actions(item, index)}
                />

                {index === 0 && type === 'emails' && (
                  <Box ml={3} mt={-1} mb={2}>
                    <StyledHelperText error={false}>
                      <MainEmailHelperText />
                    </StyledHelperText>
                  </Box>
                )}
              </>
            )}
          </>
        )
      })}
    </>
  )
}

export default observer(MetaDataContacts)
