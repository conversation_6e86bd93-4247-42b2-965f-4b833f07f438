import { styled } from 'styled-components'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const StyledFlex = styled(Flex)`
  align-items: center;

  & .action {
    opacity: 0;
  }

  &:hover {
    & .action {
      opacity: 1;
    }
  }
`

const MetaDataContactItem = ({ label, value, actions = [], onContactClick }) => {
  return (
    <StyledFlex pl={3} pr="4px" mb={1} color="black">
      {label && (
        <Box width={['130px', '170px']}>
          <Text fontWeight="700">{label}</Text>
        </Box>
      )}
      <Box flex={1}>
        <MntrButton bg="flat" isChip onClick={onContactClick} label={value} />
      </Box>
      <Box width="36px" className="action">
        {actions.map((action, index) => {
          return <MntrButton key={index.toString()} {...action} />
        })}
      </Box>
    </StyledFlex>
  )
}

export default MetaDataContactItem
