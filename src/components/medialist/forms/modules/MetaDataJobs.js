import { observer } from '~/helpers/mst'
import MetaDataJobItem from './MetaDataJobItem'

const MetaDataJobs = ({ list = [] }) => {
  return (
    <>
      {list.map((item, index) => {
        return (
          <>
            {item && (
              <MetaDataJobItem
                key={index.toString()}
                newsroom={item.newsroom_name}
                title={item.title}
                url={item.url}
                faviconUrl={item.favicon_url}
                categoryType={item.category_type}
              />
            )}
          </>
        )
      })}
    </>
  )
}

export default observer(MetaDataJobs)
