import { t } from '@lingui/core/macro'

/**
 * Tab navigation for author detail page
 * @param {Number} authorId
 * @param {string} activeTab
 * @returns {Array} tabNavigation
 */
const tabNavigation = (isOwnAuthor, authorId, activeTab) => {
  let items = [
    {
      label: t`Author Detail`,
      href: `/author/${authorId}`,
      active: activeTab === 'detail',
    },
  ]

  if (isOwnAuthor) {
    items.push({
      label: t`Overview`,
      href: `/author/${authorId}/overview`,
      active: activeTab === 'overview',
      icon: 'view_stream',
    })
  } else {
    items.push({
      label: t`Articles`,
      href: `/author/${authorId}/articles`,
      active: activeTab === 'articles',
      icon: 'view_stream',
    })
  }
  return items
}

export default tabNavigation
