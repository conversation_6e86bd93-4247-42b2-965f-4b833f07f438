import {
  attachClosestEdge,
  type Edge,
  extractClosestEdge,
} from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge'
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine'
import { draggable, dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { useEffect, useRef, useState } from 'react'
import { styled } from 'styled-components'
import invariant from 'tiny-invariant'
import { DropIndicator } from '~/components/dnd/DropIndicator'
import FeedMedialistItem from '~/components/medialist/content/FeedMedialist/FeedMedialistItem'
import { MEDIALIST_ARTICLES_PREFIX } from '~/constants'
import { ObservedFC, observer } from '~/helpers/msts'
import { routerPush, routerReplace } from '~/helpers/router'
import { IAuthorStoreArrItem } from '~/store/models/authors/AuthorStoreArrItem'

export type TItem = { id: number }

const itemDataKey = Symbol('medialist-item')

type TItemData = { [itemDataKey]: true; itemId: TItem['id'] }

// @ts-expect-error TODO refactor author baskets to TS
function getItemData(item: IAuthorStoreArrItem, baskets): TItemData {
  return {
    [itemDataKey]: true,
    // @ts-expect-error TODO isolate draggable item into a component where a
    // basket is always selected
    itemId: item.author_basket_entries.find((item) => {
      return item.author_basket.id === baskets.selected?.id
    })?.id,
  }
}

export function isItemData(data: Record<string | symbol, unknown>): data is TItemData {
  return data[itemDataKey] === true
}

type ItemState =
  | {
      type: 'idle'
    }
  | {
      type: 'preview'
      container: HTMLElement
    }
  | {
      type: 'is-dragging'
    }
  | {
      type: 'is-dragging-over'
      closestEdge: Edge | null
    }

const idle: ItemState = { type: 'idle' }

const Item = styled.div`
  &.is-dragging {
    opacity: 0.4;
  }
`

interface IDraggableMedialistItemProps {
  dnd: boolean
  item: IAuthorStoreArrItem
  selector: unknown // TODO: unknown
}

const DraggableMedialistItem: ObservedFC<IDraggableMedialistItemProps> = ({
  appStore: {
    authors: { baskets },
    viewport,
    filter,
    router: { makeRouteWithQuery },
  },
  dnd,
  item,
  selector,
}) => {
  const ref = useRef<HTMLDivElement | null>(null)
  const dragHandleRef = useRef<HTMLElement | null>(null)
  const [state, setState] = useState<ItemState>(idle)

  useEffect(() => {
    const element = ref.current
    invariant(element)
    const dragHandle = dragHandleRef.current
    invariant(dragHandle)
    return combine(
      draggable({
        element,
        dragHandle,
        getInitialData() {
          return getItemData(item, baskets)
        },
        onDragStart() {
          setState({ type: 'is-dragging' })
        },
        onDrop() {
          setState(idle)
        },
      }),
      dropTargetForElements({
        element,
        canDrop({ source }) {
          // not allowing dropping on yourself
          if (source.element === element) {
            return false
          }
          // only allowing medialist items to be dropped on me
          return isItemData(source.data)
        },
        getData({ input }) {
          const data = getItemData(item, baskets)
          return attachClosestEdge(data, {
            element,
            input,
            allowedEdges: ['top', 'bottom'],
          })
        },
        getIsSticky() {
          return true
        },
        onDragEnter({ self }) {
          const closestEdge = extractClosestEdge(self.data)
          setState({ type: 'is-dragging-over', closestEdge })
        },
        onDrag({ self }) {
          const closestEdge = extractClosestEdge(self.data)

          // Only need to update react state if nothing has changed.
          // Prevents re-rendering.
          setState((current) => {
            if (current.type === 'is-dragging-over' && current.closestEdge === closestEdge) {
              return current
            }
            return { type: 'is-dragging-over', closestEdge }
          })
        },
        onDragLeave() {
          setState(idle)
        },
        onDrop() {
          setState(idle)
        },
      }),
    )
  }, [baskets, item])

  const currRoute = makeRouteWithQuery()
  const as = `/author/${item.id}`

  // TODO: refactor filter
  const transformFilters = (): { [key: string]: string } => {
    const filters: { [key: string]: string } = {}

    for (const [key, val] of Object.entries(filter.data)) {
      if (key.startsWith(MEDIALIST_ARTICLES_PREFIX)) {
        if (Array.isArray(val)) {
          filters[key.replace(MEDIALIST_ARTICLES_PREFIX, '')] = val.join(',')
        } else if (typeof val === 'string') {
          filters[key.replace(MEDIALIST_ARTICLES_PREFIX, '')] = val
        } else {
          // Handle or log the case where val is not an array or string
          console.error('Invalid filter type', val)
        }
      }
    }

    return filters
  }

  const asFiltered = {
    pathname: as,
    query: {
      ...transformFilters(),
    },
  }

  return (
    <div style={{ position: 'relative' }}>
      <Item
        // Adding data-attribute as a way to query for this for our post drop flash
        data-dnd-item-id={
          item.author_basket_entries.find((item) => {
            return item.author_basket.id === baskets.selected?.id
          })?.id
        }
        className={state.type}
        ref={ref}
      >
        <FeedMedialistItem
          dnd={dnd}
          dragHandleRef={dragHandleRef}
          key={item.id}
          item={item}
          selector={selector}
          isSmallView={viewport?.width ? viewport.width < 1150 : false}
          urlDetail={as}
          openDetail={() => {
            routerReplace(currRoute, currRoute, { shallow: true })
            routerPush(currRoute, as, { shallow: true })
          }}
          urlDetailFiltered={item.matched_article_count ? asFiltered : null} // Explicitly pass null if not available
          openDetailFiltered={
            item.matched_article_count
              ? () => {
                  routerReplace(currRoute, currRoute, { shallow: true })
                  routerPush(currRoute, asFiltered, { shallow: true })
                }
              : undefined
          }
        />
      </Item>
      {state.type === 'is-dragging-over' && state.closestEdge ? (
        <DropIndicator edge={state.closestEdge} gap={'6px'} />
      ) : null}
    </div>
  )
}

export default observer(DraggableMedialistItem)
