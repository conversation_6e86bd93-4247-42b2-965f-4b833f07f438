import get from 'lodash/get'
import { styled } from 'styled-components'
import Flag from '~/components/Flag/Flag'
import AuthorInfoDetail from '~/components/medialist/content/AuthorInfoDetail'
import AuthorPhoto from '~/components/medialist/content/FeedMedialist/AuthorPhoto/AuthorPhoto'
import { Box, Heading } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const AuthorPhotoWrapper = styled.div`
  position: absolute;
  top: ${({ size }) => `calc(50% - ${size / 2}px)`};

  && {
    overflow: visible;
  }
`

const AuthorInfo = styled.div`
  padding-left: 42px;
`

const Country = styled(Box)`
  position: absolute;
  bottom: -2px;
  left: 17px;
`

const AuthorHeader = ({ item, pl, size = 30 }) => {
  return (
    <Box flexGrow="1" pl={pl}>
      <Heading as="div" fontSize={2} color="default">
        <AuthorPhotoWrapper size={size}>
          <AuthorPhoto
            size={size}
            authorType={item.author_type}
            image={get(item, 'photo_url.small')}
          />
          {item.country && item.country.code && (
            <Country>
              <Flag country={item.country.code} size={15} />
            </Country>
          )}
        </AuthorPhotoWrapper>

        <AuthorInfo>
          {item.name}
          <AuthorInfoDetail author={item} opacity={0.5} />
        </AuthorInfo>
      </Heading>
    </Box>
  )
}

export default observer(AuthorHeader)
