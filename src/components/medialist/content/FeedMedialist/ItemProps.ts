export interface Author {
  id: number
  name: string
  country: number
  author_type: number
  photo_url: {
    small: string
    large: string
  }
  is_own_author: boolean
  is_edited: boolean
  jobs: Job[]
  matched_article_count: number | null
  total_article_count: number
  contact_information: number[]
  google_search_url: string
  author_tags: AuthorTag[]
  author_basket_entries: AuthorBasketEntry[]
}

export interface Job {
  id: number
  newsroom_name: string
  title: string
  url: string
  favicon_url: string
  category_type: number
}

export interface AuthorBasket {
  id: number
  label: string
  color: string
}

export interface AuthorTagDefinition {
  id: number
  label: string
  color: string
}
export interface AuthorTag {
  id: number
  author_tag_definition: AuthorTagDefinition
}

export interface AuthorBasketEntry {
  id: number
  author_basket: AuthorBasket
}

export interface AuthorStat {
  label: string
  count: number
  is_bigger: boolean
  icon: string
}

export interface MediaListResponse {
  authors: Author[]
  medialist_hash: string
  filters: Record<string, unknown>
  has_next: boolean
  count: number
  is_total_count_bigger: boolean
  author_stats: AuthorStat[]
}
