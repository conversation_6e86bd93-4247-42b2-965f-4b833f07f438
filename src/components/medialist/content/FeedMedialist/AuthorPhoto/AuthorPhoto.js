import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import authorTypes from '~/constants/authorTypes'

const ImageWrapper = styled.div`
  width: ${(props) => props.size}px;
  height: ${(props) => props.size}px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: ${({ hasImage }) => (hasImage ? '0 4px 5px rgba(0, 0, 0, 0.05)' : '0')};
  background: ${({ hasImage, theme }) => (hasImage ? theme.paper.background : '#F9F7E8')};
  border: 1px solid ${({ hasImage, theme }) => (hasImage ? theme.paper.border : '#efe8d8')};
`

const Image = styled.div`
  background: url(${(props) => props.image}) 50% 50% no-repeat;
  width: ${(props) => props.size}px;
  height: ${(props) => props.size}px;
  background-size: ${({ authorType }) =>
    authorType === authorTypes.JOURNALIST || authorType === authorTypes.BLOGGER
      ? 'auto 100%'
      : 'contain'};
`
const AuthorPhoto = ({ image, size, authorType, bg = 'primary' }) => {
  return (
    <div>
      <ImageWrapper size={size} hasImage={Boolean(image) || bg !== 'primary'} type>
        {!image && (
          <Icon size={size} color="beige">
            account_circle
          </Icon>
        )}
        {image && <Image alt="author" size={size || 118} image={image} authorType={authorType} />}
      </ImageWrapper>
    </div>
  )
}

export default AuthorPhoto
