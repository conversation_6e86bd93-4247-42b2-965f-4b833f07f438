import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { css, styled } from 'styled-components'
import AuthorContactInformation from '~/components/medialist/content/AuthorContactInformation'
import AdditionalInfoTagsOrListsAuthors from '~/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors'
import AuthorHeader from '~/components/medialist/content/FeedMedialist/AuthorHeader'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import Toggle from '~/components/misc/Toggle/Toggle'
import * as format from '~/helpers/formatNumber'
import { humanizeNumber } from '~/helpers/formatNumber'
import { Link } from '~/helpers/router'

const Wrapper = styled(Box)`
  position: relative;
  border-radius: 3px;
  border: 1px solid ${({ theme }) => theme.paper.border};
  background: ${({ theme }) => theme.paper.background};
  height: 55px;
  overflow: hidden;
`

const Author = styled(Flex)`
  align-items: center;

  &:hover {
    background: ${({ theme }) => theme.paper.backgroundHover};
    cursor: pointer;
  }
  text-decoration: none;
  ${({ isSmallView }) =>
    !isSmallView
      ? css`
          padding-left: 12px;
          border-left: ${({ dashboardView, theme }) =>
            dashboardView ? 0 : `1px solid ${theme.paper.border}`};
          border-right: 1px solid ${({ theme }) => theme.paper.border};
          min-width: ${({ dashboardView }) => (dashboardView ? 100 : 380)}px;
          width: ${({ dashboardView }) => (dashboardView ? '100%' : 'auto')};
        `
      : css`
          padding-left: ${({ dashboardView }) => (dashboardView ? 10 : 51)}px;
        `}

  ${({ disabled }) => {
    return (
      disabled &&
      css`
        pointer-events: none;
      `
    )
  }}

  * {
    text-decoration: none;
    overflow: hidden;
  }
`

const ContactInfoWrapper = styled(Flex)`
  padding-left: 90px;
  padding-left: 12px;
  padding-right: 12px;
  min-width: 140px;
  text-align: right;
`

const Archive = styled(Box)`
  border-right: ${({ theme }) => `1px solid ${theme.paper.border}`};
  border-left: ${({ theme }) => `1px solid ${theme.paper.border}`};
  padding-left: 12px;
  padding-right: 12px;
  min-width: 100px;
  text-align: right;

  &:hover {
    background: ${({ theme }) => theme.paper.backgroundHover};
    cursor: pointer;
  }
  text-decoration: none;
`

const ArticleCount = styled.div`
  font-weight: 700;
  padding-top: 4px;
  color: ${({ theme }) => theme.colors.black};
`

const ArticleCountPlain = styled.div`
  font-size: 13px;
  color: ${({ theme }) => theme.colors.lightGrey};
`

const ToggleWrapper = styled(Flex)`
  min-width: 48px;
  height: 100%;
  align-items: center;
  justify-content: center;
  z-index: 2;

  ${({ isSmallView }) =>
    isSmallView &&
    css`
      position: absolute;
      top: 0;
      left: 0;
    `}
`

const DragHandle = styled(ToggleWrapper)`
  border-left: ${({ theme }) => `1px solid ${theme.paper.border}`};

  &:hover {
    cursor: grab;
  }

  ${({ isHidden }) =>
    isHidden &&
    css`
      display: none;
    `}
`

const Actions = styled(Flex)`
  justify-content: flex-end;
  align-items: center;
  flex-grow: 1;
  padding-left: 12px;
  padding-right: 12px;
  color: ${({ theme }) => theme.colors.lightGrey};
  min-width: 40px;
  padding-top: 4px;
  padding-bottom: 4px;
  text-align: right;
  position: relative;
  font-size: 13px;
  margin-left: 0px;
`

const FeedMedialistItem = ({
  dragHandleRef,
  item,
  selector,
  isSmallView,
  openDetail,
  urlDetail,
  urlDetailFiltered,
  openDetailFiltered,
  dashboardView = false,
  disabled = false,
  isPreview = false,
  dnd,
}) => {
  if (!item) {
    return <span />
  }

  const actions = (author) => {
    const buttons = [
      //   {
      //   tooltip: <Trans>Send email</Trans>,
      //   icon: "forward_to_inbox",
      //   bg: "transparent",
      //   onClick: () => {
      //     //TODO:
      //   }
      // },
      // {
      //   tooltip: <Trans>Edit</Trans>,
      //   icon: "edit",
      //   bg: "transparent",
      //   onClick: () => {
      //     //TODO:
      //   }
      // },
    ]

    if (author.google_search_url !== '') {
      buttons.push({
        tooltip: <Trans>Search on Google</Trans>,
        icon: 'person_search',
        bg: 'transparent',
        href: author.google_search_url,
        target: '_blank',
      })
    }

    return buttons
  }

  const openAuthorDetail = (event) => {
    if (event.ctrlKey || event.metaKey) {
      return true
    }

    event.preventDefault()
    openDetail()
  }

  const feedLinkButton = (
    <Flex flexWrap="nowrap" width={1} justifyContent="space-between" centerY>
      <AuthorHeader item={item} />
    </Flex>
  )

  const articleCountContent = (
    <>
      {typeof openDetailFiltered === 'function' && item.matched_article_count && (
        <Text
          mx={2}
          onClick={() => {
            openDetailFiltered()
          }}
          textAlign="right"
        >
          <ArticleCount>{item.matched_article_count}</ArticleCount>
          <ArticleCountPlain>
            {format.formatArticlesPlain(item.matched_article_count)}
          </ArticleCountPlain>
        </Text>
      )}
    </>
  )

  const archiveLinkButton = (
    <Flex flexDirection="column" mt="2px">
      <ArticleCount>{humanizeNumber(item.total_article_count)}</ArticleCount>
      <ArticleCountPlain>{t`archive`}</ArticleCountPlain>
    </Flex>
  )

  return (
    <Wrapper m={0} mb={0} data-dnd-flash>
      <Flex
        data-e2e="replace_content"
        flexWrap={isSmallView ? 'wrap' : 'nowrap'}
        height={1}
        m={dashboardView && 0}
      >
        {!dashboardView && (
          <ToggleWrapper ml="1px" isSmallView={isSmallView}>
            <Toggle id={item.id} selector={selector} />
          </ToggleWrapper>
        )}

        {!dashboardView && (
          <DragHandle isHidden={!dnd || isSmallView} ref={dragHandleRef}>
            <Icon color="black">drag_handle</Icon>
          </DragHandle>
        )}

        <Author
          disabled={disabled}
          width={isSmallView ? 1 : 'auto'}
          isSmallView={isSmallView}
          className="box-hover"
          dashboardView={dashboardView}
        >
          {typeof openDetailFiltered === 'function' && urlDetailFiltered ? (
            <Link
              href={urlDetailFiltered}
              naked
              {...(isPreview && { target: '_blank' })} // no inspector opening, so open in new tab
              {...(!isPreview && {
                onClick: (event) => {
                  if (event.ctrlKey || event.metaKey) {
                    return true
                  }

                  event.preventDefault()
                  openDetailFiltered()
                },
              })}
            >
              {feedLinkButton}
            </Link>
          ) : (
            <Link
              href={urlDetail}
              naked
              {...(isPreview && { target: '_blank' })} // no inspector opening, so open in new tab
              {...(!isPreview && {
                onClick: openAuthorDetail,
              })}
            >
              {feedLinkButton}
            </Link>
          )}
        </Author>
        {articleCountContent}

        {!dashboardView && !isSmallView && (
          <>
            <Archive className="box-hover button-link">
              <Link href={urlDetail} naked onClick={openAuthorDetail}>
                {archiveLinkButton}
              </Link>
            </Archive>

            <ContactInfoWrapper centerY>
              <AuthorContactInformation values={item.contact_information} />
            </ContactInfoWrapper>

            <AdditionalInfoTagsOrListsAuthors item={item} />

            <Flex flexWrap="nowrap" width={1} centerY>
              <Actions>
                {actions(item).map((action, key) => {
                  return (
                    <Box ml={1} key={key}>
                      <MntrButton {...action} />
                    </Box>
                  )
                })}
              </Actions>
            </Flex>
          </>
        )}
      </Flex>
    </Wrapper>
  )
}

export default FeedMedialistItem
