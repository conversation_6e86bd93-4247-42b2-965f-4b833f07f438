import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useState } from 'react'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MonitoringPromo from '~/components/monitoring/MonitoringPromo/MonitoringPromo'
import events from '~/constants/gtm'
import { pushEvent } from '~/helpers/gtm'
import { observer } from '~/helpers/mst'
import viewTransition from '~/helpers/viewTransition'

import {
  Modal,
  ModalWrapper,
  Overlay,
} from '~/components/monitoring/FeedChart/MediatypePromo/styles/StyledMediaTypePromo'
import HandleEscapeKey from '~/helpers/hooks/modules/HandleEscapeKey'

const FeedMedialistPromo = ({
  appStore: {
    appSettings: { appName },
  },
}) => {
  const [isModalOpen, setModalOpen] = useState(false)

  const handleClosePromo = () => {
    viewTransition(() => setModalOpen(false))
  }

  const handleShowPromo = () => {
    viewTransition(() => setModalOpen(true))

    pushEvent(events.PROMO_MODAL)
  }

  return (
    <div>
      <MntrPaper>
        <Flex p={4} centerY gap={3} flexDirection={['column', 'column', 'row']}>
          <Flex>
            <Text color="primary">
              <Icon size={32}>info</Icon>
            </Text>
          </Flex>
          <Flex column flex={1} gap={1}>
            <Heading as="h1" fontSize={4} color="black" textAlign={['center', 'center', 'left']}>
              <Trans>Access Full Articles via Media Monitoring</Trans>
            </Heading>
            <Text color="mediumGrey">
              <Trans>
                Access comprehensive articles via {appName}’s media monitoring, covering online,
                traditional, and social media content.
              </Trans>
            </Text>
          </Flex>
          <Flex>
            <MntrButton rounded label={t`Find out more`} bg="secondary" onClick={handleShowPromo} />
          </Flex>
        </Flex>
      </MntrPaper>

      {/* Promo Modal */}
      {isModalOpen && (
        <>
          <HandleEscapeKey onCancel={handleClosePromo} />
          <Overlay isOpen={true} onClose={handleClosePromo} onClick={handleClosePromo} />
          <ModalWrapper maxWidth={690} padding={0}>
            <Modal>
              <Box mx={[-2, -4]}>
                <MonitoringPromo />
              </Box>
            </Modal>
          </ModalWrapper>
        </>
      )}
    </div>
  )
}

export default observer(FeedMedialistPromo)
