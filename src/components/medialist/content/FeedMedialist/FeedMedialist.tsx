import { autoScrollWindowForElements } from '@atlaskit/pragmatic-drag-and-drop-auto-scroll/element'
import { triggerPostMoveFlash } from '@atlaskit/pragmatic-drag-and-drop-flourish/trigger-post-move-flash'
import { extractClosestEdge } from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge'
import { getReorderDestinationIndex } from '@atlaskit/pragmatic-drag-and-drop-hitbox/util/get-reorder-destination-index'
import { monitorForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter'
import { useEffect, useState } from 'react'
import { flushSync } from 'react-dom'
import FeedMedialistLoadMore from '~/components/medialist/content/FeedMedialist/FeedMedialistLoadMore'
import { Box, Flex } from '~/components/misc/Mntr'
import { ObservedFC, observer } from '~/helpers/msts'
import { IAuthorStoreArrItem } from '~/store/models/authors/AuthorStoreArrItem'
import DraggableMedialistItem, { isItemData } from './DraggableMedialistItem'

interface IFeedMedialistProps {
  list: IAuthorStoreArrItem[]
  canLoadMore: boolean
  selector: unknown // TODO: unknown
  loadMore: () => void
  isLoading: boolean
  isLoadingMore: boolean
  dnd: boolean
}

const FeedMedialist: ObservedFC<IFeedMedialistProps> = ({
  appStore: {
    account: { workspace },
    authors: { baskets, moveAuthorToIndex },
  },
  list,
  canLoadMore,
  selector,
  loadMore,
  isLoading,
  isLoadingMore,
  dnd,
}) => {
  const [items, setItems] = useState<IAuthorStoreArrItem[]>(list)

  useEffect(() => {
    setItems(list)
  }, [list])

  useEffect(() => {
    return monitorForElements({
      canMonitor({ source }) {
        return isItemData(source.data)
      },
      onDrop({ location, source }) {
        const target = location.current.dropTargets[0]

        if (!target) {
          return
        }

        const sourceData = source.data
        const targetData = target.data

        if (!isItemData(sourceData) || !isItemData(targetData)) {
          return
        }

        const indexOfSource = items.findIndex((item) => {
          return (
            // @ts-expect-error TODO isolate droppable list into a component
            // where a basket is always selected
            item.author_basket_entries.find((item) => {
              return item.author_basket.id === baskets.selected.id
            }).id === sourceData.itemId
          )
        })

        const indexOfTarget = items.findIndex((item) => {
          return (
            // @ts-expect-error TODO isolate droppable list into a component
            // where a basket is always selected
            item.author_basket_entries.find((item) => {
              return item.author_basket.id === baskets.selected.id
            }).id === targetData.itemId
          )
        })

        if (indexOfTarget < 0 || indexOfSource < 0) {
          return
        }

        const closestEdgeOfTarget = extractClosestEdge(targetData)

        // Using `flushSync` so we can query the DOM straight after this line
        flushSync(() => {
          moveAuthorToIndex(
            baskets.selected.id,
            sourceData.itemId,
            indexOfSource,
            getReorderDestinationIndex({
              startIndex: indexOfSource,
              indexOfTarget,
              closestEdgeOfTarget,
              axis: 'vertical',
            }),
          )
        })

        // Being simple and just querying for the item after the drop.
        // We could use react context to register the element in a lookup,
        // and then we could retrieve that element after the drop and use
        // `triggerPostMoveFlash`. But this gets the job done.
        const element = document.querySelector(
          `[data-dnd-item-id="${sourceData.itemId}"] [data-dnd-flash]`,
        )

        if (element instanceof HTMLElement) {
          triggerPostMoveFlash(element)
        }
      },
    })
  }, [baskets.selected?.id, moveAuthorToIndex, items])

  useEffect(() => {
    return autoScrollWindowForElements()
  })

  //TODO: types.optional picuje - workaround
  const canDragDrop = workspace?.permissions?.authors_database?.can_write && dnd

  return (
    <Box my={0}>
      <Flex flexWrap="wrap">
        <Flex flexDirection="column" gap={1} width={1}>
          {list.map((item) => {
            return (
              <DraggableMedialistItem
                dnd={canDragDrop}
                item={item}
                selector={selector}
                key={item.id}
              />
            )
          })}
        </Flex>
      </Flex>
      {canLoadMore && (
        <FeedMedialistLoadMore
          loadMore={loadMore}
          canLoadMore={canLoadMore}
          isLoading={isLoading}
          isLoadingMore={isLoadingMore}
        />
      )}
    </Box>
  )
}

export default observer(FeedMedialist)
