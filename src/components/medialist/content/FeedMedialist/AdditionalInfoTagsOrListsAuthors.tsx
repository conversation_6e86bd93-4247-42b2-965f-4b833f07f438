import { t } from '@lingui/core/macro'
import { Plural } from '@lingui/react/macro'
import { styled } from 'styled-components'
import {
  Author,
  AuthorBasketEntry,
  AuthorTag,
} from '~/components/medialist/content/FeedMedialist/ItemProps'
import Icon from '~/components/misc/Icon/Icon'
import { Flex, Text } from '~/components/misc/Mntr'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'

interface AdditionalInfoTagsOrListsAuthorsProps {
  item?: Author
}

const BorderedBoxWrapper = styled(Flex)`
  padding-left: 12px;
  padding-right: 12px;
  min-width: 230px;
  text-align: right;
  border-right: 1px solid ${({ theme }) => theme.paper.border};
  border-left: 1px solid ${({ theme }) => theme.paper.border};
`

const MAX_ITEMS = 5

const isAuthorTag = (item: AuthorTag | AuthorBasketEntry): item is AuthorTag => {
  return (item as AuthorTag).author_tag_definition !== undefined
}

const RenderTagsOrLists = ({
  items,
  label,
  isDisabled,
  icon,
}: {
  items: AuthorTag[] | AuthorBasketEntry[]
  label: string
  isDisabled: boolean
  tagKey: 'author_tag_definition' | 'author_basket'
  icon: string
}) => {
  return (
    <Flex centerY>
      <Text fontSize="13px" color={isDisabled ? 'disabled' : undefined} mr={1}>
        {label}
      </Text>
      {items.slice(0, MAX_ITEMS).map((item, key) => {
        let color: string
        let labelText: string

        if (isAuthorTag(item)) {
          color = item.author_tag_definition.color
          labelText = item.author_tag_definition.label
        } else {
          color = item.author_basket.color
          labelText = item.author_basket.label
        }

        return (
          <StyledTooltip key={key} tooltip={labelText}>
            <Flex centerY>
              <Icon size={18} color={color} fill={1}>
                {icon}
              </Icon>
            </Flex>
          </StyledTooltip>
        )
      })}
      {items.length > MAX_ITEMS && (
        <Text pl={1} fontSize="13px" fontWeight="normal" color="lightGrey" alignContent="center">
          <Plural value={items.length - MAX_ITEMS} one="+# more" other="+# more" />
        </Text>
      )}
    </Flex>
  )
}

const AdditionalInfoTagsOrListsAuthors: <AUTHORS>
  item,
}) => {
  if (!item) return <span />

  return (
    <BorderedBoxWrapper centerY>
      <Flex color="black" flexDirection="column">
        <RenderTagsOrLists
          items={item.author_tags}
          label={t`Tags`}
          isDisabled={item.author_tags.length === 0}
          icon={'label'}
          tagKey="author_tag_definition"
        />
        <RenderTagsOrLists
          items={item.author_basket_entries}
          label={t`Lists`}
          isDisabled={item.author_basket_entries.length === 0}
          icon={'circle'}
          tagKey="author_basket"
        />
      </Flex>
    </BorderedBoxWrapper>
  )
}

export default AdditionalInfoTagsOrListsAuthors
