import { Trans } from '@lingui/react/macro'
import noopFn from 'lodash/noop'
import { styled } from 'styled-components'
import PageContent from '~/components/Content/Content'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import FeedMedialist from '~/components/medialist/content/FeedMedialist/FeedMedialist'
import { Box, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { observer } from '~/helpers/mst'

// TODO: fetch dashboard data + render

const StatsHeading = styled(Flex)`
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid ${({ theme }) => theme.paper.border};
`

const StatsData = styled(Flex)`
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid ${({ theme }) => theme.paper.border};
`

const StatsDataItem = styled(Flex)`
  flex-direction: column;
`

const StatsButtons = styled(Flex)`
  align-items: center;
  justify-content: space-between;
  padding: 15px;
`

const StatsNumber = styled(Text)`
  font-size: 22px;
`

const StatsDescription = styled(Text)`
  font-size: 15px;
  text-transform: lowercase;
`

const MedialistDashboard = ({
  appStore: {
    authors: { medialistSelector: selector }, // tbd custom selector for both lists
  },
  isLoading = false,
}) => {
  //TODO: real data
  const data = {}
  const listViewed = data
  const listEdited = data

  const goToAuthors = noopFn

  const goToViewedAuthors = noopFn

  const goToEditedAuthors = noopFn

  const goToMyAuthors = noopFn

  const createAuthor = noopFn

  return (
    <PageContent>
      <Flex color="black" mt={5} flexDirection={['column', 'row']}>
        <Box width={[1, 1 / 2]} pr={[0, 2]} pb={[2, 0]}>
          <MntrPaper>
            <StatsHeading>
              <Heading>Authors</Heading>
              <Text>
                <Trans>Updated</Trans> 15 minutes ago
              </Text>
            </StatsHeading>
            <StatsData>
              <StatsDataItem>
                <StatsNumber>10 000+</StatsNumber>
                <StatsDescription>
                  <Trans>Authors</Trans>
                </StatsDescription>
              </StatsDataItem>
              <StatsDataItem>
                <StatsNumber>10 000+</StatsNumber>
                <StatsDescription>
                  <Trans>Contacts</Trans>
                </StatsDescription>
              </StatsDataItem>
              <StatsDataItem>
                <StatsNumber>10 000+</StatsNumber>
                <StatsDescription>
                  <Trans>New</Trans>
                </StatsDescription>
              </StatsDataItem>
            </StatsData>
            <StatsButtons>
              <MntrButton fullWidth label={<Trans>Show authors</Trans>} onClick={goToAuthors} />
            </StatsButtons>
          </MntrPaper>
        </Box>
        <Box width={[1, 1 / 2]}>
          <MntrPaper>
            <StatsHeading>
              <Heading>My authors</Heading>
              <Text>
                <Trans>Updated</Trans> 15 minutes ago
              </Text>
            </StatsHeading>
            <StatsData>
              <StatsDataItem>
                <StatsNumber>10 000+</StatsNumber>
                <StatsDescription>
                  <Trans>Authors</Trans>
                </StatsDescription>
              </StatsDataItem>
              <StatsDataItem>
                <StatsNumber>10 000+</StatsNumber>
                <StatsDescription>
                  <Trans>Contacts</Trans>
                </StatsDescription>
              </StatsDataItem>
              <StatsDataItem>
                <StatsNumber>10 000+</StatsNumber>
                <StatsDescription>
                  <Trans>New</Trans>
                </StatsDescription>
              </StatsDataItem>
            </StatsData>
            <StatsButtons>
              <Box pr={2} width={1}>
                <MntrButton
                  fullWidth
                  label={<Trans>Show</Trans>}
                  onClick={goToMyAuthors}
                  href="/authors?is_own_author=true"
                />
              </Box>

              <Box width={1}>
                <MntrButton
                  fullWidth
                  label={<Trans>Create author</Trans>}
                  onClick={createAuthor}
                  href="/authors/create"
                />
              </Box>
            </StatsButtons>
          </MntrPaper>
        </Box>
      </Flex>

      <Flex flexDirection="column" mt={5}>
        <Flex flexDirection="column">
          <Flex justifyContent="space-between">
            <Heading color="black">
              <Trans>Recently viewed authors</Trans>
            </Heading>
            <MntrButton
              bg="transparent"
              label={<Trans>Show more</Trans>}
              onClick={goToViewedAuthors}
            />
          </Flex>

          {/* selector ?  */}
          <FeedMedialist
            list={listViewed}
            canLoadMore={false}
            selector={selector}
            isLoading={isLoading}
            maxItems={5}
          />
        </Flex>
        <Flex flexDirection="column" mt={5}>
          <Flex justifyContent="space-between">
            <Heading color="black">
              <Trans>Recently edited authors</Trans>
            </Heading>
            <MntrButton
              bg="transparent"
              label={<Trans>Show more</Trans>}
              onClick={goToEditedAuthors}
            />
          </Flex>

          <FeedMedialist
            list={listEdited}
            canLoadMore={false}
            selector={selector}
            isLoading={isLoading}
            maxItems={5}
          />
        </Flex>
      </Flex>
    </PageContent>
  )
}

export default observer(MedialistDashboard)
