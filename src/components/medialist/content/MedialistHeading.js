import { Trans } from '@lingui/react/macro'
import { Box, Heading } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const MedialistHeading = ({
  appStore: {
    authors: { isEmpty, isLoading },
  },
}) => {
  return (
    <Box mb={3} mt={3}>
      <Box textAlign="center" position="relative">
        <Heading as="h1" fontSize={4} mt={isEmpty ? 4 : 0} color="black">
          {isLoading && <Trans>Loading...</Trans>}
          {!isLoading && isEmpty && <Trans>No results found</Trans>}
        </Heading>
      </Box>
    </Box>
  )
}

export default observer(MedialistHeading)
