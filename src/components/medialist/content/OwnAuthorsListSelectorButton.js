import { t } from '@lingui/core/macro'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const OwnAuthorsListSelectorButton = ({ onDelete, selected }) => {
  return (
    <MntrButton
      isChip
      icon="contacts"
      label={t`My authors`}
      bg="activeFilter"
      href="/authors?is_own_author=true"
      selected={selected}
      onDelete={onDelete}
    />
  )
}

export default OwnAuthorsListSelectorButton
