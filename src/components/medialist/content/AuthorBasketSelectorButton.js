import { t } from '@lingui/core/macro'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import AuthorBasketsMenu from './AuthorBasketsMenu'

const AuthorBasketSelectorButton = ({
  icon = 'list_alt',
  iconBg = 'primary',
  label = t`Author Lists`,
  onDelete,
}) => {
  return (
    <MntrButton
      isChip
      bg="activeFilter"
      label={label}
      popupWidth={320}
      icon={icon}
      iconBg={iconBg}
      onDelete={onDelete}
      popupPlacement="bottom-start"
      popup={(closePopup) => {
        return <AuthorBasketsMenu closeParentPopup={closePopup} />
      }}
    />
  )
}

export default AuthorBasketSelectorButton
