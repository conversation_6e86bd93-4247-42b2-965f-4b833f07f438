import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Heading, Text } from '~/components/misc/Mntr'

const Wrapper = styled(Box)`
  text-align: center;
`
const MedialistAuthorCreate = () => {
  return (
    <Wrapper mt={[0, 0, 0, '100px', '150px']} p={1}>
      <Icon size={80} color="primary">
        list_alt
      </Icon>
      <Heading my={3} fontSize={4}>
        <Trans>Activity Overview</Trans>
      </Heading>
      <Text mb={1}>
        <strong>
          <Trans>Here you will see all the activity related to this author.</Trans>
        </strong>
      </Text>
      <Text>
        <Trans>
          Eg. sent press releases, profile edits, published articles related to your press releases.
        </Trans>
      </Text>
    </Wrapper>
  )
}

export default MedialistAuthorCreate
