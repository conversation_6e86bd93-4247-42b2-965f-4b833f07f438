import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import FormEditAuthor from '~/components/medialist/forms/FormEditAuthor'
import { Overlay, StyledContent, Wrapper } from '~/components/medialist/styles/StyledContentAuthor'
import { Flex } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import isEmpty from '~/helpers/isEmpty'
import { observer } from '~/helpers/mst'

/**
 * @param {String} activeTab - active Tabs.Item
 * @param {Number} authorId - author id from url
 * @param {Boolean} isCreate - is create new author
 * @param {React.Component} contentComponent - content component
 */
const MedialistAuthorDetail = ({
  appStore: {
    account,
    authors: { updateAuthor, authorDetail },
    loader,
  },
  activeTab,
  authorId,
  contentComponent,
  mobilePreferSidebar,
  isCreate,
}) => {
  const router = useRouter()
  const [readOnly, setReadOnly] = useState(isCreate ? false : true)
  const isLoading = loader.isLoading('authors-detail')
  const canEdit = account.workspace?.permissions.authors_database.can_write

  const onSubmit = (model) => {
    return updateAuthor(model)
  }

  useEffect(() => {
    const hash = router.asPath.split('#')[1]

    if (hash === 'edit' && canEdit) {
      setReadOnly(false)
    }
  }, [router, canEdit])

  return (
    <Wrapper>
      {isLoading && isEmpty(authorDetail) && (
        <Flex center m={3}>
          <MntrCircularProgress size={26} />
        </Flex>
      )}
      {!isLoading && !isEmpty(authorDetail) && (
        <>
          {/* Header + Sidebar Form */}
          <FormEditAuthor
            isCreate={isCreate}
            onSubmit={onSubmit}
            initialValues={authorDetail}
            readOnly={readOnly}
            setReadOnly={setReadOnly}
            activeTab={activeTab}
            authorId={authorId}
            mobilePreferSidebar={mobilePreferSidebar}
          />
          <StyledContent disabled={!readOnly && !isCreate} hide={mobilePreferSidebar}>
            {/* Tablet + Desktop - Tab Navigation */}
            {/* <TabsWrapper disabled={isCreate}>
              <Tabs>
                {tabNavigation(authorId, activeTab).map((item, index) => {
                  return (
                    <Tabs.Item
                      key={index.toString()}
                      label={item.label}
                      href={item.href}
                      active={item.active}
                      icon={item.icon}
                    />
                  )
                })}
              </Tabs>
            </TabsWrapper> */}

            {/* Tab Content */}
            <Overlay disabled={!readOnly} />
            {contentComponent}
          </StyledContent>
        </>
      )}
    </Wrapper>
  )
}

export default observer(MedialistAuthorDetail)
