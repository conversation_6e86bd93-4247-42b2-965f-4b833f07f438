import { Trans } from '@lingui/react/macro'
import color from 'color'
import sortBy from 'lodash/sortBy'
import FormNewBasket from '~/components/forms/baskets/FormNewBasket'
import BasketIcon from '~/components/layout/Sidebar/modules/SidebarBaskets/BasketIcon'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'

const withAddToBasketPopup = ({
  baskets,
  addToBasket,
  errors,
  setErrors,
  closeParentPopup,
  canCreateBasket,
}) => {
  const selectedBasket = baskets.list.find((item) => item.id === baskets.selected?.id)

  return {
    onClose: () => setErrors(undefined),
    popup: (closePopup) => {
      const addBasketToMenu = (item) => {
        return {
          id: item.id,
          label: item.label,
          leftIcon: <BasketIcon label={item.label} bg={color(item.color).darken(0.3).toString()} />,
          activeBackground: color(item.color).alpha(0.4).toString(),
          selected: selectedBasket?.id === item.id,
          hoverVariant: 'light',
          onClick: () => {
            addToBasket(item)

            closeParentPopup?.()
            closePopup?.()
          },
        }
      }

      // move basket to top of the list if selected
      const list = selectedBasket
        ? sortBy(baskets.list, ({ id }) => (id === selectedBasket.id ? 0 : 1)).map(addBasketToMenu)
        : baskets.list.map(addBasketToMenu)
      const menuItems = [{ label: <Trans>Add to list</Trans> }, ...list]

      return (
        <ListScrollWrapper>
          {menuItems.length > 1 && <MntrMenu menuItems={menuItems} />}
          {canCreateBasket && (
            <Box mb={-1} mt={-2} width="320px">
              <FormNewBasket
                title={<Trans>Create and add to new list</Trans>}
                onSubmit={(model) => {
                  baskets.create(model).then((res) => {
                    if (!res.valid) {
                      return setErrors({ label: res.errors?.label })
                    }

                    addToBasket(res.author_basket)
                    closePopup?.()
                  })
                }}
                errors={errors}
              />
            </Box>
          )}
        </ListScrollWrapper>
      )
    },
  }
}

export default withAddToBasketPopup
