import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Flex } from '~/components/misc/Mntr'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import { observer } from '~/helpers/mst'

const IconWrapper = styled(Flex)`
  align-items: center;
  width: 25px;
  height: 25px;
  justify-content: center;
  cursor: default;
`

const AuthorContactInformation = ({
  values = [],
  appStore: {
    account: {
      enums: {
        authors: { contact_information },
      },
    },
  },
}) => {
  const contacts = contact_information
    .filter((item) => !item.is_social)
    .map((item) => ({
      ...item,
      size: 18,
      iconColor: values.includes(item.id) ? 'icon' : 'veryLightGrey',
    }))

  // socials zlucene do jednej ikonky podla noveho navrhu - prehodit logiku na BE niekedy ?
  const hasSocial = !!contact_information.find((item) => values.includes(item.id) && item.is_social)
  contacts.push({
    size: 19,
    text: t`Social profiles`,
    icon: 'recent_actors',
    iconColor: hasSocial ? 'icon' : 'veryLightGrey',
  })

  return (
    <Flex centerY width={1}>
      {contacts?.map(({ text, icon, iconColor, size }, key) => {
        return (
          <StyledTooltip key={key} tooltip={text}>
            <IconWrapper mr={'4px'} key={key}>
              <Icon size={size} color={iconColor}>
                {icon}
              </Icon>
            </IconWrapper>
          </StyledTooltip>
        )
      })}
    </Flex>
  )
}

export default observer(AuthorContactInformation)
