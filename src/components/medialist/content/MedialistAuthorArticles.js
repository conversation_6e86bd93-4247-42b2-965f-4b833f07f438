import { css, styled } from 'styled-components'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import AuthorChart from '~/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart'
import Feed from '~/components/medialist/content/MedialistInspector/Feed/Feed'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import staticFeeds from '~/constants/staticFeeds'
import {
  getConfigMedialistInspector,
  getConfigMedialistInspectorWithoutPermissionsMonitoringCanRead,
} from '~/helpers/getActiveFiltersConfig'
import { observer } from '~/helpers/mst'
import FeedMedialistPromo from './FeedMedialist/FeedMedialistPromo'

const FiltersWrapper = styled(Box)`
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
  left: 0px;
  overflow-y: scroll;

  ${({ isTablet }) =>
    isTablet &&
    css`
      margin-left: 18px;
    `}
`

const InnerBoxWrapper = styled(Box)`
  margin-right: ${({ isTablet }) => (isTablet ? 26 : 10)}px;
`

const MedialistAuthorArticles = ({
  appStore: {
    account,
    loader,
    monitoring: { feedMap },
    viewport: { isTablet },
  },
  authorId,
}) => {
  const feedId = staticFeeds.AUTHOR_FEED
  const feed = feedMap.get(feedId)
  const isLoading = loader.isLoading('authors-detail')
  const customRoute = window.location.pathname

  return (
    <>
      {isLoading && <MntrCircularProgress size={24} color="rgba(0,0,0,.87)" />}
      {!isLoading && (
        <FiltersWrapper width={1} isTablet={isTablet}>
          <InnerBoxWrapper isTablet={isTablet}>
            <Flex flexWrap="nowrap" flexDirection="column">
              <Flex flexDirection="column" gap={2} mt="15px">
                {/* Filters */}
                <MntrFiltersBar
                  filters={[
                    'Date',
                    'Source',
                    'LanguageMultiselect',
                    'CountryMultiselect',
                    'Sentiment',
                    'Notes',
                    'ArticleType',
                  ]}
                  feedId={feedId}
                  customRoute={customRoute}
                />

                {/* Active filters */}
                <MntrActiveFilters
                  config={
                    account.workspace?.permissions.monitoring.can_read
                      ? getConfigMedialistInspector(customRoute, feedId)
                      : getConfigMedialistInspectorWithoutPermissionsMonitoringCanRead(
                          customRoute,
                          feedId,
                        )
                  }
                />

                {/* Author chart */}
                <AuthorChart feedId={feedId} authorId={authorId} />

                {/* Banner - permisssions.monitoring request */}
                {!account.workspace?.permissions.monitoring.can_read && <FeedMedialistPromo />}
              </Flex>
            </Flex>

            {feed && (
              <Feed
                feedId={feedId}
                feedMapItem={feed}
                withView={account.workspace?.permissions.monitoring.can_read}
                withSelectActions={account.workspace?.permissions.monitoring.can_read}
              />
            )}
          </InnerBoxWrapper>
        </FiltersWrapper>
      )}
    </>
  )
}

export default observer(MedialistAuthorArticles)
