import { Trans } from '@lingui/react/macro'
import { styled } from 'styled-components'
import { Flex, Text } from '~/components/misc/Mntr'
import StyledTooltip from '~/components/misc/MntrButton/style/StyledTooltip'
import { getAuthorJobs, getAuthorJobsText } from '~/helpers/getAuthorJobs'
import { observer } from '~/helpers/mst'

const AuthorInfo = styled(Flex)`
  align-items: center;
  font-weight: 100;
  flex-shrink: 0;
`
const AuthorType = styled.div`
  text-transform: capitalize;
`

const JobsText = styled.div`
  overflow: hidden;
  max-height: 20px;
  text-overflow: ellipsis;
  display: inline-block;
  white-space: nowrap;
`

const Wrapper = styled(Flex)`
  align-content: center;
  align-items: center;
  font-size: ${({ fontSize }) => fontSize}px;
  opacity: ${({ opacity }) => opacity};
  color: currentColor;
  flex: 0 0 auto;
  flex-grow: 1;
`
const AuthorInfoDetail = ({
  appStore: {
    account: { enums },
  },
  author,
  fontSize = 13,
  opacity = 1,
}) => {
  const type = enums.authors.author_type.find((authorType) => authorType.id === author.author_type)
  const authorType = type ? type.text : ''
  const jobs = getAuthorJobs(author)
  const jobsText = getAuthorJobsText(jobs)
  const jobsTooltip = jobs.join(', ')

  return (
    <Wrapper alignContent="center" centerY fontSize={fontSize} opacity={opacity}>
      <AuthorType>{authorType}</AuthorType>
      {jobs.length > 0 && (
        <AuthorInfo>
          <Text mx={'4px'} fontSize={fontSize}>
            &#x2022;
          </Text>
          {jobs.length > 1 ? (
            <StyledTooltip tooltip={jobsTooltip}>
              <JobsText color="inherit">{jobsText}</JobsText>
            </StyledTooltip>
          ) : (
            <JobsText color="inherit">{jobsText}</JobsText>
          )}
        </AuthorInfo>
      )}
      {author.is_own_author && (
        <AuthorInfo>
          <Text mx="4px" fontSize={fontSize}>
            &#x2022;
          </Text>

          <Text color="inherit">
            <Trans>My author</Trans>
          </Text>
        </AuthorInfo>
      )}
    </Wrapper>
  )
}

export default observer(AuthorInfoDetail)
