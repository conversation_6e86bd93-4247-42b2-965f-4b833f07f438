import color from 'color'
import { useEffect } from 'react'
import { css, styled } from 'styled-components'
import PageContent from '~/components/Content/Content'
import MntrActiveFilters from '~/components/layout/MntrActiveFilters/MntrActiveFilters'
import MntrFiltersBar from '~/components/layout/MntrFiltersBar/MntrFiltersBar'
import BasketIcon from '~/components/layout/Sidebar/modules/SidebarBaskets/BasketIcon'
import AuthorBasketSelectorButton from '~/components/medialist/content/AuthorBasketSelectorButton'
import FeedMedialist from '~/components/medialist/content/FeedMedialist/FeedMedialist'
import FeedMedialistEmpty from '~/components/medialist/content/FeedMedialist/FeedMedialistEmpty/FeedMedialistEmpty'
import MedialistActionsBar from '~/components/medialist/content/MedialistActionsBar/MedialistActionsBar'
import OwnAuthorsListSelectorButton from '~/components/medialist/content/OwnAuthorsListSelectorButton'
import ActionsBarSticky from '~/components/misc/ActionsBar/Sticky/Sticky'
import Loader from '~/components/misc/Loader/Loader'
import { Box, Flex } from '~/components/misc/Mntr'
import FeedListItemDrawer from '~/components/monitoring/FeedList/FeedListItemDrawer'
import events from '~/constants/gtm'
import { getConfigMedialist } from '~/helpers/getActiveFiltersConfig'
import { pushEvent } from '~/helpers/gtm'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'
import MedialistHeading from './MedialistHeading'
import MedialistStats from './MedialistStats'

const ActiveFiltersWrapper = styled(Flex)`
  ${({ isTablet }) => {
    return (
      isTablet &&
      css`
        overflow-x: scroll;
        overflow-y: hidden;
      `
    )
  }}
`

const MedialistContent = ({
  appStore: {
    authors: {
      medialistSelector: selector,
      isEmpty,
      isLoading,
      baskets,
      medialist: { list },
      canLoadMore,
      loadMoreAuthors,
      isLoadingMore,
      authorStats,
      tags,
    },
    filter,
    viewport: { isTablet },
  },
  isBasket,
  isOwnAuthorsList,
  disableFilters = isBasket,
}) => {
  const actions = ['add_tag', 'remove_tag', 'add_to_basket', 'remove_from_basket']
  const filters = [
    'MedialistArticles',
    'AuthorTitle',
    'AuthorType',
    'AuthorFocusAreas',
    'AuthorActivity',
    'Source',
    'ContactInformation',
    'Country',
  ]
  const selectedBasket = baskets.list?.find((item) => item.id === baskets.selected?.id)

  useEffect(() => {
    if (!isLoading) {
      const { labels } = filter
      const authorsCount = authorStats
        ? authorStats.find((item) => item.label === 'Authors')?.count
        : undefined
      pushEvent(events.MEDIALIST_LOADED, {
        authors_count: authorsCount,
        author_list_count: baskets.list.length,
        tags_count: tags.list.length,
        medialist_filtered: !!baskets.selected,
        articles_filtered: labels ? !!labels.ma__topic_monitors : undefined,
        type_filtered: labels ? !!labels.author_type : undefined,
        focus_area_filtered: labels ? !!labels.author_focus_areas : undefined,
        contact_filtered: labels ? !!labels.contact_information : undefined,
        job_position_filtered: labels ? !!labels.title_query : undefined,
        source_filtered: labels ? !!labels.category_type : undefined,
        country_filtered: labels ? !!labels.country : undefined,
        filter_string: labels ? JSON.stringify(labels) : undefined,
      })
    }
  }, [filter, authorStats, tags, baskets, isLoading])

  return (
    <>
      <PageContent>
        {!disableFilters && (
          <>
            <MntrFiltersBar filters={filters} />
            {isTablet ? (
              <ActiveFiltersWrapper width={1} isTablet={isTablet}>
                {isOwnAuthorsList && !isLoading && (
                  <Box ml={1}>
                    <OwnAuthorsListSelectorButton
                      selected
                      onDelete={(e) => {
                        e.preventDefault()
                        routerPush('/authors')
                      }}
                    />
                  </Box>
                )}
                {filter.isEmpty.isEmptyFilter && !isOwnAuthorsList && (
                  <Box ml={1}>
                    <AuthorBasketSelectorButton />
                  </Box>
                )}

                <MntrActiveFilters config={getConfigMedialist()} />
              </ActiveFiltersWrapper>
            ) : (
              <MntrActiveFilters config={getConfigMedialist()} />
            )}
          </>
        )}

        {isBasket && isTablet && !isLoading && (
          <Box mt="15px" ml={disableFilters ? 1 : '8px'}>
            <AuthorBasketSelectorButton
              {...(selectedBasket && {
                label: selectedBasket.label,
                icon: (
                  <Box style={{ position: 'absolute', bottom: 0, right: 0 }}>
                    <BasketIcon
                      label={selectedBasket.label}
                      bg={color(selectedBasket.color).darken(0.3).toString()}
                      size={26}
                      fontSize={12}
                    />
                  </Box>
                ),
                iconBg: 'transparent',
                onDelete: () => {
                  routerPush('/authors')
                },
              })}
            />
          </Box>
        )}
        {/* TODO: MedialistHeading is basically loading message.
            No results found message is already handled by FeedMedialistEmpty bellow
        */}
        {isLoading ? <MedialistHeading /> : <MedialistStats />}
        <Box>
          {!isEmpty && !isLoading && (
            <ActionsBarSticky>
              <MedialistActionsBar actions={actions} />
            </ActionsBarSticky>
          )}
          {!isEmpty && !isLoading && (
            <FeedMedialist
              list={list}
              canLoadMore={canLoadMore}
              selector={selector}
              loadMore={loadMoreAuthors}
              isLoading={isLoading}
              isLoadingMore={isLoadingMore}
              dnd={isBasket}
            />
          )}
          {!isLoading && isEmpty && <FeedMedialistEmpty />}
          {isLoading && <Loader />}
        </Box>
        <FeedListItemDrawer />
      </PageContent>
    </>
  )
}

export default observer(MedialistContent)
