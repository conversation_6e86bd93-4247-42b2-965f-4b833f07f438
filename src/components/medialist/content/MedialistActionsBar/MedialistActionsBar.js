import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { useRouter } from 'next/router'
import { useState } from 'react'
import MedialistExportCounter from '~/components/medialist/content/MedialistActionsBar/MedialistExportCounter/MedialistExportCounter'
import withAddToBasketPopup from '~/components/medialist/content/withAddToBasketPopup'
import withRemoveFromBasketPopup from '~/components/medialist/content/withRemoveFromBasketPopup'
import ActionsBar from '~/components/misc/ActionsBar/ActionsBar'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withAddTagPopup from '~/components/monitoring/FeedActionsBar/withAddTagPopup/withAddTagPopup'
import withRemoveTagPopup from '~/components/monitoring/FeedActionsBar/withRemoveTagPopup/withRemoveTagPopup'
import filterOrShow from '~/helpers/filterOrShow'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'
import withModalUploadMedialist from './withModalUploadMedialist'

const MedialistActionsBar = ({
  actions,
  appStore: {
    account,
    authors: {
      medialistSelector,
      exportMedialist,
      addTagToSelected,
      removeTagFromSelected,
      addSelectedToBasket,
      removeSelectedFromBasket,
      tags,
      baskets,
    },
    viewport: { isMobile, width },
    router: { makeRouteWithQuery },
  },
}) => {
  const {
    pathname,
    query: { has_email, ...query },
  } = useRouter()

  const [errorsTags, setErrorsTags] = useState(undefined)
  const [errorsBaskets, setErrorsBaskets] = useState(undefined)
  const { isEmpty } = medialistSelector
  const listActions = []
  const ACTIONS = []
  const isSmallView = width < 750

  const addTag = (closePopup) => {
    const text = t`Assign tag`
    const icon = 'new_label'

    return {
      key: 'add_tag',
      bg: 'flat',
      ...(closePopup // is MenuItem
        ? { leftIcon: icon, label: text }
        : {
            icon,
            tooltip: text,
          }),
      ...withAddTagPopup({
        tags,
        setTag: (tag) => {
          addTagToSelected(tag)
          closePopup?.()
        },
        permissions: account.workspace?.permissions,
        errors: errorsTags,
        setErrors: setErrorsTags,
        canCreateTag: account.workspace?.permissions.authors_database.can_write,
      }),
    }
  }

  const removeTag = (closePopup) => {
    const text = t`Remove tag`
    const icon = 'label_off'

    return {
      key: 'remove_tag',
      bg: 'flatError',
      ...(closePopup
        ? { leftIcon: icon, label: text }
        : {
            icon,
            tooltip: text,
          }),
      ...withRemoveTagPopup({
        tags,
        removeTag: (tag) => {
          removeTagFromSelected(tag)
          closePopup?.()
        },
      }),
    }
  }

  const addToBasket = (closePopup) => {
    const text = t`Add authors to list`
    const icon = 'playlist_add'

    return {
      key: 'add_to_basket',
      bg: 'flat',
      ...(closePopup
        ? { leftIcon: icon, label: text }
        : {
            icon,
            tooltip: text,
          }),
      ...withAddToBasketPopup({
        baskets,
        addToBasket: (basket) => {
          addSelectedToBasket(basket)
          closePopup?.()
        },
        errors: errorsBaskets,
        setErrors: setErrorsBaskets,
        canCreateBasket: account.workspace?.permissions.authors_database.can_write,
      }),
    }
  }

  const removeFromBasket = (closePopup) => {
    const text = t`Remove authors from list`
    const icon = 'playlist_remove'

    return {
      key: 'remove_from_basket',
      bg: 'flatError',
      ...(closePopup
        ? { leftIcon: icon, label: text }
        : {
            icon,
            tooltip: text,
          }),
      ...withRemoveFromBasketPopup({
        baskets,
        removeFromBasket: (basket) => {
          removeSelectedFromBasket(basket)
          closePopup?.()
        },
      }),
    }
  }

  const actionsList = (closePopup) => [
    {
      ...addTag(closePopup),
    },
    {
      ...removeTag(closePopup),
    },
    {
      ...addToBasket(closePopup),
    },
    {
      ...removeFromBasket(closePopup),
    },
  ]

  if (!isSmallView) {
    ACTIONS.push(...actionsList())
  } else {
    ACTIONS.push({
      bg: 'flat',
      icon: 'more_vert',
      popup: (closePopup) => {
        return <MntrMenu menuItems={actionsList(closePopup)} closePopup={closePopup} />
      },
    })
  }

  if (!isEmpty && account.workspace?.permissions.authors_database.can_write) {
    ACTIONS.forEach((action) => {
      if (filterOrShow(actions, action.key)) {
        listActions.push(action)
      }
    })
  }

  // If user has no permissions to export authors, show request modal
  let authorsExportRequestId
  if (!account.workspace?.permissions.authors_export.can_write) {
    authorsExportRequestId = 'MEDIALIST-EXPORT'
  }

  return (
    <>
      <ActionsBar
        groups={{
          l: [
            <Flex position="relative" key="actions-bar-selection-actions">
              {filterOrShow(actions, 'with_contact') && (
                <Box mr={1}>
                  <MntrButton
                    href={{
                      pathname,
                      query: { ...query, ...(has_email ? {} : { has_email: 1 }) },
                    }}
                    icon={has_email ? 'check_box' : 'check_box_outline_blank'}
                    label={<Trans>With contact</Trans>}
                  />
                </Box>
              )}
              {listActions.map((item, key) => {
                return (
                  <Box mr={1} key={key}>
                    <MntrButton {...item} />
                  </Box>
                )
              })}
              {!isEmpty && (
                <Flex mr={['-10px', '-7px']} centerY key="actions-bar-medialist-export-group">
                  <>
                    <MntrButton
                      bg="secondary"
                      label={<Trans>Export</Trans>}
                      onClick={exportMedialist}
                      rounded
                      requestId={authorsExportRequestId}
                    />
                    {!isMobile && (
                      <Box ml={2}>
                        <MedialistExportCounter />
                      </Box>
                    )}
                  </>
                </Flex>
              )}
            </Flex>,
          ],
          r: [
            <Flex key="actions">
              {account.workspace?.permissions.authors_database.can_write && (
                <MntrButton
                  bg="flat"
                  icon="upload"
                  {...(isMobile && { tooltip: t`Upload medialist` })}
                  {...(!isMobile && { label: t`Upload medialist` })}
                  {...withModalUploadMedialist()}
                />
              )}
              {account.workspace?.permissions.authors_database.can_write && (
                <MntrButton
                  bg="flat"
                  icon="person_add"
                  {...(isMobile && { tooltip: t`Create author` })}
                  {...(!isMobile && { label: t`Create author` })}
                  href="/authors/create"
                  onClick={() => {
                    const currRoute = makeRouteWithQuery()

                    routerReplace(currRoute, currRoute, { shallow: true })
                    routerPush(currRoute, '/authors/create', { shallow: true })
                  }}
                />
              )}
            </Flex>,
          ],
        }}
        selector={medialistSelector}
        withSort={false}
        withView={false}
      />
    </>
  )
}

export default observer(MedialistActionsBar)
