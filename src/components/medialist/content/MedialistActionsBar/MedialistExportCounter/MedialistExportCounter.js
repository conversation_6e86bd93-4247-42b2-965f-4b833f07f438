import { styled } from 'styled-components'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import MntrLinearProgress from '~/components/misc/MntrProgress/MntrLinearProgress'
import { observer } from '~/helpers/mst'

const StyledLimitCount = styled(Box)`
  font-size: 13px;
  color: ${({ color }) => color};
`

const StyledLimitLabel = styled(Box)`
  font-size: 13px;
  flex-grow: 1;
`

const MedialistExportCounter = ({ appStore: { account } }) => {
  const limit = account.workspace?.limits.authors_export_limit
  const limitUsed = account.workspace?.limits.authors_export_limit_used || 0

  let progressColor = '#32b67a'
  if (limit <= limitUsed) {
    progressColor = '#e54b4b'
  }

  if (limit === 0) {
    return <span />
  }

  return (
    <Box width="150px">
      <Flex flexWrap="nowrap">
        <StyledLimitLabel color="black" mb={1}>
          Limit
        </StyledLimitLabel>
        <StyledLimitCount color={progressColor}>
          {limitUsed}/{limit}
        </StyledLimitCount>
      </Flex>
      <div>
        <Text fontSize="14px">
          <MntrLinearProgress
            background={'#eae4b6'}
            variant="determinate"
            color={progressColor}
            min={0}
            max={limit}
            value={limitUsed}
          />
        </Text>
      </div>
    </Box>
  )
}

export default observer(MedialistExportCounter)
