import { t } from '@lingui/core/macro'
import Icon from '~/components/misc/Icon/Icon'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import { IBaseEnumsArrItem } from '~/store/models/account/enums/BaseEnumsArrItem'
import { IAuthorBasketDefinitionsStoreArrItem } from '~/store/models/authors/Baskets/AuthorBasketDefinitionsStoreArrItem'
import ContactsImportTitle from './ContactsImportTitle'
import MedialistUploadComponent from './MedialistUploadComponent'

export const NO_BASKET_ENTRY_VALUE = -1

interface IFormImportContactsProps {
  onSubmit: (model: object) => void
  prevStep: () => void
  importActions: IBaseEnumsArrItem[]
  baskets: IAuthorBasketDefinitionsStoreArrItem[]
}

const FormImportContacts = ({
  onSubmit,
  importActions,
  baskets = [],
  prevStep,
}: IFormImportContactsProps) => {
  const schema: IFormSchemaItem[] = [
    {
      customComponent: () => {
        return <ContactsImportTitle />
      },
    },
    {
      name: 'author_basket',
      adapter: 'select',
      // @ts-expect-error: select items
      items: [{ value: NO_BASKET_ENTRY_VALUE, label: '-' }].concat(
        baskets.map(({ id, label }) => {
          return {
            value: id,
            label,
          }
        }),
      ),
      label: t`Import to`,
    },
    {
      name: 'import_action',
      adapter: 'select',
      // @ts-expect-error: select items
      items: importActions.map(({ id, text }) => {
        return {
          value: id,
          label: text,
        }
      }),
      label: t`Import options`,
    },
    {
      name: 'skip_error_rows',
      adapter: 'checkbox',
      type: 'checkbox',
      label: t`Skip error lines`,
    },
    {
      customComponent: ({ form }) => {
        return <MedialistUploadComponent form={form} />
      },
    },
    {
      actionsLeft: () => [
        {
          icon: 'arrow_back_ios',
          bg: 'flat',
          rounded: true,
          label: t`Back`,
          onClick: () => prevStep(),
        },
      ],
    },
    {
      actions: ({ values, submitting }) => [
        {
          bg: 'secondary',
          endIconElement: <Icon>group</Icon>,
          rounded: true,
          label: t`Import contacts`,
          type: 'submit',
          disabled: submitting || !values.payload,
        },
      ],
    },
  ]

  return (
    <MntrForm
      contentPadding={4}
      initialValues={{ import_action: importActions[0].id, author_basket: NO_BASKET_ENTRY_VALUE }}
      schema={schema}
      onSubmit={onSubmit}
    />
  )
}

export default FormImportContacts
