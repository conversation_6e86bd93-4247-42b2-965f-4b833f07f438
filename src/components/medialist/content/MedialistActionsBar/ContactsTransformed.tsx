import { t } from '@lingui/core/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { ButtonGroup, Flex, Heading, Text } from '~/components/misc/Mntr'
import MntrMenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'

interface IContactsTransformedProps {
  file?: File
  nextStep: () => void
  prevStep: () => void
}

const ContactsTransformed = ({ file, nextStep, prevStep }: IContactsTransformedProps) => {
  return (
    <Flex column p={4}>
      <Flex column gap={4}>
        <Heading as="h4">{t`Transform contact list`}</Heading>
        <Text>{t`Formatting is finished successfully. The prepared file is downloaded automatically. Edit it manually if needed and upload it to the medialist in the next step.`}</Text>
      </Flex>
      <MntrPaper p={2} mt={2}>
        {/* @ts-expect-error: ignore */}
        <MntrMenuItem
          // @ts-expect-error: ignore
          label={file.normalized_medialist_xlsx_name}
          leftIcon="file_present"
          keepButtonGroupVisible={true}
          buttonGroup={[
            {
              bg: 'flat',
              tooltip: t`Download`,
              icon: 'download',
              onClick: () => {
                // @ts-expect-error: ignore
                window.location = file.normalized_medialist_xlsx_url
              },
            },
          ]}
          clickable
        />
      </MntrPaper>

      <Flex column alignItems="end" pt={3}>
        <ButtonGroup
          buttonsLeft={[
            {
              icon: 'arrow_back_ios',
              bg: 'flat',
              rounded: true,
              label: t`Back`,
              onClick: () => prevStep(),
            },
          ]}
          buttons={[
            {
              bg: 'secondary',
              endIconElement: <Icon>chevron_right</Icon>,
              rounded: true,
              label: t`Continue to import`,
              onClick: () => nextStep(),
            },
          ]}
        />
      </Flex>
    </Flex>
  )
}

export default ContactsTransformed
