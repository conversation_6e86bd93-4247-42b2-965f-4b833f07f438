import { t } from '@lingui/core/macro'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { ButtonGroup, Flex, Heading } from '~/components/misc/Mntr'
import MntrHint from '~/components/misc/MntrHint/MntrHint'
import { Table, TableBody, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import { IError } from './WizardImportMedialist'

interface IErrorPageProps {
  error: IError
}

interface DataItem {
  row: string
  errors: { [field: string]: { [index: string]: string[] } }
}

interface ErrorEntry {
  field: string
  index: string
  messages: string[]
}

const transformData = (data: DataItem[]) => {
  return data.map((item) => {
    const errorsArray: ErrorEntry[] = []
    for (const field in item.errors) {
      for (const index in item.errors[field]) {
        errorsArray.push({
          field,
          index,
          messages: item.errors[field][index],
        })
      }
    }
    return { ...item, errors: errorsArray }
  })
}

const ErrorPage = ({ error: { heading, title, reason, returnBack } }: IErrorPageProps) => {
  const reasonText = Array.isArray(reason) ? transformData(reason) : reason

  return (
    <ListScrollWrapper maxWidth="1000">
      <Flex column p={4}>
        <Flex column gap={4}>
          <Heading as="h4">{heading}</Heading>
          <MntrHint
            icon="error"
            color="error"
            outerBorderColor="error"
            borderColor="error"
            // @ts-expect-error: tsx
            heading={title}
            text={
              Array.isArray(reasonText) ? (
                <Table>
                  <TableBody>
                    <Tr>
                      <Th style={{ width: '10%' }}>{t`Row`}</Th>
                      <Th style={{ width: '10%' }}>{t`Column`}</Th>
                      <Th>{t`Error detail`}</Th>
                    </Tr>
                    {reasonText.map((item, index) => {
                      return (
                        <>
                          {item.errors.map((error, i) => {
                            return (
                              <Tr key={`${index}-${i}`}>
                                {i === 0 && (
                                  <Td width={30} rowSpan={item.errors.length}>
                                    {item.row}
                                  </Td>
                                )}
                                <Td>{error.field}</Td>
                                <Td>{error.messages}</Td>
                              </Tr>
                            )
                          })}
                        </>
                      )
                    })}
                  </TableBody>
                </Table>
              ) : (
                reasonText
              )
            }
          />
        </Flex>
        <Flex column alignItems="end" pt={3}>
          <ButtonGroup
            buttonsLeft={[
              {
                icon: 'chevron_left',
                bg: 'flat',
                rounded: true,
                label: t`Back`,
                onClick: () => returnBack(),
              },
            ]}
            buttons={[]}
          />
        </Flex>
      </Flex>
    </ListScrollWrapper>
  )
}

export default ErrorPage
