import { t } from '@lingui/core/macro'
import { FormApi } from 'final-form'
import identityFn from 'lodash/identity'
import { useState } from 'react'
import { Field } from 'react-final-form'
import MntrFileAdapter from '~/components/forms/adapters/MntrFileAdapter/MntrFileAdapter'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrMenuItem from '~/components/misc/MntrMenu/modules/MntrMenuItem'
import withModalRemove from '~/helpers/modal/withModalRemove'
import prettyBytes from '~/helpers/prettyBytes'

interface IMedialistUploadComponentProps {
  form?: FormApi
  fileData?: File
}

const MedialistUploadComponent = ({ form, fileData }: IMedialistUploadComponentProps) => {
  const [file, setFile] = useState<File | undefined>(fileData)

  return (
    <>
      {file && (
        <MntrPaper p={2}>
          {/* @ts-expect-error: ignore */}
          <MntrMenuItem
            label={file.name}
            secondaryText={prettyBytes(file.size)}
            leftIcon="file_present"
            keepButtonGroupVisible={true}
            buttonGroup={[
              {
                bg: 'flat',
                tooltip: t`Delete`,
                icon: 'close',
                ...withModalRemove({
                  isStackedModal: true,
                  title: t`Delete file`,
                  onSubmit: () => {
                    form?.change('payload', undefined)
                    setFile(undefined)
                  },
                  message: t`Are you sure you want to delete ${file.name}?`,
                }),
              },
            ]}
            clickable
          />
        </MntrPaper>
      )}
      {!file && (
        <Field
          name="file"
          renderLabel
          parse={identityFn}
          //@ts-expect-error: ignore
          component={MntrFileAdapter}
          placeholderIcon="upload"
          placeholder={t`Upload your file`}
          placeholderExtensions={t`Recommended file types: XLSX, CSV`}
          accept={['.xlsx', '.csv']}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
            if (!event.target?.value) {
              return form?.change('payload', undefined)
            }

            form?.change('payload', event.target.files?.[0])
            setFile(event.target.files?.[0] ?? undefined)
          }}
        />
      )}
    </>
  )
}
export default MedialistUploadComponent
