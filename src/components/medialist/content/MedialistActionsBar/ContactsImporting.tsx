import { t } from '@lingui/core/macro'
import { ButtonGroup, Flex, Heading } from '~/components/misc/Mntr'
import MntrHint from '~/components/misc/MntrHint/MntrHint'

interface IContactsImportingProps {
  prevStep: () => void
}

const ContactsImporting = ({ prevStep }: IContactsImportingProps) => {
  return (
    <Flex column p={4}>
      <Flex column gap={4}>
        <Heading as="h4">{t`Import contacts`}</Heading>
        <MntrHint
          icon="info"
          color="info"
          borderColor="info"
          outerBorderColor="info"
          heading={t`Contacts import in progress`}
          text={t`You can safely close this window as the process will continue in the background. Once the import is complete, we will notify you. If any issues occur, you will receive a notification and an email detailing the errors.`}
        />
      </Flex>

      <Flex column alignItems="end" pt={3}>
        <ButtonGroup
          buttonsLeft={[
            {
              icon: 'arrow_back_ios',
              bg: 'flat',
              rounded: true,
              label: t`Back`,
              onClick: () => prevStep(),
            },
          ]}
          buttons={[]}
        />
      </Flex>
    </Flex>
  )
}

export default ContactsImporting
