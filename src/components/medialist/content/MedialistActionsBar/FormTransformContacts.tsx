import { t } from '@lingui/core/macro'
import Icon from '~/components/misc/Icon/Icon'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import ContactsTransformTitle from './ContactsTransformTitle'
import MedialistUploadComponent from './MedialistUploadComponent'

interface IFormTransformContactsProps {
  onSubmit: (model: object) => void
  prevStep: () => void
}

const FormTransformContacts = ({ onSubmit, prevStep }: IFormTransformContactsProps) => {
  const schema: IFormSchemaItem[] = [
    {
      customComponent: () => {
        return <ContactsTransformTitle />
      },
    },
    {
      customComponent: ({ form }) => {
        return <MedialistUploadComponent form={form} />
      },
    },
    {
      actionsLeft: () => [
        {
          icon: 'arrow_back_ios',
          bg: 'flat',
          rounded: true,
          label: t`Back`,
          onClick: () => prevStep(),
        },
      ],
    },
    {
      actions: ({ values, submitting }) => [
        {
          bg: 'secondary',
          endIconElement: <Icon>rule_settings</Icon>,
          rounded: true,
          label: t`Transform`,
          type: 'submit',
          disabled: submitting || !values.payload,
        },
      ],
    },
  ]

  return <MntrForm contentPadding={4} schema={schema} onSubmit={onSubmit} />
}

export default FormTransformContacts
