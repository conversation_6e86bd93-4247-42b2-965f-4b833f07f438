import { Trans } from '@lingui/react/macro'
import { ReactNode, useState } from 'react'
import { Flex } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import Wizard from '~/components/misc/Wizard/Wizard'
import Wizard<PERSON><PERSON><PERSON> from '~/components/misc/Wizard/WizardChoice'
import { ObservedFC, observer } from '~/helpers/msts'
import ContactsImporting from './ContactsImporting'
import ContactsImportTitle from './ContactsImportTitle'
import ContactsTransformed from './ContactsTransformed'
import ContactsTransformTitle from './ContactsTransformTitle'
import ErrorPage from './ErrorPage'
import FormImportContacts from './FormImportContacts'
import FormTransformContacts from './FormTransformContacts'

const STEPS = {
  STEP_CHOICE: 0,
  STEP_TRANSFORM: 1,
  STEP_TRANSFORMED: 2,
  STEP_IMPORT: 3,
  STEP_IMPORTING: 4,
  STEP_ERROR: 5,
}

export interface IError {
  heading: ReactNode | string
  title: ReactNode | string
  reason: string
  returnBack: () => void
}

interface IData {
  file?: File
  error?: IError
}

const WizardImportMedialist: ObservedFC = ({
  appStore: {
    account: { enums },
    authors: { transformMedialist, importMedialist, baskets },
  },
}) => {
  const [data, setData] = useState<IData | undefined>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingImport, setIsLoadingImport] = useState(false)
  const { authors } = enums

  const choices = [
    {
      id: 0,
      isDefault: true,
      imgLabel: 'psychology',
      title: <Trans>Transform & import</Trans>,
      points: [
        {
          icon: 'approval_delegation',
          text: (
            <Trans>
              Upload your contact list, and we’ll format it to fit perfectly into our medialist for
              you.
            </Trans>
          ),
        },
      ],
    },
    {
      id: 1,
      isDefault: false,
      imgLabel: 'upload',
      title: <Trans>Import</Trans>,
      points: [
        {
          icon: 'approval_delegation',
          text: (
            <Trans>
              Import your already formatted contact list or manually completed template.
            </Trans>
          ),
        },
      ],
      action: {
        label: <Trans>Download template</Trans>,
        icon: 'download',
        bg: 'flat',
        onClick: () => {
          window.location = authors.medialist_import_template_url
        },
      },
    },
  ]

  return (
    <Wizard
      steps={({ jumpTo, nextStep, prevStep }) => {
        return [
          <>
            <WizardChoice
              onClick={(activeIndex) => {
                if (activeIndex === 0) {
                  jumpTo(STEPS.STEP_TRANSFORM)
                } else {
                  jumpTo(STEPS.STEP_IMPORT)
                }
              }}
              choices={choices}
              title={<Trans>Import your contacts to medialist</Trans>}
            />
          </>,
          <>
            {isLoading ? (
              <Flex p={4} column>
                <ContactsTransformTitle />
                <Flex height={'300px'} center>
                  <MntrCircularProgress size={40} />
                </Flex>
              </Flex>
            ) : (
              <FormTransformContacts
                prevStep={prevStep}
                onSubmit={(model) => {
                  setIsLoading(true)

                  transformMedialist(model)
                    // @ts-expect-error: res
                    .then((res) => {
                      setData({ file: res })
                      window.location = res.normalized_medialist_xlsx_url
                      nextStep()
                    })
                    // @ts-expect-error: e
                    .catch((e) => {
                      setData({
                        error: {
                          title: <Trans>Transformation failed</Trans>,
                          reason: e.file,
                          returnBack: () => jumpTo(STEPS.STEP_TRANSFORM),
                          heading: <Trans>Transform contact list</Trans>,
                        },
                      })
                      jumpTo(STEPS.STEP_ERROR)
                    })
                    .finally(() => {
                      setIsLoading(false)
                    })
                }}
              />
            )}
          </>,
          <>
            <ContactsTransformed
              file={data?.file}
              nextStep={() => jumpTo(STEPS.STEP_IMPORT)}
              prevStep={prevStep}
            />
          </>,
          <>
            {isLoadingImport ? (
              <Flex p={4} column>
                <ContactsImportTitle />
                <Flex height={'300px'} center>
                  <MntrCircularProgress size={40} />
                </Flex>
              </Flex>
            ) : (
              <FormImportContacts
                prevStep={() => jumpTo(STEPS.STEP_CHOICE)}
                onSubmit={(model) => {
                  setIsLoadingImport(true)

                  importMedialist(model)
                    .then(() => {
                      nextStep()
                    })
                    // @ts-expect-error: e
                    .catch((e) => {
                      setData({
                        error: {
                          title: <Trans>Contacts cannot be imported from this file</Trans>,
                          reason: e.file,
                          returnBack: () => jumpTo(STEPS.STEP_IMPORT),
                          heading: <Trans>Import contacts</Trans>,
                        },
                      })
                      jumpTo(STEPS.STEP_ERROR)
                    })
                    .finally(() => {
                      setIsLoadingImport(false)
                    })
                }}
                importActions={authors.medialist_import_action}
                baskets={baskets.list}
              />
            )}
          </>,
          <>
            <ContactsImporting prevStep={prevStep} />
          </>,
          <>{data?.error && <ErrorPage error={data.error} />}</>,
        ]
      }}
    />
  )
}

export default observer(WizardImportMedialist)
