import { t } from '@lingui/core/macro'
import color from 'color'
import { useRouter } from 'next/router'
import { useState } from 'react'
import FormNewBasket from '~/components/forms/baskets/FormNewBasket'
import BasketIcon from '~/components/layout/Sidebar/modules/SidebarBaskets/BasketIcon'
import modalEditBasket from '~/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { observer } from '~/helpers/mst'
import { routerPush, routerReplace } from '~/helpers/router'

const AuthorBasketsMenu = ({
  appStore: {
    account: {
      workspace: { permissions },
    },
    authors: {
      baskets,
      baskets: { selected: selectedBasket },
    },
    router: { makeRouteWithQuery },
  },
  label = t`Author Lists`,
  closeParentPopup,
}) => {
  const router = useRouter()
  const [errors, setErrors] = useState(null)
  const canEditBaskets = permissions.authors_database.can_write

  if (!baskets?.list.length && !canEditBaskets) {
    return null
  }

  const menuItems = [
    { label },
    {
      leftIcon: 'contacts',
      label: t`My authors`,
      href: `/authors?is_own_author=true`,
      selected: router.query.is_own_author,
      hoverVariant: 'light',
      buttonGroup: [
        {
          icon: 'more_vert',
          popup: (closePopup, onClose) => {
            const menuItems = [
              {
                label: t`Create author`,
                leftIcon: 'person_add',
                onClick: () => {
                  const currRoute = makeRouteWithQuery()

                  routerReplace(currRoute, currRoute, { shallow: true })
                  routerPush(currRoute, '/authors/create', { shallow: true })
                },
              },
            ]

            return (
              <MntrMenu
                menuItems={menuItems}
                closePopup={() => {
                  closePopup()
                  onClose()
                }}
              />
            )
          },
          popupPlacement: 'bottom-start',
          size: 'small',
          transformOrigin: '100% 0',
        },
      ],
    },
  ]

  baskets.list.forEach((item) => {
    const { id, label, color: itemColor, edit, remove, duplicate, author_count } = item
    const selected = id === selectedBasket?.id

    menuItems.push({
      id,
      label,
      leftIcon: <BasketIcon label={label} bg={color(itemColor).darken(0.3).toString()} />,
      activeBackground: color(itemColor).alpha(0.4).toString(),
      rounded: true,
      selected,
      hoverVariant: 'light',
      counter: author_count,
      onClick: () => {
        // go to list url
        routerPush(`/authors/?listId=${id}`)
        closeParentPopup()
      },
      ...(canEditBaskets && {
        actions: () => {
          const actions = []
          actions.push({
            label: t`Edit list`,
            leftIcon: 'edit',
            ...modalEditBasket({
              initialValues: item,
              onSubmit: (model) => {
                return edit(model)
                  .then(() => {
                    closeParentPopup()
                  })
                  .catch((err) => err)
              },
            }),
          })

          actions.push({
            label: t`Duplicate`,
            leftIcon: 'folder_copy',
            onClick: () => {
              duplicate(id)
              closeParentPopup()
            },
          })

          actions.push({
            label: t`Delete list`,
            leftIcon: 'delete',
            hoverVariant: 'error',
            ...withModalRemove({
              onSubmit: () => {
                remove()
                closeParentPopup()
              },
              title: t`Delete list?`,
              message: t`List ${label} will be removed.`,
            }),
          })

          return actions
        },
      }),
    })
  })

  return (
    <ListScrollWrapper>
      <MntrMenu menuItems={menuItems} />
      {canEditBaskets && (
        <Box mb={-1} mt={-2} width="320px">
          <FormNewBasket
            title={t`Create new list`}
            onSubmit={(model) => {
              baskets.create(model).then((res) => {
                if (!res.valid) {
                  return setErrors({ label: res.errors?.label })
                }

                closeParentPopup()
              })
            }}
            errors={errors}
          />
        </Box>
      )}
    </ListScrollWrapper>
  )
}

export default observer(AuthorBasketsMenu)
