import { Trans } from '@lingui/react/macro'
import color from 'color'
import sortBy from 'lodash/sortBy'
import BasketIcon from '~/components/layout/Sidebar/modules/SidebarBaskets/BasketIcon'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'

const withRemoveFromBasketPopup = ({ baskets, removeFromBasket, closeParentPopup }) => {
  const selectedBasket = baskets.list.find((item) => item.id === baskets.selected?.id)

  return {
    popup: (closePopup) => {
      const addBasketToMenu = (item) => {
        return {
          id: item.id,
          label: item.label,
          leftIcon: <BasketIcon label={item.label} bg={color(item.color).darken(0.3).toString()} />,
          activeBackground: color(item.color).alpha(0.4).toString(),
          selected: selectedBasket?.id === item.id,
          hoverVariant: 'light',
          onClick: () => {
            removeFromBasket(item)

            closeParentPopup?.()
            closePopup?.()
          },
        }
      }

      // move basket to top of the list if selected
      const list = selectedBasket
        ? sortBy(baskets.list, ({ id }) => (id === selectedBasket.id ? 0 : 1)).map(addBasketToMenu)
        : baskets.list.map(addBasketToMenu)
      const menuItems = [{ label: <Trans>Remove from list</Trans> }, ...list]

      return (
        <ListScrollWrapper>
          {menuItems.length > 1 && <MntrMenu menuItems={menuItems} />}
        </ListScrollWrapper>
      )
    },
  }
}

export default withRemoveFromBasketPopup
