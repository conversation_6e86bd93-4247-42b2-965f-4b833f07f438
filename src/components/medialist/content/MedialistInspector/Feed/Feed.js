import { Trans } from '@lingui/react/macro'
import get from 'lodash/get'
import AuthorFeed from '~/components/medialist/content/MedialistInspector/AuthorFeed/AuthorFeed'
import { Box, Flex, Heading } from '~/components/misc/Mntr'
import FeedMapActionsBar from '~/components/monitoring/FeedActionsBar/FeedMapActionsBar'
import LoadMore from '~/components/monitoring/FeedList/LoadMore/LoadMore'
import { observer } from '~/helpers/mst'

const Feed = ({ feedMapItem, feedId, withView, withSelectActions, withSort, emptyFeedMessage }) => {
  if (!feedMapItem.feedStories) {
    return <span />
  }

  return (
    <div>
      <Flex flexWrap="nowrap">
        {!feedMapItem.loader.isLoading('feed-loading') &&
          feedMapItem.feedStories.length === 0 &&
          !feedMapItem.hasMore &&
          (emptyFeedMessage ? (
            emptyFeedMessage
          ) : (
            <Box my={3} width={1}>
              <Heading as="h1" fontSize={4} textAlign="center" color="black">
                <Trans>No results found</Trans>
              </Heading>
            </Box>
          ))}
        {!feedMapItem.loader.isLoading('feed-loading') && feedMapItem.feedStories.length > 0 && (
          <Box width={1} mb={4}>
            <Box>
              <FeedMapActionsBar
                feedId={feedId}
                actions={
                  get(feedMapItem, 'filter.normalizedLabels.topic_monitors')
                    ? ['download', 'send', 'export', 'delete']
                    : ['download', 'send', 'export']
                }
                items={feedMapItem.feedStories}
                withView={withView}
                withSelectActions={withSelectActions}
                withSort={withSort}
              />
            </Box>

            <AuthorFeed feedId={feedId} />
            {feedMapItem.hasMore && (
              <LoadMore
                isLoading={
                  feedMapItem.loader.isLoading('feed-loading') ||
                  feedMapItem.loader.isLoading('feed-loading-more')
                }
                onLoadMore={feedMapItem.loadMore}
                shouldAutoLoadMore={
                  feedId &&
                  !feedMapItem.loader.isLoading('feed-loading') &&
                  !feedMapItem.loader.isLoading('feed-loading-more')
                }
              />
            )}
          </Box>
        )}
      </Flex>
    </div>
  )
}

export default observer(Feed)
