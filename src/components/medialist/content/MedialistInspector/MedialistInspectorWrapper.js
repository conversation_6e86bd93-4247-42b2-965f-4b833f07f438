import { useRouter } from 'next/router'
import { css, styled } from 'styled-components'
import MedialistAuthorArticles from '~/components/medialist/content/MedialistAuthorArticles'
import MedialistAuthorCreate from '~/components/medialist/content/MedialistAuthorCreate'
import MedialistAuthorDetail from '~/components/medialist/content/MedialistAuthorDetail'
import { observer } from '~/helpers/mst'

const Wrapper = styled.div`
  position: fixed;
  top: 54px;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ theme }) => theme.colors.background};
  z-index: ${(props) => (props.isMobile ? 499 : 1000)};
  ${(props) =>
    props.isHidden &&
    css`
      z-index: 950;
    `}
`

const MedialistInspectorWrapper = ({
  appStore: {
    authors: { isOpenInspector, authorDetail, isCreate },
    monitoring: { inspector },
    viewport,
  },
  activeTab = 'articles',
}) => {
  const router = useRouter()
  const authorId = authorDetail?.id
  const mobilePreferSidebar = !(
    (router.pathname.endsWith('articles') || router.pathname.endsWith('overview')) &&
    viewport.isTablet
  )

  const contentComponent =
    authorDetail?.is_own_author || isCreate ? (
      <MedialistAuthorCreate />
    ) : (
      <MedialistAuthorArticles />
    )

  return (
    isOpenInspector && (
      <Wrapper isMobile={viewport.isMobile} isHidden={inspector.isOpenArticle && inspector.data}>
        <MedialistAuthorDetail
          isCreate={isCreate}
          activeTab={activeTab}
          authorId={authorId}
          contentComponent={contentComponent} // TODO: tabs
          mobilePreferSidebar={mobilePreferSidebar}
        />
      </Wrapper>
    )
  )
}

export default observer(MedialistInspectorWrapper)
