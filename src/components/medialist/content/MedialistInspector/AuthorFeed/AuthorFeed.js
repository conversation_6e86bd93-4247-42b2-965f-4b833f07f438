import get from 'lodash/get'
import { useRouter } from 'next/router'
import { Flex } from '~/components/misc/Mntr'
import PortableExport from '~/components/misc/portable/PortableExport/PortableExport'
import PortableResend from '~/components/misc/portable/PortableResend/PortableResend'
import FeedListItem from '~/components/monitoring/FeedList/FeedListItem/FeedListItem'
import { observer } from '~/helpers/mst'
import { routerPush } from '~/helpers/router'

const AuthorFeed = ({
  feedId,
  appStore: {
    viewport,
    monitoring: { feedMap, inspector, isFeedVisible, setActiveFeed },
    filter,
    account,
    account: {
      workspace: { permissions },
    },
  },
}) => {
  const { pathname, query } = useRouter()
  const feed = feedMap.get(feedId)

  if (!feed) {
    return <span />
  }
  return (
    <Flex flexDirection="column" gap={2}>
      {isFeedVisible &&
        feed.feedStories &&
        feed.feedStories.map((item, index) => {
          const as = {
            pathname: `/article/${item.article_id}/${item.token}`,
            query: {
              ...(query.query || query.ma__query ? { query: query.query || query.ma__query } : {}),
              ...(item.topic_monitor ? { topic_monitor: item.topic_monitor.id } : {}),
            },
          }

          return (
            <FeedListItem
              index={index}
              key={`feed-item${get(item, 'article_id')}${get(item, 'topic_monitor.id')}`}
              item={item}
              feedId={feedId}
              viewport={viewport}
              openArticleInspector={() => {
                setActiveFeed(feedId)
                routerPush({ pathname, query }, as, { shallow: true })
              }}
              filter={filter}
              setSentiment={(model) => feed.setSentiment(index, model)}
              setTag={(model) => feed.setTag(index, model)}
              removeTag={(model) => feed.removeTag(index, model)}
              fastExport={(fileFormatId) => inspector.fastExport(fileFormatId, index)}
              addToExportBasket={() => feed.addToExportBasket(feed.feedStories[index])}
              removeItem={() => feed.removeItem(index)}
              removeSource={() => feed.removeSource(index)}
              reportProblem={(args) => feed.reportProblem(index, args)}
              getPublished={feed.getPublished(index)}
              isFirst={index === 0}
              isVisibleDate={feed.isVisibleDate(index)}
              feedItemRoute={as}
              permissions={permissions}
              exportFileFormat={account.enums.export.export_file_format}
              translationLanguage={account.enums.translation_language}
            />
          )
        })}
      <PortableExport feedId={feedId} />
      <PortableResend feedId={feedId} />
    </Flex>
  )
}

export default observer(AuthorFeed)
