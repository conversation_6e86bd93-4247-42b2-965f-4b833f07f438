import { Trans } from '@lingui/react/macro'
import color from 'color'
import fuzzy from 'fuzzy'
import { useState } from 'react'
import FormNewTag from '~/components/forms/tags/FormNewTag/FormNewTag'
import FormFilterItems from '~/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import removeAccents from '~/helpers/removeAccents'

const AddAuthorTag = ({ tags, authorTags, canEditTags, setTag, closePopup }) => {
  const [filterValue, setFilterValue] = useState('')
  const [errors, setErrors] = useState(undefined)

  const menuItems = [
    {
      label: <Trans>Assign tag to author</Trans>,
    },
  ]

  tags.list.forEach((subject) => {
    if (
      (authorTags &&
        authorTags.filter((entry) => entry.author_tag_definition?.id === subject.id).length ===
          1) ||
      !fuzzy.test(removeAccents(filterValue) || '', removeAccents(subject.label))
    ) {
      return true
    }

    menuItems.push({
      label: subject.label,
      leftIcon: 'label',
      leftIconBg: subject.color,
      leftIconColor: color(subject.color).darken(0.6).toString(),
      onClick: () => setTag(subject),
    })
  })

  return (
    <ListScrollWrapper delta={50}>
      {tags.list.length > 10 && (
        <FormFilterItems value="" onChange={(model) => setFilterValue(model.filter)} />
      )}
      {menuItems.length > 1 && <MntrMenu menuItems={menuItems} />}
      {canEditTags && (
        <Box width="320px" mt={-2}>
          <FormNewTag
            title={<Trans>Create and assign new tag</Trans>}
            onSubmit={(model) => {
              tags.create(model).then((res) => {
                if (!res.valid) {
                  return setErrors({ label: res.errors?.label })
                }

                setTag(res.tag)
                closePopup()
              })
            }}
            errors={errors}
          />
        </Box>
      )}
    </ListScrollWrapper>
  )
}

export default observer(AddAuthorTag)
