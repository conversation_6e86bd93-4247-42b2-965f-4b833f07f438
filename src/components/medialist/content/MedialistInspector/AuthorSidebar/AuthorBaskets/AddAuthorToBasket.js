import { Trans } from '@lingui/react/macro'
import color from 'color'
import fuzzy from 'fuzzy'
import { useState } from 'react'
import FormNewBasket from '~/components/forms/baskets/FormNewBasket'
import FormFilterItems from '~/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems'
import BasketIcon from '~/components/layout/Sidebar/modules/SidebarBaskets/BasketIcon'
import ListScrollWrapper from '~/components/misc/ListScrollWrapper/ListScrollWrapper'
import { Box } from '~/components/misc/Mntr'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import { observer } from '~/helpers/mst'
import removeAccents from '~/helpers/removeAccents'

const AddAuthorToBasket = ({
  baskets,
  authorBaskets,
  canEditBaskets,
  addAuthorToBasket,
  closePopup,
}) => {
  const [filterValue, setFilterValue] = useState('')
  const [errors, setErrors] = useState(undefined)

  const menuItems = [
    {
      label: <Trans>Add to list</Trans>,
    },
  ]

  baskets.list.forEach((item) => {
    if (
      (authorBaskets &&
        authorBaskets.filter((entry) => entry.author_basket?.id === item.id).length === 1) ||
      !fuzzy.test(removeAccents(filterValue) || '', removeAccents(item.label))
    ) {
      return true
    }

    menuItems.push({
      label: item.label,
      leftIcon: <BasketIcon label={item.label} bg={color(item.color).darken(0.3).toString()} />,
      activeBackground: color(item.color).alpha(0.4).toString(),
      hoverVariant: 'light',
      onClick: () => {
        addAuthorToBasket(item)
      },
    })
  })

  return (
    <ListScrollWrapper delta={50}>
      {baskets.list.length > 10 && (
        <FormFilterItems value="" onChange={(model) => setFilterValue(model.filter)} />
      )}
      {menuItems.length > 1 && <MntrMenu menuItems={menuItems} />}
      {canEditBaskets && (
        <Box width="320px" mt={-2}>
          <FormNewBasket
            title={<Trans>Create and add to new list</Trans>}
            onSubmit={(model) => {
              baskets.create(model).then((res) => {
                if (!res.valid) {
                  return setErrors({ label: res.errors?.label })
                }

                addAuthorToBasket(res.author_basket)
                closePopup?.()
              })
            }}
            errors={errors}
          />
        </Box>
      )}
    </ListScrollWrapper>
  )
}

export default observer(AddAuthorToBasket)
