import { t } from '@lingui/core/macro'
import { styled } from 'styled-components'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

const StyledLink = styled.a`
  color: inherit;
  word-break: break-word;
  cursor: pointer;
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }
`

const AuthorFiles = ({ data }) => {
  if (!data.mediakit_url && !data.imprint_page_url) {
    return <span />
  }

  return (
    <Flex px="16px" mb="16px" flexDirection="column">
      <MntrMenuHeading noPadding label={t`Files`} />
      {data.mediakit_url && (
        <Flex centerY color="icon">
          <Icon size={18}>download</Icon>
          <Box ml={1} color="black">
            <StyledLink href={data.mediakit_url} target="_blank">{t`Mediakit`}</StyledLink>
          </Box>
        </Flex>
      )}
      {data.imprint_page_url && (
        <Flex centerY color="icon">
          <Icon size={18}>download</Icon>
          <Box ml={1} color="black">
            <StyledLink href={data.imprint_page_url} target="_blank">{t`Imprint`}</StyledLink>
          </Box>
        </Flex>
      )}
    </Flex>
  )
}

export default AuthorFiles
