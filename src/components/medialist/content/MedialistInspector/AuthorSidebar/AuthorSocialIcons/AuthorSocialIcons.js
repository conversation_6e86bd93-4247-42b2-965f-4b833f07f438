import { Box, Flex } from '~/components/misc/Mntr'

const AuthorSocialIcons = ({ item }) => {
  return (
    <Flex ml="16px" mt={1} flexDirection="row">
      {item.facebook_url && (
        <a href={item.facebook_url} target="_blank">
          <Box mr={1}>
            <img src={`/static/social/facebook.png`} width={26} height={26} alt={'facebook'} />
          </Box>
        </a>
      )}
      {item.twitter_url && (
        <a href={item.twitter_url} target="_blank">
          <Box mr={1}>
            <img src={`/static/social/twitter.png`} width={26} height={26} alt={'twitter'} />
          </Box>
        </a>
      )}
      {item.instagram_url && (
        <a href={item.instagram_url} target="_blank">
          <Box mr={1}>
            <img src={`/static/social/instagram.png`} width={26} height={26} alt={'instagram'} />
          </Box>
        </a>
      )}

      {item.linkedin_url && (
        <a href={item.linkedin_url} target="_blank">
          <Box mr={1}>
            <img src={`/static/social/linkedin.png`} width={26} height={26} alt={'linkedin'} />
          </Box>
        </a>
      )}
    </Flex>
  )
}

export default AuthorSocialIcons
