import { t } from '@lingui/core/macro'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrMenuHeading from '~/components/misc/MntrMenuHeading/MntrMenuHeading'

const AuthorAddress = ({ data }) => {
  return (
    <Box px="16px" mb="16px">
      <MntrMenuHeading noPadding label={t`Address`} />
      <Flex flexDirection="column" color="black">
        <Box>{data.street}</Box>
        <Box>
          {data.postal_code} {data.municipality}
        </Box>
        {data.reg_no && <Box>{t`CRN: ${data.reg_no}`}</Box>}
      </Flex>
    </Box>
  )
}

export default AuthorAddress
