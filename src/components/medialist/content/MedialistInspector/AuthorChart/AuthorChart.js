import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { endOfYear } from 'date-fns'
import { styled } from 'styled-components'
import OurChart from '~/components/OurChart/OurChart'
import { article } from '~/helpers/charts/formatters'
import formatDate from '~/helpers/date/format'
import parseDate from '~/helpers/date/parse'
import { observer } from '~/helpers/mst'
import { routerReplace } from '~/helpers/router'

const Label = styled.span`
  font-weight: 700;
`

const AuthorChart = ({
  feedId,
  customRoute,
  appStore: {
    monitoring: { getFeedMap },
    router: { makeRouteWithQuery },
  },
  authorId,
}) => {
  const feed = getFeedMap('feed').get(feedId)
  if (!feed) {
    return <span />
  }
  const { totalCount, granularity, setGranularity, chartData } = feed.chart

  const title = article(totalCount)
  const isLoading = feed.loader.isLoading('feed-loading')

  return (
    <OurChart
      allowNullDataPoints
      data={chartData}
      clickCallback={(point) => {
        const selectedLowerDate = formatDate(point.options.x, 'yyyy-MM-dd')
        let selectedUpperDate
        switch (granularity) {
          case 'year':
            selectedUpperDate = formatDate(endOfYear(parseDate(selectedLowerDate)), 'yyyy-MM-dd')
            break
          case 'month':
            selectedUpperDate = formatDate(
              new Date(
                new Date(point.options.x).getFullYear(),
                new Date(point.options.x).getMonth() + 1,
                0,
              ),
              'yyyy-MM-dd',
            )
            break
          default:
            selectedUpperDate = formatDate(point.options.x, 'yyyy-MM-dd')
        }
        const url = `${customRoute}?${feed.filter.queryAddParam({
          lower_date: selectedLowerDate,
          upper_date: selectedUpperDate,
        })}`
        routerReplace(makeRouteWithQuery(), url, { shallow: true })
        feed.loadUrl(url)
      }}
      dataLabel={t`Article count`}
      formatter={article}
      granularity={granularity}
      height={150}
      id={`author-${authorId}`}
      isLoading={isLoading}
      leftIcon="show_chart"
      options={{
        plotOptions: { series: { showInLegend: false } },
        xAxis: {
          labels: { autoRotation: 0, style: { whiteSpace: 'nowrap' } },
          tickPixelInterval: 160,
          type: 'datetime',
        },
        yAxis: { title: { text: '' } },
      }}
      setGranularity={setGranularity}
      switches={{ granularity: 'full' }}
      title={title}
      titleComponent={<Label>{isLoading ? <Trans>Loading...</Trans> : title}</Label>}
      titleDetached={false}
      type="column"
      view="advanced"
    />
  )
}

export default observer(AuthorChart)
