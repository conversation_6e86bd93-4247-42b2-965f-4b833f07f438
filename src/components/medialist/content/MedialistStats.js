import { css, styled } from 'styled-components'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import Icon from '~/components/misc/Icon/Icon'
import { Box, Flex, Text } from '~/components/misc/Mntr'
import { observer } from '~/helpers/mst'

const Item = styled(Box)`
  height: 80px;

  ${({ isWrapped }) =>
    isWrapped &&
    css`
      height: 50px;
    `}
`

const IconWrapper = styled(Flex)`
  border-right: 1px solid ${({ bg, theme }) => (bg ? 'transparent' : theme.paper.border)};
  height: 100%;
`

const MedialistStats = ({
  appStore: {
    authors: { authorStats },
    viewport: { isMobile, width },
  },
}) => {
  if (authorStats?.length === 0) return null
  const isWrapped = width < 640

  return (
    <Flex flexWrap="wrap" width="100%" centerY justifyContent="space-between">
      {authorStats.map(({ icon, label, count, is_bigger }, key) => {
        return (
          <Item
            key={key}
            mt={[1, 2]}
            pl={!isWrapped ? key !== 0 && 2 : key % 2 && 2}
            isWrapped={width < 640}
            width={isWrapped ? 1 / 2 : 1 / 4}
          >
            <MntrPaper>
              <Flex flexDirection="row" centerY width={1} height={1}>
                <IconWrapper width={[1 / 4]} center height={1}>
                  <Icon size={isMobile ? 25 : 30} color="black">
                    {icon}
                  </Icon>
                </IconWrapper>
                <Flex width={[3 / 4]}>
                  <Flex flexDirection="column" ml={2}>
                    <Text fontWeight="bold" fontSize={[3, 4]}>
                      {count}
                      {is_bigger && `+`}
                    </Text>
                    <Text fontSize={['11px', '13px']} textTransform="uppercase">
                      {label}
                    </Text>
                  </Flex>
                </Flex>
              </Flex>
            </MntrPaper>
          </Item>
        )
      })}
    </Flex>
  )
}

export default observer(MedialistStats)
