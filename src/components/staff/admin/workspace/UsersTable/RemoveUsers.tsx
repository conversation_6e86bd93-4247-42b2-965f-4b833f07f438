import { t } from '@lingui/core/macro'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { ObservedFC, observer } from '~/helpers/msts'

interface IRemoveUsersProps {
  onSubmit: () => void
}

const RemoveUsers: ObservedFC<IRemoveUsersProps> = ({
  appStore: {
    viewport: { isTablet },
  },
  onSubmit,
}) => {
  const label = t`Remove users`

  return (
    // @ts-expect-error: modal

    <MntrButton
      rounded
      icon="group_remove"
      bg="error"
      {...(!isTablet ? { label } : { tooltip: label })}
      {...withModalRemove({
        message: t`Are you sure you want to remove these users from the workspace?`,
        onSubmit,
      })}
    />
  )
}

export default observer(RemoveUsers)
