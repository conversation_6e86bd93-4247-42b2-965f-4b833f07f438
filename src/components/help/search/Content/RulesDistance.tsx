'use client'

import { Trans, useLingui } from '@lingui/react/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import Highlight from '~/components/misc/Highlight/Highlight'
import { Box, Heading } from '~/components/misc/Mntr'
import { Table, TableBody, TableHead, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import Text from './StyledText'

const RulesDistance = ({ appName }: { appName: string }) => {
  const { t } = useLingui()

  return (
    <MntrPaper id="rules-distance">
      <MntrPaperToolbar bg="primary" title={t`Words to distance`} />
      <Box p={2}>
        <Trans id="help.search.distance.description">
          <Heading my={1} fontSize={3} id="rules-distance-overview">
            Quick Overview
          </Heading>
          <Table fontSize={15} canOverflow>
            <TableHead>
              <Tr>
                <Th>Entered expression</Th>
                <Th>Searches for articles containing</Th>
              </Tr>
            </TableHead>
            <TableBody>
              <Tr>
                <Td>walrus+penguin~5</Td>
                <Td>the penguin sTroked the walrus</Td>
              </Tr>
              <Tr>
                <Td>toucan+walrus+penguin~10</Td>
                <Td>toucans wanted to eat a penguin, but the walrus stepped in</Td>
              </Tr>
              <Tr>
                <Td>"walrus toucan"~5</Td>
                <Td>"Walrus!" Toucan said, flying away.</Td>
              </Tr>
              <Tr>
                <Td>"toucan walrus penguin"~5</Td>
                <Td>From left: penguin, walrus, toucan.</Td>
              </Tr>
            </TableBody>
          </Table>

          <Heading mt={4} fontSize={3} id="rules-distance-declension">
            With inflection
          </Heading>
          <Text>
            If we type in the search box: <Highlight>walrus+penguin~5</Highlight> (we separate words
            with a plus sign, followed by a tilde and a number)
          </Text>
          <Text>
            {appName} will search for all articles that contain the words{' '}
            <Highlight type="primary">walrus</Highlight> and{' '}
            <Highlight type="primary">penguin</Highlight> in any order and no more than 5 words
            apart. The words you type are automatically inflected and letter size or diacritic does
            not matter.
          </Text>
          <Text>
            Its recommended to search for words related to each other and will be close to each
            other in the article text (company+Facebook~7).
          </Text>

          <Heading mt={4} fontSize={3} id="rules-distance-exact">
            Exact match
          </Heading>
          <Text>
            If we type in the search box: <Highlight>"walrus penguin"~5</Highlight> (put the words
            in quotation marks and after the second quotation mark we write the tilde and the
            number)
          </Text>
          <Text>
            {appName} will search for all articles that contain the words{' '}
            <Highlight type="primary">walrus</Highlight> and{' '}
            <Highlight type="primary">penguin</Highlight> in any order and no more than 5 words
            apart. Both words are searched only in specified shape, i.e. not inflected and diacritic
            plays a role.
          </Text>
        </Trans>
      </Box>
    </MntrPaper>
  )
}

export default RulesDistance
