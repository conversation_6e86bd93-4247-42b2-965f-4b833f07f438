'use client'

import { Trans, useLingui } from '@lingui/react/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import Highlight from '~/components/misc/Highlight/Highlight'
import { Box, Heading } from '~/components/misc/Mntr'
import { Table, TableBody, TableHead, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import Text from './StyledText'

const RulesPhrase = ({ appName }: { appName: string }) => {
  const { t } = useLingui()

  return (
    <MntrPaper id="rules-phrase">
      <MntrPaperToolbar bg="primary" title={t`Search for phrases`} />
      <Box p={2}>
        <Trans id="help.search.phrase.description">
          <Heading my={1} fontSize={3} id="rules-phrase-overview">
            Quick Overview
          </Heading>
          <Table fontSize={15} canOverflow>
            <TableHead>
              <Tr>
                <Th>Entered expression</Th>
                <Th>Searches for articles containing</Th>
              </Tr>
            </TableHead>
            <TableBody>
              <Tr>
                <Td>Caffé+Honza</Td>
                <Td>
                  Caffé Honza, CAFFÉ HONZA, Caffé honza, Caffé Honzy, caffe honza, caffé-honza
                </Td>
              </Tr>
              <Tr>
                <Td>"Caffé Honza"</Td>
                <Td>Caffé Honza, CAFFÉ HONZA, caffé honza</Td>
              </Tr>
              <Tr>
                <Td>"Caffé-Honza"</Td>
                <Td>Caffé-Honza, CAFFÉ-HONZA, caffé-honza</Td>
              </Tr>
              <Tr>
                <Td>"!Caffé Honza"</Td>
                <Td>Caffé Honza</Td>
              </Tr>
            </TableBody>
          </Table>

          <Heading mt={4} fontSize={3} id="rules-phrase-declension">
            With inflection
          </Heading>
          <Text>
            If we type in the search box: <Highlight>Caffé+Honza</Highlight>
            (words are separated by a plus sign so there is no space between them)
          </Text>
          <Text>
            {appName} will search for all articles that contain the phrase{' '}
            <Highlight type="primary">Caffé Hoza</Highlight> (ie. these words in sequence) in any
            shape. So there will be found articles containing the phrase{' '}
            <Highlight type="primary">Caffé Honzy</Highlight> ({appName} inflects the word),{' '}
            <Highlight type="primary">CAFFÉ HONZA</Highlight> (letter size does not matter),{' '}
            <Highlight type="primary">Caffe Honza</Highlight> (diacritics does not matter) or{' '}
            <Highlight type="primary">Caffé-Honza</Highlight> (there may be a separator between
            words, such as a comma or dash).
          </Text>
          <Text>
            It is recommended to search for names of persons (Michael+Smith), names of companies and
            organizations (Ministry+ for+Local+Development) or phrases (monitoring+media).
          </Text>

          <Heading mt={4} fontSize={3} id="rules-phrase-exact">
            Exact match
          </Heading>
          <Text>
            If we type in the search box: <Highlight>"Caffé Honza"</Highlight> (put the entire
            expression in quotation marks)
          </Text>
          <Text>
            {appName} will search for all articles that contain the phrase{' '}
            <Highlight type="primary">Caffé Honza</Highlight>, but only in the specified form. So
            there will be found articles containing{' '}
            <Highlight type="primary">Caffé Honza</Highlight> or{' '}
            <Highlight type="primary">CAFFÉ HONZA</Highlight> (letter size does not matter).
          </Text>
          <Text>
            {appName} will not search for those articles that contain inflected phrase{' '}
            <Highlight type="secondary">Caffé Honzy</Highlight>, the phrase without diacritics{' '}
            <Highlight type="secondary">Caffe Honza</Highlight> or phrase with separator (e.g. comma
            or dash) <Highlight type="secondary">Caffé-Honza</Highlight>.
          </Text>
          <Text>
            It is recommended to search for company and product names ("{appName} Media"),
            abbreviations ("MFF UK") or exact match ("to be or not to be") in this way.
          </Text>

          <Heading mt={4} fontSize={3} id="rules-phrase-exact-incl-separator">
            Exact match with separator
          </Heading>
          <Text>
            When searching for an exact match, you must also include quotation marks around the
            separators that may appear between words - dashes, underscores, at-signs, etc. These
            include the following characters: & @ _ + - '#
          </Text>
          <Text>
            If we type in the search box: <Highlight>"Caffé-Honza"</Highlight>
          </Text>
          <Text>
            {appName} will search for all articles that contain the{' '}
            <Highlight type="primary">Caffé-Honza</Highlight> phrase with a separator.
          </Text>
          <Text>
            {appName} will not search for articles that contain only the words{' '}
            <Highlight type="secondary">Caffé Honza</Highlight> without a separator or{' '}
            <Highlight type="secondary">Caffé&Honza</Highlight> with a separator other than the one
            you specified.
          </Text>
          <Text>
            Typical phrases where should not forget the separator are "Ernst&Young", "info@{appName}
            .cz", "Mi+Te" or "X-Men".
          </Text>
          <Heading mt={4} fontSize={3} id="rules-phrase-exact-incl-case">
            Exact match, including letter size
          </Heading>
          <Text>
            If we type in the search box: <Highlight>"!Caffé Honza"</Highlight> (put the word in
            quotation marks and after the first quotation mark an exclamation mark)
          </Text>
          <Text>
            {appName} will search for all articles that contain the phrase{' '}
            <Highlight type="primary">Caffé Honza</Highlight>, but only in the specified form,
            including the letter size. It is the strictest option.
          </Text>
          <Text>
            {appName} will not search for articles that contain, for example, only the phrase{' '}
            <Highlight type="secondary">caffé honza</Highlight> written in lowercase letters.
          </Text>
          <Text>
            Its recommended to search for company and product names ("!Golden Spoon") or
            abbreviations ("!AV ČR") this way.
          </Text>
        </Trans>
      </Box>
    </MntrPaper>
  )
}

export default RulesPhrase
