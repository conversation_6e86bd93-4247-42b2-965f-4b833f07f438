'use client'

import { Trans, useLingui } from '@lingui/react/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import Highlight from '~/components/misc/Highlight/Highlight'
import { Box, Heading } from '~/components/misc/Mntr'
import { Table, TableBody, TableHead, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import Text from './StyledText'

const RulesSearch = ({ appName }: { appName: string }) => {
  const { t } = useLingui()

  return (
    <MntrPaper id="rules-search">
      <MntrPaperToolbar bg="primary" title={t`Word Search`} />
      <Box p={2}>
        <Trans id="help.search.wordSearch.description">
          <Heading my={1} fontSize={3} id="rules-search-overview">
            Quick Overview
          </Heading>
          <div className="table-wrapper--responsive ios-scroll">
            <Table fontSize={15} canOverflow>
              <TableHead>
                <Tr>
                  <Th>Entered expression</Th>
                  <Th>What does {appName} do?</Th>
                  <Th>Searches for articles containing</Th>
                </Tr>
              </TableHead>
              <TableBody>
                <Tr>
                  <Td>Penguin</Td>
                  <Td>word inflects, diacritic and letter size doesn't matter</Td>
                  <Td>Penguin, Penguins, penguin, penguins, PENGUIN, PENGUINS</Td>
                </Tr>
                <Tr>
                  <Td>"Penguin"</Td>
                  <Td>
                    word does not inflect, diacritic plays a role, letter size does not matter
                  </Td>
                  <Td>Penguin, penguin, PENGUIN</Td>
                </Tr>
                <Tr>
                  <Td>"!Penguin"</Td>
                  <Td>word does not inflect, diacritic and letter size plays a role</Td>
                  <Td>Penguin</Td>
                </Tr>
              </TableBody>
            </Table>
          </div>
          <Heading mt={4} fontSize={3} id="rules-search-declension">
            With inflection
          </Heading>
          <Text>
            If we type in the search box: <Highlight>Penguin</Highlight>
          </Text>
          <Text>
            {appName} will search for all articles that contain the word{' '}
            <Highlight type="primary">Penguin</Highlight> in any shape. So there will be found
            articles containing the word <Highlight type="primary">Penguins</Highlight> ({appName}
            inflects the word), <Highlight type="primary">PENGUIN</Highlight> (letter size does not
            matter) or <Highlight type="primary">penguin´s</Highlight> (diacritics does not matter).
          </Text>
          <Text>
            It is recommended to search for all words that are inflected in normal text this way.
            These are typically general words (penguin), proper names (Michael) or foreign names
            (facebook).
          </Text>

          <Heading mt={4} fontSize={3} id="rules-search-exact">
            Exact match
          </Heading>
          <Text>
            If we type in the search box: <Highlight>"Penguin"</Highlight> (put the word in
            quotation marks)
          </Text>
          <Text>
            {appName} will search for all articles that contain the word{' '}
            <Highlight type="primary">Penguin</Highlight>, but only in the specified form (i.e., do
            not inflect the word). So there will be found articles containing the word{' '}
            <Highlight type="primary">Penguin</Highlight> or{' '}
            <Highlight type="primary">PENGUIN</Highlight> (letter size does not matter).
          </Text>
          <Text>
            {appName} does not look for those articles that contain inflected word{' '}
            <Highlight type="secondary">Penguins</Highlight> or the word without diacritics{' '}
            <Highlight type="secondary">penguin´s</Highlight>.
          </Text>
          <Text>
            It is recommended to search for company and product names ("McDonald's"), web domain ("
            {appName}.com"), exact match ("best") or abbreviations ("USA") in this way.
          </Text>

          <Heading mt={4} fontSize={3} id="rules-search-exact-incl-case">
            Exact match, including letter size
          </Heading>
          <Text>
            If we type in the search box: <Highlight>"!Penguin"</Highlight> (put the word in
            quotation marks and after the first quotation mark an exclamation mark)
          </Text>
          <Text>
            {appName} will search for all articles that contain the word{' '}
            <Highlight type="primary">Penguin</Highlight>, but only in the specified form, including
            the letter size. This is the strictest option.
          </Text>
          <Text>
            {appName} will not search for those articles that contain, for example, only the word{' '}
            <Highlight type="primary">penguin</Highlight> written in lowercase letters.
          </Text>
          <Text as="blockquote">
            <i>
              It is recommended to search for company and product names ("!Seznam") or abbreviations
              ("!WHO") this way.
            </i>
          </Text>
        </Trans>
      </Box>
    </MntrPaper>
  )
}

export default RulesSearch
