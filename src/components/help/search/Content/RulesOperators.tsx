'use client'

import { Trans, useLingui } from '@lingui/react/macro'
import MntrPaper from '~/components/layout/MntrPaper/MntrPaper'
import MntrPaperToolbar from '~/components/layout/MntrPaper/MntrPaperToolbar'
import Highlight from '~/components/misc/Highlight/Highlight'
import { Box, Heading } from '~/components/misc/Mntr'
import { Table, TableBody, TableHead, Td, Th, Tr } from '~/components/misc/MntrTable/MntrTable'
import Text from './StyledText'

const RulesOperators = ({ appName }: { appName: string }) => {
  const { t } = useLingui()

  return (
    <MntrPaper id="rules-operators">
      <MntrPaperToolbar bg="primary" title={t`Search operators`} />
      <Box p={2}>
        <Trans id="help.search.operators.description">
          <Heading my={1} fontSize={3} id="rules-operators-overview">
            Quick Overview
          </Heading>
          <Table fontSize={15} canOverflow>
            <TableHead>
              <Tr>
                <Th>Entered expression</Th>
                <Th>{appName} will search</Th>
              </Tr>
            </TableHead>
            <TableBody>
              <Tr>
                <Td>penguin AND walrus</Td>
                <Td>
                  Articles containing both words <Highlight type="primary">penguin</Highlight> and{' '}
                  <Highlight type="primary">walrus</Highlight>.
                </Td>
              </Tr>
              <Tr>
                <Td>penguin walrus</Td>
                <Td>
                  Articles containing both words <Highlight type="primary">penguin</Highlight> and{' '}
                  <Highlight type="primary">walrus</Highlight>. The space between words behaves as
                  if there was an AND operator.
                </Td>
              </Tr>
              <Tr>
                <Td>penguin OR walrus</Td>
                <Td>
                  Articles containing at least one of the words{' '}
                  <Highlight type="primary">penguin</Highlight> or{' '}
                  <Highlight type="primary">walrus</Highlight>.
                </Td>
              </Tr>
              <Tr>
                <Td>penguin -walrus</Td>
                <Td>
                  Articles containing the word <Highlight type="primary">penguin</Highlight> but not
                  containing the word <Highlight type="secondary">walrus</Highlight>.
                </Td>
              </Tr>
            </TableBody>
          </Table>
          <Heading mt={4} fontSize={3} id="rules-operators-and">
            AND
          </Heading>
          <Text>
            To search for articles that contain several words or phrases at the same time, enter all
            the words you want, separated by either a space or the word <strong>AND</strong> (in
            capital letters).
          </Text>
          <Text>
            If we type in the search box: <Highlight>penguin walrus Prague+zoo "!ČEZ"</Highlight>
          </Text>
          <Text>
            It’s the same as we would write:{' '}
            <Highlight>penguin AND walrus AND Prague+zoo AND "!ČEZ"</Highlight>
          </Text>
          <Heading mt={4} fontSize={3} id="rules-operators-or">
            OR
          </Heading>
          <Text>
            To search for articles that contain at least one of the words or phrases you enter,
            enter all the words you want and separate them with <strong>OR</strong> (in capital
            letters).
          </Text>
          <Text>
            Example: <Highlight>penguin OR walrus OR Prague+zoo OR "!ČEZ"</Highlight>
          </Text>
          <Heading mt={4} fontSize={3} id="rules-operators-not">
            NOT
          </Heading>
          <Text>
            To remove articles that contain certain words or phrases from the search results, we
            type a list of forbidden words and phrases after the search term and precede each with a
            minus sign.
          </Text>
          <Text>
            Example: <Highlight>penguin -walrus -Prague+zoo -"!ČEZ"</Highlight>
          </Text>
          <Heading mt={4} fontSize={3} id="rules-operators-parens">
            Brackets
          </Heading>
          <Text>
            Search operators can be combined as needed. For more complex expressions, however, we
            often need to determine the order in which we want search operators to evaluate. For
            this purpose, we use brackets that work similarly to mathematics.
          </Text>
          <Text>
            If we type in the search box:{' '}
            <Highlight>
              "!Billa" AND (store OR chain OR shop OR supermarket OR hypermarket)
            </Highlight>
          </Text>
          <Text>
            {appName} will search for all articles that contain the word{' '}
            <Highlight type="primary">Billa</Highlight> (only in the specified form including the
            letter size) in conjunction with at least one of the words{' '}
            <Highlight type="primary">store</Highlight>, <Highlight type="primary">chain</Highlight>
            , …
          </Text>
          <Text>
            Finally, a demonstration of how complex terms you can put together in {appName} search
            operators:
            <Highlight>
              Housing estate (house OR prefab OR building) AND (balcony OR (plastic+windows
              -"!Vekra")) AND Praha+Záběhlice~5 -(Spořilov OR Chodov)
            </Highlight>
          </Text>
        </Trans>
      </Box>
    </MntrPaper>
  )
}

export default RulesOperators
