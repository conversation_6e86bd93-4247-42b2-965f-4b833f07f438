import { t } from '@lingui/core/macro'
import Error from '~/components/layout/ErrorCustom/ErrorCustom'
import Inspector from '~/components/monitoring/Inspector/Inspector'
import isEmpty from '~/helpers/isEmpty'
import { observer } from '~/helpers/mst'

const Content = ({
  appStore: {
    monitoring: { inspector },
  },
}) => {
  if (inspector.articleError) {
    return <Error heading={t`Invalid article link`} />
  }

  if (inspector.tokenError) {
    return <Error heading={t`Article link has expired`} />
  }

  if (!isEmpty(inspector.data)) {
    return <Inspector />
  }

  return null
}

export default observer(Content)
