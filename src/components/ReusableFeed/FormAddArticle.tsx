import { t } from '@lingui/core/macro'
import { useRouter } from 'next/router'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'
import { ObservedFC, observer } from '~/helpers/msts'
interface IFormAddArticleProps {
  closeModal: () => void
}

const FormAddArticle: ObservedFC<IFormAddArticleProps> = ({
  appStore: {
    emailing: { campaign_detail },
  },
  closeModal,
}) => {
  const {
    query: { campaignId },
  } = useRouter()

  const onSubmit = async (articleUrl: string) => {
    await campaign_detail.addArticleMediaCoverage(articleUrl, campaignId)
    closeModal()
    campaign_detail.loadEmailingCampaignFeed(campaignId)
  }

  const formSchema: IFormSchemaItem[] = [
    {
      name: 'article',
      autoFocus: true,
      autoComplete: 'off',
      label: t`Article URL`,
      placeholder: t`URL`,
    },
    {
      actions: ({ values, pristine, submitting }) => {
        return [
          {
            rounded: true,
            type: 'submit',
            bg: 'secondary',
            icon: 'add',
            disabled: !values?.article || pristine || submitting,
            label: t`Add`,
          },
        ]
      },
    },
  ]
  return <MntrForm contentPadding={3} onSubmit={onSubmit} schema={formSchema} />
}

export default observer(FormAddArticle)
