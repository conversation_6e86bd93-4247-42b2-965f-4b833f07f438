import DashboardInspectorWrapper from '~/components/dashboards/DashboardInspectorWrapper/DashboardInspectorWrapper'
import Feed from '~/components/medialist/content/MedialistInspector/Feed/Feed'
import { Box, Flex } from '~/components/misc/Mntr'
import MntrCircularProgress from '~/components/misc/MntrProgress/MntrCircularProgress'
import { observer } from '~/helpers/mst'

const ReusableFeed = ({
  appStore: {
    monitoring: { feedMap, activeFeedMapItem },
  },
  feedId,
  withSort,
  emptyFeedMessage,
}) => {
  const feed = feedMap.get(feedId)
  if (!feed) {
    return
  }

  const isLoading = feed.loader.isLoading('feed-loading')

  return (
    <Box>
      {isLoading && (
        <Flex height={'300px'} center>
          <MntrCircularProgress size={40} />
        </Flex>
      )}
      {feed && (
        <Feed
          feedId={feedId}
          feedMapItem={feed}
          withView={true}
          withSelectActions={true}
          withSort={withSort}
          emptyFeedMessage={emptyFeedMessage}
        />
      )}

      {activeFeedMapItem && <DashboardInspectorWrapper isDashboard={false} feedId={feedId} />}
    </Box>
  )
}

export default observer(ReusableFeed)
