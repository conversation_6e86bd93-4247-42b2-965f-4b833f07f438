import { Instance, types } from 'mobx-state-tree'

export const AppSettings = types
  .model('AppSettings', {
    id: types.identifierNumber,
    appName: types.string,
    host: types.string,
    appDomain: types.string,
    sitePath: types.string,
    defaultAppLanguage: types.string,
    defaultDateFormat: 'd. M. yyyy',
    // contact
    salesEmail: types.string,
    contact: types.model('Contact', {
      phoneMain: types.maybeNull(types.string),
      phoneMobile: types.maybeNull(types.string),
      supportEmail: types.string,
    }),
    // urls
    roadmapUrl: types.maybeNull(types.string),
    TOSUrl: types.maybeNull(types.string),
    supportUrls: types.maybeNull(
      types.model('SupportUrls', {
        howToUseUrl: types.string,
        mediaAnalysisUrl: types.string,
        managementSummariesUrl: types.string,
        aveAndSentimentUrl: types.string,
        translationsUrl: types.string,
        smsAlertsUrl: types.string,
        crisisComUrl: types.string,
      }),
    ),
    mobileAppUrls: types.maybeNull(
      types.model('MobileApps', {
        appStore: types.string,
        playStore: types.string,
      }),
    ),
    // 3rd party config
    gtmProductId: types.string,
    og: types.maybeNull(
      types.model('OG', {
        image: types.string,
        locale: types.string,
      }),
    ),
    twitter: types.maybeNull(
      types.model('Twitter', {
        site: types.string,
      }),
    ),
    // functionality
    enableLanguageSelector: types.boolean,
    signupForm: types.model('SignupForm', {
      enableVatAutocomplete: types.boolean,
      enableCountrySelect: types.boolean,
    }),
    navigation: types.model('Navigation', {
      showSidebarPromoApps: types.model('ShowSidebarPromoApps', {
        tvr: types.boolean,
        medialist: types.boolean,
        newsroom: types.boolean,
        emailing: types.boolean,
      }),
    }),
    inspector: types.model('Inspector', {
      enableToggleView: types.boolean,
    }),
    enableIntercom: types.boolean,
    country: types.maybeNull(types.string),
  })
  .views((self) => {
    return {
      get primaryApp() {
        return self.id
      },
    }
  })

export interface IAppSettings extends Instance<typeof AppSettings> {}
