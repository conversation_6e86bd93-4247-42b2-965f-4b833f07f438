import * as Sentry from '@sentry/nextjs'
import App from 'next/app'
import { getPathMatch } from 'next/dist/shared/lib/router/utils/path-match'
import Router from 'next/router'
import { styled } from 'styled-components'
import HeaderWithObserver from '~/components/layout/Header/HeaderWithObserver'
import NotificationsWithObserver from '~/components/layout/Notifications/NotificationsWithObserver'
import MedialistInspectorWrapper from '~/components/medialist/content/MedialistInspector/MedialistInspectorWrapper'
import { GTM } from '~/components/misc/GTM/GTM'
import Head from '~/components/misc/Head/Head'
import Intercom from '~/components/misc/Intercom/Intercom'
import MntrProviders from '~/components/misc/MntrProviders/MntrProviders'
import MntrWebSocket from '~/components/misc/MntrWebSocket/MntrWebSocket'
import RouterWatcher from '~/components/misc/RouterWatcher/RouterWatcher'
import UploadWatcher from '~/components/misc/UploadWatcher/UploadWatcher'
import LoginContent from '~/components/page/auth/Login/Login'
import { MEDIABOARD_DEVS_CONTAINER } from '~/constants/gtm'
import getAppSettings from '~/helpers/getAppSettings'
import getRequestIp from '~/helpers/getRequestIp'
import getSessionInfo from '~/helpers/getSessionInfo'
import withSnowStorm from '~/helpers/hatchery/withSnowStorm'
import { getSnapshot } from '~/helpers/mst'
import { initStore } from '~/store/create'
import '~/styles/material-symbols.css'

const Wrapper = styled.div`
  background: ${({ theme }) => theme.colors.background};
  min-height: 100vh;
  main {
    padding-top: 54px;
  }
`

const Login = withSnowStorm(LoginContent)

export function reportWebVitals(metric) {
  switch (metric.name) {
    case 'Next.js-hydration':
    case 'Next.js-route-change-to-render':
      Sentry.getCurrentScope().setExtra('url', window.location.href)
      break

    default:
  }
}

export default class MntrApp extends App {
  static async getInitialProps(appContext) {
    const { ctx } = appContext
    const { asPath, pathname, query, req } = ctx
    const requestIp = await getRequestIp(ctx.req)
    const sessionInfo = await getSessionInfo(requestIp, ctx)
    const appSettings = await getAppSettings(sessionInfo, ctx)

    const store = initStore({
      appLanguage: appSettings.defaultAppLanguage,
      appSettings,
      filter: {},
      route: { asPath, pathname, query },
      session: {
        utm: Object.fromEntries(Object.entries(query).filter(([key]) => key.startsWith('utm_'))),
      },
    })

    store.appStore.filter.initFilter(pathname, query)

    appContext.ctx.store = store

    const appProps = await App.getInitialProps(appContext)

    return {
      ...appProps,
      snapshot: getSnapshot(store.appStore),
      userAgent: typeof window === 'undefined' ? req.headers['user-agent'] : navigator.userAgent,
    }
  }

  constructor(props) {
    super(props)
    this.store = initStore(props.snapshot)
  }

  routeChangeStart = (url) => {
    console.log('routeChangeStart')
    if (this.store.appStore.account.user.isPublic) {
      window.location.href = url
    }

    const { pathname, searchParams } = new URL(url, document.baseURI)
    const { query, topic_monitor, ...otherQueryParams } = Object.fromEntries(searchParams)

    console.table({
      curr: this.props.router.pathname,
      next: pathname,
    })

    // TODO: refactor inspector reset
    const { inspector } = this.store.appStore.monitoring
    const authorStore = this.store.appStore.authors
    if (inspector.annotation.isEnabled) {
      inspector.annotation.close()
    }

    if (inspector.mentions.isEnabled) {
      inspector.mentions.close()
    }

    if (inspector.mediaEditor.isEditing) {
      inspector.mediaEditor.reset()
    }

    if (inspector.isOpenArticle) {
      inspector.summary.reset()
    }

    if (authorStore.isOpenInspector) {
      if (!/^\/(article|author)/.test(url)) {
        authorStore.closeInspector()
      }
    }

    // Fix to display proper media length in player
    const { audioPlayer, videoPlayer } = this.store.appStore
    if (audioPlayer.isLoaded) {
      audioPlayer.init()
    }
    if (videoPlayer.isLoaded) {
      videoPlayer.init()
    }

    // Temporary reset - feed -> feedMapItem
    if (this.props.router.pathname !== pathname && pathname.split('/').length === 2) {
      this.store.appStore.filter.reset()
    }

    if (this.props.router.asPath === url) {
      // This will force a new /init/ to get proper user data
      if (this.store.appStore.account.user.isPublic) {
        window.location.href = url
      }

      console.log('same pathname, aborting handler...')
      return true
    }

    const getParams = [
      getPathMatch('/article/:articleId/:token'),
      getPathMatch('/author/:authorId'),
      getPathMatch('/authors/create'),
      getPathMatch('/crisis-communication-story/:articleId'),
    ].find((matcher) => {
      return matcher(pathname)
    })

    if (getParams) {
      const { articleId, authorId, token } = getParams(pathname)
      const pathnameStub = pathname.split('/')[1]

      switch (pathnameStub) {
        case 'article':
          if (!this.store.appStore.monitoring.activeFeedMapItem) {
            this.store.appStore.monitoring.activeFeedMapItem =
              localStorage.getItem('lastActiveFeedMapItem') || ''
          }

          // TODO: This could be wrapped in `activeFeedMapItem` check
          this.store.appStore.monitoring.inspector.loadArticle(false, {
            ...(topic_monitor ? { topic_monitor } : {}),
            articleId,
            query,
            token,
          })

          break

        case 'authors':
          if (this.store.appStore.monitoring.inspector.isOpenArticle) {
            this.store.appStore.monitoring.inspector.close(true)
          }

          this.store.appStore.authors.loadCreateAuthor()
          break

        case 'author':
          if (this.store.appStore.monitoring.inspector.isOpenArticle) {
            this.store.appStore.monitoring.inspector.close(true)
          }

          if (
            !this.store.appStore.authors.isOpenInspector ||
            this.store.appStore.authors.authorDetail.id !== authorId
          ) {
            this.store.appStore.authors.loadAuthor(authorId, {
              query,
              topic_monitor,
              ...otherQueryParams,
            })
          }

          break

        case 'crisis-communication-story':
          this.store.appStore.tvr.initStory(parseInt(articleId))
          break

        default:
      }
    }
  }

  routeChangeError = (err, ...yaaarghz) => {
    if (process.env.NEXT_PUBLIC_LOG_ROUTE_CHANGE_ERROR) {
      console.log({ err })
      console.log(err, ...yaaarghz)
    }
  }

  componentDidMount() {
    // ios safari 10+ fix
    document.addEventListener('gesturestart', (e) => {
      e.preventDefault()
    })

    document.addEventListener(
      'touchmove',
      (event) => {
        if (event.scale !== 1) {
          event.preventDefault()
        }
      },
      false,
    )

    let lastTouchEnd = 0
    document.addEventListener(
      'touchend',
      (event) => {
        const now = new Date().getTime()
        if (now - lastTouchEnd <= 300) {
          event.preventDefault()
        }
        lastTouchEnd = now
      },
      false,
    )

    Router.beforePopState(({ url, as, options: { shallow } }) => {
      console.log('beforePopState', url, as, shallow)
      const { pathname } = new URL(as, document.baseURI)

      const activeFeedMapItem = this.store.appStore.monitoring.activeFeedMapItem
      if (activeFeedMapItem) {
        localStorage.setItem('lastActiveFeedMapItem', activeFeedMapItem)
      }

      switch (pathname.split('/')[1]) {
        case 'author':
          break

        case '':
        default:
          this.store.appStore.authors.closeInspector()
          this.store.appStore.monitoring.inspector.close(true)
      }

      return true
    })

    Router.events.on('routeChangeStart', this.routeChangeStart)
    Router.events.on('routeChangeError', this.routeChangeError)
  }

  componentWillUnmount() {
    Router.events.off('routeChangeStart', this.routeChangeStart)
    Router.events.off('routeChangeError', this.routeChangeError)
  }

  render() {
    const { err, Component, pageProps } = this.props

    return (
      <MntrProviders store={this.store}>
        <>
          <RouterWatcher />
          <Head />
          <NotificationsWithObserver />
          <MntrWebSocket />
          <HeaderWithObserver />
          <Wrapper>
            <main>
              {pageProps.isWithAuth && !pageProps.token && !pageProps.isPublic ? (
                <Login />
              ) : (
                <Component {...pageProps} err={err} />
              )}
            </main>
          </Wrapper>
          <Intercom />
          <MedialistInspectorWrapper />
          <UploadWatcher />
          {process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER && (
            <GTM containerId={MEDIABOARD_DEVS_CONTAINER} />
          )}
        </>
      </MntrProviders>
    )
  }
}
