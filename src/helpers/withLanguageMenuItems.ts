import { locales } from '~/config'

type LocaleKey = keyof typeof locales

export default function withLanguageMenuItems(
  setLocale: (locale: string) => void,
  currentLocale: string,
) {
  return (Object.keys(locales) as LocaleKey[])
    .filter((locale) => locales[locale].active)
    .map((locale) => {
      return {
        flag: locale,
        label: locales[locale].label,
        onClick: () => {
          setLocale(locale)
        },
        ...(currentLocale === locale && {
          rightIcon: 'check',
        }),
      }
    })
}
