msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-07-31 13:12+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: sl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"
"Mime-Version: 1.0\n"
"X-Generator: Poedit 3.6\n"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:7
msgid "error"
msgstr "<i>Opravičujemo se, vendar imamo težave z našim AI asistentom. V<PERSON><PERSON>, da je frustriraj<PERSON><PERSON>, ko stvar<PERSON> ne <PERSON>, kot bi morale.<br> Poskusite znova čez nekaj časa.</i>"

#. placeholder {0}: data.word_count
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:305
msgid "(full text; {0} words)"
msgstr "(celotno besedilo; {0} besed)"

#: src/components/staff/admin/workspace/Workspace.js:781
msgid "(TVR) Allow automatic transcripts in monitoring"
msgstr "(TVR) Dovoli samodejne prepise v nadzoru"

#: src/components/staff/admin/workspace/Workspace.js:790
msgid "(TVR) Allow reruns in monitoring"
msgstr "(TVR) Dovoli ponovitve v spremljanju"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+access%7D+other+%7B%23+accesses%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:85
msgid "# access"
msgid_plural "# accesses"
msgstr[0] "# dostop"
msgstr[1] "# dostopa"
msgstr[2] "# dostopi"
msgstr[3] "# dostopi"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(item.article_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+article%7D+other+%7B%23+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:43
#: src/helpers/charts/formatters.js:55
#: src/components/reports/history/HistoryTable.js:199
#: src/components/analytics/AnalyticsContent.js:122
msgid "# article"
msgid_plural "# articles"
msgstr[0] "# članek"
msgstr[1] "# članka"
msgstr[2] "# članki"
msgstr[3] "# članki"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+attached+article%7D+other+%7B%23+attached+articles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:50
msgid "# attached article"
msgid_plural "# attached articles"
msgstr[0] "# priložen članek"
msgstr[1] "# priložena članka"
msgstr[2] "# priloženi članki"
msgstr[3] "# priloženi članki"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+author%7D+other+%7B%23+authors%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:57
msgid "# author"
msgid_plural "# authors"
msgstr[0] "# avtor"
msgstr[1] "# avtorja"
msgstr[2] "# avtorji"
msgstr[3] "# avtorji"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: inspector.data.versions_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+change%7D+other+%7B%23+changes%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:78
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleHistoryAction.js:22
msgid "# change"
msgid_plural "# changes"
msgstr[0] "# sprememba"
msgstr[1] "# spremembi"
msgstr[2] "# spremembe"
msgstr[3] "# spremembe"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+comment%7D+other+%7B%23+comments%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:89
msgid "# comment"
msgid_plural "# comments"
msgstr[0] "# komentar"
msgstr[1] "# komentarja"
msgstr[2] "# komentarji"
msgstr[3] "# komentarji"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+dislike%7D+other+%7B%23+dislikes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:109
msgid "# dislike"
msgid_plural "# dislikes"
msgstr[0] "# neodobravan"
msgstr[1] "# neodobravana"
msgstr[2] "# neodobravani"
msgstr[3] "# neodobravani"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+email%7D+other+%7B%23+emails%7D%7D&pluralize_on=0
#: src/components/emailing/helpers/emailing.plurals.js:3
msgid "# email"
msgid_plural "# emails"
msgstr[0] "# e-pošta"
msgstr[1] "# e-pošti"
msgstr[2] "# e-pošte"
msgstr[3] "# e-pošte"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(data.social_shares)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+interaction%7D+other+%7B%23+interactions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:76
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:78
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:120
msgid "# interaction"
msgid_plural "# interactions"
msgstr[0] "# interakcija"
msgstr[1] "# interakciji"
msgstr[2] "# interakcije"
msgstr[3] "# interakcije"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+like%7D+other+%7B%23+likes%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:69
msgid "# like"
msgid_plural "# likes"
msgstr[0] "# všeček"
msgstr[1] "# všečka"
msgstr[2] "# všečki"
msgstr[3] "# všečki"

#. placeholder {0}: parseInt(value)
#. placeholder {0}: parseInt(inspector.data.article_mentions_count)
#. placeholder {0}: parseInt(data.article_mentions_count)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+mention%7D+other+%7B%23+mentions%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:90
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:108
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:81
#: src/components/monitoring/Inspector/InspectorMonitora/StatusBar/ArticleMentionsActions.js:25
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:342
msgid "# mention"
msgid_plural "# mentions"
msgstr[0] "# omemba"
msgstr[1] "# omembi"
msgstr[2] "# omembe"
msgstr[3] "# omembe"

#. placeholder {0}: parseInt(jobs.length - 1)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+more%7D+other+%7B%23+more%7D%7D&pluralize_on=0
#: src/helpers/getAuthorJobs.js:16
msgid "# more"
msgid_plural "# more"
msgstr[0] "# več"
msgstr[1] "# več"
msgstr[2] "# več"
msgstr[3] "# več"

#. placeholder {0}: parseInt(n)
#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+output%7D+other+%7B%23+outputs%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:71
#: src/helpers/charts/formatters.js:69
msgid "# output"
msgid_plural "# outputs"
msgstr[0] "# izhodni podatek"
msgstr[1] "# izhodna podatka"
msgstr[2] "# izhodni podatki"
msgstr[3] "# izhodni podatki"

#. placeholder {0}: parseInt(value, 10)
#. placeholder {0}: payload.page_count
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+page%7D+other+%7B%23+pages%7D%7D&pluralize_on=0
#: src/components/monitoring/WorkspaceArticles/Limits.js:51
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:145
msgid "# page"
msgid_plural "# pages"
msgstr[0] "# stran"
msgstr[1] "# strani"
msgstr[2] "# strani"
msgstr[3] "# strani"

#. placeholder {0}: parseInt(value)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+person%7D+other+%7B%23+people%7D%7D&pluralize_on=0
#: src/helpers/charts/formatters.js:83
msgid "# person"
msgid_plural "# people"
msgstr[0] "# oseba"
msgstr[1] "# osebi"
msgstr[2] "# osebe"
msgstr[3] "# osebe"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+retweet%7D+other+%7B%23+retweets%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:79
msgid "# retweet"
msgid_plural "# retweets"
msgstr[0] "# posredovan tvit"
msgstr[1] "# posredovan tvita"
msgstr[2] "# posredovan tviti"
msgstr[3] "# posredovan tviti"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+share%7D+other+%7B%23+shares%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:119
msgid "# share"
msgid_plural "# shares"
msgstr[0] "# delitev"
msgstr[1] "# delitvi"
msgstr[2] "# delitve"
msgstr[3] "# delitve"

#. placeholder {0}: platform.interactions[interaction]
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+view%7D+other+%7B%23+views%7D%7D&pluralize_on=0
#: src/components/analytics/SocialEngagement.js:99
msgid "# view"
msgid_plural "# views"
msgstr[0] "# pogled"
msgstr[1] "# pogleda"
msgstr[2] "# pogledi"
msgstr[3] "# pogledi"

#. placeholder {0}: account.workspace.limits.media_archive_depth_limit
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%23+year%7D+other+%7B%23+years%7D%7D&pluralize_on=0
#: src/components/tariff/TariffLimits/TariffLimits.js:263
msgid "# year"
msgid_plural "# years"
msgstr[0] "# leto"
msgstr[1] "# leti"
msgstr[2] "# leta"
msgstr[3] "# leta"

#. placeholder {0}: item.recipients.length - shortEmailList.length
#. placeholder {0}: items.length - MAX_ITEMS
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+more%7D+other+%7B%2B%23+more%7D%7D&pluralize_on=0
#: src/components/reports/history/HistoryTable.js:373
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:73
msgid "+# more"
msgid_plural "+# more"
msgstr[0] "+# več"
msgstr[1] "+# več"
msgstr[2] "+# več"
msgstr[3] "+# več"

#. placeholder {0}: data.identical_articles.length
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7B%2B%23+other%7D+other+%7B%2B%23+other%7D%7D&pluralize_on=0
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:128
msgid "+# other"
msgid_plural "+# other"
msgstr[0] "+# drugo"
msgstr[1] "+# drugo"
msgstr[2] "+# drugo"
msgstr[3] "+# drugo"

#. placeholder {0}: 1
#. placeholder {0}: self.selector.selected.size
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BArticle+Removed%7D+other+%7BArticles+Removed%7D%7D&pluralize_on=0
#: src/store/models/monitoring/Inspector/Inspector.ts:497
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:659
#: src/store/models/emailing/campaignDetail/CampaignDetailStore/CampaignDetailStore.js:108
msgid "Article Removed"
msgid_plural "Articles Removed"
msgstr[0] "Članek izbrisan"
msgstr[1] "Članka izbrisana"
msgstr[2] "Članki izbrisani"
msgstr[3] "Članki izbrisani"

#. placeholder {0}: parseInt(n)
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7Barticle%7D+other+%7Barticles%7D%7D&pluralize_on=0
#: src/helpers/formatNumber.js:64
msgid "article"
msgid_plural "articles"
msgstr[0] "članek"
msgstr[1] "članka"
msgstr[2] "članki"
msgstr[3] "članki"

#. placeholder {0}: 1
#. js-lingui:icu=%7B0%2C+plural%2C+one+%7BItem+Removed%7D+other+%7BItems+Removed%7D%7D&pluralize_on=0
#: src/store/models/tvr/tvr.js:274
msgid "Item Removed"
msgid_plural "Items Removed"
msgstr[0] "Predmet odstranjen"
msgstr[1] "Predmeta odstranjena"
msgstr[2] "Predmeti odstranjeni"
msgstr[3] "Predmeti odstranjeni"

#: src/components/forms/dashboard/Search/SearchDeclensions.js:51
msgid "{appName} will search"
msgstr "{appName} bo iskal"

#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:206
msgid "{countriesWithActiveMedia} countries with monitoring enabled"
msgstr "{countriesWithActiveMedia} držav z omogočenim spremljanjem"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:107
msgid "{i} (current)"
msgstr "{i} (trenutno)"

#. js-lingui:icu=%7BolderCount%2C+plural%2C+one+%7B%2B+%23+older%7D+other+%7B%2B+%23+older%7D%7D&pluralize_on=olderCount
#: src/components/feed/InspectorToolbar/ToolbarPagination.js:32
msgid "+ # older"
msgid_plural "+ # older"
msgstr[0] "+ # starejši"
msgstr[1] "+ # starejša"
msgstr[2] "+ # starejši"
msgstr[3] "+ # starejši"

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+article+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+articles+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:517
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:754
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:800
msgid "You have reached the limit for this action. {processedCount} article has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} articles have been updated."
msgstr[0] "Dosegli ste omejitev za to dejanje. Posodobljen je bil {processedCount} članek."
msgstr[1] "Dosegli ste omejitev za to dejanje. Posodobljena sta bila {processedCount} članka."
msgstr[2] "Dosegli ste omejitev za to dejanje. Posodobljeni so bili {processedCount} članki."
msgstr[3] "Dosegli ste omejitev za to dejanje. Posodobljeni so bili {processedCount} članki."

#. js-lingui:icu=%7BprocessedCount%2C+plural%2C+one+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+author+has+been+updated.%7D+other+%7BYou+have+reached+the+limit+for+this+action.+%7BprocessedCount%7D+authors+have+been+updated.%7D%7D&pluralize_on=processedCount
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:553
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:590
#: src/store/models/authors/AuthorsStore.js:528
#: src/store/models/authors/AuthorsStore.js:581
#: src/store/models/authors/AuthorsStore.js:657
#: src/store/models/authors/AuthorsStore.js:709
msgid "You have reached the limit for this action. {processedCount} author has been updated."
msgid_plural "You have reached the limit for this action. {processedCount} authors have been updated."
msgstr[0] "Dosegli ste omejitev za to dejanje. Posodobljeni so bili {processedCount} avtorji."
msgstr[1] "Dosegli ste omejitev za to dejanje. Posodobljena sta bila {processedCount} članka."
msgstr[2] ""
msgstr[3] "Dosegli ste omejitev za to dejanje. Posodobljeni so bili {processedCount} avtorji."

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+post%7D+other+%7B%23+posts%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:97
msgid "# post"
msgid_plural "# posts"
msgstr[0] "# objava"
msgstr[1] "# objavi"
msgstr[2] "# objave"
msgstr[3] "# objave"

#. js-lingui:icu=%7Bvalue%2C+plural%2C+one+%7B%23+visit%7D+other+%7B%23+visits%7D%7D&pluralize_on=value
#: src/helpers/charts/formatters.js:62
msgid "# visit"
msgid_plural "# visits"
msgstr[0] "# obisk"
msgstr[1] "# obiska"
msgstr[2] "# obiski"
msgstr[3] "# obiski"

#: src/pages/newsroom/index.js:62
msgid "<0>Accurate</0> data is part of analytics."
msgstr "<0>Natančni</0> podatki so del analitike."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:96
msgid "help.engagementRate"
msgstr "<0>Engagement rate je metrika, ki se uporablja za ocenjevanje povprečnega števila interakcij, ki jih objava prejme na enega sledilca. Pomaga pri relativni primerjavi omemb iz različnih kanalov in virov. S pomočjo engagement rate lahko: </0><1><2>ugotovite, katere objave imajo najboljše in najslabše rezultate, </2><3>primerjate stopnjo vključenosti, ki jo ustvarjate na različnih družbenih omrežjih, </3><4>primerjate svoje rezultate s konkurenco, </4><5>ovrednotite vplivneže. </5></1>"

#: src/pages/authors/index.js:69
msgid "<0>Export</0> detailed lists of authors"
msgstr "<0>Izvozi</0> podrobne sezname avtorjev"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:21
msgid "<0>Immediate notifications</0> about mentions on <1>TV and radio</1>. Be in the swim of things. Non-stop."
msgstr "<0>Takojšnja obvestila</0> o omembah na <1>TV in radiu</1>. Bodite vedno na tekočem. Non-stop."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:80
msgid "help.influenceScore"
msgstr "<0>Vplivna ocena je številka (od 1 do 10), izračunana za vsako omembo iz družbenih medijev. Njena vrednost temelji predvsem na dveh parametrih:</0><1><2>kako verjetno je, da bo določena omemba videna,</2><3>kolikokrat je bila omemba prikazana, deljena ali retvitana.</3></1><4>Verjamemo, da vam bo ta vrednost pomagala odkriti omembe, avtorje in spletna mesta, ki so najbolj priljubljena in vplivna. Na ta način lahko pridobite dodatno metriko za analizo vaših marketinških kampanj.</4>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:38
msgid "<0>Non-stop</0> monitoring of selected TVs and radios"
msgstr "<0>Nonstop</0> spremljanje izbranih TV in radijskih postaj"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesSearch.js:21
msgid "help.search.wordSearch.description"
msgstr "<0>Hiter pregled</0><1><2><3><4><5>Vneseni izraz</5><6>Kaj {appName} naredi</6><7>Poišče članke, ki vsebujejo</7></4></3><8><9><10>Pingvin</10><11>besedo sklanja, diakritika in velikost črk nista pomembni</11><12>Pingvin, PINGVIN, pingvin, Pingvina, pingvini, pingvin</12></9><13><14>\"Pingvin\"</14><15>besede ne sklanja, diakritika je pomembna, velikost črk ni pomembna</15><16>Pingvin, PINGVIN, pingvin</16></13><17><18>\"!Pingvin\"</18><19>besede ne sklanja, diakritika in velikost črk sta pomembni</19><20>Pingvin</20></17></8></2></1><21>S sklanjanjem</21><22>Če v iskalno polje vpišemo: <23>Pingvin</23></22><24>{appName} poišče vse članke, ki vsebujejo besedo <25>Pingvin</25> v katerikoli obliki. Tako bodo najdeni članki, ki vsebujejo besedo <26>Pingvina</26> ({appName} besedo sklanja), <27>PINGVIN</27> (velikost črk ni pomembna) ali <28>pingvin</28> (diakritika ni pomembna).</24><29>Priporočamo, da na ta način iščete vse besede, ki se v običajnem besedilu sklanjajo. To so običajno splošne besede (pingvin), lastna imena (Miha) ali tuji nazivi (facebook).</29><30>Natančno ujemanje</30><31>Če v iskalno polje vpišemo: <32>\"Pingvin\"</32> (besedo damo v narekovaje)</31><33>{appName} poišče vse članke, ki vsebujejo besedo <34>Pingvin</34>, vendar le v navedeni obliki (torej besede ne sklanja). Tako bodo najdeni članki, ki vsebujejo besedo <35>Pingvin</35> ali<36>PINGVIN</36> (velikost črk ni pomembna).</33><37>{appName} ne bo našel tistih člankov, ki vsebujejo le sklanjano besedo <38>Pingvina</38> ali besedo <39>pingvin</39> brez diakritike.</37><40>Priporočamo, da na ta način iščete imena podjetij in izdelkov (\"McDonald's\"), spletne domene (\"{appName}.si\"), natančno ujemanje besede (\"najboljši\") ali kratice (\"ZDA\").</40><41>Natančno ujemanje vključno z velikostjo črk</41><42>Če v iskalno polje vpišemo: <43>\"!Pingvin\"</43> (besedo damo v narekovaje in za prvi narekovaj klicaj)</42><44>{appName} poišče vse članke, ki vsebujejo besedo <45>Pingvin</45>, vendar le v navedeni obliki, vključno z velikostjo črk. To je najstrožja različica.</44><46>{appName} ne bo našel tistih člankov, ki vsebujejo npr. le besedo <47>pingvin</47> napisano z malimi črkami.</46><48><49>Priporočamo, da na ta način iščete imena podjetij in izdelkov (\"!Seznam\") ali kratice (\"!WHO\").</49></48>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesOperators.js:21
msgid "help.search.operators.description"
msgstr "<0>Hiter pregled</0><1><2><3><4>Vneseni izraz</4><5>{appName} bo iskal</5></3></2><6><7><8>pingvin AND tjulenj</8><9>Članki, ki vsebujejo obe besedi <10>pingvin</10> in <11>tjulenj</11>.</9></7><12><13>pingvin tjulenj</13><14>Članki, ki vsebujejo obe besedi <15>pingvin</15> in <16>tjulenj</16>. Presledek med besedami deluje enako, kot če bi bil tam operator AND.</14></12><17><18>pingvin OR tjulenj</18><19>Članki, ki vsebujejo vsaj eno od besed <20>pingvin</20> ali <21>tjulenj</21>.</19></17><22><23>pingvin -tjulenj</23><24>Članki, ki vsebujejo besedo <25>pingvin</25>, vendar ne vsebujejo besede <26>tjulenj</26>.</24></22></6></1><27>AND</27><28>Če želimo poiskati članke, ki vsebujejo več besed ali besednih zvez hkrati, vnesemo vse zahtevane besede in jih ločimo bodisi s presledkom bodisi z besedo <29>AND</29> (napisano z velikimi črkami).</28><30>Če v iskalno polje vpišemo: <31>pingvin tjulenj Ljubljanski+živalski+vrt \"!GEN-I\"</31></30><32>Je enako, kot če bi napisali: <33>pingvin AND tjulenj AND Ljubljanski+živalski+vrt AND \"!GEN-I\"</33></32><34>OR</34><35>Če želimo poiskati članke, ki vsebujejo vsaj eno od vnesenih besed ali besednih zvez, vnesemo vse zahtevane besede in jih ločimo z besedo <36>OR</36> (napisano z velikimi črkami).</35><37>Primer: <38>pingvin OR tjulenj OR Ljubljanski+živalski+vrt OR \"!GEN-I\"</38></37><39>NOT</39><40>Če želimo iz iskalnih rezultatov odstraniti članke, ki vsebujejo določene besede ali besedne zveze, za iskalni izraz napišemo seznam prepovedanih besed in besednih zvez ter pred vsako od njih postavimo znak minus.</40><41>Primer: <42>pingvin -tjulenj -Ljubljanski+živalski+vrt -\"!GEN-I\"</42></41><43>Oklepaji</43><44>Iskalne operaterje je mogoče kombinirati po potrebi. Pri bolj zapletenih izrazih pa pogosto potrebujemo določiti tudi vrstni red, v katerem želimo, da se iskalni operaterji ovrednotijo. Za ta namen uporabimo oklepaje, ki delujejo podobno kot v matematiki.</44><45>Če v iskalno polje vpišemo: <46>\"!Mercator\" AND (trgovina OR veriga OR prodajalna OR supermarket OR hipermarket)</46></45><47>{appName} bo poiskal vse članke, ki vsebujejo besedo <48>Mercator</48> (samo v vneseni obliki, vključno z velikostjo črk) v povezavi z vsaj eno od besed <49>trgovina</49>, <50>veriga</50>, …</47><51>Na koncu primer, kako kompleksne izraze lahko v aplikaciji sestavite iz iskalnih operaterjev: <52>naselje (hiša OR stavba OR zgradba) AND (balkon OR (plastična+okna -\"!Velux\")) AND Ljubljana+Bežigrad~5 -(Šiška OR Vič)</52></51>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesPhrase.js:21
msgid "help.search.phrase.description"
msgstr "<0>Hiter pregled</0><1><2><3><4>Vneseni izraz</4><5>Iskanje člankov, ki vsebujejo</5></3></2><6><7><8>Pameten+Janez</8><9>Pameten Janez, PAMETEN JANEZ, pameten janez, Pametnega Janeza, pameten janez, pameten-janez</9></7><10><11>\"Pameten Janez\"</11><12>Pameten Janez, PAMETEN JANEZ, pameten janez</12></10><13><14>\"Pameten-Janez\"</14><15>Pameten-Janez, PAMETEN-JANEZ, pameten-janez</15></13><16><17>\"!Pameten Janez\"</17><18>Pameten Janez</18></16></6></1><19>Z upoštevanjem sklanjatev</19><20>Če v iskalno polje vpišemo: <21>Pameten+Janez</21> (besede ločimo z znakom plus, tako da med njimi ni presledka)</20><22>{appName} poišče vse članke, ki vsebujejo besedno zvezo <23>Pameten Janez</23> (torej te besede v tem zaporedju) v katerikoli obliki. Tako bodo najdeni članki, ki vsebujejo besedno zvezo <24>Pametnega Janeza</24> ({appName} upošteva sklanjatve), <25>PAMETEN JANEZ</25> (velikost črk ni pomembna), <26>pameten janez</26> (diakritika ni pomembna) ali <27>pameten-janez</27> (med besedami je lahko tudi ločilo, npr. vejica ali pomišljaj).</22><28>Priporočamo, da na ta način iščete imena oseb (Janez+Novak), imena podjetij in organizacij (Ministrstvo+za+okolje) ali besedne zveze (medijski+monitoring).</28><29>Natančno ujemanje</29><30>Če v iskalno polje vpišemo: <31>\"Pameten Janez\"</31> (celoten izraz damo v narekovaje)</30><32>{appName} poišče vse članke, ki vsebujejo besedno zvezo <33>Pameten Janez</33>, vendar le v navedeni obliki. Tako bodo najdeni članki, ki vsebujejo <34>Pameten Janez</34> ali <35>PAMETEN JANEZ</35> (velikost črk ni pomembna).</32><36>{appName} ne bo našel člankov, ki vsebujejo le sklanjano besedno zvezo <37>Pametnega Janeza</37>, besedno zvezo <38>pameten janez</38> brez diakritike ali besedno zvezo <39>pameten-janez</39> z ločilom.</36><40>Priporočamo, da na ta način iščete imena podjetij in izdelkov (\"{appName} Media\"), kratice (\"MFF UK\") ali natančno ujemanje zvez (\"biti ali ne biti\").</40><41>Natančno ujemanje z ločilom</41><42>Pri iskanju natančnega ujemanja je potrebno v narekovaje vključiti tudi ločila, ki se lahko pojavijo med besedami - pomišljaji, podčrtaji, afne itd. To velja za naslednje znake: & @ _ + - ' #</42><43>Če v iskalno polje vpišemo: <44>\"Pameten-Janez\"</44></43><45>{appName} poišče vse članke, ki vsebujejo besedno zvezo <46>Pameten-Janez</46> z ločilom.</45><47>{appName} ne bo našel člankov, ki vsebujejo le besedno zvezo <48>Pameten Janez</48> brez ločila ali <49>Pameten&Janez</49> z drugim ločilom, kot smo ga navedli.</47><50>Tipične besedne zveze, pri katerih ne pozabite na ločilo, so \"Ernst & Young\", \"info@{appName}.si\", \"Mi+Te\" ali \"X-Men\".</50><51>Natančno ujemanje vključno z velikostjo črk</51><52>Če v iskalno polje vpišemo: <53>\"!Pameten Janez\"</53> (celoten izraz damo v narekovaje in za prvi narekovaj klicaj)</52><54>{appName} poišče vse članke, ki vsebujejo besedno zvezo <55>Pameten Janez</55>, vendar le v navedeni obliki, vključno z velikostjo črk. To je najstrožja varianta.</54><56>{appName} ne bo našel člankov, ki vsebujejo npr. le besedno zvezo <57>pameten janez</57> napisano z malimi črkami.</56><58>Priporočamo, da na ta način iščete imena podjetij in izdelkov (\"!Zlata žlica\") ali kratice (\"!SAZU\").</58>"

#. js-lingui-explicit-id
#: src/components/help/search/Content/RulesDistance.js:21
msgid "help.search.distance.description"
msgstr "<0>Hiter pregled</0><1><2><3><4>Vneseni izraz</4><5>Iskanje člankov, ki vsebujejo</5></3></2><6><7><8>tjulenj+pingvin~5</8><9>pingvin je pobožal tjulnja</9></7><10><11>tukan+tjulenj+pingvin~10</11><12>tukani so želeli pojesti pingvina, vendar je tjulenj posredoval</12></10><13><14>\"tjulenj tukan\"~5</14><15>\"Tjulenj!\" je rekel tukan in odletel.</15></13><16><17>\"tukan tjulenj pingvin\"~5</17><18>Na fotografiji z leve: pingvin, tjulenj, tukan.</18></16></6></1><19>Z upoštevanjem sklanjatev</19><20>Če v iskalno polje vpišemo: <21>tjulenj+pingvin~5</21> (besede ločimo z znakom plus in za njimi napišemo tildo in številko)</20><22>{appName} poišče vse članke, ki vsebujejo besedi <23>tjulenj</23> in <24>pingvin</24> v poljubnem vrstnem redu in na razdalji največ 5 besed med seboj. Vnesene besede se samodejno sklanjajo in velikost črk ali diakritika ne igrata vloge.</22><25>Priporočamo, da na ta način iščete besede, ki so med seboj povezane in bodo v besedilu članka blizu skupaj (podjetje+Facebook~7).</25><26>Natančno ujemanje</26><27>Če v iskalno polje vpišemo: <28>\"tjulenj pingvin\"~5</28> (besede damo v narekovaje in za drugim narekovajem napišemo tildo in številko)</27><29>{appName} poišče vse članke, ki vsebujejo besedi <30>tjulenj</30> in <31>pingvin</31> v poljubnem vrstnem redu in na razdalji največ 5 besed med seboj. Obe vneseni besedi se iščeta le v vneseni obliki, tj. ne sklanjata se in diakritika igra vlogo.</29>"

#: src/pages/newsroom/index.js:35
msgid "<0>Share press releases</0> and other external and internal communication with <1>Newsroom</1> and have an accurate overview of traffic directly in the application."
msgstr "<0>Delite sporočila za javnost</0> in drugo zunanjo ter notranjo komunikacijo z <1>Medijskim središčem</1> in pridobite natančen pregled prometa neposredno v aplikaciji."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:40
msgid "help.ave"
msgstr "<0>Koeficient AVE (Advertising Value Equivalent) predstavlja finančno ovrednotenje medijskih aktivnosti. Je ekvivalent tega, koliko bi stal prostor, pridobljen z vsebino, preračunano na vrednost oglasnega prostora po ceniku določenega medija.</0><1>Za strojni izračun AVE uporabljamo naslednje spremenljivke:</1><2><3>enotna cena oglaševanja v določenem mediju (npr: cena za normostrani v tisku / 1s predvajane novice na TV ali radiu)</3><4>velikost članka v tisku / dolžina reportaže na TV ali radiu</4><5>obseg informacij, namenjenih temi v okviru prispevka</5></2>"

#. js-lingui-explicit-id
#: src/components/layout/Header/MessageDirty/MessageDirty.js:12
msgid "message.dirty.description"
msgstr "<0>Prikazani podatki morda ne ustrezajo vašim trenutnim nastavitvam, ker je bila ena ali več tem spremenjenih.</0><1>Osvežite stran čez nekaj minut.</1>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:113
msgid "help.socialInteractions"
msgstr "<0>Število socialnih interakcij (všeček, delitev, komentar, ogled, retweet) pri omembah.</0>"

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:67
msgid "help.socialInteractionsOnline"
msgstr "<0>Število socialnih interakcij (všeček, delitev, komentar) pri spletnih člankih na Facebooku.</0><1>Statistika se posodobi enkrat na 24 ur.</1>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:11
msgid "missingBasics"
msgstr "<div>Hvala za oddajo prispevka. Opravili smo začetni pregled, da se prepričamo, da izpolnjuje naše osnovne zahteve.</div><br> <strong>Ugotovili smo naslednje:</strong>"

#. js-lingui-explicit-id
#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:15
msgid "responseInfo"
msgstr ""
"Ta postopek lahko traja nekaj časa, saj izvajamo naslednje korake:</div>\n"
" <ol>\n"
" <li><strong>Začetna kontrola:</strong> Hiter pregled celotne strukture in formata vaše vsebine.</li>\n"
" <li><strong>Poglobljena analiza:</strong> Skrbno preučevanje podrobnosti, jezika in konteksta vašega prispevka.</li>\n"
" <li><strong>Ocena kakovosti:</strong> Ocena različnih vidikov, kot so jasnost, kohezivnost in ustreznost.</li>\n"
" <li><strong>Odkrivanje napak:</strong> Identifikacija morebitnih težav, neskladnosti ali področij, ki jih je treba izboljšati.</li>\n"
" <li><strong>Predlogi za optimizacijo:</strong> Priprava priporočil za izboljšanje vsebine, če je potrebno.</li>\n"
" </ol>\n"
" <div>Naša umetna inteligenca si prizadeva, da vam zagotovi natančne in koristne povratne informacije. Cenimo vašo potrpežljivost med to kompleksno analizo. Rezultati bodo kmalu na voljo.</div>"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:124
#: src/components/newsroom/components/PostsList/PostsList.js:98
msgid "<No title yet>"
msgstr "<Brez naslova>"

#: src/store/models/admin/customer/CustomerStore.js:220
msgid "<user already exists>"
msgstr "<uporabnik že obstaja>"

#: src/components/tariff/TariffLimits/TariffLimits.js:26
#: src/components/staff/admin/workspace/Workspace.js:359
msgid "30-day article limit"
msgstr "30-dnevna omejitev števila člankov"

#: src/components/tariff/UsageTracker/UsageTracker.js:13
msgid "30-day limit"
msgstr "30-dnevna omejitev"

#: src/components/tariff/TariffLimits/TariffLimits.js:63
#: src/components/staff/admin/workspace/Workspace.js:378
msgid "30-day limit on exported articles"
msgstr "30-dnevna omejitev za izvoz člankov"

#: src/components/tariff/TariffLimits/TariffLimits.js:224
#: src/components/staff/admin/workspace/Workspace.js:559
msgid "30-day limit on exported social media mentions"
msgstr "30-dnevna omejitev za izvoz omemb na družbenih omrežjih"

#: src/components/tariff/TariffLimits/TariffLimits.js:241
#: src/components/staff/admin/workspace/Workspace.js:514
msgid "30-day limit on licensed article downloads"
msgstr "30-dnevna omejitev za prenos licenciranih člankov"

#: src/components/staff/admin/workspace/Workspace.js:629
msgid "30-day limit on OCR pages"
msgstr "30-dnevna omejitev za strani OCR"

#: src/components/tariff/TariffLimits/TariffLimits.js:203
#: src/components/staff/admin/workspace/Workspace.js:540
msgid "30-day limit on social media mentions"
msgstr "30-dnevna omejitev na število omemb na družbenih omrežjih"

#: src/components/staff/admin/workspace/Workspace.js:650
msgid "30-day limit on transcribed seconds"
msgstr "30-dnevna omejitev na število prepisanih sekund"

#: src/components/staff/admin/workspace/Workspace.js:399
msgid "30-day limit on translated articles with Google Translate"
msgstr "30-dnevna omejitev za število prevedenih člankov prek Google Translate"

#: src/components/medialist/forms/FormEditAuthor.js:755
msgid "About Author"
msgstr "O avtorju"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:132
msgid "Above avg."
msgstr "Nadpovprečno"

#: src/components/tariff/Permissions/Permissions.js:45
msgid "Access"
msgstr "Dostop"

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:52
msgid "Access comprehensive articles via {appName}’s media monitoring, covering online, traditional, and social media content."
msgstr "Dostopajte do celovitih člankov prek spremljanja medijev aplikacije {appName}, ki pokriva vsebine spletnih, tradicionalnih in družbenih medijev."

#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:49
msgid "Access Full Articles via Media Monitoring"
msgstr "Dostop do celotnih člankov prek spremljanja medijev"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:37
msgid "Access to this dashboard has expired."
msgstr "Dostop do te nadzorne plošče je potekel."

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:22
msgid "Account info"
msgstr "Podatki o računu"

#: src/components/misc/Changelog/ChangelogTableRow.js:114
msgid "Account manager"
msgstr "Upravitelj računa"

#: src/components/staff/admin/customer/bio/CustomerBio.js:112
msgid "Account managers"
msgstr "Upravitelji računov"

#: src/components/settings/SettingsHeader/SettingsHeader.js:8
msgid "Account settings"
msgstr "Nastavitve računa"

#: src/components/settings/SettingsTheme/SettingsTheme.js:11
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:87
msgid "Account theme"
msgstr "Videz računa"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:57
msgid "Activate"
msgstr "Aktiviraj"

#: src/components/staff/admin/user/User.js:218
msgid "Activated"
msgstr "Aktiviran"

#: src/components/emailing/content/EmailingSettingsContent.js:76
#: src/components/emailing/content/EmailingCampaignsContent.tsx:32
msgid "Activated senders without verification:"
msgstr "Aktivirani pošiljatelji brez napredne verifikacije:"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:121
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/forms/dashboard/Search/SearchUsers.js:99
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Active"
msgstr "Aktiven"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:80
msgid "Active Article Language"
msgstr "Dovoljeni jeziki člankov"

#: src/components/staff/admin/workspace/ToggleActiveMedia.js:29
msgid "Active only"
msgstr "Samo aktivno"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:34
msgid "Active report"
msgstr "Aktivno poročilo"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:93
msgid "Active Source Country"
msgstr "Dovoljene države pri člankih"

#: src/components/medialist/content/MedialistAuthorCreate.js:16
msgid "Activity Overview"
msgstr "Pregled dejavnosti"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:54
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:592
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:61
#: src/components/medialist/forms/modules/FormArray.js:198
#: src/components/medialist/forms/modules/FormArray.js:220
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:38
#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:190
#: src/components/emailing/forms/FormEmailRecipients.js:120
#: src/components/ReusableFeed/FormAddArticle.tsx:42
msgid "Add"
msgstr "Dodaj"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:178
msgid "Add a sender to activate Emailing."
msgstr "Dodajte pošiljatelja za aktivacijo e-pošte."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:92
msgid "Add all to selection"
msgstr "Dodaj vse k izbiri"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:96
msgid "Add annotation"
msgstr "Dodaj opombo"

#: src/helpers/modal/withModalAddArticle/withModalAddArticle.tsx:17
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:95
#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:156
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:90
#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:10
msgid "Add article"
msgstr "Dodaj članek"

#: src/components/emailing/content/mediaCoverage/AddArticleToMediaCoverage.tsx:13
msgid "Add article media coverage"
msgstr "Dodaj članek v medijsko pokritost"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:237
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:219
msgid "Add article to topic"
msgstr "Dodaj članek k temi"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:537
#: src/components/newsroom/content/post/AttachmentsList.js:89
msgid "Add Attachment"
msgstr "Dodaj prilogo"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:129
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:100
msgid "Add authors to list"
msgstr "Dodaj avtorje na seznam"

#: src/components/newsroom/forms/FormNewsroomPost/CategoriesSelector.js:71
msgid "Add Category"
msgstr "Dodaj kategorijo"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:118
msgid "Add content"
msgstr "Dodaj vsebino"

#: src/components/dashboards/DashboardSelector/CreateDashboard.js:20
msgid "Add Dashboard"
msgstr "Dodaj nadzorno ploščo"

#: src/components/reports/Content/ReportsList/AddDay.js:25
msgid "Add day"
msgstr "Dodaj dan"

#: src/components/staff/admin/workspace/Workspace.js:827
msgid "Add domains separated by a comma (domain1.com, domain2.com)"
msgstr "Dodajte domene, ločene z vejico (domain1.com, domain2.com)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:79
msgid "Add Gallery"
msgstr "Dodaj galerijo"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:66
msgid "Add Image"
msgstr "Dodaj sliko"

#: src/components/topics/Content/TopicsList/FormAddKeyword/FormAddKeyword.tsx:45
msgid "Add Keyword"
msgstr "Dodaj ključno besedo"

#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:91
msgid "Add language variant"
msgstr "Dodaj jezikovno različico"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:109
msgid "Add main message"
msgstr "Dodaj glavno sporočilo"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:57
msgid "Add manually"
msgstr "Dodaj ročno"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:117
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:137
msgid "Add missing data"
msgstr "Dodaj manjkajoče podatke"

#: src/components/emailing/components/EmailRecipientsList/RecipientsButton.tsx:37
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:164
msgid "Add Missing Info"
msgstr "Dodaj manjkajoče informacije"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:64
msgid "Add new keypoint"
msgstr "Dodaj novo ključno točko"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:111
msgid "Add new mediatypes"
msgstr "Dodaj nove mediatipe"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:84
msgid "Add new quote"
msgstr "Dodaj nov citat"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:52
msgid "Add new sender"
msgstr "Dodaj novega pošiljatelja"

#: src/components/topics/Content/TopicsList/TopicsList.js:63
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:44
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:70
msgid "Add New Topic"
msgstr "Dodaj novo temo"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:28
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Add note"
msgstr "Dodaj opombo"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:37
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:57
msgid "Add note to article"
msgstr "Dodaj opombo k članku"

#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:55
msgid "Add recipient"
msgstr "Dodaj prejemnika"

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:32
#: src/components/emailing/content/tabs/AddRecipients.tsx:78
msgid "Add recipients"
msgstr "Dodaj prejemnike"

#: src/components/emailing/content/Signature.tsx:113
#: src/components/emailing/content/Signature.tsx:116
msgid "Add signature"
msgstr "Dodaj podpis"

#: src/components/emailing/forms/FormEmailRecipients.js:112
msgid "Add single authors, author’s lists or emails"
msgstr "Dodajanje posameznih avtorjev, seznamov avtorjev ali e-poštnih naslovov"

#: src/components/newsroom/content/modules/CustomQuotes.tsx:35
msgid "Add specific quotes you want to include in your article, along with the name of the person being quoted. We will use these quotes exactly as provided.\""
msgstr "Dodajte specifične citate, ki jih želite vključiti v svoj članek, skupaj z imenom osebe, ki jo citirate. Te citate bomo uporabili točno tako, kot so navedeni."

#: src/components/reports/Content/ReportsList/AddTime.js:27
msgid "Add time"
msgstr "Dodaj čas"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Add to Dashboard"
msgstr "Dodaj na nadzorno ploščo"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:179
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:210
msgid "Add to export"
msgstr "Dodaj v izvoz"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:38
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:94
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:150
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:207
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:130
msgid "Add to filters"
msgstr "Dodaj k filtrom"

#: src/components/medialist/forms/FormEditAuthor.js:670
#: src/components/medialist/content/withAddToBasketPopup.js:44
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:26
msgid "Add to list"
msgstr "Dodaj na seznam"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:263
msgid "Add to next report"
msgstr "Dodaj v naslednje poročilo"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Add to report"
msgstr "Dodaj v poročilo"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:91
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:52
msgid "Add to search"
msgstr "Dodaj v iskanje"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:58
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:60
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:895
msgid "Add Topic"
msgstr "Dodaj temo"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:41
#: src/components/settings/SettingsUserManagement/AddUsers.tsx:23
msgid "Add users"
msgstr "Dodaj uporabnike"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:42
msgid "Add users to workspace"
msgstr "Dodaj uporabnike v delovni prostor"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:101
msgid "Add Video"
msgstr "Dodaj video"

#: src/components/dashboards/Content.js:89
#: src/components/dashboards/Content.js:90
msgid "Add Widget"
msgstr "Dodaj gradnik"

#: src/store/models/ExportStore.js:316
#: src/store/models/monitoring/Inspector/Inspector.ts:449
msgid "Added to export."
msgstr "Dodano v izvoz."

#: src/store/models/monitoring/Inspector/Inspector.ts:422
msgid "Added to next report."
msgstr "Dodano v naslednje poročilo."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:29
msgid "Additional settings"
msgstr "Dodatne nastavitve"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:8
msgid "Address"
msgstr "Naslov"

#: src/constants/analytics.js:143
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:42
#: src/components/misc/ActionsBar/View/ViewMenu.js:237
msgid "Adjusted Reach"
msgstr "Prilagojen doseg"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:160
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:162
#: src/components/staff/admin/workspace/Workspace.js:162
#: src/components/staff/admin/user/getUserAttributes.js:9
#: src/components/staff/admin/user/User.js:88
#: src/components/reports/history/HistoryTable.js:452
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:60
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:62
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:406
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:413
#: src/components/medialist/forms/FormEditAuthor.js:396
#: src/components/medialist/forms/FormEditAuthor.js:542
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:291
#: src/components/layout/Header/UserMenu/UserMenu.js:159
msgid "Admin"
msgstr "Admin"

#: src/components/reports/Content/ReportsList/ReportsForm.js:331
#: src/components/forms/dashboard/ExportResend/ExportResend.js:133
msgid "Advanced attachment settings"
msgstr "Napredne nastavitve priloge"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:15
msgid "Advanced export settings"
msgstr "Napredne nastavitve izvoza"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:79
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:174
msgid "Advanced settings"
msgstr "Napredne nastavitve"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:58
msgid "Advanced template settings"
msgstr "Napredne nastavitve predloge"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:256
msgid "Advertising Value Equivalency"
msgstr "Advertising Value Equivalency"

#: src/constants/analytics.js:101
#: src/constants/analytics.js:621
#: src/constants/analytics.js:755
#: src/components/layout/AuthWrapper/constants/features.slides.js:191
msgid "Advertising Value Equivalent (AVE)"
msgstr "Advertising Value Equivalent (AVE)"

#: src/constants/analytics.js:99
msgid "Advertising Value Equivalent (AVE) by sentiment"
msgstr "Advertising Value Equivalent (AVE) po sentimentu"

#: src/components/staff/admin/workspace/Workspace.js:920
#: src/components/settings/SettingsTariff/SettingsTariff.js:37
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:48
msgid "Agency media"
msgstr "Agencijske novice"

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:88
msgid "AI check"
msgstr "AI preverjanje"

#: src/components/newsroom/components/AiTools/AiCheckLoadingInfo.tsx:28
msgid "AI Checkup information"
msgstr "Informacije o AI pregledu članka"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:218
msgid "Align Center"
msgstr "Poravnaj na sredino"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:230
msgid "Align Justify"
msgstr "Poravnaj obojestransko"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:212
msgid "Align Left"
msgstr "Poravnaj levo"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:224
msgid "Align Right"
msgstr "Poravnaj desno"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:83
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:37
#: src/components/reports/history/HistoryTable.js:86
#: src/components/reports/history/HistoryTable.js:96
#: src/components/reports/history/HistoryTable.js:331
#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:123
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:144
#: src/components/misc/portable/PortableExport/CounterTitle.js:8
#: src/components/misc/ActionsBar/Selector/Selector.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:146
#: src/components/analytics/AnalyticsContent.js:156
#: src/components/analytics/AnalyticsContent.js:166
msgid "All"
msgstr "Vse"

#: src/store/models/ExportStore.js:318
msgid "All articles are already in export."
msgstr "Vsi članki so že v izvozu."

#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:110
#: src/components/exportList/Content/Content.tsx:97
msgid "All articles will be removed from export."
msgstr "Vsi članki bodo odstranjeni iz izvoza."

#: src/components/misc/portable/PortableExport/CounterTitle.js:10
msgid "All except"
msgstr "Vse razen"

#: src/components/layout/AuthWrapper/constants/features.slides.js:400
msgid "All features of the browser app are accessible on a mobile device. The app keeps you informed even when you are drinking a morning cup of coffee."
msgstr "Vse funkcije spletne aplikacije so dostopne tudi na mobilni napravi. Aplikacija vas obvešča, tudi ko pijete jutranjo kavo."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
msgid "all mediatypes for"
msgstr "vse mediatipi za"

#: src/store/models/Megalist/MegalistFilter.js:49
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:11
msgid "All Sources"
msgstr "Vsi viri"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:40
msgid "All topics"
msgstr "Vse teme"

#: src/components/medialist/forms/FormEditAuthor.js:576
msgid "All unsaved changes will be lost. Do you really want to cancel the changes?"
msgstr "Vse neshranjene spremembe bodo izgubljene. Ali res želite preklicati spremembe?"

#: src/components/staff/admin/workspace/Workspace.js:743
msgid "Allow adjusted reach (PL)"
msgstr ""

#: src/components/staff/admin/workspace/Workspace.js:716
msgid "Allow automatic sentiment"
msgstr "Dovoli samodejno zaznavanje sentimenta"

#: src/components/staff/admin/workspace/Workspace.js:725
msgid "Allow automatic summarization"
msgstr "Dovoli samodejno povzemanje"

#: src/components/staff/admin/workspace/Workspace.js:734
msgid "Allow AVE"
msgstr "Dovoli AVE"

#: src/components/staff/admin/workspace/Workspace.js:752
msgid "Allow AVE Coefficient (for media analysis)"
msgstr "Dovoli AVE koeficient (za medijsko analizo)"

#: src/components/staff/admin/workspace/Workspace.js:707
msgid "Allow custom logo"
msgstr "Dovoli naložitev lastnega logotipa"

#: src/components/staff/admin/workspace/Workspace.js:598
msgid "Allow english social media"
msgstr "Dovoli objave iz družbenih omrežij v angleščini"

#: src/components/staff/admin/workspace/Workspace.js:772
msgid "Allow forcing articles to email reports"
msgstr "Dovoli funkcijo \"Pošlji članek v naslednjem poročilu\""

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:93
msgid "Allow search engines to index this blog (including inclusion of articles in media monitoring and analysis of online mentions)"
msgstr "Dovoli iskalnikom indeksirati ta blog (vključno z vključitvijo člankov v medijski monitoring in analizo spletnih omemb)"

#: src/components/staff/admin/workspace/Workspace.js:609
msgid "Allow users to create own articles"
msgstr "Dovoli uporabnikom ustvarjanje lastnih člankov"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:31
msgid "Allowing detailed tracking of distribution campaigns."
msgstr "Omogoča podrobno sledenje distribucijskim kampanjam."

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:63
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:86
#: src/components/staff/admin/customer/expenses/ExpenseTable.js:81
#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:38
msgid "Amount"
msgstr "Znesek"

#: src/components/emailing/content/EmailingSettingsContent.js:32
msgid "An email sender record with this address already exists. Please check your existing records or try again."
msgstr "Zapis pošiljatelja e-pošte s tem naslovom že obstaja. Preverite obstoječe zapise ali poskusite znova."

#: src/pages/_error.js:36
msgid "An error {statusCode} occurred on server"
msgstr "Na strežniku je prišlo do napake {statusCode}"

#: src/pages/_error.js:37
msgid "An error occurred on client"
msgstr "Prišlo je do napake na odjemalcu"

#: src/components/emailing/content/EmailingSettingsContent.js:17
msgid "An error occurred while authorizing our application to use the external service."
msgstr "Pri avtorizaciji naše aplikacije za uporabo zunanje storitve je prišlo do napake."

#: src/components/staff/admin/user/getUserAttributes.js:19
msgid "Analyst"
msgstr "Analitik"

#: src/store/models/dashboards/DashboardPreview.js:87
#: src/pages/analytcs.js:16
#: src/components/widgets/modules/stats/WidgetStats.js:67
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:29
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:102
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:27
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:61
#: src/components/layout/AuthWrapper/constants/features.slides.js:165
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:16
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:16
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:20
#: src/components/analytics/AnalyticsContent.js:105
msgid "Analytics"
msgstr "Analitika"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:138
msgid "AND"
msgstr "IN"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:43
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:25
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:37
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:69
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:25
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationHeader.js:29
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:134
msgid "Annotation"
msgstr "Opomba"

#: src/pages/user/yoy-analysis.js:34
msgid "Annual Media Analysis"
msgstr "Letna medijska analiza"

#: src/components/notifications/Content.js:28
msgid "App"
msgstr "Aplikacija"

#: src/components/staff/admin/workspace/Workspace.js:932
#: src/components/settings/SettingsTariff/SettingsTariff.js:45
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:55
msgid "Application permissions"
msgstr "Dovoljenja aplikacije"

#: src/components/settings/SettingsApplication/SettingsApplication.js:19
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:98
msgid "Application settings"
msgstr "Nastavitve aplikacije"

#: src/components/newsroom/components/AiTools/AiGenerateContent.tsx:138
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:161
msgid "Apply"
msgstr "Uporabi"

#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:229
msgid "archive"
msgstr "arhiv"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:22
msgid "Archive"
msgstr "Arhiv"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:129
msgid "Are you ready to send the email?"
msgstr "Ste pripravljeni poslati e-pošto?"

#. placeholder {0}: item.filename
#. placeholder {0}: file.name
#: src/components/newsroom/content/post/AttachmentsList.js:66
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:42
msgid "Are you sure you want to delete {0}?"
msgstr "Ali ste prepričani, da želite izbrisati {0}?"

#: src/components/emailing/content/SignaturePopup.tsx:36
msgid "Are you sure you want to delete signature?"
msgstr "Ali ste prepričani, da želite izbrisati podpis?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:397
msgid "Are you sure you want to delete this blog post?"
msgstr "Ali ste prepričani, da želite izbrisati to objavo na blogu?"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:87
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:100
msgid "Are you sure you want to delete this email?"
msgstr "Ali ste prepričani, da želite izbrisati to e-pošto?"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:122
msgid "Are you sure you want to delete this sender?"
msgstr "Ali ste prepričani, da želite izbrisati tega pošiljatelja?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:666
msgid "Are you sure you want to delete your Newsroom? This action will delete all articles and settings."
msgstr "Ali ste prepričani, da želite izbrisati vaše Medijsko središče? Ta dejanje bo izbrisalo vse članke in nastavitve."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:299
msgid "Are you sure you want to publish the changes?"
msgstr "Ali ste prepričani, da želite objaviti spremembe?"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:110
msgid "Are you sure you want to remove all recipients from this report?"
msgstr "Ali ste prepričani, da želite odstraniti vse prejemnike iz tega poročila?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:27
msgid "Are you sure you want to remove these users from the workspace?"
msgstr "Ali ste prepričani, da želite odstraniti te uporabnike iz delovnega prostora?"

#: src/components/newsroom/content/posts/NewsroomPosts.js:206
msgid "Are you sure you want to remove this article?"
msgstr "Ali ste prepričani, da želite odstraniti ta članek?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:683
msgid "Are you sure you want to remove this Newsroom?"
msgstr "Ali ste prepričani, da želite odstraniti to Medijsko središče?"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:30
#: src/components/staff/admin/user/WorkspacesTable.js:143
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:378
msgid "Are you sure you want to remove this user from the workspace?"
msgstr "Ali ste prepričani, da želite odstraniti tega uporabnika iz delovnega prostora?"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:300
msgid "Are you sure you want to set this post to draft?"
msgstr "Ali ste prepričani, da želite to objavo nastaviti kot osnutek?"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:206
msgid "area"
msgstr "površina"

#: src/components/OurChart/OurChartAdvanced.js:148
msgid "Area"
msgstr "Površina"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:56
#: src/components/misc/MntrEditor/extensions/ExtensionArticle.tsx:33
msgid "Article"
msgstr "Članek"

#. placeholder {0}: item.title
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:364
msgid "Article '<0>{0}</0>' will be removed."
msgstr "Članek '<0>{0}</0>' bo odstranjen."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:576
msgid "Article '<0>{title}</0>' will be removed."
msgstr "Članek '<0>{title}</0>' bo odstranjen."

#: src/components/misc/ActionsBar/View/ViewMenu.js:140
msgid "Article Area"
msgstr "Območje članka"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:53
msgid "Article can still be attached later"
msgstr "Članek je mogoče priložiti tudi kasneje"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:48
msgid "Article clipping"
msgstr "Izrezek članka"

#: src/store/models/OurChart.js:531
#: src/store/models/OurChart.js:563
#: src/store/models/OurChart.js:788
#: src/constants/stats.ts:11
#: src/constants/analytics.js:26
#: src/constants/analytics.js:1097
#: src/components/widgets/modules/stats/WidgetStats.js:203
#: src/components/widgets/modules/stats/WidgetStats.js:216
#: src/components/widgets/modules/stats/WidgetStats.js:229
#: src/components/monitoring/FeedChart/FeedChart.js:28
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:65
msgid "Article count"
msgstr "Število člankov"

#: src/components/topics/Content/TopicsList/KeywordStatsTable.js:23
msgid "Article count for the last 30 days"
msgstr "Število člankov v zadnjih 30 dneh"

#: src/constants/analytics.js:192
msgid "Article count vs. GRP vs. AVE"
msgstr "Število člankov vs. GRP vs. AVE"

#: src/store/models/monitoring/Inspector/Inspector.ts:778
msgid "Article has been copied to the clipboard."
msgstr "Članek je bil kopiran v odložišče."

#: src/store/models/monitoring/WorkspaceArticles.js:219
msgid "Article has been removed."
msgstr "Članek je bil odstranjen."

#: src/store/models/monitoring/WorkspaceArticles.js:193
msgid "Article has been updated."
msgstr "Članek je bil posodobljen."

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:215
msgid "Article has no annotations assigned. Select the text to add."
msgstr "Članku niso dodeljene nobene opombe. Izberite besedilo, ki ga želite dodati."

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:63
msgid "Article history"
msgstr "Zgodovina članka"

#: src/store/models/monitoring/Inspector/Inspector.ts:451
msgid "Article is already in export."
msgstr "Članek je že v izvozu."

#: src/components/article/Content.js:17
msgid "Article link has expired"
msgstr "Povezava do članka je potekla"

#: src/components/layout/MntrActiveFilters/modules/ArticleMentions.js:12
msgid "Article mentions"
msgstr "Omembe članka"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:146
msgid "Article numbering"
msgstr "Oštevilčenje člankov"

#: src/store/models/admin/customer/CustomerStore.js:303
msgid "Article recreation started successfully."
msgstr "Članki se ponovno ustvarjajo."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:181
msgid "Article screenshot"
msgstr "Posnetek zaslona članka"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:37
#: src/components/layout/MntrActiveFilters/modules/EmptyTags.js:23
msgid "Article Tags"
msgstr "Oznake člankov"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
msgid "Article Text"
msgstr "Besedilo članka"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:83
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:129
msgid "Article transcript"
msgstr "Prepis članka"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:101
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:381
msgid "Article Type"
msgstr "Vrsta članka"

#: src/components/ReusableFeed/FormAddArticle.tsx:30
msgid "Article URL"
msgstr "URL članka"

#: src/store/models/monitoring/Inspector/Inspector.ts:751
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:58
#: src/components/monitoring/FeedList/FeedListItem/FeedListItem.js:138
msgid "Article URL has been copied to the clipboard. Without a login, it will be accessible for 30 days."
msgstr "URL naslov članka je bil kopiran v odložišče. Brez prijave bo dostopen 30 dni."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:70
msgid "Article view"
msgstr "Pogled članka"

#: src/store/models/monitoring/Inspector/Inspector.ts:551
msgid "Article was reported"
msgstr "Članek je bil prijavljen"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:278
msgid "Article was successfully published on your Newsroom page"
msgstr "Članek je bil uspešno objavljen na vaši strani Medijskega središča"

#: src/store/models/dashboards/DashboardPreview.js:75
#: src/components/trash/Content.js:55
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:95
#: src/components/newsroom/content/posts/NewsroomPosts.js:80
#: src/components/medialist/constants/medialist.tabNavigation.js:27
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:20
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:18
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:51
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:109
#: src/components/layout/MntrActiveFilters/modules/MedialistArticles.js:18
#: src/components/forms/dashboard/Search/SearchNewsroom.js:31
#: src/components/exportList/History/HistoryTable/HistoryTable.js:63
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:36
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:30
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:30
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:58
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:17
msgid "Articles"
msgstr "Članki"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:525
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:808
msgid "Articles updated successfully."
msgstr "Članki so bili uspešno posodobljeni."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:38
msgid "Assess competitors and trends to refine your strategy."
msgstr "Ocenite konkurente in trende, da izpopolnite svojo strategijo."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:28
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:64
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:260
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:361
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:16
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:50
msgid "Assign tag"
msgstr "Dodeli oznako"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:34
msgid "Assign tag to article"
msgstr "Dodeli oznako članku"

#: src/components/medialist/forms/FormEditAuthor.js:626
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:19
msgid "Assign tag to author"
msgstr "Dodeli oznako avtorju"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:25
msgid "Assistant creates a draft of the email content based on your specific needs"
msgstr "Pomočnik ustvari osnutek vsebine e-pošte glede na vaše specifične potrebe"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:192
msgid "Attached articles"
msgstr "Priloženi članki"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:238
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:52
msgid "Attachment"
msgstr "Priloga"

#: src/components/reports/history/HistoryTable.js:173
#: src/components/newsroom/content/post/AttachmentsList.js:81
msgid "Attachments"
msgstr "Priloge"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:68
msgid "Audio"
msgstr "Avdio"

#: src/constants/analytics.js:1094
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:485
#: src/components/newsroom/content/modules/CustomQuotes.tsx:69
#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:258
msgid "Author"
msgstr "Avtor"

#: src/components/medialist/constants/medialist.tabNavigation.js:12
msgid "Author Detail"
msgstr "Podrobnosti o avtorju"

#: src/components/medialist/content/AuthorBasketsMenu.js:26
#: src/components/medialist/content/AuthorBasketSelectorButton.js:8
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:19
msgid "Author Lists"
msgstr "Seznami avtorjev"

#: src/pages/authors/index.js:89
msgid "Author tags"
msgstr "Oznake avtorjev"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:874
msgid "Author Tags"
msgstr "Oznake avtorjev"

#: src/components/medialist/forms/FormEditAuthor.js:233
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:122
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:67
msgid "Author type"
msgstr "Vrsta avtorja"

#: src/store/models/authors/AuthorsStore.js:1079
msgid "Author was deleted."
msgstr "Avtor je bil izbrisan."

#: src/store/models/authors/AuthorsStore.js:1168
msgid "Author was reported."
msgstr "Avtor je bil prijavljen."

#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Author will be deleted."
msgstr "Avtor bo izbrisan."

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:18
msgid "Author's name"
msgstr "Ime avtorja"

#: src/components/medialist/forms/FormEditAuthor.js:997
msgid "Author's shortname"
msgstr "Kratica avtorja"

#: src/components/medialist/forms/FormEditAuthor.js:834
#: src/components/medialist/forms/FormEditAuthor.js:992
msgid "Author's shortnames"
msgstr "Avtorjeve kratice"

#: src/components/monitoring/Inspector/InspectorMonitora/AuthorsList/AuthorsList.js:21
#: src/components/medialist/content/MedialistDashboard.js:82
#: src/components/medialist/content/MedialistDashboard.js:115
#: src/components/layout/Sidebar/modules/AuthorsNavigation/AuthorsNavigation.js:20
#: src/components/forms/dashboard/Search/SearchAuthors.js:39
#: src/components/emailing/forms/FormEmailRecipients.js:131
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:40
msgid "Authors"
msgstr "Avtorji"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:560
#: src/store/models/authors/AuthorsStore.js:535
msgid "Authors added."
msgstr "Avtorji so bili dodani."

#: src/components/emailing/content/tabs/AddRecipients.tsx:69
msgid "Authors lists"
msgstr "Seznami avtorjev"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:598
#: src/store/models/authors/AuthorsStore.js:603
msgid "Authors removed."
msgstr "Avtorji so bili odstranjeni."

#: src/store/models/authors/AuthorsStore.js:664
msgid "Authors updated successfully."
msgstr "Avtorji so bili uspešno posodobljeni."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:94
msgid "Authors with types “agency”, “publisher” or “editorial office” can’t use merge tags  *|LAST_NAME|*,  *|VOKATIV_L|*. If you want to apply these merge tags to the author, change their type to “author” or “blogger” and add the last name."
msgstr "Avtorji s tipi \"agencija\", \"založnik\" ali \"uredništvo\" ne morejo uporabljati združevalnih oznak *|LAST_NAME|*, *|VOKATIV_L|*. Če želite te združevalne oznake uporabiti za avtorja, spremenite njihov tip v \"avtor\" ali \"bloger\" in dodajte priimek."

#: src/components/staff/admin/user/User.js:62
msgid "Autologin link"
msgstr "Povezava za samodejno prijavo"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:178
msgid "Automatic summary"
msgstr "Samodejni povzetek"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:270
#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:74
msgid "Automatic transcript"
msgstr "Samodejni prepis"

#: src/components/tariff/TariffLimits/TariffLimits.js:99
msgid "Automatic translations 30-day limit"
msgstr "30-dnevna omejitev za število prevedenih člankov"

#: src/constants/stats.ts:6
#: src/constants/analytics.js:994
#: src/components/widgets/modules/stats/WidgetStats.js:154
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:253
#: src/components/misc/ActionsBar/View/ViewMenu.js:203
msgid "AVE"
msgstr "AVE"

#: src/components/layout/Sidebar/SidebarMainNav.js:204
msgid "AVE and sentiment"
msgstr "AVE in sentiment"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:39
msgid "AVE Coefficient"
msgstr "Koeficient AVE"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:23
msgid "formatNumber.B"
msgstr "mrd."

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:165
#: src/components/notifications/AppNotifications/AppNotifications.js:18
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:197
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:106
#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:82
#: src/components/newsroom/content/posts/ChooseTemplates.tsx:98
#: src/components/newsroom/components/NewsroomHeading/NewsroomHeading.js:20
#: src/components/misc/ActionsBar/View/ViewMenu.js:40
#: src/components/misc/ActionsBar/View/ViewMenu.js:252
#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:30
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:73
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:97
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:49
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:31
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:145
#: src/components/layout/Header/AppNotifications/AppNotifications.js:110
#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:163
#: src/components/emailing/content/CreateEmailContent.js:296
msgid "Back"
msgstr "Nazaj"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:29
msgid "Back to Log In"
msgstr "Nazaj na prijavo"

#: src/components/page/auth/ResetPassword/ResetPasswordFooter.js:19
msgid "Back to settings"
msgstr "Nazaj na nastavitve"

#: src/pages/_error.js:61
#: src/pages/404.js:29
#: src/pages/user/yoy-analysis.js:79
#: src/pages/user/reactivate-24.js:79
#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:28
#: src/components/page/auth/UserInactive/UserInactive.js:25
#: src/components/page/auth/Expired/Expired.js:119
#: src/components/layout/ErrorCustom/ErrorCustom.js:13
msgid "Back to the main page"
msgstr "Nazaj na glavno stran"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:68
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:107
msgid "Background Color"
msgstr "Barva ozadja"

#: src/constants/analytics.js:529
#: src/constants/analytics.js:547
#: src/constants/analytics.js:565
#: src/constants/analytics.js:584
#: src/constants/analytics.js:602
#: src/constants/analytics.js:620
#: src/constants/analytics.js:638
#: src/constants/analytics.js:658
#: src/components/OurChart/OurChartAdvanced.js:141
msgid "Bar"
msgstr "Stolpec"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:46
msgid "Basic settings"
msgstr "Osnovni podatki"

#: src/components/layout/MntrActiveFilters/modules/Paywalled.js:6
msgid "Behind paywall"
msgstr "Za plačljivim zidom"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:141
msgid "Below avg."
msgstr "Podpovprečno"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:81
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:220
msgid "best"
msgstr "najboljši"

#: src/constants/analytics/primeScoreCharts.ts:94
msgid "Best PRIMe mediatypes"
msgstr "Najboljši mediatipi PRIMe"

#: src/components/staff/admin/customer/bio/CustomerBio.js:106
msgid "Billing email"
msgstr "E-pošta za obračun"

#: src/components/medialist/forms/FormEditAuthor.js:855
#: src/components/medialist/forms/FormEditAuthor.js:1023
msgid "Bio"
msgstr "Bio"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:273
msgid "Blockquote"
msgstr "Citat"

#: src/store/models/newsroom/blogs/posts/NewsroomPostsStoreArrItem.ts:106
msgid "Blog post was successfully deleted."
msgstr "Objava na blogu je bila uspešno izbrisana."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:123
msgid "Bold"
msgstr "Krepko"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:156
msgid "Brackets"
msgstr "Oklepaji"

#: src/pages/brand-tracking.tsx:29
#: src/components/layout/Sidebar/SidebarMainNav.js:135
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:278
msgid "Brand Tracking"
msgstr "Sledenje blagovni znamki"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:256
msgid "Browse keywords"
msgstr "Brskaj po ključnih besedah"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:257
msgid "Bullet list"
msgstr "Oznake"

#: src/components/newsroom/components/PostsList/PostsList.js:162
msgid "By"
msgstr "Od"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:48
msgid "by source"
msgstr "po viru"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:152
msgid "By submitting the form, you agree to our <0>terms</0>."
msgstr "Z oddajo obrazca se strinjate z našimi <0>pogoji</0>."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:61
msgid "Call to Action (CTA):"
msgstr "Poziv k akciji (CTA):"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:136
msgid "Campaign"
msgstr "Kampanja"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:54
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:92
msgid "Campaign will be removed"
msgstr "Kampanja bo odstranjena"

#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:26
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:17
#: src/components/emailing/content/EmailingCampaignsContent.tsx:49
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:44
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:57
msgid "Campaigns"
msgstr "Kampanje"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:139
msgid "Can unsubscribe"
msgstr "Se lahko odjavi"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:76
msgid "Can't find an article in your feed? Enter a link to the article you are looking for and select a topic."
msgstr "Ne najdete članka v svojem viru? Vnesite povezavo do članka, ki ga iščete, in izberite temo."

#: src/components/reports/Content/ReportsList/ReportsForm.js:350
#: src/components/misc/VideoPlayer/getCropAction.js:7
#: src/components/misc/MntrForm/MntrForm.tsx:516
#: src/components/medialist/forms/FormEditAuthor.js:559
#: src/components/medialist/forms/FormEditAuthor.js:571
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:86
msgid "Cancel"
msgstr "Prekliči"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:273
msgid "Cancel choice"
msgstr "Prekliči"

#: src/components/misc/Changelog/ChangelogTableRow.js:247
msgid "Cancel revert"
msgstr "Prekliči razveljavitev"

#: src/components/misc/UploadWatcher/UploadWatcher.js:40
msgid "Cancel Upload"
msgstr "Prekliči nalaganje"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:35
msgid "Cannot delete articles, run manual sentiment, create/edit topics or reports, change account settings, delete TV/radio stories, or edit CRM info."
msgstr "Ne more brisati člankov, zagnati ročne analize sentimenta, ustvarjati/urejati tem ali poročil, spreminjati nastavitev računa, brisati TV/radio vsebin ali urejati CRM podatkov."

#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:120
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:279
msgid "Captured on the screen"
msgstr "Zajeto na zaslonu"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:67
msgid "Categories"
msgstr "Kategorije"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:29
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomCategory.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomCategory.js:21
msgid "Category"
msgstr "Kategorija"

#. placeholder {0}: item.name
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:146
msgid "Category <0>{0}</0> will be removed."
msgstr "Kategorija <0>{0}</0> bo odstranjena."

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:25
msgid "Category name"
msgstr "Ime kategorije"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:47
msgid "Change email"
msgstr "Spremeni e-pošto"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:27
#: src/components/settings/SettingsUserManagement/UpdateRole.tsx:20
msgid "Change role"
msgstr "Spremeni vlogo"

#: src/components/misc/Changelog/ChangelogTable.js:36
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:394
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChangeType.js:24
msgid "Change Type"
msgstr "Vrsta spremembe"

#: src/pages/topics/[topicId]/changelog.js:13
#: src/pages/staff/admin/workspaces/[workspaceId]/changelog.js:12
#: src/pages/reports/[reportId]/changelog.js:13
#: src/components/staff/admin/workspace/Workspace.js:147
msgid "Changelog"
msgstr "Dnevnik sprememb"

#: src/store/models/admin/customer/CustomerStore.js:163
#: src/store/models/admin/customer/CustomerStore.js:177
#: src/store/models/admin/customer/CustomerStore.js:186
#: src/store/models/admin/customer/CustomerStore.js:231
#: src/store/models/admin/customer/CustomerStore.js:258
#: src/store/models/admin/customer/CustomerStore.js:286
msgid "Changes successfully saved."
msgstr "Spremembe uspešno shranjene."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:27
msgid "Channel"
msgstr "Kanal"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:241
msgid "Channels"
msgstr "Kanali"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:169
msgid "Chart"
msgstr "Grafikon"

#: src/components/OurChart/OurChartAdvanced.js:128
msgid "Chart Settings"
msgstr "Nastavitve grafikona"

#: src/components/OurChart/OurChartAdvanced.js:137
msgid "Chart Type"
msgstr "Vrsta grafikona"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:238
msgid "Check"
msgstr "Preveri"

#: src/components/emailing/content/tabs/AddRecipients.tsx:80
msgid "Choose authors list or tag:"
msgstr "Izberite seznam avtorjev ali oznako:"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:35
msgid "Choose how to add/edit your signature"
msgstr "Izberite način dodajanja/urejanja vašega podpisa"

#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:44
msgid "Clear Color"
msgstr "Počisti barvo"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:139
msgid "Clear formatting"
msgstr "Počisti oblikovanje"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/EntityInfoBox.js:220
msgid "click to open the detail"
msgstr "kliknite za odprtje podrobnosti"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:342
msgid "Click to see options"
msgstr "Kliknite za prikaz možnosti"

#: src/components/forms/inspector/FormMediaEditor.js:124
msgid "Clip duration"
msgstr "Dolžina posnetka"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:127
#: src/components/reports/history/Compose.js:36
#: src/components/misc/portable/PortableResend/PortableResend.js:59
#: src/components/misc/portable/PortableResend/PortableResend.js:99
#: src/components/misc/portable/PortableExport/PortableExport.js:55
#: src/components/misc/portable/PortableExport/PortableExport.js:95
#: src/components/misc/MntrHint/MntrHint.js:77
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:111
#: src/components/misc/Mntr/ButtonGroup.tsx:51
msgid "Close"
msgstr "Zapri"

#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:129
msgid "Collapse"
msgstr "Strni"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:53
msgid "Color palette"
msgstr "Barvna paleta"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:208
msgid "Colors"
msgstr "Barve"

#: src/constants/analytics/primeScoreCharts.ts:114
#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:60
msgid "Column"
msgstr "Stolpec"

#: src/components/newsroom/content/posts/NewsroomPosts.js:126
msgid "Compact"
msgstr "Kompaktno"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:66
msgid "Company"
msgstr "Podjetje"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:93
msgid "Company (Name or CRN)"
msgstr "Podjetje (ime ali matična številka)"

#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:79
msgid "Company detail"
msgstr "Podrobnosti podjetja"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:72
#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:26
msgid "Company info"
msgstr "Podatki o podjetju"

#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:893
msgid "Compare Topic"
msgstr "Primerjaj temo"

#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up-completion.js:26
msgid "Completion"
msgstr "Dokončanje registracije"

#: src/components/emailing/forms/FormSenderSettings.js:260
msgid "Configuring DKIM (DomainKeys Identified Mail) enhances the integrity and authenticity of your emails, reducing the likelihood of them being marked as spam:"
msgstr "Konfiguracija DKIM (DomainKeys Identified Mail) povečuje integriteto in avtentičnost vaših e-poštnih sporočil ter zmanjšuje verjetnost, da bodo označena kot vsiljena pošta:"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:51
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:319
msgid "Confirm"
msgstr "Potrdi"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:93
msgid "Confirm new password"
msgstr "Potrdite novo geslo"

#: src/helpers/store/apiClient.js:240
msgid "Connection with the server was lost. Please try again."
msgstr "Povezava s strežnikom je bila prekinjena. Prosimo, poskusite znova."

#: src/components/layout/Sidebar/SidebarMainNav.js:230
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:160
msgid "Contact"
msgstr "Kontakt"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:123
msgid "Contact information"
msgstr "Kontaktni podatki"

#: src/components/medialist/forms/FormEditAuthor.js:761
#: src/components/medialist/content/MedialistDashboard.js:88
#: src/components/medialist/content/MedialistDashboard.js:121
#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:56
msgid "Contacts"
msgstr "Stiki"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:178
msgid "Contacts cannot be imported from this file"
msgstr "Stikov ni mogoče uvoziti iz te datoteke"

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:19
msgid "Contacts import in progress"
msgstr "Uvažanje stikov je v teku"

#: src/pages/user/reset-password/new.tsx:48
#: src/pages/user/reset-password/index.tsx:28
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:167
#: src/components/page/auth/SignUp/SignUp.js:58
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:614
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:246
#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:19
#: src/components/misc/Wizard/WizardChoice.tsx:150
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:143
#: src/components/medialist/forms/FormEditAuthor.js:300
#: src/components/medialist/forms/FormEditAuthor.js:318
#: src/components/medialist/forms/FormEditAuthor.js:464
#: src/components/medialist/forms/FormEditAuthor.js:483
#: src/components/medialist/forms/FormEditAuthor.js:578
#: src/components/emailing/forms/FormAddCampaign.tsx:20
#: src/components/emailing/content/CreateEmailContent.js:354
msgid "Continue"
msgstr "Nadaljuj"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:58
msgid "Continue to import"
msgstr "Nadaljuj z uvozom"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:229
#: src/components/medialist/forms/FormEditAuthor.js:116
#: src/components/medialist/forms/FormEditAuthor.js:120
#: src/components/medialist/forms/FormEditAuthor.js:872
#: src/components/medialist/forms/FormEditAuthor.js:1048
#: src/components/emailing/forms/FormSenderSettings.js:69
msgid "Copied to the clipboard."
msgstr "Kopirano v odložišče."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:64
msgid "Copy article to clipboard"
msgstr "Kopiraj članek v odložišče"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:12
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:115
msgid "Copy link to clipboard"
msgstr "Kopiraj povezavo v odložišče"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:27
msgid "Copy password"
msgstr "Kopiraj geslo"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:117
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:76
msgid "Copy public URL to clipboard"
msgstr "Kopiraj javni URL v odložišče"

#: src/components/reports/history/HistoryTable.js:436
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:96
msgid "Copy recipients to clipboard"
msgstr "Kopiraj prejemnika v odložišče"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:34
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:66
msgid "Copy share link"
msgstr "Kopiraj povezavo za deljenje"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:70
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:82
msgid "Copy to another campaign"
msgstr "Kopiraj v drugo kampanjo"

#: src/helpers/modal/withModalEmailPreview.js:102
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:589
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:648
#: src/components/medialist/forms/FormEditAuthor.js:773
#: src/components/medialist/forms/FormEditAuthor.js:801
#: src/components/medialist/forms/FormEditAuthor.js:869
#: src/components/medialist/forms/FormEditAuthor.js:1045
#: src/components/emailing/forms/FormSenderSettings.js:65
msgid "Copy to clipboard"
msgstr "Kopiraj v odložišče"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:112
msgid "Copy to Dashboard"
msgstr "Kopiraj na nadzorno ploščo"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:239
#: src/components/misc/ActionsBar/View/ViewMenu.js:187
msgid "Cost per Point (CCP) - how much does one second of advertising cost for each GRP point (AVE = CPP * GRP * duration)"
msgstr "Cost per Point (CCP) - koliko stane ena sekunda oglaševanja za vsako GRP točko (AVE = CPP * GRP * trajanje)"

#: src/constants/analytics.js:864
msgid "Countries"
msgstr "Države"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:124
msgid "countries with enabled mediatype"
msgstr "države z omogočenim mediatipom"

#: src/constants/analytics.js:851
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:74
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:76
#: src/components/medialist/forms/FormEditAuthor.js:246
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:271
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:291
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:80
msgid "Country"
msgstr "Država"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:63
msgid "Cover page"
msgstr "Naslovna stran"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:234
#: src/components/misc/ActionsBar/View/ViewMenu.js:182
msgid "CPP"
msgstr "CPP"

#: src/components/medialist/forms/FormEditAuthor.js:601
msgid "Create"
msgstr "Ustvari"

#: src/components/page/auth/Login/Login.tsx:75
msgid "Create an account for free"
msgstr "Ustvarite račun brezplačno"

#: src/components/emailing/content/NewEmailWizardButton.tsx:15
msgid "Create an email"
msgstr "Ustvari e-pošto"

#: src/components/medialist/content/withAddToBasketPopup.js:52
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorBaskets/AddAuthorToBasket.js:59
msgid "Create and add to new list"
msgstr "Ustvari seznam in dodaj avtorja"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/AddTag.js:68
#: src/components/monitoring/FeedActionsBar/withAddTagPopup/AddTagPopupContent.js:49
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorTags/AddAuthorTag.js:51
msgid "Create and assign new tag"
msgstr "Ustvari in dodeli novo oznako"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:48
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:191
msgid "Create article"
msgstr "Ustvari članek"

#: src/pages/workspace-articles.js:64
#: src/components/monitoring/WorkspaceArticles/withWorkspaceArticleModal.js:9
msgid "Create Article"
msgstr "Ustvari članek"

#: src/components/medialist/content/MedialistDashboard.js:144
#: src/components/medialist/content/AuthorBasketsMenu.js:51
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:249
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:250
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:168
msgid "Create author"
msgstr "Ustvari avtorja"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:76
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:77
msgid "Create Folder"
msgstr "Ustvari mapo"

#: src/components/medialist/content/AuthorBasketsMenu.js:151
msgid "Create new list"
msgstr "Ustvari nov seznam"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:69
msgid "Create new Newsroom"
msgstr "Ustvari novo Medijsko središče"

#: src/components/newsroom/content/newsroom/NewsroomCreateBlog.js:18
msgid "Create Newsroom"
msgstr "Ustvari Medijsko središče"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:240
msgid "Create Own Article"
msgstr "Ustvari svoj članek"

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:16
msgid "Create post"
msgstr "Ustvari objavo"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:69
msgid "Create Report"
msgstr "Ustvari poročilo"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:24
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:26
msgid "Create workspace"
msgstr "Ustvari delovni prostor"

#: src/pages/authors/index.js:61
msgid "Create your own <0>lists</0> and <1>tags</1>"
msgstr "Ustvarite svoje <0>sezname</0> in <1>oznake</1>"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:44
msgid "Created"
msgstr "Ustvarjeno"

#: src/store/models/dashboards/DashboardPreview.js:134
#: src/pages/crisis-communication.js:10
#: src/pages/crisis-communication-story/[articleId].js:10
#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:19
#: src/components/layout/Sidebar/SidebarMainNav.js:96
#: src/components/layout/Sidebar/SidebarMainNav.js:222
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:235
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:26
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:37
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:23
msgid "Crisis communication"
msgstr "Krizno komuniciranje"

#: src/store/models/monitoring/Inspector/EntityKnowledgeBaseStore/EntityKnowledgeBaseStore.js:23
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:55
msgid "CRN"
msgstr "MŠ"

#. placeholder {0}: option.reg_no
#. placeholder {0}: data.reg_no
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:117
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorAddress/AuthorAddress.js:14
msgid "CRN: {0}"
msgstr "MŠ: {0}"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:124
#: src/components/misc/MntrEditor/modals/withModalCTAButton.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionCTAButton.js:23
msgid "CTA Button"
msgstr "CTA gumb"

#: src/components/staff/admin/user/User.js:269
#: src/components/settings/SettingsApplication/SettingsApplication.js:42
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:408
msgid "Currency"
msgstr "Valuta"

#: src/components/settings/SettingsApplication/SettingsApplication.js:33
msgid "Currency in which to calculate AVE."
msgstr "Valuta, v kateri se izračunava AVE."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:81
msgid "Current password"
msgstr "Trenutno geslo"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:11
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:645
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:65
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:49
#: src/components/emailing/content/CreateEmailContent.js:548
msgid "Custom"
msgstr "Po meri"

#: src/components/emailing/content/promo/PromoEmailing.js:22
msgid "Custom branding"
msgstr "Prilagojena blagovna znamka"

#: src/components/settings/SettingsTheme/SettingsThemePreview/LogoColorPicker/LogoColorPicker.js:70
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:65
msgid "Custom color"
msgstr "Barva po meri"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:171
#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:135
#: src/components/misc/MntrEditor/modals/withModalHtmlCode.js:17
#: src/components/misc/MntrEditor/extensions/ExtensionHtmlCode.js:23
msgid "Custom HTML Code"
msgstr "Lastna HTML koda"

#: src/components/newsroom/modals/withModalCustomAiRewrite.tsx:24
msgid "Custom insruction"
msgstr "Lastno navodilo"

#: src/components/settings/SettingsLogo/SettingsLogo.js:63
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:78
msgid "Custom logo"
msgstr "Prilagojen logotip"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:176
msgid "Custom meta/script/style for <head> section"
msgstr "Lastna meta/script/style koda za sekcijo <head>"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:83
msgid "Custom selection"
msgstr "Lastna izbira"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:353
msgid "Custom Slug"
msgstr "Prilagojen URL slug"

#: src/components/reports/Content/ReportsList/ReportsForm.js:306
msgid "Custom subject (optional)"
msgstr "Prilagojena zadeva (neobvezno)"

#: src/pages/staff/admin/customers/index.js:12
#: src/components/staff/admin/customers/Customers.js:20
#: src/components/layout/Sidebar/SidebarMainNav.js:154
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:294
#: src/components/layout/Header/UserMenu/UserMenu.js:178
#: src/components/forms/dashboard/Search/SearchCustomers.js:54
msgid "Customers"
msgstr "Stranke"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:317
msgid "Customization"
msgstr "Nastavitve po meri"

#: src/components/misc/VideoPlayer/getCropAction.js:7
msgid "Cut clip"
msgstr "Izreži posnetek"

#: src/components/forms/inspector/FormMediaEditor.js:117
msgid "Cut from"
msgstr "Izreži od"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:433
msgid "Cut media"
msgstr "Izreži posnetek"

#: src/components/forms/inspector/FormMediaEditor.js:120
msgid "Cut to"
msgstr "Izreži v"

#: src/pages/staff/admin/workspaces/[workspaceId]/daily-access.js:12
#: src/pages/staff/admin/users/[userId]/daily-access.js:12
#: src/components/staff/admin/workspace/Workspace.js:153
#: src/components/staff/admin/user/User.js:77
msgid "Daily Access"
msgstr "Dostopi do aplikacije"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:54
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:70
#: src/components/misc/ActionsBar/View/ViewMenu.js:166
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:107
msgid "Daily listenership"
msgstr "Dnevna poslušanost"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:71
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:45
msgid "daily users"
msgstr "dnevni uporabniki"

#: src/components/misc/ActionsBar/View/ViewMenu.js:66
msgid "Daily users"
msgstr "Dnevni uporabniki"

#: src/components/layout/Header/UserMenu/UserMenu.js:107
msgid "Dark"
msgstr "Temno"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Dark mode preview"
msgstr "Predogled temnega načina"

#: src/pages/dashboard/index.js:18
#: src/pages/dashboard/shared/[dashboardKey].js:15
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:71
msgid "Dashboard"
msgstr "Nadzorna plošča"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:19
msgid "Dashboard sharing"
msgstr "Deljenje nadzorne plošče"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:56
msgid "Dashboard will be removed."
msgstr "Nadzorna plošča bo odstranjena."

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:47
msgid "Dashboard will be shared in read-only form (non-interactive) with currently displayed data. Link expiration is 30 days."
msgstr "Nadzorna plošča bo deljena v obliki samo za branje (neinteraktivno) s trenutno prikazanimi podatki. Povezava poteče čez 30 dni."

#: src/components/tariff/TariffLimits/TariffLimits.js:150
#: src/components/staff/admin/workspace/Workspace.js:460
msgid "Dashboards limit"
msgstr "Omejitev nadzornih plošč"

#: src/components/staff/admin/DailyAccess/Table.js:21
#: src/components/reports/history/HistoryTable.js:146
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:180
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:81
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:263
#: src/components/exportList/History/HistoryTable/HistoryTable.js:48
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:177
msgid "Date"
msgstr "Datum"

#: src/components/misc/Changelog/ChangelogTable.js:30
msgid "Date & User"
msgstr "Datum in uporabnik"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:238
msgid "Date and time must be in the future"
msgstr "Datum in čas morata biti v prihodnosti"

#. placeholder {0}: format(effectiveMinDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:35
msgid "Date cannot be earlier than {0}"
msgstr "Datum ne sme biti starejši od {0}"

#. placeholder {0}: format(effectiveMaxDate, DATE_FORMAT)
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:38
msgid "Date cannot be later than {0}"
msgstr "Datum ne sme biti poznejši od {0}"

#: src/constants/analytics.js:827
msgid "Day of the week"
msgstr "Dan v tednu"

#: src/helpers/charts/makeGranularityMenu.js:10
#: src/helpers/charts/getGranularityLabel.js:16
msgid "Days"
msgstr "Dnevi"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:289
msgid "DD.MM.YYYY"
msgstr "DD.MM.YYYY"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:32
#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:51
msgid "Deduplicate articles"
msgstr "Odstrani podvojene članke"

#: src/components/staff/admin/workspace/Workspace.js:763
msgid "Deduplicate feed articles"
msgstr "Odstrani podvojene članke v viru"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:36
msgid "Deduplication will remove same or similar articles from the report according to your settings. It will not remove any article from the feed."
msgstr "Dedupliciranje bo iz poročila odstranilo enake ali podobne članke glede na vaše nastavitve. Članki bodo še vedno na voljo v viru."

#: src/components/newsroom/content/posts/NewsroomPosts.js:133
msgid "Default"
msgstr "Privzeto"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:62
msgid "Define the action you want recipients to take."
msgstr "Določite akcijo, ki jo naj prejemniki izvedejo. Na primer: Prijavi se, Klikni tu."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:37
msgid "Define the subject line to set the focus and tone."
msgstr "Določite zadevo, da nastavite fokus in ton."

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:137
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:78
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:160
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:63
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:87
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:60
#: src/components/newsroom/content/posts/NewsroomPosts.js:201
#: src/components/newsroom/content/post/AttachmentsList.js:52
#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:357
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:388
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:37
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:35
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:279
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:75
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:37
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:35
#: src/components/medialist/forms/modules/FormArray.js:119
#: src/components/medialist/forms/modules/FormArray.js:165
#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:33
#: src/components/emailing/content/SignaturePopup.tsx:31
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:85
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:96
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:117
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:53
msgid "Delete"
msgstr "Izbriši"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:348
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:360
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:385
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:313
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:388
msgid "Delete Article"
msgstr "Izbriši članek"

#: src/components/newsroom/content/post/AttachmentsList.js:56
msgid "Delete attachment"
msgstr "Izbriši prilogo"

#: src/components/medialist/forms/FormEditAuthor.js:311
#: src/components/medialist/forms/FormEditAuthor.js:317
#: src/components/medialist/forms/FormEditAuthor.js:476
#: src/components/medialist/forms/FormEditAuthor.js:482
msgid "Delete author"
msgstr "Izbriši avtorja"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:393
msgid "Delete blog post"
msgstr "Izbriši objavo"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:133
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:142
msgid "Delete category"
msgstr "Izbriši kategorijo"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:37
msgid "Delete file"
msgstr "Izbriši datoteko"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:286
msgid "Delete Folder"
msgstr "Izbriši mapo"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:375
msgid "Delete from media coverage"
msgstr "Izbriši iz medijskega poročanja"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:131
#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:140
msgid "Delete Item"
msgstr "Izbriši element"

#: src/components/medialist/content/AuthorBasketsMenu.js:126
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:108
msgid "Delete list"
msgstr "Izbriši seznam"

#: src/components/medialist/content/AuthorBasketsMenu.js:134
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:123
msgid "Delete list?"
msgstr "Izbrisati seznam?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:663
msgid "Delete Newsroom"
msgstr "Izbriši Medijsko središče"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:147
msgid "Delete recipient"
msgstr "Izbriši prejemnika"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:121
msgid "Delete Sender"
msgstr "Izbriši pošiljatelja"

#: src/components/emailing/content/SignaturePopup.tsx:34
msgid "Delete signature"
msgstr "Izbriši podpis"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:302
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:311
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:192
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:201
msgid "Delete tag"
msgstr "Izbriši oznako"

#: src/pages/trash.js:16
msgid "Deleted Articles"
msgstr "Izbrisani članki"

#. placeholder {0}: feed.totalCount
#: src/components/trash/Content.js:41
msgid "Deleted Articles ({0})"
msgstr "Izbrisani članki ({0})"

#: src/components/trash/Content.js:49
msgid "Deleted articles will appear here when deleted in the Articles section."
msgstr "Tukaj se bodo prikazali izbrisani članki iz razdelka Članki."

#: src/components/reports/history/RecipientsTableRow.js:40
#: src/components/reports/history/HistoryTable.js:84
#: src/components/reports/history/HistoryTable.js:118
#: src/components/reports/history/HistoryTable.js:328
msgid "Delivered"
msgstr "Dostavljeno"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:204
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:230
msgid "Delivery rate"
msgstr "Delež dostavljenih sporočil"

#: src/components/reports/history/HistoryTable.js:162
msgid "Delivery stats"
msgstr "Dostavljivost"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:20
msgid "Demo"
msgstr "Demo"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:111
msgid "Demographic Data"
msgstr "Demografski podatki"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:47
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:96
#: src/components/feed/InspectorToolbar/InspectorToolbar.js:145
msgid "Demographics"
msgstr "Demografija"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:42
msgid "Describe who will receive the email (demographics, interests)."
msgstr "Opišite, kdo bo prejel e-pošto (demografski podatki, interesi)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:59
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:373
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:223
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:43
msgid "Description"
msgstr "Opis"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:44
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:76
msgid "Deselect all"
msgstr "Počisti izbor"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:23
msgid "Designed for PR professionals, generates a press release structure."
msgstr "Zasnovano za PR strokovnjake, ustvarja strukturo sporočila za javnost."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:80
msgid "Detailed instructions for email text creation"
msgstr "Podrobna navodila za ustvarjanje besedila e-pošte"

#: src/pages/newsroom/index.js:53
msgid "Detailed traffic<0/> <1>analytics</1>"
msgstr "Podrobna<0/> <1>analitika prometa</1>"

#: src/constants/analytics/primeScoreCharts.ts:31
msgid "Development of PRIMe by rating"
msgstr "Razvoj PRIMe po oceni"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:149
msgid "Deviation from the average"
msgstr "Odstopanje od povprečja. Višja kot je vrednost, bolj zanimivi so rezultati."

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Disable"
msgstr "Onemogoči"

#: src/components/staff/admin/workspace/Workspace.js:225
#: src/components/staff/admin/workspace/Workspace.js:958
#: src/components/staff/admin/user/User.js:150
#: src/components/staff/admin/user/User.js:319
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:283
msgid "Discard"
msgstr "Zavrzi spremembe"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:227
msgid "Discard changes"
msgstr "Zavrzi spremembe"

#: src/components/monitoring/Inspector/InspectorMonitora/DiscussionThreadBar/DiscussionThreadBar.js:20
#: src/components/layout/MntrActiveFilters/modules/DiscussionThread.js:12
msgid "Discussion thread"
msgstr "Razpravljalna nit"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:98
msgid "Display empty categories"
msgstr "Prikaži prazne kategorije"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:205
msgid "Display the article"
msgstr "Prikaži članek"

#. js-lingui-explicit-id
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:155
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:75
msgid "pressAmount.abbr"
msgstr "Natisnjeno"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:116
msgid "Distribution amount"
msgstr "Naklada"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:149
msgid "DNS settings are invalid."
msgstr "Nastavitve DNS niso pravilne."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:147
msgid "DNS settings are valid."
msgstr "Nastavitve DNS so veljavne."

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:108
msgid "Do not add new media to the medium"
msgstr "Ne dodajajte novih medijev k mediju"

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:316
#: src/components/medialist/forms/FormEditAuthor.js:463
#: src/components/medialist/forms/FormEditAuthor.js:481
msgid "Do you really want to continue?"
msgstr "Ali res želite nadaljevati?"

#. placeholder {0}: menuItem.name
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:294
msgid "Do you really want to delete '<0>{0}</0>'?"
msgstr "Ali res želite izbrisati '<0>{0}</0>'?"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:200
msgid "Do you want to add author addresses from this list?"
msgstr "Ali želite dodati naslove avtorjev s tega seznama?"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:84
msgid "Do you want to start with AI assistant?"
msgstr "Ali želite začeti z AI asistentom?"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:124
msgid "Do you wish to reactivate this recipient?"
msgstr "Ali želite ponovno aktivirati tega prejemnika?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:55
#: src/components/emailing/forms/FormSenderSettings.js:267
msgid "Domain"
msgstr "Domena"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:493
msgid "Domain change"
msgstr "Sprememba domene"

#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:130
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:24
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:23
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:184
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:30
#: src/components/forms/dashboard/Export/ExportForm.js:100
#: src/components/exportList/History/HistoryTable/HistoryTable.js:96
#: src/components/exportList/Content/Content.tsx:78
#: src/components/OurChart/OurChartAdvanced.js:181
msgid "Download"
msgstr "Prenesi"

#: src/components/forms/inspector/FormMediaEditor.js:141
msgid "Download clip"
msgstr "Prenesi posnetek"

#: src/components/staff/admin/workspace/Workspace.js:126
msgid "Download settings (.xlsx)"
msgstr "Prenesi nastavitve (.xlsx)"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:82
msgid "Download template"
msgstr "Prenesi predlogo"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:60
msgid "Drag 'n' drop image or click to select files"
msgstr "Povlecite in spustite sliko ali kliknite za izbiro datotek"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryAdapter.js:59
msgid "Drag 'n' drop some images here, or click to select files"
msgstr "Povlecite slike sem ali kliknite za izbiro datotek"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:66
msgid "Due date"
msgstr "Datum zapadlosti"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:36
#: src/components/medialist/content/AuthorBasketsMenu.js:117
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:101
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:59
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:71
msgid "Duplicate"
msgstr "Podvoji"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:74
msgid "Duplicate widget"
msgstr "Podvoji gradnik"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:214
#: src/components/misc/ActionsBar/View/ViewMenu.js:174
msgid "Duration"
msgstr "Trajanje"

#: src/components/layout/AuthWrapper/constants/features.slides.js:307
msgid "Dynamic platform for creating, curating, and sharing captivating content."
msgstr "Ustvarjajte in delite vizualno privlačna sporočila za javnost z našo sodobno platformo."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:89
#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:51
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:53
#: src/components/emailing/content/SignaturePopup.tsx:22
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:65
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:46
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:34
msgid "Edit"
msgstr "Uredi"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:443
msgid "Edit article"
msgstr "Uredi članek"

#: src/components/newsroom/content/posts/NewsroomPosts.js:194
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:169
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:170
msgid "Edit Article"
msgstr "Uredi članek"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:34
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:37
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:77
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:80
msgid "Edit Campaign"
msgstr "Uredi kampanjo"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:10
#: src/components/layout/Sidebar/modules/NewsroomNavigation/modalEditCategory.js:24
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:113
msgid "Edit category"
msgstr "Uredi kategorijo"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:37
msgid "Edit dashboard"
msgstr "Uredi nadzorno ploščo"

#: src/components/topics/Content/TopicsList/Keyword/Keyword.js:62
msgid "Edit keyword"
msgstr "Uredi ključno besedo"

#: src/components/medialist/content/AuthorBasketsMenu.js:102
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:19
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:87
msgid "Edit list"
msgstr "Uredi seznam"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:63
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:135
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:60
msgid "Edit mediatypes"
msgstr "Uredi tip medija"

#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:56
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:106
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:131
#: src/components/medialist/forms/FormEditAuthor.js:710
msgid "Edit note"
msgstr "Uredi opombo"

#: src/components/medialist/forms/FormEditAuthor.js:282
#: src/components/medialist/forms/FormEditAuthor.js:448
msgid "Edit profile"
msgstr "Uredi profil"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:67
msgid "Edit recipient"
msgstr "Uredi prejemnika"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:48
msgid "Edit Sender"
msgstr "Uredi pošiljatelja"

#: src/components/newsroom/content/posts/NewsroomPosts.js:300
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:23
msgid "Edit settings"
msgstr "Uredi nastavitve"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:26
msgid "Edit signature"
msgstr "Uredi podpis"

#: src/components/layout/Sidebar/modules/SidebarTags/modalEditTags.js:10
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:279
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:168
msgid "Edit tag"
msgstr "Uredi oznako"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:32
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:58
msgid "Edit topic"
msgstr "Uredi temo"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:59
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:63
msgid "Edit widget"
msgstr "Uredi gradnik"

#: src/components/medialist/forms/modules/FormArray.js:94
msgid "Editorial Office"
msgstr "Uredništvo"

#: src/components/medialist/forms/FormEditAuthor.js:848
#: src/components/medialist/forms/FormEditAuthor.js:1012
msgid "Editorial offices and positions"
msgstr "Uredništva in položaji"

#: src/components/medialist/content/MedialistAuthorCreate.js:24
msgid "Eg. sent press releases, profile edits, published articles related to your press releases."
msgstr "Npr. poslana sporočila za javnost, urejanje profila, objavljeni članki povezani z vašimi sporočili za javnost."

#: src/pages/user/reset-password/index.tsx:19
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:116
#: src/components/staff/admin/user/User.js:229
#: src/components/staff/admin/customer/users/UsersTable.js:68
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:61
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:222
#: src/components/reports/history/RecipientsTableHeader.js:30
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:133
#: src/components/page/auth/SignUp/SignUp.js:37
#: src/components/page/auth/Login/Login.tsx:40
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:128
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:74
#: src/components/medialist/forms/FormEditAuthor.js:793
#: src/components/medialist/forms/FormEditAuthor.js:911
#: src/components/medialist/forms/FormEditAuthor.js:916
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:33
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:100
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:111
#: src/components/emailing/content/EmailDetailEmailContent.js:37
#: src/components/emailing/content/CreateEmailContent.js:262
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:50
msgid "Email"
msgstr "E-pošta"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Email address was verified"
msgstr "E-poštni naslov je bil preverjen"

#. placeholder {0}: campaign.name
#: src/components/emailing/content/CampaignAutocompleteList.tsx:41
msgid "Email copied to {0}"
msgstr "E-pošta kopirana v {0}"

#: src/store/models/ExportStore.js:126
msgid "Email has been successfully sent."
msgstr "E-pošta je bila uspešno poslana."

#: src/components/emailing/content/CreateEmailContent.js:77
msgid "Email is locked and cannot be edited. If you want to edit the email, return it to the draft state."
msgstr "E-pošta je zaklenjena in je ni mogoče urejati. Če želite urejati e-pošto, jo vrnite v stanje osnutka."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:45
msgid "Email is missing"
msgstr "Manjka e-pošta"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:12
msgid "Email is required"
msgstr "Email je obvezen"

#: src/components/emailing/content/CreateEmailContent.js:72
msgid "Email is sending"
msgstr "E-pošta se pošilja"

#: src/helpers/modal/withModalEmailPreview.js:120
msgid "Email preview"
msgstr "Predogled e-pošte"

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:10
msgid "Email reports ({counter})"
msgstr "E-poštna poročila ({counter})"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:142
msgid "Email subject"
msgstr "Zadeva e-pošte"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:36
msgid "Email Subject:"
msgstr "Zadeva e-pošte:"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:62
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:74
msgid "Email successfully duplicated"
msgstr "E-pošta je bila uspešno podvojena"

#: src/components/emailing/content/CreateEmailContent.js:134
msgid "Email was saved"
msgstr "E-pošta je bila shranjena"

#: src/components/emailing/content/CreateEmailContent.js:153
msgid "Email was sent"
msgstr "E-pošta je bila poslana"

#: src/components/emailing/content/CreateEmailContent.js:124
msgid "Email was set to draft"
msgstr "E-pošta je bila nastavljena kot osnutek"

#: src/components/emailing/content/CreateEmailContent.js:74
msgid "Email will be sent at: {scheduledDate}"
msgstr "E-pošta bo poslana ob: {scheduledDate}"

#: src/pages/emailing/settings.tsx:16
#: src/pages/emailing/index.tsx:14
#: src/pages/emailing/campaign/[campaignId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/media-coverage.tsx:18
#: src/pages/emailing/campaign/[campaignId]/index.tsx:19
#: src/pages/emailing/campaign/[campaignId]/email/create.tsx:15
#: src/pages/emailing/campaign/[campaignId]/email/edit/[emailId]/index.tsx:12
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/recipients.tsx:16
#: src/pages/emailing/campaign/[campaignId]/email/[emailId]/index.tsx:16
#: src/components/layout/Sidebar/SidebarMainNav.js:124
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:267
#: src/components/layout/AuthWrapper/constants/features.slides.js:259
#: src/components/emailing/content/promo/PromoEmailing.js:17
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:79
msgid "Emailing"
msgstr "Pošiljanje e-pošte"

#: src/components/reports/history/Compose.js:71
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:27
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:106
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:54
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:158
msgid "Emails"
msgstr "E-pošta"

#: src/components/medialist/forms/modules/MainEmailHelperText.js:6
msgid "Emails from <0>Emailing</0> will be sent to this address."
msgstr "Na ta naslov bodo poslani e-poštni naslovi iz <0>Emailinga</0>."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:488
msgid "Embed"
msgstr "Vstavi"

#: src/components/misc/ActionsBar/View/ViewMenu.js:273
msgid "Emoji reactions"
msgstr "Reakcije z emojiji"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:83
msgid "Empty Body"
msgstr "Prazen članek"

#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:103
msgid "Empty export"
msgstr "Izprazni izvoz"

#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:109
msgid "Empty export?"
msgstr "Izprazni izvoz?"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:74
msgid "Empty Perex"
msgstr "Prazen perex"

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:65
msgid "Empty Title"
msgstr "Prazen naslov"

#: src/components/tariff/MonitoredMedia/RowSelector/RowSelector.js:74
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:89
msgid "Enable"
msgstr "Omogoči"

#: src/components/notifications/Permissions.js:58
msgid "Enable notifications"
msgstr "Omogoči obvestila"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:69
#: src/components/misc/ActionsBar/View/ViewMenu.js:52
#: src/components/misc/ActionsBar/View/ViewMenu.js:264
msgid "Enabled"
msgstr "Omogočeno"

#: src/components/emailing/forms/FormSenderSettings.js:112
msgid "Encryption method"
msgstr "Metoda šifriranja"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:99
msgid "Enforce primary color as header"
msgstr "Uveljavi primarno barvo kot glavo"

#: src/constants/analytics.js:315
#: src/constants/analytics.js:424
#: src/constants/analytics.js:489
#: src/constants/analytics.js:511
#: src/constants/analytics.js:949
#: src/constants/analytics.js:964
#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataEngagement/MetaDataEngagement.js:19
#: src/components/misc/ActionsBar/View/ViewMenu.js:297
msgid "Engagement rate"
msgstr "Stopnja angažiranosti"

#: src/constants/analytics.js:336
msgid "Engagement rate by mention type"
msgstr "Stopnja angažiranosti glede na vrsto omembe"

#: src/constants/analytics.js:334
#: src/constants/analytics.js:443
#: src/constants/analytics.js:509
msgid "Engagement rate by sentiment"
msgstr "Stopnja angažiranosti glede na sentiment"

#: src/constants/analytics.js:445
msgid "Engagement rate by social network"
msgstr "Stopnja angažiranosti po družbenem omrežju"

#: src/components/analytics/SocialMedia.js:25
msgid "Engagement summary"
msgstr "Povzetek interakcij"

#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:62
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:18
msgid "Enter a word or phrase"
msgstr "Vnesite besedo ali besedno zvezo"

#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:53
msgid "Enter text here..."
msgstr "Vnesite besedilo tukaj..."

#: src/components/emailing/forms/FormSenderSettings.js:83
msgid "Enter the hostname of your SMTP server"
msgstr "Vnesite ime gostitelja vašega SMTP strežnika"

#: src/components/emailing/forms/FormSenderSettings.js:85
msgid "Enter the hostname of your SMTP server. This is usually in the format of \"smtp.yourdomain.com\"."
msgstr "Vnesite ime gostitelja vašega SMTP strežnika. Običajno je v obliki \"smtp.vasadomena.com\"."

#: src/components/emailing/forms/FormSenderSettings.js:98
msgid "Enter the password to login to SMTP server"
msgstr "Vnesite geslo za prijavo na SMTP strežnik"

#: src/components/emailing/forms/FormSenderSettings.js:100
msgid "Enter the password to login to SMTP server. This is usually the same password you use for your email."
msgstr "Vnesite geslo za prijavo na SMTP strežnik. To je običajno isto geslo, ki ga uporabljate za prijavo v e-pošto."

#: src/components/emailing/forms/FormSenderSettings.js:90
msgid "Enter the username to login to SMTP server"
msgstr "Vnesite uporabniško ime za prijavo na SMTP strežnik"

#. placeholder {0}: initialValues.email
#: src/components/emailing/forms/FormSenderSettings.js:92
msgid "Enter the username to login to SMTP server. If left blank, \"{0}\" is used by default."
msgstr "Vnesite uporabniško ime za prijavo na SMTP strežnik. Če je polje prazno, se privzeto uporabi \"{0}\"."

#: src/pages/user/reset-password/index.tsx:16
msgid "Enter your email. We'll send you instructions on how to reset your password."
msgstr "Vnesite svoj e-poštni naslov. Poslali vam bomo navodila za ponastavitev gesla."

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:61
msgid "Error detail"
msgstr "Podrobnosti o napaki"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:164
msgid "Estimated number of distributed copies (print and digital)."
msgstr "Oceno števila distribuiranih izvodov (tiskanih in digitalnih)."

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:17
msgid "Eternal"
msgstr "Eternal"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Everything enabled"
msgstr "Vse omogočeno"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:24
msgid "Everything went well."
msgstr "Vse je šlo dobro."

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:42
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:76
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:116
msgid "Exact match"
msgstr "Natančno ujemanje"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:82
msgid "Exact match with separator"
msgstr "Natančno ujemanje z ločilom"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:48
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:88
msgid "Exact match, including letter size"
msgstr "Natančno ujemanje, vključno z velikostjo črk"

#: src/pages/newsroom/index.js:72
msgid "Example Newsroom"
msgstr "Primer Medijskega središča"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Exit fullscreen"
msgstr "Izhod iz celozaslonskega načina"

#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:83
msgid "Expense type"
msgstr "Vrsta stroška"

#: src/pages/staff/admin/customers/[customerId]/expenses.js:12
#: src/components/staff/admin/customer/expenses/Expenses.js:26
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:50
msgid "Expenses"
msgstr "Stroški"

#: src/components/page/auth/Expired/Expired.js:24
msgid "expired"
msgstr "potekel"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:12
msgid "Expired"
msgstr "Potekel"

#: src/components/staff/admin/workspace/Workspace.js:316
msgid "Expires at"
msgstr "Poteče dne"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:30
msgid "Expires on: {formattedDate}"
msgstr "Poteče: {formattedDate}"

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:219
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:82
#: src/components/exportList/History/History.js:28
#: src/components/exportList/Content/Content.tsx:58
#: src/components/exportList/Content/Content.tsx:58
msgid "Export"
msgstr "Izvoz"

#: src/components/misc/portable/PortableExport/PortableExport.js:64
#: src/components/misc/portable/PortableExport/PortableExport.js:104
msgid "Export all articles"
msgstr "Izvozi vse članke"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:142
msgid "Export article"
msgstr "Izvozi članek"

#: src/components/misc/portable/PortableExport/PortableExport.js:62
#: src/components/misc/portable/PortableExport/PortableExport.js:102
msgid "Export articles"
msgstr "Izvoz člankov"

#: src/components/staff/admin/workspace/Workspace.js:329
msgid "Export basket mode"
msgstr "Izvozni košarica"

#: src/pages/export/history.js:12
#: src/components/exportList/History/History.js:35
msgid "Export History"
msgstr "Zgodovina izvoza"

#: src/store/models/ExportStore.js:311
#: src/store/models/monitoring/Inspector/Inspector.ts:447
msgid "Export is full."
msgstr "Izvozni koš je poln. Vanj ni mogoče dodati več člankov."

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:21
msgid "Export list is empty"
msgstr "Izvozni seznam je prazen"

#: src/components/medialist/forms/FormEditAuthor.js:362
#: src/components/medialist/forms/FormEditAuthor.js:502
msgid "Export XLSX"
msgstr "Izvoz XLSX"

#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:97
#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:17
msgid "Exports to download"
msgstr "Izvozi za prenos"

#: src/pages/external-analytics.tsx:29
#: src/components/layout/Sidebar/SidebarMainNav.js:145
msgid "External analytics"
msgstr "Zunanja analitika"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:41
msgid "External Communication Manager"
msgstr "Upravitelj zunanje komunikacije"

#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:29
msgid "Facebook Post URL"
msgstr "URL objave na Facebooku"

#: src/components/emailing/content/EmailingSettingsContent.js:30
msgid "Failed to fetch your email address from the service provider. Please try again or contact support if the issue persists."
msgstr "Ni uspelo pridobiti vašega e-poštnega naslova od ponudnika storitev. Poskusite znova ali kontaktirajte podporo, če težava ostaja."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:41
msgid "Feature has been requested."
msgstr "Funkcionalnost je bila zahtevana."

#: src/components/forms/dashboard/Export/ExportForm.js:66
msgid "File format"
msgstr "Oblika datoteke"

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:24
msgid "Files"
msgstr "Datoteke"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:207
msgid "Fill from URL"
msgstr "Izpolni iz URL"

#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:105
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:122
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:136
#: src/components/layout/MntrFiltersBar/forms/FormFilterItems/FormFilterItems.js:38
#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:13
#: src/components/layout/MntrFiltersBar/forms/FormChannelSearch/FormChannelSearch.js:31
#: src/components/emailing/content/CampaignAutocomplete.tsx:25
msgid "Filter"
msgstr "Filter"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:104
msgid "Filter by absolute score"
msgstr "Filtriraj po absolutni oceni"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorActivity.js:27
msgid "Filter by activity"
msgstr "Filtriraj po aktivnosti"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterArticleType.js:26
msgid "Filter by article type"
msgstr "Filtriraj po vrsti članka"

#: src/components/layout/MntrFiltersBar/forms/FormAuthor/FormAuthor.tsx:13
msgid "Filter by author"
msgstr "Filtriraj po avtorju"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTypeMultiselect.js:32
msgid "Filter by author type"
msgstr "Filtriraj po vrsti avtorja"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/withModalMedialistArticlesFilter.tsx:21
msgid "Filter by author's articles"
msgstr "Filtriraj po člankih avtorja"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterContactInformationMultiselect.js:32
msgid "Filter by contact"
msgstr "Filtriraj po kontaktu"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:98
#: src/components/layout/MntrFiltersBar/modules/MenuFilterCountryMultiselect.js:29
msgid "Filter by country"
msgstr "Filtriraj po državi"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:239
msgid "Filter by date"
msgstr "Filtriraj po datumu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorFocusAreasMultiselect.js:32
msgid "Filter by focus area"
msgstr "Filtriraj po kategoriji"

#: src/components/layout/MntrFiltersBar/forms/FormFilterAuthorTitles/FormFilterAuthorTitles.js:32
msgid "Filter by job position"
msgstr "Filtriraj po delovnem mestu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterLanguageMultiselect.js:26
msgid "Filter by language"
msgstr "Filtriraj po jeziku"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSourceTVR.js:24
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:115
msgid "Filter by media"
msgstr "Filtriraj po mediju"

#: src/components/emailing/forms/FormFilter.js:35
msgid "Filter by name"
msgstr "Filtriraj po imenu"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:50
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:80
msgid "Filter by rank"
msgstr "Filtriraj po rangu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:187
msgid "Filter by reach"
msgstr "Filtriraj po dosegu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:141
msgid "Filter by relevance"
msgstr "Filtriraj po ustreznosti"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSentimentMultiselect.js:27
msgid "Filter by sentiment"
msgstr "Filtriraj po sentimentu"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:59
msgid "Filter sources"
msgstr "Filtriraj vire"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:120
#: src/components/layout/MntrFiltersBar/modals/withModalPageNumbers.js:15
msgid "Filter specific pages"
msgstr "Filtriraj določene strani"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:354
msgid "Filter tags"
msgstr "Filtriraj oznake"

#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:76
msgid "Filter topics"
msgstr "Filtriraj teme"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:56
msgid "Final version"
msgstr "Končna različica"

#. placeholder {0}: account.enums.analytics.export_charts_file_format.find( ({ id }) => id === fileFormatId, ).text
#: src/components/misc/Capture/Capture.js:304
msgid "finalizing {0} file for download"
msgstr "zaključevanje prenosa datoteke {0}"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:67
#: src/components/medialist/content/FeedMedialist/FeedMedialistPromo.js:59
msgid "Find out more"
msgstr "Izvedi več"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:228
msgid "Find similar articles"
msgstr "Poišči podobne članke"

#. js-lingui-explicit-id
#: src/components/misc/ActionsBar/Selector/Selector.js:45
msgid "selector.first"
msgstr "Prvih"

#: src/components/page/auth/SignUp/SignUp.js:20
msgid "First Name"
msgstr "Ime"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:21
msgid "First Step"
msgstr "Prvi korak"

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:141
msgid "Focus area"
msgstr "Kategorije"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:301
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:53
msgid "Font Size"
msgstr "Velikost pisave"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:30
msgid "For each functionality choose one of three levels:"
msgstr "Za vsako funkcionalnost izberite eno od treh stopenj:"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:68
msgid "For example"
msgstr "Na primer"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:81
msgid "For example, \"1,4-6\" will filter out 1,4,5,6"
msgstr "Na primer, \"1,4-6\" bo filtriral strani 1,4,5,6"

#: src/components/dashboards/PageExpiredSharedDashboard/PageExpiredSharedDashboard.js:40
msgid "For renewal, contact account admin."
msgstr "Za obnovitev se obrnite na skrbnika računa."

#. placeholder {0}: self.filters.topic_monitors[0].text
#: src/store/models/OurChart.js:853
msgid "for topic: {0}"
msgstr "za temo: {0}"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:39
msgid "Forbidden:"
msgstr "Prepovedano:"

#: src/components/layout/AuthWrapper/constants/features.slides.js:146
msgid "Foreign Media"
msgstr "Tujina mediji"

#: src/components/page/auth/Login/Login.tsx:86
msgid "Forgot password?"
msgstr "Ste pozabili geslo?"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:51
msgid "Format"
msgstr "Format"

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:18
msgid "Formatting is finished successfully. The prepared file is downloaded automatically. Edit it manually if needed and upload it to the medialist in the next step."
msgstr "Oblikovanje je bilo uspešno zaključeno. Pripravljena datoteka se samodejno prenese. Če je potrebno, jo ročno uredite in naložite v medijski seznam v naslednjem koraku."

#: src/components/reports/Content/ReportsList/ReportsForm.js:111
msgid "Frequency of report dispatch"
msgstr "Pogostost pošiljanja poročila"

#: src/components/reports/Content/ReportsList/ReportsForm.js:279
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:56
msgid "From"
msgstr "Od"

#: src/components/forms/dashboard/ExportResend/ExportResend.js:90
msgid "From email"
msgstr "Email pošiljatelja"

#: src/components/misc/ActionsBar/View/ViewMenu.js:101
msgid "Frontpage promo"
msgstr "Promocija na prvi strani"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:32
msgid "Full access:"
msgstr "Popoln dostop:"

#: src/components/misc/ActionsBar/View/ViewMenu.js:148
msgid "Full page ad price"
msgstr "Cena celostranskega oglasa"

#: src/components/misc/VideoPlayer/Controls.js:143
msgid "Fullscreen"
msgstr "Celoten zaslon"

#: src/components/tariff/Permissions/Permissions.js:40
msgid "Functionality"
msgstr "Funkcionalnost"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:93
msgid "Generate structure"
msgstr "Ustvari strukturo"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:149
msgid "Generate text"
msgstr "Ustvari besedilo"

#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:40
msgid "Generating link"
msgstr "Povezava se ustvarja"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:115
msgid "Get a comprehensive view of the topics that matter to you."
msgstr "Pridobite celovit pregled tem, ki so za vas pomembne."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:102
msgid "Get a more complete view of the topics that interest you"
msgstr "Pridobite bolj celovit pregled tem, ki vas zanimajo"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:34
msgid "Get access to our media archive."
msgstr "Pridobite dostop do našega medijskega arhiva."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:71
msgid "Go back to Emailing"
msgstr "Nazaj na e-pošto"

#: src/components/staff/admin/workspace/Workspace.js:403
msgid "Google Translate price: 1 article = 2.5 Kč = 0.09 €"
msgstr "Google Translate cena: 1 članek = 2.5 Kč = 0.09 €"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:269
msgid "Gross Rating Point"
msgstr "Gross Rating Point"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:107
msgid "Group articles"
msgstr "Združi članke"

#: src/constants/stats.ts:16
#: src/constants/analytics.js:1010
#: src/components/widgets/modules/stats/WidgetStats.js:150
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:266
#: src/components/misc/ActionsBar/View/ViewMenu.js:211
msgid "GRP"
msgstr "GRP"

#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:24
msgid "Hashtags"
msgstr "Hashtagi"

#: src/components/staff/admin/workspace/getWorkspaceAttributes.js:33
msgid "Hasn't started yet"
msgstr "Še ni začel"

#: src/pages/sign-up-completion.tsx:42
#: src/components/page/auth/SignUp/SignUp.js:81
msgid "Have an account? Sign in"
msgstr "Imate račun? Prijavite se"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:11
msgid "Head of External and Internal Communication"
msgstr "Vodja zunanje in notranje komunikacije"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:225
msgid "Header"
msgstr "Glava"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:166
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:172
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:178
msgid "Heading"
msgstr "Naslov"

#: src/pages/user/reactivate-24.js:37
msgid "Hello!<0/><1/>Thank you for your interest in trying Mediaboard with all the features. Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Pozdravljeni!<0/><1/>Hvala za vaše zanimanje za preizkus Mediaboard z vsemi funkcijami. Prosimo, potrdite s klikom na spodnji gumb. Kontaktirali vas bomo v najkrajšem možnem času.<2/><3/>Lep pozdrav,<4/><5/>ekipa {appName}"

#: src/pages/user/yoy-analysis.js:37
msgid "Hello!<0/><1/>Thank you for your interest! Please confirm by clicking on the button below. We will contact you as soon as possible.<2/><3/>Kind regards,<4/><5/>{appName} team"
msgstr "Pozdravljeni!<0/><1/>Hvala za vaš interes! Prosimo, potrdite s klikom na spodnji gumb. Kontaktirali vas bomo v najkrajšem možnem času.<2/><3/>Lep pozdrav,<4/><5/>ekipa {appName}"

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:30
msgid "Hello!<0/><1/>Thanks for your interest in our Emailing platform. To activate it you need to verify your email first. To do so just check, that the email you have entered is correct and click the activation button.<2/><3/><4/><5/>Best regards,<6/><7/>{appName} team"
msgstr "Pozdravljeni!<0/><1/>Hvala za vaše zanimanje za našo platformo za pošiljanje e-pošte. Za aktivacijo morate najprej preveriti svoj e-poštni naslov. Preverite, ali je vneseni e-poštni naslov pravilen, in kliknite na aktivacijski gumb.<2/><3/><4/><5/>Lep pozdrav,<6/><7/>ekipa {appName}"

#: src/helpers/modal/withModalHelp.tsx:17
#: src/components/layout/Sidebar/SidebarMainNav.js:182
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:18
#: src/components/OurChart/OurChartAdvanced.js:268
msgid "Help"
msgstr "Pomoč"

#: src/components/medialist/content/MedialistAuthorCreate.js:20
msgid "Here you will see all the activity related to this author."
msgstr "Tukaj boste videli vse dejavnosti, povezane s tem avtorjem."

#: src/components/emailing/components/FunnelStats/StatBlock.tsx:121
msgid "Here you will see newsroom analytics affected by the campaign"
msgstr "Tukaj boste videli analitiko medijska središča in kako je kampanja vplivala"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "hide"
msgstr "skrij"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:246
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:210
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:249
msgid "Hide"
msgstr "Skrij"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:561
msgid "Hide header and footer"
msgstr "Skrij glavo in nogo"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:63
msgid "hide stats"
msgstr "skrij statistiko"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:230
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:241
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:144
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:153
msgid "Hide tag"
msgstr "Skrij oznako"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:229
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:190
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:202
msgid "Hide topic"
msgstr "Skrij temo"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:210
msgid "Hide topics in folder"
msgstr "Skrij teme v mapi"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:65
msgid "Highlight Only"
msgstr "Samo označi"

#: src/components/tariff/Permissions/Permissions.js:51
msgid "Hints"
msgstr "Namigi"

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:28
#: src/components/exportList/Content/Content.tsx:72
msgid "History"
msgstr "Zgodovina"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:84
msgid "Homepage url"
msgstr "Spletni naslov podjetja"

#: src/components/emailing/forms/FormSenderSettings.js:82
msgid "Host"
msgstr "Gost"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "Hostname"
msgstr "Ime gostitelja"

#: src/components/staff/admin/workspace/Workspace.js:504
msgid "How many years back is the user allowed to search in media archive."
msgstr "Koliko let nazaj lahko uporabnik išče v medijskem arhivu."

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:88
msgid "How permissions work"
msgstr "Kako delujejo dovoljenja"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:135
msgid "How to help the AI generate a more satisfying and detailed email"
msgstr "Kako pomagati umetni inteligenci ustvariti bolj zadovoljiv in podroben e-poštni naslov"

#: src/components/layout/Sidebar/SidebarMainNav.js:185
msgid "How to use {appName}"
msgstr "Kako uporabljati aplikacijo {appName}"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:111
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:91
msgid "HTML"
msgstr "HTML"

#: src/pages/user/yoy-analysis.js:59
#: src/pages/user/reactivate-24.js:59
msgid "I am interested"
msgstr "Zanima me"

#: src/components/staff/admin/user/WorkspacesTable.js:74
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:74
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:80
msgid "ID"
msgstr "ID"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:43
msgid "Ideal for those with their own content ready."
msgstr "Idealno za tiste, ki imajo svojo vsebino pripravljeno."

#: src/components/staff/admin/workspace/Workspace.js:382
msgid "If set to 0, then: no export basket, no exporting or email sending from feed or export basket."
msgstr "Če je nastavljeno na 0, potem: ni izvoznega košarice, ni izvoza ali pošiljanja e-pošte iz vira ali izvoznega košarice."

#: src/helpers/modal/withModalReportArticle.tsx:23
msgid "If the article has a bad transcript or screenshot, please report the problem and our staff will look into it and fix the issue."
msgstr "Če ima članek slab prepis ali posnetek zaslona, prosimo, da prijavite težavo in naše osebje jo bo preučilo ter odpravilo."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:92
msgid "If this was a mistake or if you'd like to re-subscribe at any time, please contact us at"
msgstr "Če je prišlo do napake ali če se želite kadar koli znova naročiti, nas kontaktirajte na"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:64
msgid "If this was a mistake or you'd prefer to stay available for our emails, no further action is needed."
msgstr "Če je šlo za napako ali želite ostati na voljo za naša e-poštna sporočila, ni potrebnih nobenih dodatnih ukrepov."

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:39
msgid "If you don't remember your current password, you can <0>reset it</0> or contact us at <1>{salesEmail}</1>."
msgstr "Če se ne spomnite svojega trenutnega gesla, ga lahko <0>ponastavite</0> ali nas kontaktirate na <1>{salesEmail}</1>."

#: src/components/page/auth/Expired/Expired.js:63
msgid "If you liked our service and would like to purchase the account, send us an email to <0>{salesEmail}</0>"
msgstr "Če vam je bila naša storitev všeč in želite kupiti račun, nam pošljite e-pošto na <0>{salesEmail}</0>"

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:20
msgid "If you would like to purchase a workspace account, send us an email to <0>{salesEmail}</0>"
msgstr "Če želite kupiti račun za delovni prostor, nam pošljite e-pošto na <0>{salesEmail}</0>"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:126
msgid "If you'd like to re-subscribe, please contact us at"
msgstr "Če se želite znova naročiti, nas kontaktirajte na"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:56
msgid "If you'd like to unsubscribe from emails, sent via mediaboard.com, simply click the button below. Your email <0>{0}</0> will no longer receive new emails form us."
msgstr "Če se želite odjaviti od prejemanja e-pošte, poslane prek mediaboard.com, preprosto kliknite na spodnji gumb. Vaš e-poštni naslov <0>{0}</0> ne bo več prejemal novih e-poštnih sporočil od nas."

#: src/components/reports/history/Content.js:38
msgid "If your report wasn't delivered, make sure to check your spam folder and your promotions inbox."
msgstr "Če vašega poročila niste prejeli, preverite mapo z vsiljeno pošto in mapo za promocije."

#: src/store/models/dashboards/DashboardPreview.js:147
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:49
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:47
#: src/components/misc/MntrEditor/extensions/ExtensionImageGallery.js:49
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:70
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:70
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewImage/PreviewImage.js:29
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:38
msgid "Image"
msgstr "Slika"

#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/ImageGalleryList.tsx:61
msgid "Images"
msgstr "Slike"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:70
msgid "Import"
msgstr "Uvoz"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:181
#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:84
#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:13
#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:7
msgid "Import contacts"
msgstr "Uvozi stike"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:54
msgid "Import options"
msgstr "Možnosti uvoza"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:42
msgid "Import to"
msgstr "Uvozi v"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:75
msgid "Import your already formatted contact list or manually completed template."
msgstr "Uvozite že oblikovan seznam stikov ali ročno izpolnjeno predlogo."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:106
msgid "Import your contacts to medialist"
msgstr "Uvozite svoje stike v medialist"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:37
msgid "Imprint"
msgstr "Kolofon"

#. placeholder {0}: formatDate(lowerDate, 'd. M. yyyy')
#. placeholder {1}: formatDate(upperDate, 'd. M. yyyy')
#: src/helpers/getTitleWithDateFromTo.js:5
msgid "in period from {0} to {1}"
msgstr "v obdobju od {0} do {1}"

#: src/components/newsroom/content/posts/NewsroomPosts.js:297
msgid "In the settings you can edit basic information about the Newsroom, appearance, web address, etc."
msgstr "V nastavitvah lahko uredite osnovne informacije o Medijskem središču, videz, spletni naslov itd."

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:13
msgid "In three years with Mediaboard, our experience has been exceptional. Their professionalism, wide range of services, and top-notch quarterly and annual analyses are highly valuable. We recommend Mediaboard for quality and reliability."
msgstr "V treh letih sodelovanja z Mediaboardom so bile naše izkušnje izjemne. Njihova profesionalnost, širok nabor storitev in vrhunske četrtletne in letne analize so za nas izjemno dragocene. Mediaboard priporočamo zaradi kakovosti in zanesljivosti."

#: src/components/settings/SettingsApplication/SettingsApplication.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:106
msgid "In-app currency"
msgstr "Valuta v aplikaciji"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:173
#: src/components/staff/admin/user/User.js:121
#: src/components/staff/admin/customer/users/UsersTable.js:116
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoStaticItem.js:14
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:31
#: src/components/forms/dashboard/Search/SearchUsers.js:99
msgid "Inactive"
msgstr "Neaktiven"

#: src/components/reports/Content/ReportsList/FormToggleActive/FormToggleActive.js:36
msgid "Inactive report"
msgstr "Neaktivno poročilo"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:22
msgid "Include all the key points you want to specifically mention in your article. These should be the essential details, arguments, or highlights that support and enhance the main content.\""
msgstr "Vključite vse ključne točke, ki jih želite posebej omeniti v svojem članku. To bi morale biti bistvene podrobnosti, argumenti ali poudarki, ki podpirajo in izboljšujejo glavno vsebino."

#: src/components/tariff/TariffLimits/TariffLimits.js:51
#: src/components/tariff/TariffLimits/TariffLimits.js:88
#: src/components/tariff/TariffLimits/TariffLimits.js:123
#: src/components/tariff/TariffLimits/TariffLimits.js:143
#: src/components/tariff/TariffLimits/TariffLimits.js:159
#: src/components/tariff/TariffLimits/TariffLimits.js:176
#: src/components/tariff/TariffLimits/TariffLimits.js:195
#: src/components/tariff/TariffLimits/TariffLimits.js:212
#: src/components/tariff/TariffLimits/TariffLimits.js:233
#: src/components/tariff/TariffLimits/TariffLimits.js:250
#: src/components/tariff/TariffLimits/TariffLimits.js:283
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:20
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:114
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:85
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:102
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:110
msgid "Increase limit"
msgstr "Povečaj omejitev"

#: src/components/tariff/TariffLimits/TariffLimits.js:50
#: src/components/tariff/TariffLimits/TariffLimits.js:87
#: src/components/tariff/TariffLimits/TariffLimits.js:122
#: src/components/tariff/TariffLimits/TariffLimits.js:142
#: src/components/tariff/TariffLimits/TariffLimits.js:158
#: src/components/tariff/TariffLimits/TariffLimits.js:175
#: src/components/tariff/TariffLimits/TariffLimits.js:194
#: src/components/tariff/TariffLimits/TariffLimits.js:211
#: src/components/tariff/TariffLimits/TariffLimits.js:232
#: src/components/tariff/TariffLimits/TariffLimits.js:249
#: src/components/tariff/TariffLimits/TariffLimits.js:282
#: src/components/tariff/TariffLimits/SingleValueLimit/SingleValueLimit.js:18
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:84
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:101
#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:109
msgid "Increase limit?"
msgstr "Povečati omejitev?"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:52
msgid "Indicate the desired tone (formal, casual) and style (informative, promotional)."
msgstr "Navedite želeni ton (formalen, neformalen) in slog (informativni, promocijski)."

#: src/components/monitoring/FeedList/FeedListItem/MetaData/modules/MetaDataScore/MetaDataScore.js:38
msgid "influence score"
msgstr "ocena vpliva"

#: src/constants/stats.ts:21
#: src/constants/analytics.js:262
#: src/constants/analytics.js:371
#: src/constants/analytics.js:451
#: src/constants/analytics.js:483
#: src/constants/analytics.js:644
#: src/constants/analytics.js:659
#: src/constants/analytics.js:929
#: src/constants/analytics.js:944
#: src/components/widgets/modules/stats/WidgetStats.js:162
#: src/components/misc/ActionsBar/View/ViewMenu.js:289
msgid "Influence score"
msgstr "Ocena vpliva"

#: src/constants/analytics.js:283
#: src/constants/analytics.js:530
msgid "Influence score by mention type"
msgstr "Ocena vpliva glede na vrsto omembe"

#: src/constants/analytics.js:281
#: src/constants/analytics.js:390
#: src/constants/analytics.js:481
msgid "Influence score by sentiment"
msgstr "Vplivna ocena (Influence Score) glede na sentiment"

#: src/constants/analytics.js:392
msgid "Influence score by social network"
msgstr "Ocena vpliva glede na družbeno omrežje"

#: src/components/emailing/content/sender/EmailingSenderContent.js:17
msgid "Initial Emailing settings"
msgstr "Začetne nastavitve e-pošte"

#: src/components/misc/MntrEditor/modules/FormEditorEmbedUrl.tsx:124
msgid "Insert"
msgstr "Vstavi"

#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:155
msgid "Insert button label to view preview"
msgstr "Vnesite oznako gumba za ogled predogleda"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:125
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:169
msgid "Insert HTML code to view preview"
msgstr "Vstavite HTML kodo za ogled predogleda"

#: src/components/emailing/content/CreateEmailContent.js:408
msgid "Insert internal name of email"
msgstr "Vnesite interno ime e-pošte"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:363
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:385
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:409
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:413
msgid "Insert link"
msgstr "Vstavi povezavo"

#: src/components/emailing/content/CreateEmailContent.js:449
msgid "Insert subject"
msgstr "Vnesite zadevo"

#: src/components/medialist/forms/FormEditAuthor.js:736
#: src/components/medialist/forms/FormEditAuthor.js:1031
msgid "Insert text..."
msgstr "Vstavite besedilo..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:121
msgid "Instructions"
msgstr "Navodila"

#: src/constants/analytics.js:416
msgid "Interactions by sentiment"
msgstr "Interakcije po sentimentu"

#: src/constants/analytics.js:418
msgid "Interactions by social network"
msgstr "Interakcije po družbenem omrežju"

#: src/constants/analytics.js:227
#: src/constants/analytics.js:639
#: src/constants/analytics.js:774
#: src/components/layout/AuthWrapper/constants/features.slides.js:199
msgid "Interactions on social networks"
msgstr "Interakcije na družbenih omrežjih"

#: src/constants/analytics.js:225
msgid "Interactions on social networks by sentiment"
msgstr "Interakcije na družbenih omrežjih po sentimentu"

#: src/components/emailing/content/CreateEmailContent.js:405
msgid "Internal name of email"
msgstr "Notranje ime e-pošte"

#: src/components/emailing/forms/FormEmailRecipients.js:37
msgid "Invalid"
msgstr "Neveljavno"

#: src/components/article/Content.js:13
msgid "Invalid article link"
msgstr "Neveljavna povezava do članka"

#. placeholder {0}: format( startOfYear(today), DATE_FORMAT, )
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:83
msgid "Invalid date format. Expected format is {0}"
msgstr "Neveljaven format datuma. Pričakovani format je {0}"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:16
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:10
#: src/components/reports/Content/ReportsList/RecipientsList/FormAddRecipients.js:33
msgid "Invalid email format"
msgstr "Neveljavna oblika e-pošte"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:118
msgid "Invalid page number"
msgstr "Neveljavna številka strani"

#: src/components/forms/inspector/FormMediaEditor.js:76
#: src/components/forms/inspector/FormMediaEditor.js:79
msgid "Invalid time format. Enter hh:mm:ss"
msgstr "Neveljaven format časa. Vnesite hh:mm:ss"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:57
msgid "Invoice no."
msgstr "Številka računa"

#: src/pages/staff/admin/customers/[customerId]/invoices.js:12
#: src/components/staff/admin/customer/invoices/Invoices.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:42
msgid "Invoices"
msgstr "Računi"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:56
msgid "Irrelevant"
msgstr "Nepomembno"

#: src/components/misc/Changelog/ChangelogTableRow.js:156
msgid "Irreversible"
msgstr "Nepovratno"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:104
msgid "Is overdue"
msgstr "Zapadlo"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:296
msgid "Is there a problem with the article?"
msgstr "Ali je s člankom kakšna težava?"

#. placeholder {0}: data.publication.issue
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:225
msgid "Issue: {0}"
msgstr "Številka: {0}"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:72
msgid "Issued via"
msgstr "Izdano prek"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:368
msgid "It appears above the description on the search results page."
msgstr "Pojavi se nad opisom na strani z rezultati iskanja."

#: src/pages/_error.js:49
msgid "It looks like you're trying to access a malformed URL. Please review it and try again."
msgstr "Videti je, da poskušate dostopati do napačnega URL-ja. Prosimo, preverite ga in poskusite znova."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:131
msgid "Italic"
msgstr "Ležeče"

#: src/components/tvr/Inspector/InspectorMonitora/InspectorMonitora.js:144
msgid "Item '<0>{title}</0>' will be removed."
msgstr "Element '<0>{title}</0>' bo odstranjen."

#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:179
msgid "Job position"
msgstr "Delovno mesto"

#: src/components/medialist/forms/modules/FormArray.js:107
msgid "Job Position"
msgstr "Delovno mesto"

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:21
msgid "formatNumber.k"
msgstr "tis."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:68
msgid "Keep original"
msgstr "Ohrani izvirnik"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:20
msgid "Key points list"
msgstr "Seznam ključnih točk"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:56
msgid "Key Points:"
msgstr "Ključne točke:"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:40
msgid "Keyword"
msgstr "Ključna beseda"

#: src/helpers/modal/withModalTvrTopics.tsx:53
#: src/constants/analytics.js:1054
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/KeywordsListMedia/KeywordsListMedia.js:22
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:53
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:119
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:255
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:337
msgid "Keywords"
msgstr "Ključne besede"

#: src/components/misc/ResendSettings/SaveResendSettings/FormSaveResendSettings.js:21
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:122
#: src/components/misc/ExportSettings/SaveExportSettings/FormSaveExportSettings.js:21
#: src/components/emailing/forms/FormAddCampaign.tsx:15
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:26
msgid "Label"
msgstr "Oznaka"

#: src/constants/analytics.js:870
#: src/components/staff/admin/user/User.js:261
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:274
#: src/components/misc/ActionsBar/View/ViewMenu.js:325
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:310
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:329
#: src/components/layout/Header/UserMenu/UserMenu.js:73
msgid "Language"
msgstr "Jezik"

#: src/constants/analytics.js:883
msgid "Languages"
msgstr "Jeziki"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:106
msgid "Languages & connected newsrooms"
msgstr "Jeziki in povezana Medijska središča"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:234
msgid "Last access"
msgstr "Zadnji dostop"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:122
#: src/components/staff/admin/customer/users/UsersTable.js:74
msgid "Last login"
msgstr "Zadnja prijava"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:147
msgid "Last month"
msgstr "Prejšnji mesec"

#: src/components/page/auth/SignUp/SignUp.js:29
msgid "Last Name"
msgstr "Priimek"

#: src/components/newsroom/components/PostsList/PostsList.js:166
#: src/components/newsroom/components/PostsList/PostsList.js:181
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:152
msgid "Last update"
msgstr "Zadnja posodobitev"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:128
msgid "Last week"
msgstr "Prejšnji teden"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:160
msgid "Last year"
msgstr "Lansko leto"

#: src/components/emailing/forms/FormSenderSettings.js:106
msgid "Leave blank to use the default port"
msgstr "Pustite prazno, da uporabite privzeta vrata"

#: src/components/layout/Header/UserMenu/UserMenu.js:97
msgid "Light"
msgstr "Svetel"

#: src/components/settings/SettingsTheme/SettingsThemePreview/SettingsThemePreview.js:193
msgid "Light mode preview"
msgstr "Predogled svetlega načina"

#: src/components/medialist/forms/FormEditAuthor.js:504
msgid "limit"
msgstr "omejitev"

#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:260
#: src/store/models/monitoring/MedialistMapItem/MedialistMapItem.js:285
#: src/store/models/authors/AuthorsStore.js:410
#: src/store/models/authors/AuthorsStore.js:435
msgid "Limit exceeded. Sucessfully exported {generated} of {requested} requested authors."
msgstr "Presežena omejitev. Uspešno izvoženih {generated} od zahtevanih {requested} avtorjev."

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Limit reached. You have selected too many articles."
msgstr "Dosežena je omejitev. Izbrali ste preveč člankov."

#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:35
msgid "Limits"
msgstr "Omejitve"

#: src/components/OurChart/OurChartAdvanced.js:162
msgid "Line"
msgstr "Črtni"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:81
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:325
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:131
msgid "Link"
msgstr "Povezava"

#: src/helpers/modal/withModalCreateSharedDashboard/withModalCreateSharedDashboard.tsx:40
#: src/helpers/modal/withModalCreateSharedDashboard/ModalContent.tsx:69
#: src/components/staff/admin/user/User.js:67
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:224
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:15
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:16
#: src/components/exportList/History/HistoryTable/HistoryTable.js:111
msgid "Link has been copied to the clipboard."
msgstr "Povezava je bila kopirana v odložišče."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:116
msgid "Link to other language"
msgstr "Povezava na drug jezik"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:95
msgid "Link to the article"
msgstr "Povezava do članka"

#: src/components/emailing/content/EmailingSettingsContent.js:31
msgid "Linking with Google account timed out. Please, try again."
msgstr "Čas za povezavo z Googlovim računom je potekel. Poskusite znova."

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:240
#: src/components/monitoring/Inspector/InspectorMonitora/Links/Links.js:14
msgid "Links"
msgstr "Povezave"

#: src/components/medialist/content/AuthorBasketsMenu.js:135
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:124
msgid "List {label} will be removed."
msgstr "Seznam {label} bo odstranjen."

#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:31
#: src/components/forms/baskets/FormNewBasket.js:26
msgid "List name"
msgstr "Ime seznama"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:26
msgid "List of tags"
msgstr "Seznam oznak"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:99
msgid "List of topics"
msgstr "Seznam tem"

#: src/components/medialist/forms/FormEditAuthor.js:660
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:97
msgid "Lists"
msgstr "Seznami"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:20
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:20
msgid "Load"
msgstr "Naloži"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:54
msgid "Load from"
msgstr "Naloži iz"

#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
msgid "Load more"
msgstr "Naloži več"

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:140
msgid "Load more..."
msgstr "Naloži več..."

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:46
#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:48
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:48
msgid "Load settings"
msgstr "Naloži nastavitve"

#: src/components/tvr/Content/Content.js:82
#: src/components/trash/Content.js:39
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:70
#: src/components/reports/history/HistoryTable.js:491
#: src/components/notifications/Permissions.js:84
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/newsroom/content/dashboard/ChartVisits.js:127
#: src/components/monitoring/WorkspaceArticles/Intro.js:20
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:57
#: src/components/monitoring/FeedList/LoadMore/LoadMore.js:27
#: src/components/monitoring/FeedChart/FeedChart.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:47
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:55
#: src/components/misc/MntrEditor/forms/FormMediaUpload/UploadProgress.js:31
#: src/components/medialist/content/MedialistHeading.js:14
#: src/components/medialist/content/MedialistInspector/AuthorChart/AuthorChart.js:84
#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:31
#: src/components/exportList/Content/HeadingExport/HeadingExport.js:25
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:40
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:48
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:21
#: src/components/analytics/AnalyticsContent.js:38
#: src/components/OurChart/OurChartAdvanced.js:316
msgid "Loading..."
msgstr "Nalagam..."

#: src/components/layout/Header/UserMenu/UserMenu.js:187
msgid "Log back in"
msgstr "Prijavite se nazaj"

#: src/components/page/auth/Login/Login.tsx:55
#: src/components/page/auth/Login/Login.tsx:69
#: src/components/page/auth/Expired/Expired.js:104
msgid "Log In"
msgstr "Prijava"

#: src/components/staff/admin/user/User.js:175
#: src/components/staff/admin/customer/users/UsersTable.js:129
msgid "Login as this user"
msgstr "Prijavi se kot ta uporabnik"

#: src/components/staff/admin/workspace/Workspace.js:249
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:137
msgid "Login into this workspace"
msgstr "Prijavite se v to delovno okolje"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:187
msgid "Login link"
msgstr "Povezava za prijavo"

#: src/components/page/auth/Login/Login.tsx:63
msgid "Login to {appName}"
msgstr "Prijava v aplikacijo {appName}"

#: src/components/page/auth/Login/Login.tsx:64
msgid "Login to {appName}, the next generation media monitoring tool."
msgstr "Prijava v aplikacijo {appName}, orodje za spremljanje medijev nove generacije. Spremljajte, merite in analizirajte svojo komunikacijo."

#: src/components/page/auth/Expired/Expired.js:75
msgid "Login to different workspace"
msgstr "Prijava v drugo delovno okolje"

#: src/components/layout/Header/UserMenu/UserMenu.js:146
msgid "Logout"
msgstr "Odjava"

#: src/helpers/auth.js:47
msgid "Logout performed in another window."
msgstr "Odjava je bila izvedena v drugem oknu."

#. js-lingui-explicit-id
#: src/helpers/formatNumber.js:22
msgid "formatNumber.M"
msgstr "mil."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:155
msgid "Magazine cover pages"
msgstr "Naslovnice revij"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:59
msgid "Main message"
msgstr "Glavno sporočilo"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:48
msgid "Main message & key points"
msgstr "Glavno sporočilo in ključne točke"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:46
msgid "Main Objective:"
msgstr "Glavni cilj:"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:61
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:65
msgid "Mainstream sources"
msgstr "Mainstream viri"

#: src/pages/_error.js:44
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:422
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:156
#: src/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl.tsx:44
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:88
msgid "Malformed URL"
msgstr "Neveljaven URL"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:104
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:107
msgid "Manage hidden tags"
msgstr "Upravljaj skrite oznake"

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:95
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:98
msgid "Manage hidden topics"
msgstr "Upravljaj skrite teme"

#: src/components/layout/Sidebar/SidebarMainNav.js:198
msgid "Management summaries"
msgstr "Vodstveni povzetki"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:49
msgid "Manual writing"
msgstr "Ročno pisanje"

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:36
msgid "Manually add the information for your signature, which will appear in every email or customize it using your own HTML."
msgstr "Ročno dodajte informacije za svoj podpis, ki se bo pojavil v vsakem e-poštnem sporočilu, ali ga prilagodite z lastnim HTML-jem."

#: src/components/emailing/content/promo/PromoEmailing.js:27
msgid "Mass mailing"
msgstr "Masovno pošiljanje"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:488
msgid "Max. file size:"
msgstr "Max. velikost datoteke:"

#: src/components/layout/Sidebar/SidebarMainNav.js:192
msgid "Media analysis"
msgstr "Medijska analiza"

#: src/components/tariff/TariffLimits/TariffLimits.js:261
#: src/components/staff/admin/workspace/Workspace.js:500
msgid "Media archive depth limit"
msgstr "Globina medijskega arhiva"

#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:120
msgid "Media Coverage"
msgstr "Medijska pokritost"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:330
#: src/components/misc/ActionsBar/View/ViewMenu.js:49
#: src/components/misc/ActionsBar/View/ViewMenu.js:336
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:37
msgid "Media data"
msgstr "Medijski podatki"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:92
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:168
msgid "Media data (GRP, OTS, AVE, PRIMe)"
msgstr "Medijski podatki (GRP, OTS, AVE, PRIMe)"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:16
msgid "Media Monitoring"
msgstr "Medijsko spremljanje"

#: src/constants/analytics.js:79
#: src/constants/analytics.js:585
#: src/constants/analytics.js:717
#: src/components/layout/AuthWrapper/constants/features.slides.js:183
msgid "Media reach (GRP)"
msgstr "Medijski doseg (GRP)"

#: src/constants/analytics.js:77
msgid "Media reach (GRP) by sentiment"
msgstr "Medijski doseg (GRP) po sentimentu"

#: src/components/layout/Sidebar/SidebarMainNav.js:189
msgid "Media services"
msgstr "Medijske storitve"

#: src/components/layout/AuthWrapper/constants/references.slides.com.js:43
msgid "Mediaboard transformed our communication at Coca-Cola HBC! A daily essential for top-notch media monitoring, with a user-friendly interface and insightful analytics. Their exceptional customer support makes it a joy to work with Mediaboard."
msgstr "Mediaboard je spremenil našo komunikacijo v Coca-Cola HBC! Nepogrešljivo orodje za vrhunsko spremljanje medijev z uporabniku prijaznim vmesnikom in poglobljenimi analizami. Njihova izjemna podpora strankam naredi delo z Mediaboardom pravo veselje."

#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorFiles/AuthorFiles.js:29
msgid "Mediakit"
msgstr "Mediakit"

#: src/store/models/dashboards/DashboardPreview.js:99
#: src/pages/authors/index.js:32
#: src/pages/authors/index.js:41
#: src/pages/authors/create.js:10
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:110
#: src/components/layout/Sidebar/SidebarMainNav.js:104
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:36
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:246
#: src/components/layout/AuthWrapper/constants/features.slides.js:214
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:44
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:29
msgid "Medialist"
msgstr "Medijski seznam"

#: src/constants/analytics.js:793
#: src/components/topics/Content/TopicsList/MegalistModal.js:52
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:70
#: src/components/topics/Content/TopicsList/MegalistToolbar/MediatypeFilterPopup.js:8
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:247
msgid "Mediatype"
msgstr "Medij"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
msgid "mediatype for all countries"
msgstr "mediatip za vse države"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:67
msgid "Mention any personalization details (name, company)."
msgstr "Navedite vse podrobnosti o personalizaciji (ime, podjetje)."

#: src/constants/analytics.js:233
#: src/constants/analytics.js:342
#: src/constants/analytics.js:889
#: src/constants/analytics.js:909
#: src/constants/analytics.js:1312
#: src/components/widgets/modules/stats/WidgetStats.js:241
#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:51
msgid "Mentions"
msgstr "Omembe"

#: src/constants/analytics.js:254
#: src/constants/analytics.js:363
msgid "Mentions by sentiment"
msgstr "Omembe po sentimentu"

#: src/constants/analytics.js:365
#: src/constants/analytics.js:923
#: src/constants/analytics.js:1325
msgid "Mentions by social network"
msgstr "Omembe po družbenem omrežju"

#: src/constants/analytics.js:256
#: src/constants/analytics.js:903
msgid "Mentions by type"
msgstr "Omembe po vrsti"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:586
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:96
msgid "Merge Tags"
msgstr "Združi oznake"

#: src/pages/staff/admin/customers/[customerId]/merged-customers.js:12
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:60
msgid "Merged customers"
msgstr "Združene stranke"

#: src/components/staff/admin/customer/bio/CustomerBio.js:79
msgid "Merged to"
msgstr "Združeno v"

#: src/components/misc/ActionsBar/View/ViewMenu.js:199
msgid "Metrics"
msgstr "Metrične vrednosti"

#: src/components/misc/portable/PortableResend/PortableResend.js:93
#: src/components/misc/portable/PortableExport/PortableExport.js:88
msgid "Minimize"
msgstr "Minimiziraj"

#: src/components/tariff/MonitoredMedia/MissedArticles/MissedArticles.js:9
msgid "Missed articles"
msgstr "Zgrešeni članki"

#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:54
#: src/components/misc/ActionsBar/RefineArticles/RefineArticles.js:74
msgid "Missing article"
msgstr "Manjkajoči članek"

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:39
msgid "Missing data"
msgstr "Manjkajoči podatki"

#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:46
msgid "Missing recipient info"
msgstr "Manjkajoče informacije o prejemniku"

#: src/components/layout/Sidebar/SidebarMainNav.js:265
#: src/components/layout/AuthWrapper/constants/features.slides.js:399
msgid "Mobile Apps"
msgstr "Mobilne aplikacije"

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:41
msgid "Modified"
msgstr "Spremenjeno"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromoItem.js:87
msgid "Monitor a wide range of social media platforms including Facebook, LinkedIn, Instagram, TikTok, X.com, and YouTube."
msgstr "Spremljajte širok spekter družbenih medijev, vključno s Facebookom, LinkedInom, Instagramom, TikTokom, X.com in YouTube."

#: src/components/layout/AuthWrapper/constants/features.slides.js:23
msgid "Monitor newspapers, magazines, radios, TV stations or the entire online world. Reach out to media, react, track, analyze, and build your brand."
msgstr "Spremljajte dogajanje v časopisih, revijah, radijskih in televizijskih postajah ter v celotnem spletnem svetu. Obrnite se na medije, reagirajte, spremljajte, analizirajte in gradite svojo blagovno znamko."

#: src/components/topics/Content/TopicsList/MediaCard.js:21
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:127
#: src/components/staff/admin/workspace/Workspace.js:877
#: src/components/settings/SettingsTariff/SettingsTariff.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:41
msgid "Monitored media"
msgstr "Spremljani mediji"

#: src/components/notifications/Content.js:34
#: src/components/monitoring/Monitoring.js:109
#: src/components/monitoring/Monitoring.js:110
#: src/components/monitoring/Monitoring.js:165
#: src/components/layout/Sidebar/SidebarMainNav.js:87
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:45
#: src/components/layout/AuthWrapper/constants/features.slides.js:22
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:10
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:10
msgid "Monitoring"
msgstr "Nadzor"

#: src/components/staff/admin/customer/expenses/ExpenseTable.js:77
msgid "Month"
msgstr "Mesec"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:107
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:65
msgid "monthly sessions"
msgstr "mesečnih obiskov"

#: src/components/misc/ActionsBar/View/ViewMenu.js:82
msgid "Monthly sessions"
msgstr "Mesečni obiski"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:87
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:55
msgid "monthly users"
msgstr "uporabnikov na mesec"

#: src/components/misc/ActionsBar/View/ViewMenu.js:74
msgid "Monthly users"
msgstr "Mesečno uporabnikov"

#: src/helpers/charts/makeGranularityMenu.js:26
#: src/helpers/charts/getGranularityLabel.js:9
msgid "Months"
msgstr "Meseci"

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:159
msgid "more"
msgstr "več"

#: src/components/OurChart/OurChartAdvanced.js:247
msgid "More"
msgstr "Več"

#: src/constants/analytics.js:1338
msgid "Most common terms"
msgstr "Najpogostejši izrazi"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:96
msgid "Move article"
msgstr "Premakni članek"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:119
msgid "Move to Dashboard"
msgstr "Premakni na nadzorno ploščo"

#: src/pages/workspace-articles.js:51
#: src/components/monitoring/WorkspaceArticles/Intro.js:23
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:182
msgid "My Articles"
msgstr "Moji članki"

#: src/components/medialist/content/AuthorInfoDetail.js:72
msgid "My author"
msgstr "Moj avtor"

#: src/components/medialist/content/OwnAuthorsListSelectorButton.js:9
#: src/components/medialist/content/AuthorBasketsMenu.js:41
#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:158
msgid "My authors"
msgstr "Moji avtorji"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:8
#: src/components/staff/admin/workspace/Workspace.js:288
#: src/components/staff/admin/user/WorkspacesTable.js:71
#: src/components/staff/admin/user/User.js:239
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:71
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:52
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:62
#: src/components/medialist/forms/FormEditAuthor.js:211
#: src/components/medialist/forms/FormEditAuthor.js:212
#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:28
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:53
#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:45
msgid "Name"
msgstr "Ime"

#. js-lingui-explicit-id
#: src/components/dashboards/DashboardSelector/FormCreateDashboard.js:25
msgid "name.nazev"
msgstr "Naziv"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:57
msgid "Name for expression (optional)"
msgstr "Ime za izraz (neobvezno)"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:341
msgid "Need help? Ask AI assistant. Select a sentence or paragraph"
msgstr "Potrebujete pomoč? Vprašajte AI pomočnika. Izberite stavek ali odstavek."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:147
msgid "Need help? Check our <0>tutorial</0> or contact us."
msgstr "Potrebujete pomoč? Preberite si ta <0>navodila</0> ali nas kontaktirajte."

#: src/components/medialist/content/MedialistDashboard.js:94
#: src/components/medialist/content/MedialistDashboard.js:127
msgid "New"
msgstr "Nov"

#. js-lingui-explicit-id
#: src/components/layout/Header/AppNotifications/AppNotifications.js:133
msgid "new.notifications"
msgstr "Novo"

#: src/components/emailing/modules/withModalAddCampaign.tsx:20
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:91
#: src/components/emailing/content/EmailingCampaignsContent.tsx:59
msgid "New Campaign"
msgstr "Nova kampanja"

#: src/components/newsroom/forms/FormCreateCategory/FormCreateCategory.js:29
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:72
msgid "New Category"
msgstr "Nova kategorija"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:29
#: src/components/emailing/content/NewEmailWizardButton.tsx:13
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:72
msgid "New Email"
msgstr "Nov e-poštni naslov"

#: src/components/layout/Sidebar/modules/SidebarBaskets/SidebarBaskets.js:38
#: src/components/forms/baskets/FormNewBasket.js:10
msgid "New list"
msgstr "Nov seznam"

#: src/pages/user/reset-password/success.tsx:7
#: src/pages/user/reset-password/new.tsx:54
#: src/components/staff/admin/user/User.js:250
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:87
msgid "New password"
msgstr "Novo geslo"

#: src/store/models/admin/customer/CustomerStore.js:227
msgid "New passwords have been copied to the clipboard."
msgstr "Nova gesla so bila kopirana v odložišče."

#: src/components/newsroom/content/posts/NewPostWizardButton.tsx:13
msgid "New post"
msgstr "Nov prispevek"

#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:71
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:70
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:103
msgid "New report"
msgstr "Novo poročilo"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:78
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:264
#: src/components/forms/tags/FormNewTag/FormNewTag.js:10
msgid "New Tag"
msgstr "Nova oznaka"

#: src/components/topics/Content/TopicsList/TopicsList.js:36
msgid "New topic"
msgstr "Nova tema"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:55
msgid "New value"
msgstr "Nova vrednost"

#: src/pages/newsroom/index.js:24
#: src/pages/newsroom/index.js:33
#: src/pages/newsroom/create.js:17
#: src/pages/newsroom/[blogId]/settings.js:15
#: src/pages/newsroom/[blogId]/index.js:16
#: src/pages/newsroom/[blogId]/post/[postId].js:10
#: src/components/newsroom/forms/FormNewsroomSettings/LanguageSection.tsx:61
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:112
#: src/components/layout/Sidebar/SidebarMainNav.js:113
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:256
#: src/components/layout/AuthWrapper/constants/features.slides.js:306
#: src/components/emailing/components/FunnelStats/FunnelStats.tsx:80
msgid "Newsroom"
msgstr "Medijsko središče"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:85
msgid "Newsroom Articles"
msgstr "Članki v medijskem središču"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:254
msgid "Newsroom is a blogging platform that allows you to easily share your external and internal communication (e.g. press releases, announcements, etc.). You can read more about the Newsroom <0>on our website</0>."
msgstr "Medijsko središče je platforma za bloganje, ki vam omogoča enostavno deljenje zunanje in notranje komunikacije (npr. sporočila za javnost, obvestila itd.). Več o medijskem središču si lahko preberete <0>na naši spletni strani</0>."

#: src/components/staff/admin/workspace/Workspace.js:481
msgid "Newsroom limit"
msgstr "Omejitev števila medijskih središč"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:309
msgid "Newsroom settings"
msgstr "Nastavitve medijskega središča"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:69
msgid "Next page"
msgstr "Naslednja stran"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:40
msgid "No access to monitoring feeds, archive search, analytics, topic or report settings, crisis communications, medialist, or user settings."
msgstr "Brez dostopa do virov za spremljanje, iskanja po arhivu, analitike, nastavitev tem ali poročil, kriznega komuniciranja, medijskega seznama ali uporabniških nastavitev."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:8
msgid "No articles yet"
msgstr "Trenutno ni člankov"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:81
msgid "No campaigns yet"
msgstr "Trenutno ni kampanj"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:189
msgid "No categories yet."
msgstr "Zaenkrat ni kategorij."

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:109
msgid "No companies found"
msgstr "Ni najdenih podjetij"

#: src/components/staff/admin/user/WorkspacesTable.js:164
#: src/components/staff/admin/customer/workspaces/Workspaces.js:53
#: src/components/staff/admin/customer/users/UsersTable.js:148
#: src/components/staff/admin/customer/users/Users.js:53
#: src/components/staff/admin/customer/invoices/Invoices.js:49
#: src/components/staff/admin/customer/expenses/Expenses.js:46
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomers.js:49
msgid "No data"
msgstr "Ni podatkov"

#: src/helpers/charts/highcharts.js:20
msgid "No data to display"
msgstr "Ni podatkov za prikaz"

#: src/components/trash/Content.js:43
msgid "No Deleted Articles"
msgstr "Ni izbrisanih člankov"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:47
msgid "No detailed data to track."
msgstr "Ni podrobnih podatkov za sledenje."

#: src/components/reports/Content/ReportsList/ReportsHeading/ReportsHeading.js:9
msgid "No email reports created"
msgstr "Nobeno e-poštno poročilo ni bilo ustvarjeno"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:145
msgid "No emails found"
msgstr "Ni najdenih e-poštnih sporočil"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:127
msgid "No emails yet"
msgstr "Zaenkrat ni e-pošte"

#: src/components/newsroom/content/posts/NewsroomPosts.js:291
msgid "No posts yet"
msgstr "Zaenkrat še ni objav."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:66
msgid "No recipients found"
msgstr "Ni bilo najdenih prejemnikov"

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:50
msgid "No recipients yet"
msgstr "Zaenkrat še ni prejemnikov"

#: src/components/reports/Content/ReportsList/ReportsList.js:85
msgid "No reports are assigned to the topic."
msgstr "K temi niso dodeljena nobena poročila."

#: src/store/models/Megalist/MegalistFilter.js:42
#: src/helpers/withTranslatePopup/TranslatePopupContent.js:89
#: src/helpers/withMenuPopup/MntrMenuPopupContent.js:58
#: src/components/widgets/modules/tvr/WidgetTvr.js:78
#: src/components/widgets/modules/medialist/WidgetMedialist.js:127
#: src/components/widgets/modules/feed/WidgetFeedSimple.js:75
#: src/components/staff/admin/customers/Customers.js:37
#: src/components/misc/MntrMultiSelect/MultiSelect.js:22
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:16
#: src/components/medialist/content/MedialistHeading.js:15
#: src/components/medialist/content/MedialistInspector/Feed/Feed.js:25
#: src/components/medialist/content/FeedMedialist/FeedMedialistEmpty/FeedMedialistEmpty.js:8
#: src/components/layout/Sidebar/modules/SidebarTopics/SidebarTopicsFolders.js:118
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:371
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitorsReports.js:64
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTopicMonitors.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:280
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:34
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:99
#: src/components/forms/dashboard/Search/SearchEmailingEmailMessages.js:32
#: src/components/forms/dashboard/Search/SearchEmailingCampaigns.js:31
#: src/components/emailing/content/CampaignAutocompleteList.tsx:23
msgid "No results found"
msgstr "Ni najdenih rezultatov"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:177
msgid "No senders"
msgstr "Ni pošiljateljev"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:129
msgid "No subject"
msgstr "Brez zadeve"

#: src/components/emailing/helpers/displayEmailingTitle.js:18
#: src/components/emailing/helpers/displayEmailingTitle.js:21
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/EmailMessagesList.js:13
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:123
msgid "No title"
msgstr "Brez naslova"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:9
msgid "No topics created"
msgstr "Nimate ustvarjene nobene teme"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:209
msgid "No users"
msgstr "Ni uporabnikov"

#: src/components/staff/admin/workspace/Workspace.js:862
msgid "No users assigned to this workspace"
msgstr "K tej delovni skupini ni dodeljenih nobenih uporabnikov"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:185
msgid "No users found"
msgstr "Ni najdenih uporabnikov"

#: src/components/newsroom/content/dashboard/ChartVisits.js:59
msgid "No visits yet"
msgstr "Zaenkrat še ni obiskov."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:14
msgid "No workspace"
msgstr "Ni delovnega prostora"

#: src/components/staff/admin/user/User.js:294
msgid "No workspaces assigned to this user"
msgstr "Temu uporabniku niso dodeljeni nobeni delovni prostori"

#. js-lingui-explicit-id
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:543
msgid "filetypes.none"
msgstr "nič"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:90
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:43
#: src/components/misc/ActionsBar/Selector/Selector.js:58
msgid "None"
msgstr "Nič"

#. js-lingui-explicit-id
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:43
msgid "attachment.none"
msgstr "Nobena"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:150
msgid "NOT"
msgstr "NE"

#: src/components/reports/history/RecipientsTableRow.js:49
#: src/components/reports/history/HistoryTable.js:82
#: src/components/reports/history/HistoryTable.js:111
#: src/components/reports/history/HistoryTable.js:325
msgid "Not delivered"
msgstr "Ni dostavljeno"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:151
msgid "Not verified"
msgstr "Nepreverjeno"

#: src/store/models/dashboards/DashboardPreview.js:156
#: src/components/staff/admin/workspace/Workspace.js:339
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Note.js:36
#: src/components/medialist/forms/FormEditAuthor.js:703
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:63
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:63
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:35
msgid "Note"
msgstr "Opomba"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:128
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:368
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:83
msgid "Notes"
msgstr "Opombe"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:30
msgid "Notification about mention <0>within 3 minutes</0>"
msgstr "Obvestilo o omembi <0>v 3 minutah</0>"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:99
#: src/components/layout/Header/AppNotifications/AppNotifications.js:107
msgid "Notification Settings"
msgstr "Nastavitve obvestil"

#: src/components/notifications/ContentTvr.js:39
#: src/components/notifications/ContentTopics.js:24
#: src/components/notifications/AppNotifications/AppNotifications.js:21
#: src/components/layout/Header/AppNotifications/AppNotifications.js:152
msgid "Notifications"
msgstr "Obvestila"

#: src/constants/analytics.js:55
#: src/constants/analytics.js:566
#: src/constants/analytics.js:678
msgid "Number of articles"
msgstr "Število člankov"

#: src/constants/analytics/primeScoreCharts.ts:57
msgid "Number of articles by PRIMe relevant vs irrelevant"
msgstr "Število člankov po PRIMe relevantni vs. nerelevantni"

#: src/constants/analytics.js:53
msgid "Number of articles by sentiment"
msgstr "Število člankov po sentimentu"

#: src/components/monitoring/Inspector/InspectorMonitora/SocialParentText/SocialParentHeader.js:94
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:41
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:132
#: src/components/misc/ActionsBar/View/ViewMenu.js:281
msgid "Number of followers"
msgstr "Število sledilcev"

#: src/store/models/OurChart.js:188
msgid "Number of mentions"
msgstr "Število omemb"

#: src/components/tvr/Content/Content.js:61
msgid "Number of outputs"
msgstr "Število izhodov"

#: src/components/reports/Content/ReportsList/ReportsTopMentionsMode.js:21
msgid "Number of TOP stories"
msgstr "Število TOP zgodb"

#: src/components/misc/Changelog/ChangelogTable.js:33
msgid "Object"
msgstr "Objekt"

#: src/components/monitoring/WorkspaceArticles/Limits.js:57
msgid "OCR"
msgstr "OCR"

#: src/components/monitoring/FeedList/FeedListItem/FeedListOlderDivider/FeedListOlderDivider.js:24
msgid "Older articles"
msgstr "Starejši članki"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:143
msgid "on frontpage"
msgstr "na prvi strani"

#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:91
#: src/components/tariff/MonitoredMedia/ColumnSelector/ColumnSelector.js:125
msgid "on this continent"
msgstr "na tej celini"

#: src/components/misc/ActionsBar/View/ViewMenu.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:47
msgid "Online"
msgstr "Online"

#: src/constants/analytics.js:1287
msgid "Online categories"
msgstr "Kategorije - Spletne"

#: src/store/models/Megalist/MegalistFilter.js:34
msgid "Only Selected"
msgstr "Samo izbrano"

#: src/store/models/Megalist/MegalistFilter.js:38
msgid "Only Unselected"
msgstr "Samo neizbrano"

#: src/components/medialist/forms/FormEditAuthor.js:892
msgid "Only you can see all the data you entered and the changes made."
msgstr "Vse vnesene podatke in opravljene spremembe lahko vidite samo vi in vaši sodelavci."

#: src/components/newsroom/content/posts/NewsroomPosts.js:185
#: src/components/monitoring/WorkspaceArticles/ArticleItem.js:159
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderVideo/HeaderVideo.js:52
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderSocial/HeaderSocial.js:34
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:105
#: src/components/monitoring/Inspector/InspectorEntityKnowledgeBase/InspectorKnowledgeBaseHeader.js:12
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:51
#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthor.js:55
#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:20
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:54
msgid "Open"
msgstr "Odpri"

#: src/components/staff/admin/customers/Customer.js:187
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:102
msgid "Open customer detail"
msgstr "Odpri podrobnosti stranke"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:107
msgid "Open In Feed"
msgstr "Odpri v viru"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook.tsx:49
msgid "Open on Facebook"
msgstr "Odpri na Facebooku"

#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:209
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:225
msgid "Open rate"
msgstr "Delež odprtih sporočil"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:199
#: src/components/staff/admin/customer/users/UsersTable.js:138
msgid "Open user detail"
msgstr "Odpri podrobnosti uporabnika"

#: src/components/staff/admin/user/WorkspacesTable.js:154
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:146
msgid "Open workspace detail"
msgstr "Odpri podrobnosti delovnega prostora"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:282
msgid "Opportunity to see"
msgstr "Priložnost za ogled"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:360
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:387
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:441
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:511
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:230
#: src/components/emailing/forms/FormSenderSettings.js:89
#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "optional"
msgstr "neobvezno"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:144
msgid "OR"
msgstr "ALI"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:70
msgid "Or use an external service"
msgstr "Ali uporabljate zunanjo storitev"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:91
msgid "Order articles"
msgstr "Razvrsti članke"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:265
msgid "Ordered list"
msgstr "Oštevilčen seznam"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:70
msgid "Original"
msgstr "Izvirnik"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:45
msgid "Original value"
msgstr "Prvotna vrednost"

#: src/constants/analytics.js:1125
#: src/constants/analytics.js:1205
#: src/constants/analytics.js:1227
#: src/constants/analytics.js:1247
#: src/constants/analytics.js:1267
#: src/constants/analytics.js:1286
#: src/constants/analytics.js:1305
#: src/constants/analytics.js:1324
#: src/constants/analytics.js:1337
#: src/constants/analytics/primeScoreCharts.ts:134
#: src/components/notifications/ContentTvr.js:118
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:229
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:57
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:57
msgid "Other"
msgstr "Drugo"

#: src/store/models/Megalist/Megalist.js:48
msgid "Other Regions"
msgstr "Druge regije"

#: src/components/staff/admin/workspace/Workspace.js:671
msgid "Other settings"
msgstr "Druge nastavitve"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:155
msgid "Others"
msgstr "Drugi"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:279
#: src/components/misc/ActionsBar/View/ViewMenu.js:219
msgid "OTS"
msgstr "OTS"

#: src/pages/_error.js:55
msgid "Our team has been notified. We're sorry for the inconvenience."
msgstr "Naša ekipa je bila obveščena. Opravičujemo se za nevšečnosti."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:57
msgid "Outline the main content or details you want included."
msgstr "Oris glavne vsebine ali podrobnosti, ki jih želite vključiti."

#: src/components/medialist/constants/medialist.tabNavigation.js:20
msgid "Overview"
msgstr "Pregled"

#: src/components/staff/admin/workspace/Workspace.js:603
msgid "Own content"
msgstr "Lastna vsebina"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:86
msgid "Own selection"
msgstr "Lastna izbira"

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:92
msgid "page.shortened"
msgstr "str."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:64
#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/printDownloadMenu.js:76
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:72
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:221
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:27
#: src/components/misc/Pagination/Pagination.js:26
#: src/components/misc/Pagination/Pagination.js:39
#: src/components/misc/Pagination/Pagination.js:45
#: src/components/misc/Pagination/Pagination.js:76
#: src/components/misc/Pagination/Pagination.js:84
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:119
msgid "Page"
msgstr "Stran"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:30
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPagination.js:48
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:258
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupPagesContent.js:18
#: src/components/layout/MntrActiveFilters/modules/PageNumbers.js:22
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:123
msgid "Pages"
msgstr "Strani"

#. placeholder {0}: data.publication.pages.length
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaData.js:238
msgid "Pages ({0} total)"
msgstr "Strani ({0} skupaj)"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:60
msgid "Paid"
msgstr "Plačano"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:160
msgid "Paragraph"
msgstr "Odstavek"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:423
msgid "Parse PDF"
msgstr "Razčleni PDF"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:140
msgid "Partner Code (optional)"
msgstr "Partnerska koda (neobvezno)"

#: src/pages/user/reset-password/new.tsx:32
#: src/components/page/auth/SignUp/SignUp.js:42
#: src/components/page/auth/Login/Login.tsx:46
#: src/components/emailing/forms/FormSenderSettings.js:96
msgid "Password"
msgstr "Geslo"

#: src/pages/user/reset-password/new.tsx:39
msgid "Password again"
msgstr "Geslo ponovno"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:29
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:119
msgid "Password change"
msgstr "Sprememba gesla"

#: src/components/staff/admin/workspace/UsersTable/CopyPassword.js:34
msgid "Password copied to the clipboard."
msgstr "Geslo je bilo kopirano v odložišče."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Pause"
msgstr "Premor"

#: src/components/reports/history/RecipientsTableRow.js:58
msgid "Pending"
msgstr "V čakanju"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "people"
msgstr "ljudi"

#: src/helpers/formatNumber.js:29
msgid "per month"
msgstr "na mesec"

#: src/components/OurChart/OurChartAdvanced.js:155
msgid "Percent Share"
msgstr "Odstotni delež"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:137
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:562
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:564
#: src/components/misc/ActionsBar/View/ViewMenu.js:318
msgid "Perex"
msgstr "Perex"

#: src/components/exportList/History/HistoryTable/HistoryTable.js:60
msgid "Period"
msgstr "Za obdobje"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:39
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:44
msgid "Periodicity"
msgstr "Periodičnost"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:60
msgid "Permissions"
msgstr "Dovoljenja"

#: src/components/medialist/forms/FormEditAuthor.js:841
#: src/components/medialist/forms/FormEditAuthor.js:1002
#: src/components/medialist/forms/FormEditAuthor.js:1007
msgid "Personal Website"
msgstr "Osebna spletna stran"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:66
msgid "Personalization:"
msgstr "Prilagajanje:"

#: src/components/page/auth/SignUp/SignUp.js:49
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:132
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:80
#: src/components/medialist/forms/FormEditAuthor.js:766
#: src/components/medialist/forms/FormEditAuthor.js:900
#: src/components/medialist/forms/FormEditAuthor.js:906
msgid "Phone"
msgstr "Telefon"

#: src/constants/analytics.js:677
#: src/constants/analytics.js:697
#: src/constants/analytics.js:716
#: src/constants/analytics.js:735
#: src/constants/analytics.js:754
#: src/constants/analytics.js:773
#: src/constants/analytics.js:792
#: src/constants/analytics.js:811
#: src/constants/analytics.js:826
#: src/constants/analytics.js:844
#: src/constants/analytics.js:863
#: src/constants/analytics.js:882
#: src/constants/analytics.js:902
#: src/constants/analytics.js:922
#: src/constants/analytics.js:943
#: src/constants/analytics.js:963
#: src/constants/analytics.js:978
#: src/constants/analytics.js:992
#: src/constants/analytics.js:1008
#: src/constants/analytics.js:1023
#: src/constants/analytics.js:1038
#: src/constants/analytics/primeScoreCharts.ts:93
msgid "Pie"
msgstr "Pita"

#: src/helpers/formatNumber.js:39
msgid "pieces"
msgstr "kosov"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:71
msgid "Plain"
msgstr "Preprost"

#: src/components/staff/admin/workspace/Workspace.js:309
msgid "Plan"
msgstr "Načrt"

#: src/components/emailing/content/promo/PromoEmailing.js:18
msgid "Platform for email communication with journalists."
msgstr "Platforma za e-poštno komunikacijo z novinarji."

#: src/components/misc/VideoPlayer/CropControls.js:127
#: src/components/misc/VideoPlayer/Controls.js:98
msgid "Play"
msgstr "Predvajaj"

#: src/components/emailing/content/sender/EmailingSenderContent.js:34
msgid "Please add a sender address that will be used for sending emails."
msgstr "Prosimo, dodajte naslov pošiljatelja, ki bo uporabljen za pošiljanje e-pošte."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:549
msgid "Please copy and insert this code into your website. Modify the width and height values of the iframe according to your requirements. Additionally, it’s possible to hide the header and footer if necessary."
msgstr "Kopirajte in prilepite to kodo v izvorno kodo vaše spletne strani. Širino in višino iframa lahko prilagodite glede na vaše zahteve. Prav tako je mogoče skriti glavo in nogo, če je potrebno."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:95
msgid "Please remove some recipients."
msgstr "Prosimo, odstranite nekatere prejemnike."

#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:168
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:81
msgid "Please select Image"
msgstr "Prosimo, izberite sliko"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepTitleAndCommunicationPlan.tsx:60
msgid "Please select the title and review the communication plan. If it does not meet your expectations, restart the process."
msgstr "Prosimo, izberite naslov in preglejte komunikacijski načrt. Če ne izpolnjuje vaših pričakovanj, ponovno zaženite postopek."

#: src/components/emailing/forms/FormSenderSettings.js:105
msgid "Port"
msgstr "Vrata"

#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:133
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:100
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:68
msgid "Position"
msgstr "Položaj"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostPreview.tsx:80
msgid "Post preview"
msgstr "Predogled objave"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:351
msgid "Post settings"
msgstr "Nastavitve objave"

#: src/constants/analytics.js:1132
#: src/constants/analytics.js:1157
#: src/constants/analytics.js:1212
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:37
msgid "Posts"
msgstr "Objave"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:26
msgid "Predefined queries"
msgstr "Vnaprej določena poizvedba"

#: src/components/misc/Capture/Capture.js:283
msgid "Preparing export..."
msgstr "Pripravljamo izvoz..."

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:86
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:28
#: src/components/reports/history/HistoryTable.js:406
#: src/components/reports/Content/ReportsList/ReportPreview.js:18
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:20
#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:115
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:140
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:140
#: src/components/forms/dashboard/ExportResend/ExportResend.js:163
#: src/components/emailing/content/CreateEmailContent.js:278
msgid "Preview"
msgstr "Predogled"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Preview & Publish"
msgstr "Predogled in objava"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:110
msgid "Preview images"
msgstr "Predogledne slike"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/PrintPaginationItem.js:66
msgid "Previous page"
msgstr "Prejšnja stran"

#: src/components/staff/SignUp.js:17
#: src/components/staff/admin/workspace/Workspace.js:296
#: src/components/staff/admin/DailyAccess/Table.js:30
msgid "Primary app"
msgstr "Aplikacija"

#: src/constants/analytics/primeScoreCharts.ts:121
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:420
#: src/components/layout/MntrActiveFilters/modules/PrimeFilter.tsx:25
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:46
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:33
#: src/components/analytics/AnalyticsContent.js:152
#: src/components/analytics/AnalyticsContent.js:179
msgid "PRIMe"
msgstr "PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:115
msgid "PRIMe in mediatype"
msgstr "PRIMe v mediatipu"

#: src/components/widgets/modules/stats/WidgetStats.js:190
msgid "PRIMe negative total value"
msgstr "PRIMe negativna skupna vrednost"

#: src/components/widgets/modules/stats/WidgetStats.js:183
msgid "PRIMe positive total value"
msgstr "PRIMe pozitivna skupna vrednost"

#: src/constants/analytics/primeScoreCharts.ts:75
msgid "PRIMe scale"
msgstr "Lestvica PRIMe"

#: src/constants/analytics/primeScoreCharts.ts:9
#: src/constants/analytics/primeScoreCharts.ts:37
#: src/constants/analytics/primeScoreCharts.ts:81
#: src/constants/analytics/primeScoreCharts.ts:101
msgid "PRIMe score"
msgstr "PRIMe rezultat"

#: src/components/widgets/modules/stats/WidgetStats.js:176
msgid "PRIMe total average"
msgstr "PRIMe skupno povprečje"

#: src/components/widgets/modules/stats/WidgetStats.js:169
msgid "PRIMe total value"
msgstr "PRIMe skupna vrednost"

#: src/components/misc/ActionsBar/View/ViewMenu.js:112
#: src/components/layout/AuthWrapper/constants/features.slides.js:57
#: src/components/OurChart/OurChartAdvanced.js:260
msgid "Print"
msgstr "Natisni"

#: src/constants/analytics.js:1306
msgid "Print categories"
msgstr "Kategorije - Tiskanje"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:89
msgid "Professional"
msgstr "Strokovno"

#: src/constants/analytics.js:1154
msgid "Profile"
msgstr "Profil"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:113
#: src/components/misc/MntrEditor/modals/withModalPromoBox.js:8
#: src/components/misc/MntrEditor/extensions/ExtensionPromoBox.js:33
msgid "Promo Box"
msgstr "Promo okno"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:66
msgid "Provide additional feedback..."
msgstr "Navedite dodatne povratne informacije..."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:33
msgid "Provide information such as:"
msgstr "Navedite informacije, kot so:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:74
msgid "Providing information about your company allows the AI assistant to generate more accurate and tailored content for your newsroom articles. This ensures the text aligns closely with your brand's identity and messaging"
msgstr "Z zagotavljanjem informacij o vašem podjetju omogočite AI asistentu, da ustvari natančnejšo in prilagojeno vsebino za vaše članke. To zagotavlja, da je besedilo usklajeno z identiteto in sporočilom vaše blagovne znamke"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:291
msgid "Publication Date"
msgstr "Datum objave"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:95
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:142
msgid "Publish"
msgstr "Objavi"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:49
msgid "Publish date set to"
msgstr "Datum objave nastavljen na"

#: src/components/newsroom/forms/FormNewsroomPost/PostStatus.js:73
msgid "Publish date set to {scheduledFormatted}"
msgstr "Datum objave nastavljen na {scheduledFormatted}"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:154
msgid "Publish now"
msgstr "Objavi zdaj"

#: src/pages/newsroom/index.js:45
msgid "Publish press releases <0>easily and quickly</0>"
msgstr "Objavite sporočila za javnost <0>enostavno in hitro</0>"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:155
msgid "Publish this post immediately"
msgstr "Takoj objavi to objavo"

#: src/components/newsroom/components/PostsList/PostsList.js:184
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:30
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:66
msgid "Published"
msgstr "Objavljeno"

#: src/components/staff/admin/user/getUserAttributes.js:14
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:35
#: src/components/layout/Sidebar/SidebarMainNav.js:161
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:261
#: src/components/layout/MntrActiveFilters/modules/Publisher.js:13
msgid "Publisher"
msgstr "Založnik"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:30
msgid "Publisher copyright fees"
msgstr "Avtorske pristojbine za založnika"

#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Publishers"
msgstr "Založniki"

#: src/components/layout/Header/AppNotifications/AppNotifications.js:201
msgid "Push Notifications"
msgstr "Potisna obvestila"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:30
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:64
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:104
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:132
msgid "Quick Overview"
msgstr "Hiter pregled"

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:30
msgid "Quickly protect your brand reputation and stakeholder trust."
msgstr "Hitro zaščitite ugled svoje blagovne znamke in zaupanje deležnikov."

#: src/components/newsroom/content/modules/CustomQuotes.tsx:64
msgid "Quote"
msgstr "Citat"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:74
#: src/components/newsroom/content/modules/CustomQuotes.tsx:33
msgid "Quotes"
msgstr "Citat"

#: src/components/notifications/ContentTvrRequest.js:74
#: src/components/notifications/ContentTvr.js:81
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:62
#: src/components/layout/AuthWrapper/constants/features.slides.js:75
msgid "Radio"
msgstr "Radio"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:92
msgid "Rank is primarily based on the reach and the importance of the news source."
msgstr "Uvrstitev temelji predvsem na dosegu in pomembnosti vira novic."

#: src/components/newsroom/components/AiTools/AiCheckPostResult.tsx:92
msgid "Re-run check"
msgstr "Ponovno zaženi AI preverjanje"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:30
msgid "reach"
msgstr "doseg"

#: src/constants/stats.ts:26
#: src/constants/analytics.js:107
#: src/constants/analytics.js:121
#: src/constants/analytics.js:123
#: src/constants/analytics.js:129
#: src/constants/analytics.js:590
#: src/constants/analytics.js:603
#: src/constants/analytics.js:736
#: src/constants/analytics.js:1025
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:421
#: src/components/misc/ActionsBar/View/ViewMenu.js:227
#: src/components/analytics/TraditionalMedia.js:34
#: src/components/analytics/TraditionalMedia.js:40
msgid "Reach"
msgstr "Domet"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:98
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:112
msgid "Reactivate"
msgstr "Ponovno aktiviraj"

#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:101
msgid "Reactivate recipient"
msgstr "Ponovno aktiviraj prejemnika"

#: src/components/reports/history/RecipientsTableRow.js:31
msgid "Read"
msgstr "Prebrano"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:34
msgid "Read only:"
msgstr "Samo za branje:"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:190
#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:63
#: src/components/misc/ActionsBar/View/ViewMenu.js:132
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:96
msgid "Readership"
msgstr "Branost"

#: src/helpers/modal/withModalReportProblem.tsx:32
#: src/helpers/modal/withModalReportArticle.tsx:46
#: src/components/reports/history/RecipientsTableHeader.js:38
msgid "Reason"
msgstr "Razlog"

#: src/components/medialist/content/MedialistDashboard.js:179
msgid "Recently edited authors"
msgstr "Nedavno urejeni avtorji"

#: src/components/medialist/content/MedialistDashboard.js:158
msgid "Recently viewed authors"
msgstr "Nedavno ogledani avtorji"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:70
msgid "Recipient"
msgstr "Prejemnik"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipient added."
msgstr "Prejemnik je bil dodan."

#: src/components/forms/dashboard/ExportResend/ExportResend.js:79
msgid "Recipient emails"
msgstr "E-poštni naslovi prejemnikov"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:83
msgid "Recipient has no email address"
msgstr "Prejemnik nima e-poštnega naslova"

#: src/store/models/reports/recipients/Recipients.js:56
msgid "Recipient removed."
msgstr "Prejemnik je bil odstranjen."

#: src/store/models/reports/recipients/Recipients.js:44
msgid "Recipient updated."
msgstr "Prejemnik je bil posodobljen."

#: src/helpers/modal/withModalTvrTopics.tsx:77
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:246
#: src/components/reports/history/HistoryTable.js:169
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:59
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:55
#: src/components/emailing/modules/PreviewEmail/PreviewEmail.tsx:63
#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:101
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:112
#: src/components/emailing/content/CreateEmailContent.js:269
#: src/components/emailing/content/tabs/RecipientsTab.tsx:23
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:163
msgid "Recipients"
msgstr "Prejemniki"

#: src/store/models/reports/recipients/Recipients.js:27
msgid "Recipients added."
msgstr "Prejemniki so bili dodani."

#: src/components/reports/history/HistoryTable.js:52
msgid "Recipients from: {formattedCreated}"
msgstr "Prejemniki od: {formattedCreated}"

#: src/components/reports/history/HistoryTable.js:432
#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:50
msgid "Recipients have been copied to the clipboard."
msgstr "Prejemniki so bili kopirani v odložišče."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:45
msgid "Recipients limit"
msgstr "Omejitev prejemnikov"

#: src/store/models/reports/recipients/Recipients.js:68
msgid "Recipients removed."
msgstr "Prejemniki so bili odstranjeni."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:25
msgid "Recipients with missing information"
msgstr "Prejemniki z manjkajočimi informacijami"

#: src/components/forms/dashboard/Export/RecommendedLimit.js:32
msgid "Recomended limit"
msgstr "Priporočena omejitev"

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:59
msgid "Recommended file types: XLSX, CSV"
msgstr "Priporočene vrste datotek: XLSX, CSV"

#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:70
msgid "Recommended resolution"
msgstr "Priporočena ločljivost"

#: src/components/staff/admin/workspace/Workspace.js:136
msgid "Recreate articles"
msgstr "Ponovno ustvari članke"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:566
msgid "Redo"
msgstr "Ponovi"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:31
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:110
msgid "Regenerate content"
msgstr "Ponovno generiraj vsebino"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:31
msgid "Regenerate until the email text aligns perfectly with your requirements"
msgstr "Regenerirajte, dokler se besedilo e-pošte popolnoma ne ujema z vašimi zahtevami"

#: src/components/staff/admin/customers/Customers.js:27
msgid "Register new user"
msgstr "Registriraj novega uporabnika"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:55
msgid "Relevant"
msgstr "Pomembno"

#: src/helpers/modal/withModalRemove.tsx:37
#: src/helpers/modal/withModalRemove.tsx:51
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:142
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:47
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:85
#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:7
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:680
#: src/components/newsroom/content/modules/CustomQuotes.tsx:58
#: src/components/newsroom/content/modules/CustomKeypoints.tsx:49
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:571
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:74
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:24
#: src/components/emailing/modules/withModalRemoveRecipients.tsx:39
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:88
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:68
msgid "Remove"
msgstr "Odstrani"

#: src/components/reports/Content/ReportsList/RecipientsList/RecipientsList.js:105
#: src/components/exportList/Content/Content.tsx:95
#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:56
msgid "Remove All"
msgstr "Odstrani vse"

#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:34
msgid "Remove all from report"
msgstr "Odstrani vse iz poročila"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:97
msgid "Remove all from selection"
msgstr "Odstrani vse iz izbora"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:155
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:126
msgid "Remove authors from list"
msgstr "Odstrani avtorje s seznama"

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:50
#: src/components/emailing/content/EmailingCampaignDetailContent.tsx:88
msgid "Remove Campaign"
msgstr "Odstrani kampanjo"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:57
msgid "Remove from article"
msgstr "Odstrani iz članka"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:338
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:285
msgid "Remove from Export"
msgstr "Odstrani iz izvoza"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Tags.js:40
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:99
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:173
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:257
msgid "Remove from filters"
msgstr "Odstrani iz filtrov"

#: src/components/medialist/content/withRemoveFromBasketPopup.js:34
msgid "Remove from list"
msgstr "Odstrani s seznama"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:273
msgid "Remove from next report"
msgstr "Odstrani iz naslednjega poročila"

#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:282
msgid "Remove from report"
msgstr "Odstrani iz poročila"

#: src/components/staff/admin/user/WorkspacesTable.js:138
msgid "Remove from workspace"
msgstr "Odstrani iz delovnega prostora"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:200
#: src/components/settings/SettingsLogo/SettingsLogo.js:145
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:339
msgid "Remove Image"
msgstr "Odstrani sliko"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:359
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:457
msgid "Remove Link"
msgstr "Odstrani povezavo"

#: src/components/medialist/forms/modules/FormFieldUploadPhoto.js:53
msgid "Remove Photo"
msgstr "Odstrani fotografijo"

#: src/components/emailing/components/EmailRecipientsList/EmailRecipientsList.tsx:69
msgid "Remove Recipients"
msgstr "Odstrani prejemnike"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:73
msgid "Remove report"
msgstr "Odstrani poročilo"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:99
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:274
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:374
#: src/components/monitoring/FeedActionsBar/withRemoveTagPopup/RemoveTagPopupContent.js:11
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:77
msgid "Remove tag"
msgstr "Odstrani oznako"

#: src/components/staff/admin/workspace/UsersTable/RemoveUser.js:25
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:376
msgid "Remove user"
msgstr "Odstrani uporabnika"

#: src/components/staff/admin/workspace/UsersTable/RemoveUsers.tsx:16
msgid "Remove users"
msgstr "Odstrani uporabnike"

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:132
msgid "Remove widget"
msgstr "Odstrani gradnik"

#: src/store/models/monitoring/Inspector/Inspector.ts:428
msgid "Removed from next report."
msgstr "Odstranjeno iz poročila."

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:257
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:261
msgid "Rename"
msgstr "Preimenuj"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:94
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:36
#: src/components/reports/Content/ReportsList/ReportPreview.js:26
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:28
msgid "Report preview"
msgstr "Predogled poročila"

#: src/helpers/modal/withModalReportProblem.tsx:45
#: src/helpers/modal/withModalReportArticle.tsx:70
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:104
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:26
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:289
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:295
#: src/components/medialist/forms/FormEditAuthor.js:381
#: src/components/medialist/forms/FormEditAuthor.js:387
#: src/components/medialist/forms/FormEditAuthor.js:528
#: src/components/medialist/forms/FormEditAuthor.js:534
msgid "Report problem"
msgstr "Prijavi težavo"

#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:76
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:51
msgid "Report will be removed."
msgstr "Poročilo bo odstranjeno."

#: src/pages/reports/index.js:15
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:118
#: src/components/reports/ReportChangelog.js:18
#: src/components/reports/history/Content.js:31
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:45
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:144
msgid "Reports"
msgstr "Poročila"

#: src/components/layout/AuthWrapper/constants/features.slides.js:353
msgid "Reports and exports"
msgstr "Poročila in izvozi"

#: src/pages/reports/history.js:12
#: src/components/reports/history/Content.js:35
#: src/components/reports/Content/ReportsList/ReportsList.js:37
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:158
msgid "Reports History"
msgstr "Poslana poročila"

#. js-lingui-explicit-id
#: src/helpers/modal/withModalRequestFeature.tsx:24
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:57
#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:244
msgid "featureRequest.Request"
msgstr "Potrdi"

#: src/helpers/modal/withModalRequestFeature.tsx:50
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:36
msgid "Request Access?"
msgstr "Zahtevaj dostop?"

#: src/helpers/modal/withModalTvrTopics.tsx:41
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:513
#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:514
msgid "Request change"
msgstr "Zahtevaj spremembo"

#: src/components/notifications/ContentTvrRequest.js:32
#: src/components/notifications/ContentTvr.js:120
msgid "Request Channels"
msgstr "Zahtevaj kanale"

#: src/components/analytics/AnalyticsContent.js:250
msgid "Request social media?"
msgstr "Zahtevati družbena omrežja?"

#: src/components/analytics/AnalyticsContent.js:217
msgid "Request traditional media?"
msgstr "Zahtevati tradicionalne medije?"

#: src/helpers/store/apiClient.js:153
msgid "Request was cancelled."
msgstr "Zahteva je bila preklicana."

#. js-lingui-explicit-id
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:104
#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:78
#: src/components/misc/RequestFeatureButton/RequestFeatureButton.js:49
#: src/components/misc/PromoBox/PromoBox.js:142
#: src/components/misc/MntrButton/modules/ButtonRequestFeature.js:25
#: src/components/analytics/AnalyticsContent.js:199
#: src/components/analytics/AnalyticsContent.js:232
msgid "featureRequest.Requested"
msgstr "Zahtevano"

#: src/components/staff/admin/DailyAccess/Table.js:33
msgid "Requests"
msgstr "Zahteve"

#: src/components/reports/history/HistoryTable.js:65
#: src/components/reports/history/HistoryTable.js:446
msgid "Resend"
msgstr "Posreduj"

#: src/components/reports/history/Compose.js:42
msgid "Resend email report"
msgstr "Ponovno pošlji poročilo"

#: src/components/reports/history/Compose.js:44
msgid "Resend email report from: {formattedCreated}"
msgstr "Posreduj poročilo iz: {formattedCreated}"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:102
msgid "Resend verification email"
msgstr "Ponovno pošlji potrditveno e-pošto"

#: src/store/models/reports/history/History.js:92
msgid "Resending email report. Check back later."
msgstr "Ponovno pošiljanje poročila po e-pošti. Preverite kasneje."

#: src/components/reports/history/HistoryTable.js:215
msgid "Resent report"
msgstr "Posredovano poročilo"

#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:33
#: src/components/medialist/forms/FormEditAuthor.js:591
msgid "Reset"
msgstr "Ponastavi"

#: src/components/medialist/forms/FormEditAuthor.js:295
#: src/components/medialist/forms/FormEditAuthor.js:459
msgid "Reset author profile"
msgstr "Ponastavi profil avtorja"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:153
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:72
msgid "Reset filter"
msgstr "Ponastavi filter"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:61
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:241
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:258
#: src/components/layout/MntrActiveFilters/MntrActiveFilters.js:277
msgid "Reset filters"
msgstr "Ponastavi filtre"

#: src/pages/user/reset-password/index.tsx:35
msgid "Reset Password"
msgstr "Ponastavi geslo"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:69
msgid "Reset selection"
msgstr "Ponastavi izbiro"

#: src/helpers/modal/withModalResetAuthor.tsx:25
#: src/helpers/modal/withModalResetAuthor.tsx:39
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:87
msgid "Restore"
msgstr "Obnovi"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:403
msgid "Restore articles"
msgstr "Obnovi članke"

#: src/components/settings/SettingsTheme/ThemePicker.tsx:122
msgid "Restore default"
msgstr "Obnovi privzeto"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:131
#: src/components/misc/Changelog/ChangelogTable.js:40
msgid "Revert"
msgstr "Razveljavi"

#: src/components/misc/Changelog/ChangelogTableRow.js:192
msgid "Revert actions"
msgstr "Razveljavi dejanja"

#: src/components/misc/Changelog/ChangelogTableRow.js:209
msgid "Revert now"
msgstr "Povrni zdaj"

#: src/components/misc/Changelog/ChangelogTableRow.js:163
msgid "Reverted on:"
msgstr "Povrnjeno dne:"

#: src/components/newsroom/components/AiTools/AiGenerateCommunicationPlan.tsx:34
msgid "Review communication plan"
msgstr "Pregled komunikacijskega načrta"

#: src/components/layout/Sidebar/SidebarMainNav.js:171
msgid "Roadmap"
msgstr "Načrt"

#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:65
#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:228
msgid "Role"
msgstr ""

#: src/components/medialist/content/MedialistActionsBar/ErrorPage.tsx:59
msgid "Row"
msgstr "Vrstica"

#: src/components/medialist/forms/FormEditAuthor.js:862
#: src/components/medialist/forms/FormEditAuthor.js:1038
msgid "Salutation"
msgstr "Nagovor"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:103
#: src/components/topics/Content/TopicsList/Reports/AddReportRow.js:44
#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:116
#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:215
#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:101
#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:70
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:122
#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:47
#: src/components/staff/admin/workspace/Workspace.js:234
#: src/components/staff/admin/workspace/Workspace.js:951
#: src/components/staff/admin/user/User.js:159
#: src/components/staff/admin/user/User.js:312
#: src/components/settings/SettingsTheme/ThemePicker.tsx:139
#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:103
#: src/components/settings/SettingsLogo/SettingsLogo.js:164
#: src/components/settings/SettingsApplication/SettingsApplication.js:50
#: src/components/reports/Content/ReportsList/ReportsForm.js:342
#: src/components/reports/Content/ReportsList/NewReportButtonModal.js:36
#: src/components/reports/Content/ReportsList/RecipientsList/Recipient.js:150
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:224
#: src/components/newsroom/content/dashboard/NewsroomBlogSettings.js:21
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:592
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:73
#: src/components/monitoring/FeedList/FeedListItem/ArticleNote/ArticleNote.tsx:82
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:21
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:467
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:86
#: src/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload.js:242
#: src/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload.tsx:116
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:191
#: src/components/misc/MntrEditor/forms/FormEditorFontSize/FormEditorFontSize.tsx:35
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:170
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:21
#: src/components/misc/Capture/Capture.js:238
#: src/components/medialist/forms/FormEditAuthor.js:601
#: src/components/medialist/forms/FormEditAuthor.js:741
#: src/components/layout/Sidebar/modules/SidebarTopics/FormFolder.tsx:25
#: src/components/layout/Sidebar/modules/SidebarBaskets/modalEditBasket.tsx:55
#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:91
#: src/components/forms/tags/FormEditTag/FormEditTag.js:48
#: src/components/emailing/forms/FormSenderSettings.js:193
#: src/components/emailing/content/CreateEmailContent.js:321
#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:58
#: src/components/dashboards/DashboardSelector/FormEditDashboard.js:35
msgid "Save"
msgstr "Shrani"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:147
msgid "Save & Publish"
msgstr "Shrani in objavi"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:151
msgid "Save & Schedule"
msgstr "Shrani in načrtuj"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:188
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:61
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:61
msgid "Save as"
msgstr "Shrani kot"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/ModalAddDashboardFooter.tsx:35
msgid "Save changes"
msgstr "Shrani spremembe"

#: src/components/misc/Capture/Capture.js:245
#: src/components/OurChart/OurChartAdvanced.js:187
msgid "Save in format"
msgstr "Shrani v formatu"

#: src/helpers/modal/withModalEmailPreview.js:94
msgid "Save report"
msgstr "Shrani poročilo"

#: src/components/topics/Content/TopicsList/FormSaveMegalist/FormSaveMegalist.js:20
msgid "Save selection"
msgstr "Shrani izbiro"

#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:142
#: src/components/misc/ResendSettings/SaveResendSettings/SaveResendSettings.js:51
#: src/components/misc/ExportSettings/SaveExportSettings/SaveExportSettings.js:51
msgid "Save settings"
msgstr "Shrani nastavitve"

#: src/constants/analytics/primeScoreCharts.ts:74
msgid "Scatter"
msgstr "Raztros"

#: src/components/misc/Changelog/ChangelogTableRow.js:212
msgid "Schedule revert"
msgstr "Načrtuj povrnitev"

#: src/components/newsroom/components/PostsList/PostsList.js:188
msgid "Scheduled"
msgstr "Načrtovano"

#: src/components/misc/Changelog/ChangelogTableRow.js:172
msgid "Scheduled on:"
msgstr "Načrtovano za:"

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:99
msgid "Scheduled to send at {scheduledDateFormatted}, are you sure you want to delete this email?"
msgstr "Načrtovano pošiljanje ob {scheduledDateFormatted}, ste prepričani, da želite izbrisati to e-pošto?"

#: src/components/emailing/content/CreateEmailContent.js:150
msgid "Scheduled to send email"
msgstr "Načrtovano pošiljanje e-pošte"

#: src/components/tvr/Inspector/InspectorMedia/PaginationMedia/PaginationMedia.js:46
msgid "Screens"
msgstr "Zasloni"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:33
msgid "Screenshot"
msgstr "Posnetek zaslona"

#: src/components/topics/Content/TopicsList/Keyword/FormEditKeyword.js:106
#: src/components/tariff/MonitoredMedia/MonitoredMedia.js:174
#: src/components/monitoring/Inspector/InspectorMonitora/HashTagsList/HashTagsList.js:55
#: src/components/monitoring/Inspector/InspectorMonitora/Entities/EntityItem.js:59
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Keywords.js:38
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:139
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:55
#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/MedialistArticlesFilterSearchQuery.js:84
#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:33
#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:51
#: src/components/forms/dashboard/Search/SearchForm.js:71
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:56
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormQuery/FormQuery.js:85
msgid "Search"
msgstr "Iskanje"

#: src/components/forms/dashboard/Search/SearchForm.js:75
msgid "Search authors"
msgstr "Iskanje avtorjev"

#: src/pages/authors/index.js:53
msgid "Search authors by <0>many filters</0>"
msgstr "Iščite avtorje s pomočjo <0>številnih filtrov</0>"

#: src/components/forms/dashboard/Search/SearchForm.js:79
msgid "Search changelog"
msgstr "Išči v dnevniku sprememb"

#: src/components/forms/dashboard/Search/SearchForm.js:43
#: src/components/forms/dashboard/Search/SearchAdmin.js:39
msgid "Search customers"
msgstr "Iskanje strank"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:360
msgid "Search engine metadata"
msgstr "Metapodatki za iskalnike"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:56
#: src/components/help/search/Content/RulesPhrase.js:19
msgid "Search for phrases"
msgstr "Iskanje fraz"

#: src/pages/help/search.js:12
#: src/components/misc/MediaArchiveMessage/MediaArchiveMessage.js:28
#: src/components/layout/Header/Header.js:257
#: src/components/layout/Header/Header.js:320
msgid "Search help"
msgstr "Pomoč pri iskanju"

#: src/components/layout/Header/SearchSuggest/SearchSuggest.js:70
msgid "Search History"
msgstr "Zgodovina iskanja"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:62
msgid "Search in"
msgstr "Išči v"

#: src/components/forms/dashboard/Search/SearchForm.js:52
msgid "Search in {topicName}"
msgstr "Iskanje v {topicName}"

#: src/components/forms/dashboard/Search/SearchForm.js:33
msgid "Search in archive"
msgstr "Iskanje v arhivu"

#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:44
msgid "Search in author"
msgstr "Iskanje po avtorju"

#: src/components/forms/dashboard/Search/SearchForm.js:63
msgid "Search in Emailing"
msgstr "Iskanje v e-pošti"

#: src/components/forms/dashboard/Search/SearchForm.js:59
msgid "Search in Newsroom"
msgstr "Iskanje v Medijskem središču"

#: src/components/layout/MntrFiltersBar/forms/FormNote/FormNote.js:31
msgid "Search in Notes"
msgstr "Besedilo v opombi"

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:102
#: src/components/forms/dashboard/Search/SearchForm.js:54
msgid "Search in topic"
msgstr "Iskanje v temi"

#: src/components/forms/dashboard/Search/SearchForm.js:34
#: src/components/forms/dashboard/Search/SearchForm.js:67
#: src/components/forms/dashboard/Search/SearchBarTopicSelector.js:36
msgid "Search in topics"
msgstr "Iskanje po področjih "

#: src/components/layout/MntrFiltersBar/modules/MenuFilterAuthorTitle.js:90
msgid "Search job position"
msgstr "Iskanje delovnega mesta"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:112
#: src/components/topics/Content/TopicsList/Keyword/KeywordExtraQuery.js:37
#: src/components/topics/Content/TopicsList/FormAdvanced/FormAdvanced.js:47
msgid "Search keywords in conjunction with the phrase"
msgstr "Išči ključne besede v povezavi z izrazom"

#: src/components/staff/admin/workspace/Workspace.js:172
#: src/components/staff/admin/user/User.js:100
msgid "Search log"
msgstr "Dnevnik iskanja"

#: src/components/medialist/forms/FormEditAuthor.js:371
#: src/components/medialist/forms/FormEditAuthor.js:514
#: src/components/medialist/content/FeedMedialist/FeedMedialistItem.js:181
msgid "Search on Google"
msgstr "Iskanje na Google"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:124
#: src/components/help/search/Content/RulesOperators.js:19
msgid "Search operators"
msgstr "Iskalni operaterji"

#: src/components/topics/Content/TopicsList/FormEditExtraQuery/FormEditExtraQuery.js:41
msgid "Search query"
msgstr "Iskalni poizvedek"

#: src/store/models/Megalist/MegalistFilter.js:46
msgid "Search Results"
msgstr "Rezultati iskanja"

#: src/components/layout/MntrFiltersBar/forms/FormSearchSources/FormSearchSources.js:33
msgid "Search source or publisher"
msgstr "Išči vir ali založnika"

#. js-lingui-explicit-id
#: src/components/topics/Content/TopicsList/MegalistSearch.js:42
msgid "megalist.search"
msgstr "Poišči vir ali založbo"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:120
msgid "Search users"
msgstr "Išči uporabnike"

#: src/components/layout/AuthWrapper/modules/AuthSignUpStepper.js:25
msgid "Second Step"
msgstr "Drugi korak"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleDetail.js:311
msgid "Section"
msgstr "Rubrika"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:55
#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:79
#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:30
#: src/components/misc/ActionsBar/Selector/Selector.js:29
#: src/components/misc/ActionsBar/Selector/Selector.js:70
msgid "Select"
msgstr "Izberi"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:106
msgid "Select a method"
msgstr "Izberite metodo"

#: src/helpers/modal/withModalAddArticle/ArticleBoxesPreview.tsx:31
msgid "Select a preview card for the newsroom article to include in the email"
msgstr "Izberite predogledno kartico za članek iz novic, ki bo vključen v e-pošto"

#: src/components/topics/Content/TopicsList/FormAdvanced/AddItemToSelectorModal/AddItemToSelectorModalFooter.js:33
#: src/components/layout/MntrFiltersBar/modules/MenuFilterToggleAllButtons.js:59
msgid "Select all"
msgstr "Izberi vse"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:120
msgid "Select article"
msgstr "Izberite članek"

#: src/components/misc/portable/PortableResend/PortableResend.js:118
#: src/components/misc/portable/PortableExport/PortableExport.js:113
msgid "Select articles to export."
msgstr "Izberite članke za izvoz."

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:65
msgid "Select at least one mediatype"
msgstr "Izberite vsaj eno vrsto medija"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterContent.tsx:67
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:52
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewSocialEngagement/PreviewSocialEngagement.js:32
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:51
#: src/components/analytics/AnalyticsContent.js:106
msgid "Select at least one topic"
msgstr "Izberite vsaj eno temo"

#: src/components/emailing/content/EmailingCampaignEmailDetailContent.tsx:73
#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:85
msgid "Select campaign"
msgstr "Izberite kampanjo"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:41
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:48
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:54
msgid "Select category"
msgstr "Izberite kategorijo"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:354
#: src/components/misc/ColorPicker/ColorPickerSelector.js:100
#: src/components/misc/ColorPicker/ColorPicker.js:61
#: src/components/misc/ColorPicker/ColorPicker.js:67
msgid "Select color"
msgstr "Izberite barvo"

#: src/components/newsroom/forms/FormNewsroomPost/CoverImageUpload.js:85
msgid "Select Cover Image"
msgstr "Izberite naslovno sliko"

#: src/components/topics/Content/TopicsList/KeywordExtraQueryAdvanced/KeywordExtraQueryAdvanced.tsx:25
msgid "Select from our list of predefined queries"
msgstr "Izberite s seznama vnaprej pripravljenih poizvedb"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:130
#: src/components/settings/SettingsLogo/SettingsLogo.js:129
#: src/components/forms/adapters/MntrFileAdapter/MntrFileAdapter.js:58
msgid "Select Image"
msgstr "Izberi sliko"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:324
msgid "Select Logo"
msgstr "Izberi logotip"

#: src/helpers/modal/withModalAddArticle/ModalAddArticle.tsx:108
msgid "Select newsroom"
msgstr "Izberite Medijsko središče"

#: src/components/layout/MntrFiltersBar/forms/FormPageNumbers/FormPageNumbers.tsx:75
msgid "Select pages"
msgstr "Izberite strani"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterSource.js:96
msgid "Select sources"
msgstr "Izberite vire"

#: src/components/emailing/forms/FormSenderSettings.js:116
msgid "Select the encryption method used by your SMTP server."
msgstr "Izberite metodo šifriranja, ki jo uporablja vaš SMTP strežnik."

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:45
msgid "Select the title"
msgstr "Izberite naslov"

#: src/components/widgets/modules/stats/WidgetStats.js:79
#: src/components/widgets/modules/socialEngagement/WidgetSocialEngagement.js:41
#: src/components/widgets/modules/analytics/WidgetAnalytics.js:52
#: src/components/monitoring/Monitoring.js:166
msgid "Select Topic"
msgstr "Izberite temo"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:85
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormSubtype/FormSubtype.js:81
msgid "Select type"
msgstr "Izberite vrsto"

#: src/components/emailing/forms/FormSenderSettings.js:207
msgid "Select verification method"
msgstr "Izberite način preverjanja"

#: src/helpers/charts/makeGranularityMenu.js:6
msgid "Select view"
msgstr "Izberite pogled"

#: src/components/layout/Header/UserMenu/UserMenu.js:60
msgid "Select workspace"
msgstr "Izberite delovni prostor"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:79
msgid "Selected"
msgstr "Izbrano"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:122
msgid "Selected articles will be removed."
msgstr "Izbrani članki bodo odstranjeni."

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:96
msgid "Selected merge tags can not be applied to the author"
msgstr "Izbranih združevalnih oznak ni mogoče uporabiti za avtorja"

#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterList.js:188
msgid "Selected sources"
msgstr "Izbrani viri"

#: src/components/topics/Content/TopicsList/FormIncludeCategoryTypes/FormIncludeCategoryTypes.js:69
#: src/components/misc/TopicsMultiSelector/TopicsMultiSelector.js:133
msgid "Selected: {selectedLength}/{MAX_SELECTED_LIMIT}"
msgstr "Izbrano: {selectedLength} od {MAX_SELECTED_LIMIT}"

#: src/store/models/Megalist/Megalist.js:376
msgid "Selection \"{name}\" was removed."
msgstr "Izbor \"{name}\" je bil odstranjen."

#: src/store/models/Megalist/Megalist.js:335
#: src/store/models/Megalist/Megalist.js:354
msgid "Selection saved as \"{name}\"."
msgstr "Izbor je bil shranjen kot \"{name}\"."

#: src/components/reports/history/Compose.js:84
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:198
#: src/components/forms/dashboard/ExportResend/ExportResend.js:179
#: src/components/exportList/Content/Content.tsx:86
#: src/components/emailing/content/CreateEmailContent.js:325
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:122
msgid "Send"
msgstr "Pošlji"

#: src/components/misc/portable/PortableResend/PortableResend.js:70
#: src/components/misc/portable/PortableResend/PortableResend.js:110
msgid "Send all articles to email"
msgstr "Pošlji vse članke na e-pošto"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:132
msgid "Send article"
msgstr "Pošlji članek"

#: src/components/misc/portable/PortableResend/PortableResend.js:66
#: src/components/misc/portable/PortableResend/PortableResend.js:106
msgid "Send article to email"
msgstr "Pošlji članek na e-pošto"

#: src/components/misc/portable/PortableResend/PortableResend.js:68
#: src/components/misc/portable/PortableResend/PortableResend.js:108
msgid "Send articles to email"
msgstr "Pošlji članke na e-pošto"

#: src/components/reports/Content/ReportsList/ReportsForm.js:121
msgid "Send empty reports"
msgstr "Pošlji tudi prazna poročila"

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:76
msgid "Send Feedback"
msgstr "Pošlji povratne informacije"

#: src/components/reports/Content/ReportsList/ReportsForm.js:270
msgid "Send in times (optional)"
msgstr "Pošlji le ob določenih časih (neobvezno)"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:150
msgid "Send now"
msgstr "Pošlji zdaj"

#: src/components/reports/Content/ReportsList/ReportsForm.js:131
#: src/components/reports/Content/ReportsList/ReportsForm.js:218
msgid "Send on days"
msgstr "Dnevi pošiljanja"

#: src/components/reports/Content/ReportsList/ReportsForm.js:155
msgid "Send on holidays"
msgstr "Pošiljaj tudi med prazniki"

#: src/components/reports/Content/ReportsList/ReportsForm.js:166
msgid "Send on times"
msgstr "Časi pošiljanja"

#: src/components/emailing/content/promo/PromoEmailing.js:28
msgid "Send press releases to journalists with one click."
msgstr "Pošiljanje sporočil za javnost novinarjem z enim klikom."

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:151
msgid "Send this email immediately"
msgstr "Pošlji ta e-poštni naslov takoj"

#: src/components/reports/history/RecipientsTableHeader.js:41
msgid "Send this to your IT specialist"
msgstr "Posredujte to svojemu IT strokovnjaku"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:58
#: src/components/emailing/content/EmailDetailEmailContent.js:17
#: src/components/emailing/content/CreateEmailContent.js:418
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:135
msgid "Sender"
msgstr "Pošiljatelj"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:186
msgid "Senders"
msgstr "Pošiljatelji"

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:58
msgid "Sending a generic email without an attached article provides no useful data for tracking"
msgstr "Pošiljanje splošnega e-poštnega sporočila brez priloženega članka ne zagotavlja uporabnih podatkov za sledenje"

#: src/components/reports/history/HistoryTable.js:223
msgid "Sent automatically via scheduled report"
msgstr "Poslano samodejno prek načrtovanega poročila"

#: src/components/staff/admin/customer/invoices/InvoicesTable.js:69
msgid "Sent to"
msgstr "Poslano na"

#: src/constants/analytics.js:812
#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:101
#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/Sentiment.js:30
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:349
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:61
#: src/components/OurChart/OurChartAdvanced.js:108
msgid "Sentiment"
msgstr "Sentiment"

#: src/components/emailing/forms/FormEmailRecipients.js:107
msgid "Separate emails with a space, comma, or semicolon"
msgstr "E-poštne naslove ločite z presledkom, vejico ali podpičjem"

#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:50
msgid "Separate regional duplicates"
msgstr "Ne združuj regionalnih podvojenih zapisov"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:57
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:60
msgid "Separated by space, newline, comma, or semicolon."
msgstr "Ločeno s presledkom, novo vrstico, vejico ali podpičjem."

#: src/components/newsroom/content/dashboard/ChartVisits.js:110
msgid "Sessions"
msgstr "Seje"

#: src/components/monitoring/Inspector/AnnotationTool/AnnotationTool.js:117
msgid "Set annotation"
msgstr "Nastavi anotacijo"

#: src/components/layout/AuthWrapper/constants/features.slides.js:354
msgid "Set any number of reports that will be sent to any number of contacts. Everyone receives the correct info at the right time and in the format you choose."
msgstr "Nastavite lahko poljubno število poročil, ki bodo poslana na poljubno število kontaktov. Vsakdo prejme pravilne informacije ob pravem času in v formatu, ki ga izberete."

#: src/components/emailing/content/CreateEmailContent.js:372
msgid "Set as Draft"
msgstr "Nastavi kot osnutek"

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:93
msgid "Set as primary"
msgstr "Nastavi kot primarno"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:163
msgid "Set automatic publishing of this post"
msgstr "Nastavi samodejno objavo te objave"

#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:36
#: src/components/staff/admin/workspace/UsersTable/UpdatePermissions.tsx:40
msgid "Set permissions"
msgstr "Nastavi dovoljenja"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:96
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:162
msgid "Set publish date"
msgstr "Nastavi datum objave"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:123
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:158
msgid "Set send date"
msgstr "Nastavi datum pošiljanja"

#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:229
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:233
#: src/components/monitoring/FeedActionsBar/FeedMapActionsBar.js:339
msgid "Set sentiment"
msgstr "Nastavi sentiment"

#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:159
msgid "Set this email to auto-send"
msgstr "Nastavi samodejno pošiljanje e-pošte"

#: src/pages/user/settings.js:19
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:387
#: src/components/newsroom/content/posts/NewsroomPosts.js:293
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:57
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:22
#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:48
#: src/components/layout/Header/UserMenu/UserMenu.js:135
#: src/components/emailing/sidebar/EmailingSidebarDashboard.js:27
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:76
#: src/components/emailing/forms/FormSenderSettings.js:252
#: src/components/emailing/forms/FormSenderSettings.js:283
#: src/components/emailing/content/EmailingSettingsContent.js:54
#: src/components/emailing/content/EmailingCampaignsContent.tsx:40
msgid "Settings"
msgstr "Nastavitve"

#. placeholder {0}: model.name
#: src/store/models/ResendSettings.ts:38
#: src/store/models/ExportSettings.js:26
msgid "Settings \"{0}\" was applied."
msgstr "Nastavitev \"{0}\" je bila uporabljena."

#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:114
#: src/store/models/ExportSettings.js:80
msgid "Settings \"{0}\" was removed."
msgstr "Nastavitev \"{0}\" je bila odstranjena."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:21
msgid "Settings complete"
msgstr "Nastavitve dokončane"

#. placeholder {0}: model.name
#. placeholder {0}: exportSettings.name
#: src/store/models/ResendSettings.ts:93
#: src/store/models/ResendSettings.ts:132
#: src/store/models/ExportSettings.js:62
#: src/store/models/ExportSettings.js:96
msgid "Settings saved as \"{0}\"."
msgstr "Nastavitve so shranjene kot \"{0}\"."

#: src/store/models/topics/TopicsStore.js:229
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:197
msgid "Settings saved."
msgstr "Nastavitve shranjene."

#: src/components/dashboards/Content.js:75
#: src/components/dashboards/Content.js:76
#: src/components/dashboards/DashboardSelector/DashboardSelector.js:69
msgid "Share"
msgstr "Deli"

#: src/constants/analytics.js:698
#: src/constants/analytics.js:979
#: src/constants/analytics.js:993
#: src/constants/analytics.js:1009
#: src/constants/analytics.js:1024
#: src/constants/analytics.js:1039
msgid "Share of voice"
msgstr "Delež glasu"

#: src/store/models/dashboards/Dashboards.js:523
msgid "Shared link will expire"
msgstr "Povezava za deljenje bo potekla"

#: src/components/topics/Content/TopicsList/MegalistToolbar/MegalistToolbar.tsx:107
#: src/components/medialist/content/MedialistDashboard.js:135
msgid "Show"
msgstr "Prikaži"

#. placeholder {0}: format.formatAttachedArticles(item.mentioned_article_count)
#: src/components/emailing/components/EmailingFeed/EmailingFeedItem.js:191
msgid "Show {0}"
msgstr "Pokaži {0}"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:14
#: src/components/layout/Header/AppNotifications/AppNotifications.js:186
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
#: src/components/OurChart/OurChartAdvanced.js:116
msgid "Show All"
msgstr "Pokaži vse"

#. placeholder {0}: filteredRecipients.length - displayLimit
#: src/components/emailing/modules/PreviewEmail/RecipientsList.tsx:130
msgid "Show all recipients (+{0})"
msgstr "Prikaži vse prejemnike (+{0})"

#: src/components/medialist/forms/FormEditAuthor.js:772
#: src/components/medialist/forms/FormEditAuthor.js:800
msgid "Show and copy to clipboard"
msgstr "Prikaži in kopiraj v odložišče"

#: src/components/reports/history/HistoryTable.js:397
#: src/components/exportList/History/HistoryTable/HistoryTable.js:86
msgid "Show articles"
msgstr "Prikaži članke"

#: src/components/layout/MntrFiltersBar/modals/withModalMedialistArticlesFilter/ModalMedialistArticlesFilterFooter.tsx:123
msgid "Show articles in feed"
msgstr "Prikaži članke v viru"

#: src/components/medialist/forms/FormEditAuthor.js:343
#: src/components/medialist/forms/FormEditAuthor.js:351
#: src/components/medialist/forms/FormEditAuthor.js:424
#: src/components/medialist/forms/FormEditAuthor.js:433
#: src/components/medialist/content/MedialistDashboard.js:99
msgid "Show authors"
msgstr "Prikaži avtorje"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:133
#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:28
msgid "Show changes"
msgstr "Pokaži spremembe"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:23
msgid "Show checked only"
msgstr "Prikaži samo označeno"

#: src/components/reports/Content/ReportsList/ReportsListItemToolbar.js:21
msgid "Show history for this report"
msgstr "Prikaži zgodovino za to poročilo"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
msgid "Show less"
msgstr "Skrij"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPosts.tsx:95
msgid "Show Less"
msgstr "Pokaži manj"

#: src/components/monitoring/Inspector/DemographicsData/modules/EntriesList/EntriesListContent.js:93
#: src/components/medialist/content/MedialistDashboard.js:162
#: src/components/medialist/content/MedialistDashboard.js:183
msgid "Show more"
msgstr "Prikaži več"

#: src/components/medialist/forms/FormEditAuthor.js:356
#: src/components/medialist/forms/FormEditAuthor.js:439
msgid "Show newsrooms"
msgstr "Prikaži Medijsko središče"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:65
msgid "show stats"
msgstr "prikaži statistiko"

#: src/components/topics/Content/TopicsList/MegalistToolbar/VisibilityFilterPopup.js:32
msgid "Show unchecked only"
msgstr "Prikaži samo neoznačene"

#: src/components/monitoring/Inspector/MentionsList/MentionsList.js:52
msgid "showing {counterFrom} out of {counterTo}"
msgstr "prikazano {counterFrom} od {counterTo}"

#: src/pages/sign-up.tsx:11
#: src/pages/sign-up-completion.tsx:30
#: src/pages/staff/sign-up.js:11
#: src/pages/staff/sign-up-completion.js:26
#: src/components/staff/SignUp.js:30
#: src/components/page/auth/SignUpCompletion/SignUpCompletion.tsx:174
#: src/components/page/auth/SignUp/SignUp.js:74
msgid "Sign Up"
msgstr "Registriraj se"

#: src/components/emailing/content/Signature.tsx:104
msgid "Signature"
msgstr "Podpis"

#: src/components/layout/MntrActiveFilters/modules/SimilarArticle.js:12
msgid "Similar to"
msgstr "Podobno kot"

#: src/components/reports/Content/ReportsList/ReportsFormDeduplicate.js:62
msgid "Similarity"
msgstr "Podobnost"

#: src/components/emailing/content/promo/PromoEmailing.js:23
msgid "Simple setting of the appearance of the email template."
msgstr "Preprosta nastavitev videza e-poštne predloge."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:124
msgid "Skip"
msgstr "Preskoči"

#: src/components/medialist/content/MedialistActionsBar/FormImportContacts.tsx:60
msgid "Skip error lines"
msgstr "Preskoči vrstico z napako"

#: src/components/layout/Sidebar/SidebarMainNav.js:216
msgid "SMS alerts"
msgstr "SMS opozorila"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:134
msgid "SMTP settings are invalid."
msgstr "Nastavitve SMTP niso pravilne."

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:132
msgid "SMTP settings are valid."
msgstr "Nastavitve SMTP so veljavne."

#: src/components/misc/ActionsBar/View/ViewMenu.js:261
#: src/components/misc/ActionsBar/View/ViewMenu.js:345
msgid "Social data"
msgstr "Podatki iz družbenih omrežij"

#: src/store/models/dashboards/DashboardPreview.js:110
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:51
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:51
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:32
msgid "Social Engagement"
msgstr "Družbena interakcija"

#: src/constants/analytics.js:198
#: src/constants/analytics.js:289
#: src/constants/analytics.js:398
#: src/constants/analytics.js:1040
#: src/components/misc/ActionsBar/View/ViewMenu.js:93
msgid "Social interactions"
msgstr "Družbene interakcije"

#: src/constants/stats.ts:31
#: src/components/widgets/modules/stats/WidgetStats.js:143
msgid "Social Interactions"
msgstr "Družbene interakcije"

#: src/constants/analytics.js:309
#: src/constants/analytics.js:548
msgid "Social interactions by mention type"
msgstr "Družbene interakcije glede na vrsto omembe"

#: src/constants/analytics.js:307
msgid "Social interactions by sentiment"
msgstr "Družbene interakcije po sentimentu"

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:179
#: src/components/staff/admin/workspace/Workspace.js:534
#: src/components/medialist/forms/FormEditAuthor.js:819
#: src/components/medialist/forms/FormEditAuthor.js:922
#: src/components/layout/AuthWrapper/constants/features.slides.js:87
#: src/components/exportList/ExportLimit/ExportLimit.js:28
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:29
#: src/components/analytics/AnalyticsContent.js:149
#: src/components/analytics/AnalyticsContent.js:228
msgid "Social Media"
msgstr "Družbeni mediji"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:37
msgid "Social Media in {appName}"
msgstr "Družbeni mediji v aplikaciji {appName}"

#: src/components/tariff/TariffLimits/TariffLimits.js:186
msgid "Social media topics limit"
msgstr "Omejitev tem (družbena omrežja)"

#: src/components/staff/admin/workspace/Workspace.js:578
msgid "Social media topics limit (Sentione price = 500 Kč per topic)"
msgstr "Omejeno število tem družbenih omrežij (Sentione cena = 20 Eur/tema)"

#: src/components/misc/MntrEditor/MntrEditorFloatingBar.js:147
msgid "Social post"
msgstr "Objava na družbenem omrežju"

#: src/components/medialist/content/AuthorContactInformation.js:38
msgid "Social profiles"
msgstr "Družbeni profili"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/PopupSourceContent.js:56
#: src/components/misc/ActionsBar/View/ViewMenu.js:124
msgid "Sold amount"
msgstr "Prodana količina"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/MetaDataMedia.js:174
#: src/components/medialist/content/MedialistInspector/AuthorSidebar/AuthorMediaData/AuthorMediaData.js:86
msgid "Sold amount (print+digital)"
msgstr "Prodano (tisk+el.)"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:652
msgid "Some articles may not be deleted."
msgstr "Nekateri članki morda niso bili izbrisani."

#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:155
msgid "Some data are missing in the generated content. Add them manually before proceeding."
msgstr "V ustvarjeni vsebini manjkajo nekateri podatki. Pred nadaljevanjem jih dodajte ročno."

#: src/components/emailing/components/EmailRecipientsList/RenderInvalidRecipients.tsx:23
msgid "Some recipients are missing information for merge tags or email. Please add the missing information or replace the recipients by clicking on them."
msgstr "Nekaterim prejemnikom manjkajo informacije za združevalne oznake ali e-pošto. Prosimo, dodajte manjkajoče informacije ali zamenjajte prejemnike s klikom nanje."

#: src/helpers/store/apiClient.js:249
msgid "Something failed while preparing a server request. Our team was notified."
msgstr "Pri pripravi strežniške zahteve je prišlo do napake. Naša ekipa je bila obveščena."

#: src/pages/_error.js:44
msgid "Something's gone wrong"
msgstr "Prišlo je do napake"

#: src/components/misc/ActionsBar/Sort/SortExport.js:19
#: src/components/misc/ActionsBar/Sort/Sort.js:21
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:468
#: src/components/layout/MntrFiltersBar/modules/MenuFilterOrderBy.js:23
msgid "Sort"
msgstr "Razvrsti"

#: src/components/misc/ActionsBar/Sort/SortExport.js:25
#: src/components/misc/ActionsBar/Sort/Sort.js:38
msgid "Sort List"
msgstr "Razvrsti seznam"

#: src/components/tariff/AgencyMedia/AgencyMedia.js:64
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:371
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:205
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:226
#: src/components/layout/MntrActiveFilters/modules/NewsSource.js:13
#: src/components/exportList/History/HistoryTable/HistoryTable.js:54
msgid "Source"
msgstr "Vir"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:326
msgid "Source <0>{newsSourceName}</0> will be removed from the topic <1>{topicMonitorName}</1>."
msgstr "Vir <0>{newsSourceName}</0> bo odstranjen iz teme <1>{topicMonitorName}</1>."

#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:37
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:456
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:511
msgid "Source File"
msgstr "Izvorna datoteka"

#: src/store/models/monitoring/Inspector/Inspector.ts:532
#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:885
msgid "Source removed"
msgstr "Vir odstranjen"

#: src/components/staff/admin/workspace/Workspace.js:892
msgid "Sources"
msgstr "Viri"

#: src/components/staff/admin/workspace/Workspace.js:894
#: src/components/staff/admin/workspace/Workspace.js:901
msgid "Sources per client"
msgstr "Viri na stranko"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:231
msgid "Special tag"
msgstr "Posebna oznaka"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:47
msgid "Specify the primary goal (inform, persuade, invite, etc.)."
msgstr "Določite primarni cilj (obvestiti, prepričati, povabiti itd.)."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:137
msgid "Spokesperson"
msgstr "Tiskovni predstavnik"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:57
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:194
msgid "Start editing"
msgstr "Začnite urejati"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/StepGenerateContent.tsx:75
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:213
msgid "Start over"
msgstr "Začnite znova"

#: src/components/forms/inspector/FormMediaEditor.js:82
msgid "Start time must be lower than end time"
msgstr "Začetni čas mora biti manjši od končnega časa"

#: src/components/emailing/content/CreateEmailContent.js:584
msgid "Start typing or click + to add more content"
msgstr "Začnite tipkati ali kliknite + za dodajanje več vsebine"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:681
msgid "Start typing or insert image, video…"
msgstr "Začnite tipkati ali vstavite sliko, video…"

#: src/components/newsroom/content/posts/ChooseTemplates.tsx:76
msgid "Start with template"
msgstr "Začnite s predlogo"

#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:119
#: src/components/staff/admin/user/WorkspacesTable.js:77
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:80
#: src/components/staff/admin/customer/users/UsersTable.js:71
msgid "State"
msgstr "Stanje"

#: src/store/models/dashboards/DashboardPreview.js:121
#: src/components/emailing/content/promo/PromoEmailing.js:32
#: src/components/dashboards/EmptyDashboard/EmptyDashboardPage.js:23
#: src/components/dashboards/EmptyDashboard/EmptyDashboard.js:23
#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:44
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:54
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/AddWidgetHeader/AddWidgetHeader.js:26
msgid "Statistics"
msgstr "Statistika"

#: src/components/reports/history/RecipientsTableHeader.js:33
#: src/components/newsroom/content/posts/NewsroomPosts.js:166
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:27
#: src/components/layout/MntrFiltersBar/MntrFiltersBar.js:407
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNewsroomStatus.js:24
#: src/components/layout/MntrActiveFilters/modules/NewsroomStatus.js:21
msgid "Status"
msgstr "Status"

#: src/components/reports/history/HistoryTable.js:80
#: src/components/reports/history/HistoryTable.js:104
#: src/components/reports/history/HistoryTable.js:322
msgid "Status unknown"
msgstr "Stanje neznano"

#: src/components/layout/AuthWrapper/constants/features.slides.js:260
msgid "Streamline communication efforts and maximize your PR impact."
msgstr "Komunicirajte z mediji enostavno in učinkovito ter povečajte učinkovitost svojega PR."

#: src/components/reports/history/HistoryTable.js:149
#: src/components/reports/history/Compose.js:62
#: src/components/forms/dashboard/ExportResend/ExportResend.js:102
#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:92
#: src/components/emailing/forms/WizardGenerateEmail/StepPreviewGenerated.tsx:86
#: src/components/emailing/content/EmailDetailEmailContent.js:27
#: src/components/emailing/content/CreateEmailContent.js:446
msgid "Subject"
msgstr "Zadeva"

#: src/components/misc/MntrForm/MntrForm.tsx:525
msgid "Submit"
msgstr "Pošlji"

#: src/constants/stats.ts:36
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:79
#: src/components/forms/dashboard/ExportResend/ExportResend.js:124
msgid "Summary"
msgstr "Povzetek"

#: src/components/staff/admin/customer/expenses/DetailExpenseModal.js:35
msgid "Supplier"
msgstr "Dobavitelj"

#: src/components/layout/Sidebar/SidebarMainNav.js:177
msgid "Support"
msgstr "Podpora"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:472
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:539
msgid "Supported file types:"
msgstr "Podprte vrste datotek:"

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:245
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:157
msgid "Tag <0>{0}</0> will be hidden."
msgstr "Oznaka <0>{0}</0> bo skrita."

#. placeholder {0}: item.label
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:315
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:205
msgid "Tag <0>{0}</0> will be removed."
msgstr "Oznaka <0>{0}</0> bo odstranjena."

#: src/components/forms/tags/FormNewTag/FormNewTag.js:26
#: src/components/forms/tags/FormEditTag/FormEditTag.js:25
#: src/components/forms/tags/FormEditTag/FormEditTag.js:28
msgid "Tag name"
msgstr "Ime oznake"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:761
#: src/store/models/authors/AuthorsStore.js:716
msgid "Tag removed successfully."
msgstr "Oznaka je bila uspešno odstranjena."

#: src/constants/analytics.js:845
#: src/constants/analytics.js:1069
#: src/components/medialist/forms/FormEditAuthor.js:616
#: src/components/medialist/content/FeedMedialist/AdditionalInfoTagsOrListsAuthors.tsx:90
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:79
#: src/components/forms/dashboard/Export/AdvancedSettingsForm.js:72
#: src/components/emailing/content/tabs/AddRecipients.tsx:70
msgid "Tags"
msgstr "Oznake"

#: src/components/medialist/forms/FormEditAuthor.js:610
msgid "Tags, lists and note"
msgstr "Oznake, seznami in opomba"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:41
msgid "Target Audience:"
msgstr "Ciljna publika:"

#: src/components/settings/SettingsTariff/SettingsTariff.js:22
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:27
msgid "Tariff information"
msgstr "Informacije o tarifi"

#: src/components/reports/Content/ReportsList/ReportsForm.js:317
#: src/components/forms/dashboard/ExportResend/ExportResend.js:107
msgid "Template"
msgstr "Predloga"

#: src/components/emailing/forms/FormSenderSettings.js:167
msgid "Test DNS Settings"
msgstr "Preizkusi nastavitve DNS"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:377
msgid "Text"
msgstr "Besedilo"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:207
msgid "Text align"
msgstr "Poravnava besedila"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:281
#: src/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor.tsx:76
#: src/components/misc/MntrEditor/forms/FormEditorColorPicker/FormEditorColorPicker.js:36
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:113
msgid "Text Color"
msgstr "Barva besedila"

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:155
msgid "Text format"
msgstr "Oblika besedila"

#: src/components/page/auth/Expired/Expired.js:60
msgid "Thank you for trying out {appName}."
msgstr "Hvala, ker ste preizkusili aplikacijo {appName}."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:91
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:113
msgid "Thank you for your feedback!"
msgstr "Hvala za vaše povratne informacije!"

#: src/components/page/auth/UserInactive/UserInactive.js:17
msgid "Thank you for your interest in using {appName}."
msgstr "Hvala za vaše zanimanje za uporabo aplikacije {appName}."

#: src/pages/user/yoy-analysis.js:67
#: src/pages/user/reactivate-24.js:67
msgid "Thank you for your interest. We will contact you soon.<0/><1/>Have a great day,<2/><3/>{appName} team"
msgstr "Hvala za vaš interes. Kmalu vas bomo kontaktirali.<0/><1/>Želim vam lep dan,<2/><3/>ekipa {appName}"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:100
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:131
msgid "Thank you!"
msgstr "Hvala!"

#: src/store/models/monitoring/Inspector/Inspector.ts:910
msgid "The article already belongs to the topic."
msgstr "Članek že spada pod temo."

#: src/store/models/monitoring/WorkspaceArticles.js:164
msgid "The article has been uploaded and is currently being processed. After that it will be added to your feed. You can see the processing status in My Articles."
msgstr "Članek je bil uspešno naložen in trenutno poteka obdelava. Nato bo dodan v vaš vir. Stanje obdelave lahko vidite v Mojih člankih."

#: src/store/models/monitoring/Inspector/Inspector.ts:907
msgid "The article was added to the topic. Please reload the feed to see the changes."
msgstr "Članek je bil dodan k temi. Osvežite vir, da vidite spremembe. Če ga ne vidite, preverite nastavitve filtriranja."

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:392
msgid "The article will be removed from your media coverage view. If the article also exists in your feed, it will remain there and will not be deleted."
msgstr "Članek bo odstranjen iz vašega pregleda medijskega pokrivanja. Če članek obstaja tudi v vašem viru, bo tam ostal in ne bo izbrisan."

#: src/components/medialist/forms/FormEditAuthor.js:299
#: src/components/medialist/forms/FormEditAuthor.js:463
msgid "The author's profile will be reset to its original values."
msgstr "Profil avtorja bo ponastavljen na izvirne vrednosti."

#: src/store/models/monitoring/Inspector/MediaEditor/MediaEditorStore.js:55
msgid "The clip is being prepared. It may take a while. When the clip is ready for download, you will receive a notification."
msgstr "Posnetek se pripravlja. Lahko traja nekaj časa. Ko bo posnetek pripravljen za prenos, boste prejeli obvestilo."

#: src/components/emailing/forms/FormSenderSettings.js:239
msgid "The DNS Verification Is Unavailable"
msgstr "Preverjanje DNS ni na voljo"

#: src/components/emailing/forms/FormSenderSettings.js:240
msgid "The DNS verification settings for this email are not accessible. We suggest opting for SMTP (Simple Mail Transfer Protocol) as an alternative way of verification. If you need any additional information or help, our support team is here to assist you."
msgstr "Nastavitve za preverjanje DNS za ta e-poštni naslov niso dostopne. Kot alternativno metodo preverjanja predlagamo izbiro protokola SMTP (Simple Mail Transfer Protocol). Če potrebujete dodatne informacije ali pomoč, je naša podpora tukaj, da vam pomaga."

#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:37
msgid "The email content can be automatically adjusted to include personalized details for each recipient"
msgstr "Vsebina e-pošte se lahko samodejno prilagodi, da vključuje personalizirane podrobnosti za vsakega prejemnika."

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:113
msgid "The email is currently empty. Please add some content to the email."
msgstr "E-pošta je trenutno prazna. Prosimo, dodajte nekaj vsebine v e-pošto."

#: src/components/monitoring/Inspector/InspectorMonitora/Summary/forms/FormSummaryFeedback.js:28
msgid "The following summary was generated by a machine and may not accurately represent the original content."
msgstr "Naslednji povzetek je bil ustvarjen strojno in morda ne odraža natančno izvirne vsebine."

#: src/store/models/ExportStore.js:251
msgid "The full article text cannot be downloaded as you have reached your limit. To adjust this limit, please contact support."
msgstr "Celotnega besedila članka ni mogoče prenesti, ker ste dosegli svojo omejitev. Za prilagoditev te omejitve se obrnite na podporo."

#: src/components/tariff/TariffLimits/TariffLimits.js:31
msgid "The limit applies to the number of articles found in the last 30 days generated by set keywords. If you have reached the limit for the number of found articles, <0>edit keywords</0> or contact us to increase the limit."
msgstr "Omejitev velja za število najdenih člankov v zadnjih 30 dneh, ki jih ustvarijo nastavljene ključne besede. Če ste dosegli omejitev za število najdenih člankov, <0>uredi ključne besede</0> ali nas kontaktirajte za povečanje omejitve."

#: src/components/tariff/TariffLimits/TariffLimits.js:68
msgid "The limit applies to the number of exported articles in the last 30 days (topics, archive or report attachments). If you have reached the limit for the number of exported articles, you must wait until the limit is restored or contact us to increase the limit."
msgstr "Omejitev velja za število izvoženih člankov v zadnjih 30 dneh (teme, arhiv ali priloge poročila). Če ste dosegli omejitev za število izvoženih člankov, morate počakati, da se omejitev obnovi, ali pa nas kontaktirajte za povečanje omejitve."

#: src/components/tariff/TariffLimits/TariffLimits.js:104
msgid "The limit applies to the number of translated articles in the last 30 days (topics, archive, report or report attachments). If you are interested in increasing this limit, please contact us."
msgstr "Omejitev velja za število prevedenih člankov v zadnjih 30 dneh (teme, arhiv, poročilo ali priloge poročila). Če vas zanima povečanje te omejitve, nas prosimo kontaktirajte."

#: src/components/exportList/Sidebar/ExportHistory/ExportHistory.js:20
msgid "The list of already exported articles can be downloaded without limitation."
msgstr "Seznam že izvoženih člankov lahko prenesete brez omejitev."

#: src/store/models/authors/Baskets/AuthorBasketDefinitionsStoreArrItem.ts:54
msgid "The list was successfully duplicated."
msgstr "Seznam je bil uspešno podvojen."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:84
msgid "The main content of your post appears to be empty. The body is where you elaborate on your ideas, present your arguments, or share your story. Please add substantial content to your post to engage your readers and convey your message effectively."
msgstr "Zdi se, da je glavna vsebina vašega prispevka prazna. Telo prispevka je mesto, kjer razvijate svoje ideje, predstavljate svoje argumente ali delite svojo zgodbo. Dodajte bistveno vsebino svojemu prispevku, da pritegnete svoje bralce in učinkovito prenesete svoje sporočilo."

#. js-lingui-explicit-id
#: src/components/OurChart/hints.js:20
msgid "help.grp"
msgstr "Medijski vpliv si prizadeva bolje kot absolutno število člankov predstaviti resnično medijsko podobo spremljanega subjekta, kot jo prejme najširša skupina poslušalcev, gledalcev in bralcev medijev. Temelji predvsem na branosti (tisk), poslušanosti (radio), gledanosti (TV) in mesečni obiskanosti spletnih strani (splet). Enota merjenja medijskega vpliva so GRP točke (Gross Rating Points), pri čemer ena GRP točka ustreza enemu odstotku populacije, starejše od petnajst let (npr. za Slovenijo skupini 20.000 posameznikov). Gre za bralce, poslušalce ali gledalce, ki so lahko bili nagovorjeni z objavljenim prispevkom. Bralec, ki je lahko prebral več kot en prispevek, je prištet večkrat. OTS (Opportunity to See) pa pomeni, kolikokrat je imel pripadnik ciljne skupine povprečno možnost prispevek prebrati ali videti. V primeru ciljne skupine vseh prebivalcev Slovenije, starejših od petnajst let, velja: OTS = GRP / 100."

#: src/pages/authors/index.js:43
msgid "The most <0>extensive</0> and the most <1>actual</1> medialist of journalists, publishers & other authors, in which you will find detailed information including contacts."
msgstr "<0>Najobsežnejši</0> in <1>najaktualnejši</1> seznam medijev novinarjev, založnikov in drugih avtorjev, v katerem boste našli podrobne informacije, vključno s kontakti."

#: src/components/emailing/components/EmailingSettingsList/EmailingSendersList.tsx:31
msgid "The primary sender is used as the default sender for emails. You can change this when you create an email."
msgstr "Primarni pošiljatelj se uporablja kot privzeti pošiljatelj za e-pošto. To lahko spremenite, ko ustvarite e-pošto."

#. placeholder {0}: senderItem.unverified_recipients_limit
#. placeholder {1}: senderItem.verified_recipients_limit
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:68
msgid "The recipient limit is set to {0}. For a higher limit of {1} recipients, enable DNS or SMTP verification."
msgstr "Omejitev prejemnikov je nastavljena na {0}. Za višjo omejitev {1} prejemnikov omogočite preverjanje DNS ali SMTP."

#. placeholder {0}: appSettings.appName
#: src/components/monitoring/Inspector/InspectorMonitora/Summary/Summary.js:136
msgid "The summary was created with the {0} application."
msgstr "Povzetek je bil ustvarjen s pomočjo aplikacije {0}."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:493
msgid "The user is responsible for the content uploaded to the {appName} application. By uploading files, you confirm that you own the rights to the file or that the file is licensed under <0>CC0</0>."
msgstr "Uporabnik je odgovoren za vsebino, naloženo v aplikacijo {appName}. Z nalaganjem datotek potrjujete, da imate pravice do datoteke ali da je datoteka licencirana pod <0>CC0</0>."

#: src/components/layout/Header/UserMenu/UserMenu.js:93
msgid "Theme"
msgstr "Videz"

#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItem.js:81
msgid "There are no keywords assigned to this topic"
msgstr "K tej temi niso dodeljene nobene ključne besede"

#: src/pages/404.js:18
msgid "There's nothing here..."
msgstr "Tukaj ni ničesar..."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:107
msgid "These settings allow not only change language of the newsroom, but to link newsrooms together. Pair them in different languages for quick and seamless transitions."
msgstr "Te nastavitve omogočajo ne le spremembo jezika Medijskega središča, temveč tudi povezovanje le teh med seboj. Povežite jih v različnih jezikih za hitre in nemotene prehode."

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:184
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:229
msgid "This field is required"
msgstr "To polje je obvezno"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:222
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:305
#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:418
#: src/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode.tsx:75
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:78
#: src/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton.tsx:83
msgid "This field is required."
msgstr "To polje je obvezno."

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:380
msgid "This is a summary of the page's content. It appears below the headline on the search results page."
msgstr "To je povzetek vsebine strani. Prikazuje se pod naslovom na strani z rezultati iskanja."

#: src/components/emailing/forms/FormSenderSettings.js:109
msgid "This is the port number that your SMTP server uses to send email. If you're not sure, leave it blank to use the default port."
msgstr "To je številka vrat, ki jih vaš SMTP strežnik uporablja za pošiljanje e-pošte. Če niste prepričani, pustite prazno, da se uporabi privzeta vrata."

#: src/components/emailing/forms/FormEmailRecipientsBasket.tsx:134
msgid "This list is empty"
msgstr "Ta seznam je prazen"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:65
msgid "This month"
msgstr "Ta mesec"

#: src/components/misc/Capture/Capture.js:300
msgid "this should take only a couple of seconds"
msgstr "to bo trajalo le nekaj sekund"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:269
msgid "This template was custom tailored for you. For further customization please contact our <0>support</0>."
msgstr "Ta predloga je bila prilagojena posebej za vas. Za nadaljnje prilagoditve se obrnite na našo <0>podporo</0>."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:52
msgid "This week"
msgstr "Ta teden"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:78
msgid "This year"
msgstr "To leto"

#: src/components/monitoring/Inspector/DemographicsData/DemographicsData.js:151
msgid "Threshold"
msgstr "Relevanca (%)"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPostModal.tsx:192
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:316
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:320
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:70
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:99
#: src/components/emailing/components/EmailToolbarActions/FormSendEmail.tsx:190
msgid "Time"
msgstr "Čas"

#: src/components/forms/inspector/FormMediaEditor.js:85
#: src/components/forms/inspector/FormMediaEditor.js:88
msgid "Time must not exceed media length"
msgstr "Čas ne sme preseči dolžine medija"

#: src/components/monitoring/Inspector/InspectorMonitora/Paragraph/Paragraph.js:70
msgid "Timed"
msgstr "Časovno"

#: src/constants/analytics.js:54
#: src/constants/analytics.js:78
#: src/constants/analytics.js:100
#: src/constants/analytics.js:122
#: src/constants/analytics.js:142
#: src/constants/analytics.js:191
#: src/constants/analytics.js:226
#: src/constants/analytics.js:255
#: src/constants/analytics.js:282
#: src/constants/analytics.js:308
#: src/constants/analytics.js:335
#: src/constants/analytics.js:364
#: src/constants/analytics.js:391
#: src/constants/analytics.js:417
#: src/constants/analytics.js:444
#: src/constants/analytics.js:482
#: src/constants/analytics.js:510
#: src/constants/analytics/primeScoreCharts.ts:30
#: src/constants/analytics/primeScoreCharts.ts:56
msgid "Timeline"
msgstr "Časovnica"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:51
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:506
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:507
#: src/components/newsroom/content/posts/NewsroomPosts.js:156
#: src/components/monitoring/WorkspaceArticles/ArticleTable.js:34
#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:218
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/FormWidgetTitle/FormWidgetTitle.js:17
msgid "Title"
msgstr "Naslov"

#. js-lingui-explicit-id
#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:362
msgid "metadata.title"
msgstr "Naslov"

#: src/components/reports/Content/ReportsList/ReportsForm.js:289
#: src/components/layout/MntrFiltersBar/forms/FormFilterDate/FormFilterDate.js:85
msgid "To"
msgstr "Do"

#: src/components/settings/SettingsPasswordChange/SettingsPasswordChange.js:33
msgid "To change your password, enter your current password and then the new password."
msgstr "Za spremembo gesla vnesite trenutno geslo in nato novo geslo."

#: src/components/emailing/forms/FormSenderSettings.js:286
msgid "To ensure the successful delivery of emails from our system, it's necessary to configure your SMTP server with the following details:"
msgstr "Za zagotovitev uspešne dostave e-poštnih sporočil iz našega sistema je potrebno konfigurirati vaš SMTP strežnik z naslednjimi podatki:"

#: src/helpers/modal/withModalToggleVisibility/withModalToggleVisibility.tsx:27
msgid "To hide some tags from the list, uncheck these tags. The user can add hidden tags back to their feed again at any time if necessary."
msgstr "Če želite nekatere oznake skriti s seznama, odstranite njihovo označitev. Uporabnik lahko skrite oznake kadar koli znova doda v svoj vir, če je to potrebno."

#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:100
msgid "To hide some topics from the list, uncheck these topics. The user can add hidden topics back to their feed again at any time if necessary."
msgstr "Če želite nekatere teme skriti s seznama, odstranite njihovo označbo. Uporabnik lahko skrite teme kadar koli znova doda v svoj vir, če je to potrebno."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:496
msgid "To set up your own domain (e.g. companyname.com), please contact our team. We will be happy to help you set up your domain. We have also written a detailed guide for you."
msgstr "Za nastavitev lastne domene (npr. blog.podjetje.si) prosimo, kontaktirajte našo ekipo. Z veseljem vam bomo pomagali pri nastavitvi domene."

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:39
msgid "To view all mentions, it is necessary to activate social media monitoring."
msgstr "Za ogled vseh omemb je potrebno aktivirati spremljanje družbenih medijev."

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:19
msgid "today"
msgstr "danes"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:110
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:39
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/forms/adapters/MntrDatepickerAdapter/MntrDatepickerAdapter.js:327
msgid "Today"
msgstr "Danes"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:51
msgid "Tone and Style:"
msgstr "Ton in slog:"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:88
msgid "Tone of voice"
msgstr "Ton glasu"

#: src/constants/analytics.js:1126
msgid "Top authors"
msgstr "Najboljši avtorji"

#: src/constants/analytics.js:1228
msgid "Top hashtags"
msgstr "Najboljši hashtagi"

#: src/constants/analytics.js:1206
msgid "Top profiles"
msgstr "Najboljši profili"

#: src/constants/analytics.js:1248
msgid "Top publishers"
msgstr "Najpogostejši založniki"

#: src/constants/analytics.js:1268
msgid "Top sources"
msgstr "Najpogostejši viri"

#: src/constants/analytics/primeScoreCharts.ts:135
msgid "Top sources by overall PRIMe"
msgstr "Viri po skupnem PRIMe"

#: src/components/reports/Content/ReportsList/ReportAdvancedSettings.js:200
msgid "Top stories"
msgstr "Najboljše zgodbe"

#: src/helpers/charts/tableTemplates.js:74
#: src/components/exportList/History/HistoryTable/HistoryTable.js:57
msgid "Topic"
msgstr "Tema"

#. placeholder {0}: item.data.name
#: src/components/layout/Sidebar/modules/SidebarTopics/dndMenuItems.js:206
msgid "Topic <0>{0}</0> will be hidden."
msgstr "Tema <0>{0}</0> bo skrita."

#. placeholder {0}: item.data.name
#: src/components/topics/Content/TopicsList/TopicsItem/TopicsItemToolbar.js:147
msgid "Topic <0>{0}</0> will be removed."
msgstr "Tema <0>{0}</0> bo odstranjena."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:147
msgid "Topic Name"
msgstr "Ime teme"

#: src/pages/topics/index.js:24
#: src/components/topics/Content/TopicChangelog.js:18
#: src/components/topics/Content/TopicsList/MegalistModalFooter.js:113
#: src/components/topics/Content/TopicsList/Reports/ReportItem.js:253
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:26
#: src/components/reports/Content/ReportsList/TopicsList/TopicsList.js:80
#: src/components/notifications/ContentTopics.js:29
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:32
#: src/components/layout/Sidebar/modules/SidebarTopics/CreateFolderAddTopic.js:33
#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:133
#: src/components/layout/MntrActiveFilters/modules/TvrTopics.js:10
#: src/components/layout/MntrActiveFilters/modules/EmptyTopics.js:21
msgid "Topics"
msgstr "Teme"

#: src/components/topics/Content/TopicsHeading/TopicsHeading.js:10
msgid "Topics ({counter})"
msgstr "Teme ({counter})"

#. placeholder {0}: menuItem.topic_monitors .map((item) => { // @ts-expect-error TODO refactor topics to TS return item.label }) .join(', ')
#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:233
msgid "Topics <0>{0}</0> will be hidden."
msgstr "Teme <0>{0}</0> bodo skrite."

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:142
msgid "Topics and keywords"
msgstr "Teme in ključne besede"

#: src/components/misc/MntrMenu/modules/MntrMenuFolder.tsx:291
msgid "Topics in this folder will be displayed separately and won't be deleted."
msgstr "Teme v tej mapi bodo prikazane ločeno in ne bodo izbrisane."

#: src/components/tariff/TariffLimits/TariffLimits.js:167
#: src/components/staff/admin/workspace/Workspace.js:439
msgid "Topics limit"
msgstr "Omejitev tem"

#: src/components/monitoring/Inspector/InspectorMonitora/KeywordsPagination/KeywordsPagination.js:304
msgid "total"
msgstr "skupaj"

#: src/helpers/charts/tableTemplates.js:55
#: src/helpers/charts/tableTemplates.js:97
#: src/helpers/charts/tableTemplates.js:136
#: src/components/widgets/modules/stats/StatsBySource.js:120
#: src/components/tvr/Content/Content.js:92
msgid "Total"
msgstr "Skupaj"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:215
msgid "Total {0} interactions"
msgstr "Skupaj {0} interakcij"

#. placeholder {0}: humanizeNumber(data)
#: src/constants/analytics.js:471
msgid "Total influence score: {0}"
msgstr "Skupni vplivni rezultat: {0}"

#. placeholder {0}: formatter( this.points.reduce((sum, { y }) => sum + y, 0), unit, )
#: src/components/OurChart/HighchartsRenderer.js:645
msgid "Total: {0}"
msgstr "Skupaj: {0}"

#: src/components/emailing/content/promo/PromoEmailing.js:33
msgid "Track delivery and opening statistics."
msgstr "Spremljajte statistiko dostave in odpiranja."

#: src/components/monitoring/MonitoringPromo/MonitoringPromo.js:20
msgid "Track online, traditional and social media with {appName} for a complete view of your brand and trends - never miss a beat."
msgstr "Spremljajte spletne, tradicionalne in družbene medije z aplikacijo {appName} za popoln pregled vaše blagovne znamke in trendov - nikoli ne zamudite ničesar."

#: src/components/layout/AuthWrapper/constants/features.slides.js:166
msgid "Tracking, analysis, and reporting are an integral part of PR. Use comprehensible charts that make data analysis easier. Compare your media output with your competition."
msgstr "Sledenje, analiza in poročanje so nepogrešljiv del PR. Uporabljajte pregledne grafe, ki olajšajo analizo podatkov, in primerjajte svoje medijske objave s konkurenco."

#: src/components/topics/Content/TopicsList/Keyword/FormAddTopic.js:159
#: src/components/staff/admin/workspace/Workspace.js:353
#: src/components/layout/AuthWrapper/constants/features.slides.js:41
#: src/components/exportList/ExportLimit/ExportLimit.js:17
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:19
#: src/components/analytics/AnalyticsContent.js:146
#: src/components/analytics/AnalyticsContent.js:195
msgid "Traditional Media"
msgstr "Tradicionalni mediji"

#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/FormStatsSubtype.js:25
msgid "Traditional Media w/o percentage change"
msgstr "Tradicionalni mediji (brez odstotne spremembe)"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:533
msgid "Transcribe the source file"
msgstr "Prepisati izvorno datoteko"

#: src/components/monitoring/WorkspaceArticles/Limits.js:69
msgid "Transcribed seconds"
msgstr "Prepisanih sekund"

#: src/components/monitoring/WorkspaceArticles/Limits.js:73
msgid "Transcript"
msgstr "Prepis"

#: src/components/medialist/content/MedialistActionsBar/FormTransformContacts.tsx:41
msgid "Transform"
msgstr "Pretvori"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:53
msgid "Transform & import"
msgstr "Pretvori in uvozi"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:137
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformed.tsx:17
#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:7
msgid "Transform contact list"
msgstr "Pretvori seznam stikov"

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:134
msgid "Transformation failed"
msgstr "Pretvorba ni uspela"

#: src/helpers/withTranslatePopup/TranslatePopupContent.js:49
#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:75
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:77
#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:110
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:189
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:194
msgid "Translate"
msgstr "Prevedi"

#: src/components/layout/Sidebar/SidebarMainNav.js:210
msgid "Translations"
msgstr "Prevodi"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:573
msgid "Transparent background"
msgstr "Prosojno ozadje"

#: src/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation.js:219
msgid "Trash"
msgstr "Koš"

#: src/constants/analytics.js:1053
#: src/constants/analytics.js:1068
msgid "Treemap"
msgstr "Drevesni zemljevid"

#: src/components/notifications/Permissions.js:74
msgid "Try again"
msgstr "Poskusi znova"

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:82
#: src/components/misc/PromoBox/PromoBox.js:144
msgid "Try for free"
msgstr "Preizkusi brezplačno"

#: src/pages/user/reactivate-24.js:34
msgid "Try out Mediaboard"
msgstr "Preizkusite Mediaboard"

#: src/components/monitoring/Inspector/MentionsList/MentionsPromo.js:57
msgid "Try social media monitoring"
msgstr "Preizkusite spremljanje družbenih medijev"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:18
msgid "Turn off these notifications"
msgstr "Izklopi te obvestila"

#: src/components/notifications/AppNotifications/NotificationsList/appNotificationActions.js:27
msgid "Turn on these notifications"
msgstr "Vklopi ta obvestila"

#: src/components/notifications/ContentTvrRequest.js:41
#: src/components/notifications/ContentTvr.js:46
#: src/components/misc/ActionsBar/View/ViewMenu.js:162
#: src/components/layout/MntrFiltersBar/modules/MenuFilterChannelsTVR.js:41
#: src/components/layout/AuthWrapper/constants/features.slides.js:65
msgid "TV"
msgstr "TV"

#: src/components/emailing/forms/FormSenderSettings.js:123
msgid "TXT record"
msgstr "TXT zapis"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemFooter/modules/FormAveWeight.js:58
msgid "Type a coefficient"
msgstr "Vnesite koeficient"

#: src/components/newsroom/content/modules/CustomKeypoints.tsx:38
msgid "Type your keypoint"
msgstr "Vnesite svojo ključno točko"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:56
msgid "Type your main message"
msgstr "Vnesite glavno sporočilo"

#: src/components/emailing/forms/WizardGenerateEmail/StepDetailedInstructions.tsx:125
msgid "Type your subject or other instructions. Clearly outline the main message or information you want to convey.Provide instructions on how to structure the information, for example: use bullet points or numbered lists."
msgstr "Vnesite zadevo ali druga navodila. Jasno opredelite glavno sporočilo ali informacije, ki jih želite posredovati. Navedite navodila, kako strukturirati informacije, tako da na primer uporabite oznake ali oštevilčene sezname."

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:175
msgid "Unable to extract data from the URL."
msgstr "Ni bilo mogoče pridobiti podatkov iz URL-ja."

#: src/components/emailing/content/EmailingSettingsContent.js:29
msgid "Unable to retrieve access token from the OAuth2 provider. This may be due to a network issue or provider outage. Please try again later."
msgstr "Ni mogoče pridobiti dostopnega žetona od ponudnika OAuth2. To je lahko posledica težave v omrežju ali izpada ponudnika. Poskusite znova pozneje."

#: src/components/misc/MntrEditor/MntrEditorMenuBar.js:558
msgid "Undo"
msgstr "Razveljavi"

#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:79
msgid "Unique visits"
msgstr "Edinstveni obiski"

#: src/components/newsroom/content/dashboard/ChartVisits.js:54
#: src/components/newsroom/content/dashboard/ChartVisits.js:96
#: src/components/newsroom/components/PostsList/PostsList.js:209
msgid "Unique Visits"
msgstr "Edinstveni obiski"

#: src/components/reports/history/RecipientsTableRow.js:68
msgid "Unknown"
msgstr "Neznano"

#: src/components/reports/Content/ReportsList/FormAttachment/FormAttachment.js:194
msgid "Unlock licensed articles"
msgstr "Odkleni licencirane članke"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:148
msgid "Unpublish"
msgstr "Prekliči objavo"

#: src/components/medialist/forms/FormEditAuthor.js:577
msgid "Unsaved changes"
msgstr "Neshranjene spremembe"

#: src/components/newsroom/forms/FormNewsroomPost/FormPublishingPost.js:152
msgid "Unschedule"
msgstr "Prekliči načrtovano objavo"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:149
msgid "Unsubscribe"
msgstr "Odjava"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:47
msgid "Unsubscribe from emails"
msgstr "Odjava od e-pošte"

#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:311
#: src/components/monitoring/FeedList/FeedListItem/FeedListMenu/FeedListMenuItems.js:322
msgid "Unsubscribe news source"
msgstr "Odjava vira novic"

#: src/components/emailing/components/EmailRecipientsList/AddMissingInfo.tsx:138
msgid "Update recipient"
msgstr "Posodobi prejemnika"

#: src/components/medialist/content/MedialistDashboard.js:75
#: src/components/medialist/content/MedialistDashboard.js:108
msgid "Updated"
msgstr "Posodobljeno"

#: src/components/medialist/content/MedialistActionsBar/ContactsImportTitle.tsx:8
msgid "Upload either a manually completed template or a formatted contact list file. Once you import contacts, they will automatically appear in the Import. You can also add the contacts to one of the existing lists."
msgstr "Naložite lahko bodisi ročno izpolnjeno predlogo bodisi oblikovano datoteko s seznamom stikov. Ko uvozite stike, se bodo samodejno prikazali v Uvozu. Stike lahko dodate tudi na enega od obstoječih seznamov."

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:26
msgid "Upload File"
msgstr "Naloži datoteko"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:20
msgid "Upload Image"
msgstr "Naloži sliko"

#: src/components/medialist/content/MedialistActionsBar/withModalUploadMedialist.tsx:8
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:240
#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:241
msgid "Upload medialist"
msgstr "Naloži seznam medijev"

#: src/components/misc/MntrEditor/modals/withModalMediaUpload.js:23
msgid "Upload Video"
msgstr "Naloži video"

#: src/components/settings/SettingsLogo/SettingsLogo.js:95
msgid "Upload your company logo, which will then be displayed in email reports, exports and in the application itself, instead of the {appName} logo."
msgstr "Naložite logotip vašega podjetja, ki bo nato prikazan v e-poštnih poročilih, izvozih in v sami aplikaciji, namesto logotipa {appName}."

#: src/components/medialist/content/MedialistActionsBar/ContactsTransformTitle.tsx:8
msgid "Upload your contact list and we'll transform it to fit our medialist for you."
msgstr "Naložite svoj seznam stikov in ga bomo za vas preoblikovali, da bo ustrezal našemu medijskemu seznamu."

#: src/components/medialist/content/MedialistActionsBar/WizardImportMedialist.tsx:58
msgid "Upload your contact list, and we’ll format it to fit perfectly into our medialist for you."
msgstr "Naložite svoj seznam stikov in ga bomo za vas oblikovali tako, da se bo popolnoma prilegal našemu medijskemu seznamu."

#: src/components/medialist/content/MedialistActionsBar/MedialistUploadComponent.tsx:58
msgid "Upload your file"
msgstr "Naložite svojo datoteko"

#: src/components/misc/UploadWatcher/UploadWatcher.js:18
msgid "Uploading has not finished. Please do not refresh or close this page."
msgstr "Nalaganje še ni končano. Prosimo, ne osvežujte ali zapirajte te strani."

#: src/components/misc/UploadWatcher/UploadWatcher.js:46
msgid "Uploading: {lastProgress}%"
msgstr "Nalaganje: {lastProgress}%"

#: src/components/medialist/forms/modules/FormArray.js:131
msgid "Url"
msgstr "Url"

#: src/components/monitoring/WorkspaceArticles/ArticleItemEdit.js:190
#: src/components/ReusableFeed/FormAddArticle.tsx:31
msgid "URL"
msgstr "URL"

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:38
msgid "Use another email address"
msgstr "Uporabi drug e-poštni naslov"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:76
msgid "Use Google account as sender"
msgstr "Uporabite Google račun kot pošiljatelja"

#: src/components/emailing/forms/FormEmailingSettingsSender.tsx:82
msgid "Use Microsoft 365 account as sender"
msgstr "Uporabite račun Microsoft 365 kot pošiljatelja"

#: src/components/staff/admin/workspace/Workspace.js:369
#: src/components/staff/admin/workspace/Workspace.js:389
#: src/components/staff/admin/workspace/Workspace.js:410
#: src/components/staff/admin/workspace/Workspace.js:430
#: src/components/staff/admin/workspace/Workspace.js:449
#: src/components/staff/admin/workspace/Workspace.js:470
#: src/components/staff/admin/workspace/Workspace.js:491
#: src/components/staff/admin/workspace/Workspace.js:524
#: src/components/staff/admin/workspace/Workspace.js:550
#: src/components/staff/admin/workspace/Workspace.js:569
#: src/components/staff/admin/workspace/Workspace.js:588
#: src/components/staff/admin/workspace/Workspace.js:639
#: src/components/staff/admin/workspace/Workspace.js:660
#: src/components/staff/admin/workspace/Workspace.js:686
msgid "Used"
msgstr "Uporabljeno"

#: src/pages/staff/admin/users/[userId]/index.js:12
#: src/components/staff/admin/DailyAccess/Table.js:24
#: src/components/staff/admin/DailyAccess/Content.js:32
msgid "User"
msgstr "Uporabnik"

#: src/components/tariff/TariffLimits/TariffLimits.js:274
#: src/components/staff/admin/workspace/Workspace.js:677
msgid "User accounts limit"
msgstr "Omejitev uporabniških računov"

#: src/components/staff/admin/workspace/UsersTable/AddUsers.tsx:53
#: src/components/settings/SettingsUserManagement/withModalUpdateRole.tsx:59
msgid "User emails"
msgstr "E-poštni naslovi uporabnikov"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:102
#: src/components/layout/Sidebar/modules/SettingsNavigation/SettingsNavigation.js:67
msgid "User management"
msgstr "Upravljanje uporabnikov"

#: src/components/staff/admin/user/User.js:207
msgid "User settings"
msgstr "Uporabniške nastavitve"

#: src/components/emailing/forms/FormSenderSettings.js:89
msgid "Username"
msgstr "Uporabniško ime"

#: src/pages/staff/admin/customers/[customerId]/users.js:12
#: src/components/staff/admin/workspace/Workspace.js:858
#: src/components/staff/admin/workspace/UsersTable/UsersTable.js:69
#: src/components/staff/admin/customers/Customer.js:173
#: src/components/staff/admin/customer/workspaces/WorkspacesTable.js:77
#: src/components/staff/admin/customer/users/Users.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:34
#: src/components/forms/dashboard/Search/SearchUsers.js:36
msgid "Users"
msgstr "Uporabniki"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:27
msgid "Utilizes company profiles for more tailored content."
msgstr "Uporablja profile podjetij za bolj prilagojeno vsebino."

#: src/components/emailing/forms/FormSenderSettings.js:168
msgid "Validate"
msgstr "Preveri"

#: src/components/emailing/forms/FormSenderSettings.js:134
msgid "Value"
msgstr "Vrednost"

#: src/components/staff/admin/customer/bio/CustomerBio.js:95
#: src/components/staff/admin/customer/MergedCustomers/MergedCustomersTable.js:58
msgid "VAT"
msgstr "DDV"

#: src/components/settings/SettingsUserManagement/SettingsUserManagement.tsx:225
msgid "Verification"
msgstr "Verifikacija"

#: src/store/models/emailing/settings/EmailSettingsSendersStoreArrItem.ts:101
msgid "Verification email sent."
msgstr "Potrditveno e-poštno sporočilo je bilo poslano."

#. placeholder {0}: values.email
#: src/components/emailing/content/EmailingSettingsContent.js:93
msgid "Verification email was sent to {0}. Please check your inbox."
msgstr "Potrditveno e-poštno sporočilo je bilo poslano na {0}. Preverite svoj nabiralnik."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:26
msgid "Verify your email"
msgstr "Preverite svoj e-poštni naslov"

#: src/components/emailing/forms/FormSenderSettings.js:208
msgid "Verifying your email address with SMTP or DNS can improve email deliverability, protect against spoofing, improve sender reputation, and provide better analytics. It demonstrates legitimacy and helps email providers ensure that emails are not spam."
msgstr "Preverjanje vašega e-poštnega naslova s pomočjo SMTP ali DNS lahko izboljša dostavljivost e-pošte, zaščiti pred lažnim predstavljanjem, izboljša ugled pošiljatelja in zagotovi boljšo analitiko. Dokazuje legitimnost in pomaga ponudnikom e-poštnih storitev zagotoviti, da e-pošta ni označena kot neželena pošta."

#: src/components/monitoring/Inspector/InspectorSource/MediaView/modules/mediaDownloadMenu.js:87
#: src/components/misc/MntrEditor/extensions/ExtensionMedia.js:48
msgid "Video"
msgstr "Video"

#: src/components/newsroom/content/posts/NewsroomPosts.js:119
#: src/components/newsroom/content/posts/NewsroomPosts.js:123
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderPrint/HeaderPrint.js:23
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:32
#: src/components/misc/ActionsBar/View/ViewMenu.js:315
#: src/components/misc/ActionsBar/View/View.js:16
msgid "View"
msgstr "Pogled"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleVersions/ArticleVersions.js:84
msgid "View changes"
msgstr "Prikaži spremembe"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:44
msgid "View full article"
msgstr "Poglej celoten članek"

#: src/components/OurChart/OurChartAdvanced.js:253
msgid "View in full screen"
msgstr "Prikaži na celotnem zaslonu"

#: src/components/layout/Sidebar/modules/NewsroomNavigation/NewsroomNavigation.js:58
msgid "View Newsroom"
msgstr "Ogled Medijskega središča"

#: src/components/feed/InspectorToolbar/InspectorToolbar.js:120
msgid "View preview"
msgstr "Prikaži predogled"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
msgid "View Screenshot"
msgstr "Prikaži posnetek zaslona"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Video"
msgstr "Ogled videa"

#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:41
#: src/components/monitoring/Inspector/InspectorSource/MediaView/HeaderOnline/HeaderOnline.js:47
msgid "View Web"
msgstr "Ogled spletne strani"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleLockedInfo/ArticleLockedInfo.tsx:35
msgid "Viewing this press publication incurs an additional fee as per the Table of Fees approved by the Minister of Culture and National Heritage."
msgstr "Ogled te tiskane publikacije je dodatno zaračunan v skladu s cenikom, ki ga je odobrilo Ministrstvo za kulturo in nacionalno dediščino."

#: src/components/monitoring/FeedList/FeedListItem/SocialInteractions/SocialInteractions.js:24
#: src/components/monitoring/FeedList/FeedListItem/SocialBar/SocialBar.js:95
msgid "views"
msgstr "ogledi"

#: src/components/newsroom/content/posts/NewsroomPosts.js:163
msgid "Views"
msgstr "Ogledi"

#: src/components/misc/ActionsBar/Selector/Selector.js:34
msgid "Visible"
msgstr "Vidno"

#: src/components/newsroom/content/posts/NewsroomPosts.js:261
#: src/components/newsroom/content/dashboard/ChartVisits.js:86
#: src/components/newsroom/components/PostsList/PostsList.js:209
#: src/components/emailing/components/MentionedNewsroomPosts/MentionedNewsroomPost.tsx:74
msgid "Visits"
msgstr "Obiski"

#: src/components/newsroom/content/dashboard/ChartVisits.js:49
msgid "Visits (last 30 days / total):"
msgstr "Obiski (zadnjih 30 dni / skupaj):"

#: src/components/emailing/modules/PreviewEmail/PreviewEditor.js:107
msgid "Warning"
msgstr "Opozorilo"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:46
msgid "Warning via <0>SMS</0>, <1>email</1> or <2>notification</2>"
msgstr "Opozorilo prek <0>SMS</0>, <1>e-pošte</1> ali <2>obvestila</2>"

#: src/components/tvr/Content/TvrPromoBox/TvrPromoBox.js:57
msgid "We are the only ones in the Czech Republic to monitor <0>text mentions in the broadcast</0> for selected channels."
msgstr "Kot edini v Sloveniji spremljamo tudi <0>besedilne omembe v oddaji</0> za izbrane kanale."

#: src/pages/user/reset-password/success.tsx:8
msgid "We have sent password reset link to your email."
msgstr "Na vaš e-poštni naslov smo poslali povezavo za ponastavitev gesla."

#: src/components/emailing/content/sender/EmailingSenderVerifyContent.js:29
msgid "We have sent you an activation link. To activate Emailing and to confirm your email address please open the link."
msgstr "Poslali smo vam aktivacijsko povezavo. Za aktivacijo Emailinga in potrditev vašega e-poštnega naslova prosimo, da odprete povezavo."

#: src/components/emailing/content/EmailingSettingsContent.js:28
msgid "We haven't been granted access to send emails on your behalf. Please try again and make sure to grant us access."
msgstr "Nismo dobili dovoljenja za pošiljanje e-pošte v vašem imenu. Poskusite znova in se prepričajte, da ste nam podelili dostop."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:75
msgid "We noticed that your post lacks an introduction or perex. This section is crucial as it provides a brief overview of your post and entices readers to continue. Consider adding a short paragraph that summarizes your main points or sets the context for your post."
msgstr "Opazili smo, da vašemu prispevku manjka uvod ali perex. Ta del je ključen, saj zagotavlja kratek pregled vašega prispevka in spodbuja bralce k nadaljevanju. Razmislite o dodajanju kratkega odstavka, ki povzame vaše glavne točke ali postavi kontekst vašega prispevka."

#: src/components/emailing/forms/FormSenderSettings.js:226
msgid "We recommend that you verify your email address"
msgstr "Priporočamo, da preverite svoj e-poštni naslov"

#: src/components/page/auth/UserInactive/UserInactive.js:20
msgid "We will contact you shortly, once we setup your account."
msgstr "Ko bomo vse nastavili, vas bomo kmalu kontaktirali."

#: src/pages/404.js:24
msgid "We're sorry, but the requested page was not found. It is possible that the page was either removed or moved somewhere else. Please make sure you entered the correct URL address."
msgstr "Opravičujemo se, vendar zahtevana stran ni bila najdena. Možno je, da je bila stran odstranjena ali premaknjena drugam. Prosimo, preverite, ali ste vnesli pravilen URL naslov."

#. placeholder {0}: topics.getTopicNameById(missingArticle.topicMonitorId)
#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:170
msgid "We've added the article to the the topic \"{0}\" and adjusted its settings. The article will appear in your feed shortly."
msgstr "Članek smo dodali pod temo \"{0}\" in prilagodili njegove nastavitve. Članek se bo kmalu pojavil v vašem viru."

#: src/components/monitoring/FeedChart/MediatypePromo/MediatypePromo.js:110
msgid "We've discovered more content in the media landscape related to your topics and areas of interest."
msgstr "Odkritih je več vsebin v medijskem prostoru, povezanih z vašimi temami in področji zanimanja."

#: src/components/medialist/forms/FormEditAuthor.js:842
msgid "Website"
msgstr "Spletna stran"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:141
msgid "Website URL"
msgstr "Povezava do vaše spletne strani"

#: src/helpers/charts/makeGranularityMenu.js:18
#: src/helpers/charts/getGranularityLabel.js:6
msgid "Weeks"
msgstr "Tedni"

#: src/components/emailing/content/sender/EmailingSenderContent.js:48
msgid "What is Emailing used for?"
msgstr "Za kaj se uporablja e-pošta?"

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:250
msgid "What is Newsroom?"
msgstr "Kaj je Medijsko središče?"

#: src/components/emailing/content/sender/EmailingSenderContent.js:49
msgid "While our Emailing tool is designed to send press and PR messages to journalists, its functionality goes beyond that. You can use it for various types of communication, opening up possibilities beyond traditional media outreach."
msgstr "Naše orodje za pošiljanje e-pošte je zasnovano za pošiljanje tiskovnih in PR sporočil novinarjem, vendar njegova funkcionalnost presega to. Uporabite ga lahko za različne vrste komunikacije, kar odpira možnosti, ki presegajo tradicionalno medijsko obveščanje."

#: src/components/staff/admin/workspace/Workspace.js:799
msgid "Whitelisted domains"
msgstr "Dovoljene domene"

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:323
msgid "Widget copied to \"{0}\"."
msgstr "Gradnik je bil kopiran v \"{0}\"."

#. placeholder {0}: targetDashboard.name
#: src/store/models/dashboards/DashboardItem/DashboardItem.js:314
msgid "Widget moved to \"{0}\"."
msgstr "Gradnik je bil premaknjen na \"{0}\"."

#: src/components/dashboards/DashboardsGrid/DashboardsGridMenu.js:138
msgid "Widget will be removed"
msgstr "Gradnik bo odstranjen."

#: src/components/medialist/content/MedialistActionsBar/MedialistActionsBar.js:203
msgid "With contact"
msgstr "S kontaktom"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:36
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:70
#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:110
msgid "With inflection"
msgstr "Z upogibanjem"

#: src/components/layout/AuthWrapper/constants/features.slides.js:215
msgid "With Medialist, you don't send your media output to randomly selected journalists. You only send it to those who are most likely to publish it."
msgstr "Z Medialistom ne pošiljate svojih medijskih objav naključno izbranim novinarjem, temveč le tistim, ki bodo vaše sporočilo najverjetneje objavili."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:32
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:37
#: src/components/layout/MntrActiveFilters/modules/Note.js:11
msgid "With note"
msgstr "Z opombo"

#. placeholder {0}: values.unverified_recipients_limit
#: src/components/emailing/forms/FormSenderSettings.js:227
msgid "Without a verified email address, your emails risk being marked as spam and rejected by providers, potentially damaging your reputation. You will also be limited to sending an email to only {0} recipients at a time."
msgstr "Brez preverjenega e-poštnega naslova obstaja tveganje, da bodo vaša e-poštna sporočila označena kot vsiljena pošta in zavrnjena s strani ponudnikov, kar lahko škoduje vašemu ugledu. Prav tako boste omejeni na pošiljanje e-pošte le {0} prejemnikom naenkrat."

#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:29
msgid "Without limit"
msgstr "Brez omejitve"

#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:49
#: src/components/layout/MntrFiltersBar/modules/MenuFilterNote.js:54
#: src/components/layout/MntrActiveFilters/modules/Note.js:15
msgid "Without note"
msgstr "Brez opombe"

#: src/components/staff/admin/customers/Customers.js:26
msgid "without sending registration email"
msgstr "brez pošiljanja registracijskega e-poštnega sporočila"

#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:154
#: src/components/layout/Sidebar/modules/SidebarTags/SidebarTags.js:166
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:86
#: src/components/layout/MntrFiltersBar/modules/MenuFilterTags.js:92
#: src/components/layout/MntrActiveFilters/modules/Tags.js:46
#: src/components/dashboards/DashboardsGrid/WidgetFilterList/WidgetFilterTags.js:19
msgid "Without tags"
msgstr "Brez oznak"

#: src/pages/404.js:14
msgid "Woop woop woop woop, page not found"
msgstr "Woop woop woop woop, stran ni bila najdena"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:22
#: src/components/help/search/Content/RulesSearch.js:19
msgid "Word Search"
msgstr "Iskanje besed"

#: src/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation.js:96
#: src/components/help/search/Content/RulesDistance.js:19
msgid "Words to distance"
msgstr "Besede do razdalje"

#: src/pages/staff/admin/workspaces/[workspaceId]/index.js:12
#: src/components/staff/admin/workspace/WorkspaceChangelog.js:21
#: src/components/staff/admin/customer/expenses/GroupedExpensesTable.js:77
#: src/components/staff/admin/DailyAccess/Table.js:27
#: src/components/staff/admin/DailyAccess/Content.js:32
#: src/components/page/auth/Expired/Expired.js:53
#: src/components/layout/Header/UserMenu/UserMenu.js:55
msgid "Workspace"
msgstr "Delovni prostor"

#: src/components/layout/Header/UserMenu/UserMenu.js:169
msgid "Workspace admin"
msgstr "Skrbnik delovnega prostora"

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:55
msgid "Workspace created."
msgstr "Delovni prostor je bil ustvarjen."

#: src/components/staff/admin/workspace/WorkspaceCreate.tsx:37
msgid "Workspace name"
msgstr "Ime delovnega prostora"

#: src/components/staff/admin/workspace/Workspace.js:280
msgid "Workspace settings"
msgstr "Nastavitve delovnega prostora"

#: src/pages/staff/admin/customers/[customerId]/workspaces.js:12
#: src/components/staff/admin/user/WorkspacesTable.js:61
#: src/components/staff/admin/user/User.js:290
#: src/components/staff/admin/customers/Customer.js:150
#: src/components/staff/admin/customer/workspaces/Workspaces.js:27
#: src/components/layout/Sidebar/modules/CustomerNavigation/CustomerNavigation.js:26
#: src/components/forms/dashboard/Search/SearchWorkspaces.js:43
msgid "Workspaces"
msgstr "Delovni prostori"

#: src/components/topics/Content/TopicsList/MegalistToolbar/RankFilterPopup.tsx:65
#: src/components/layout/MntrFiltersBar/modules/MenuFilterPrime.tsx:204
msgid "worst"
msgstr "najslabše"

#: src/components/settings/SettingsLogo/SettingsLogo.js:76
msgid "Would you like to customize the appearance of the app, email reports and exports with your own logo? Contact us at <0>{salesEmail}</0>"
msgstr "Bi želeli prilagoditi videz aplikacije, e-poštnih poročil in izvozov z vašim logotipom? Kontaktirajte nas na <0>{salesEmail}</0>"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/FormCreateAIPost.tsx:61
msgid "Write the main content of your article that you want to create. The main content is considered as the primary theme or topic of your article."
msgstr "Napišite glavno vsebino svojega članka, ki ga želite ustvariti. Glavna vsebina se šteje kot primarna tema ali predmet vašega članka."

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:19
#: src/components/emailing/forms/WizardGenerateEmail/WizardGenerateEmail.tsx:20
msgid "Write with AI assistant"
msgstr "Pisanje s pomočjo AI asistenta"

#: src/components/newsroom/forms/FormNewsroomPost/WizardGeneratePost/WizardGeneratePost.tsx:39
msgid "Write without AI assistant"
msgstr "Pisanje brez AI asistenta"

#: src/components/newsroom/components/AiTools/AiGenerateTitles.tsx:79
msgid "Write your own"
msgstr "Napišite lasten naslov"

#: src/components/tariff/TariffLimits/TariffLimits.js:132
#: src/components/staff/admin/workspace/Workspace.js:420
msgid "Yearly authors export limit"
msgstr "Letna omejitev za izvoz avtorjev"

#: src/helpers/charts/makeGranularityMenu.js:34
#: src/helpers/charts/getGranularityLabel.js:12
msgid "Years"
msgstr "Leta"

#: src/components/monitoring/FeedList/FeedListItem/FeedItemMetaData/modules/metaDataDate.js:30
msgid "yesterday"
msgstr "včeraj"

#: src/store/models/monitoring/FeedMapItem/FeedMapItem.js:114
#: src/components/tvr/Inspector/InspectorMonitora/MediaDetail/MediaDetail.js:72
#: src/components/tvr/Content/TvrStories/TvrStory/TvrStory.js:52
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:100
#: src/components/layout/MntrFiltersBar/modules/MenuFilterDate.js:115
msgid "Yesterday"
msgstr "Včeraj"

#: src/components/emailing/modules/withModalRemoveRecipients.tsx:60
msgid "You are about to remove the selected recipients. However, you can keep some of them by clicking on the recipients."
msgstr "Izbrali ste odstranitev izbranih prejemnikov. Vendar lahko nekatere od njih obdržite s klikom na prejemnike."

#: src/components/emailing/content/mediaCoverage/EmptyFeedMessage.tsx:9
msgid "You can add articles to media coverage"
msgstr "Članke lahko dodate v medijsko pokritost"

#: src/components/exportList/Content/HeadingExport/HeadingExport.js:30
msgid "You can add items in Articles section."
msgstr "Elemente lahko dodate v razdelku Članki."

#: src/components/emailing/modules/CampaignFeedList/CampaignFeedList.js:88
msgid "You can create your first campaign by clicking the button below."
msgstr "Svojo prvo kampanjo lahko ustvarite s klikom na spodnji gumb."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:133
msgid "You can create your first email by clicking the button below."
msgstr "Svoje prvo e-pošto lahko ustvarite s klikom na spodnji gumb."

#: src/components/monitoring/WorkspaceArticles/Intro.js:30
msgid "You can create your own articles here. They will be added to <0>your feed only</0>."
msgstr "Tukaj lahko ustvarite svoje članke. Ti bodo dodani samo v <0>vaš vir</0>."

#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:51
msgid "You can edit recipients in email settings."
msgstr "Prejemnike lahko uredite v nastavitvah e-pošte."

#: src/components/emailing/content/CampaignDetailEmailsContent.tsx:149
#: src/components/emailing/components/RecipientsFeed/RecipientsFeedContent.js:70
msgid "You can reset your filter by clicking the button below."
msgstr "Filter lahko ponastavite s klikom na spodnji gumb."

#: src/components/medialist/content/MedialistActionsBar/ContactsImporting.tsx:20
msgid "You can safely close this window as the process will continue in the background. Once the import is complete, we will notify you. If any issues occur, you will receive a notification and an email detailing the errors."
msgstr "To okno lahko varno zaprete, saj bo proces nadaljeval v ozadju. Ko bo uvoz končan, vas bomo obvestili. Če pride do kakršnih koli težav, boste prejeli obvestilo in e-pošto s podrobnostmi o napakah."

#: src/components/misc/ActionsBar/RefineArticles/InputForm.js:91
msgid "You can use an external link to the article or {appName} link."
msgstr "Uporabite lahko zunanjo povezavo do članka ali povezavo iz aplikacije {appName}."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:244
msgid "You don't have a Newsroom yet, but you can create a new one right now."
msgstr "Trenutno nimate nobenega Medijskega središča, vendar lahko ustvarite novo prav zdaj."

#: src/components/monitoring/WorkspaceArticles/Intro.js:26
msgid "You don't have any articles yet, but you can create one right now."
msgstr "Še nimate nobenega članka, vendar ga lahko ustvarite takoj."

#: src/components/emailing/content/sender/EmailingSenderContent.js:20
msgid "You don't have Emailing set up yet. It only takes a few minutes to set it up."
msgstr "E-pošta še ni nastavljena. Nastavitev traja le nekaj minut."

#: src/components/widgets/modules/stats/StatsBySource.js:35
#: src/components/widgets/components/PermissionErrorHint/PermissionErrorHint.js:13
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewStats/PreviewStats.js:37
#: src/components/dashboards/DashboardsGrid/AddModuleDrawer/PreviewBox/PreviewAnalytics/PreviewAnalytics.js:36
msgid "You don't have permission to view"
msgstr "Nimate dovoljenja za ogled"

#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:55
msgid "You have no topics created"
msgstr "Nimate ustvarjenih nobenih tem"

#: src/components/notifications/AppNotifications/AppNotifications.js:25
#: src/components/layout/Header/AppNotifications/AppNotifications.js:173
msgid "You have not received any notifications yet."
msgstr "Še niste prejeli nobenih obvestil."

#: src/components/misc/ResendSettings/LoadResendSettings/LoadResendSettings.js:52
#: src/components/misc/ExportSettings/LoadExportSettings/LoadExportSettings.js:52
msgid "You have not saved any settings"
msgstr "Nimate shranjenih nobenih nastavitev"

#: src/components/monitoring/Inspector/InspectorMonitora/ArticleTranslate/ArticleTranslate.js:106
msgid "You have reached 30-day limit on the number of translated articles."
msgstr "Dosegli ste 30-dnevno omejitev števila prevedenih člankov."

#: src/components/tariff/TariffLimits/TariffLimits.js:59
#: src/components/exportList/ExportLimit/ExportLimit.js:19
msgid "You have reached 30-day limit. You cannot export any new articles."
msgstr "Dosegli ste 30-dnevno omejitev. Ne morete izvoziti novih člankov."

#: src/components/tariff/TariffLimits/TariffLimits.js:220
#: src/components/exportList/ExportLimit/ExportLimit.js:30
msgid "You have reached 30-day limit. You cannot export any new social media mentions."
msgstr "Dosegli ste 30-dnevno omejitev. Novih omemb na družbenih omrežjih ne morete izvoziti."

#: src/store/models/ExportStore.js:238
msgid "You have reached the 30-day limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Dosegli ste 30-dnevno omejitev števila izvoznih člankov. Izvožena datoteka ne vsebuje vseh izbranih člankov."

#: src/store/models/ExportStore.js:240
msgid "You have reached the 30-day limit on the number of exported social media mentions. Exported file doesn't contain all selected items."
msgstr "Dosegli ste 30-dnevno omejitev števila izvozov omemb na družbenih omrežjih. Izvožena datoteka ne vsebuje vseh izbranih elementov."

#: src/components/monitoring/WorkspaceArticles/Limits.js:59
msgid "You have reached the 30-day limit on the number of OCR pages."
msgstr "Dosegli ste 30-dnevno omejitev števila strani za OCR."

#: src/components/monitoring/WorkspaceArticles/Limits.js:75
msgid "You have reached the 30-day limit on the number of transcribed seconds."
msgstr "Dosegli ste 30-dnevno omejitev števila prepisanih sekund."

#: src/store/models/ExportStore.js:246
msgid "You have reached the 30-day limit on the number of translated articles. Exported file doesn't contain all the selected articles."
msgstr "Dosegli ste 30-dnevno omejitev števila prevedenih člankov. Izvožena datoteka ne vsebuje vseh izbranih člankov."

#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:92
msgid "You have reached the limit of recipients per email"
msgstr "Dosegli ste omejitev števila prejemnikov na en e-poštni naslov"

#: src/components/layout/Header/MessageLimit/MessageLimit.js:19
msgid "You have reached the limit on found articles"
msgstr "Dosegli ste omejitev za število najdenih člankov"

#: src/components/dashboards/DashboardSelector/DashboardSelectorPopup.js:104
msgid "You have reached the limit on the number of dashboards."
msgstr "Dosegli ste omejitev števila nadzornih plošč."

#: src/store/models/ExportStore.js:229
msgid "You have reached the limit on the number of exported articles. Exported file doesn't contain all the selected articles."
msgstr "Dosegli ste omejitev števila izvoznih člankov. Izvožena datoteka ne vsebuje vseh izbranih člankov."

#: src/components/layout/Sidebar/modules/NewsroomNavigation/BlogsSelector.js:79
msgid "You have reached the limit on the number of Newsrooms."
msgstr "Dosegli ste omejitev števila Medijskih središč."

#: src/store/models/emailing/emailEdit/EmailEditStore/recipients/EmailRecipientsStore/EmailRecipientsStore.js:122
#: src/components/emailing/modules/EmailingRecipientsLimitWarning/EmailingRecipientsLimitWarning.js:48
msgid "You have reached the limit on the number of recipients."
msgstr "Dosegli ste omejitev števila prejemnikov."

#: src/components/emailing/content/EmailingSettingsContent.js:22
msgid "You have successfully authorized our application to use the external service."
msgstr "Uspešno ste pooblastili našo aplikacijo za uporabo zunanje storitve."

#: src/components/topics/Content/TopicsList/FormAdvanced/AdvancedSettingsSelector.js:123
msgid "You have unsaved changes."
msgstr "Imate neshranjene spremembe."

#: src/components/tariff/TariffLimits/TariffLimits.js:23
msgid "You may not see the latest articles."
msgstr "Morda ne vidite najnovejših člankov."

#: src/components/layout/Header/MessageLimit/MessageLimit.js:13
msgid "You may not see the latest articles. We recommend that you change your keyword settings or limit your watched media in the Topics section."
msgstr "Zadnji članki se vam morda ne bodo prikazali. Priporočamo, da v razdelku Teme spremenite nastavitve ključnih besed ali omejite spremljane medije."

#: src/components/newsroom/forms/FormNewsroomSettings/FormNewsroomSettings.js:474
msgid "You will be able to edit the link later to match your own domain."
msgstr "Povezavo boste lahko uredili pozneje, da bo ustrezala vaši domeni."

#: src/components/emailing/modules/PreviewEmail/RecipientsIsEmpty.tsx:27
#: src/components/emailing/components/EmailRecipientsList/RenderAllRecipients.tsx:34
msgid "You will see your recipients here"
msgstr "Tukaj boste videli svoje prejemnike"

#: src/helpers/modal/withModalRequestFeature.tsx:40
#: src/components/misc/PromoBox/PromoBox.js:135
#: src/components/layout/Sidebar/modules/TvrNavigation/TvrNavigation.js:12
msgid "You'll be contacted by our Sales management."
msgstr "Kontaktiral vas bo naš predstavnik prodaje."

#: src/components/page/auth/UserNoWorkspace/UserNoWorkspace.js:17
msgid "Your account does not have access to any workspace."
msgstr "Vaš račun nima dostopa do nobenega delovnega prostora."

#: src/components/page/auth/Expired/Expired.js:49
msgid "Your account has expired"
msgstr "Vaš račun je potekel"

#: src/components/page/auth/UserInactive/UserInactive.js:14
msgid "Your account is being prepared"
msgstr "Vaš račun se pripravlja"

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:118
msgid "Your email <0>{0}</0> is already unsubscribed from our email list. There is nothing you need to do to stop receiving emails from {host}"
msgstr "Vaš e-poštni naslov <0>{0}</0> je že odjavljen z našega seznama e-pošte. Ni vam treba storiti ničesar, da prenehate prejemati e-pošto od {host}."

#. placeholder {0}: query.email
#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:85
msgid "Your email <0>{0}</0> successfully unsubscribed from our email list. You will no longer receive emails from us."
msgstr "Vaš e-poštni naslov <0>{0}</0> je bil uspešno odjavljen z našega seznama e-pošte. Od nas ne boste več prejemali e-pošte."

#: src/components/page/auth/ActivateEmailingSender/ActivateEmailingSender.js:65
msgid "Your email has been verified. Now you can fully enjoy our platform."
msgstr "Vaš e-poštni naslov je bil preverjen. Zdaj lahko v celoti uživate na naši platformi."

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:76
msgid "Your email successfully unsubscribed"
msgstr "Vaš e-poštni naslov je bil uspešno odjavljen"

#: src/pages/emailing/unsubscribe/[token]/[recipientId]/[email]/index.tsx:109
msgid "Your email was already unsubscribed"
msgstr "Vaš e-poštni naslov je že bil odjavljen"

#: src/components/emailing/content/EmailingSettingsContent.js:71
#: src/components/emailing/content/EmailingCampaignsContent.tsx:28
msgid "Your Emailing is not fully set up and verified"
msgstr "Vaše pošiljanje e-pošte še ni povsem nastavljeno in preverjeno"

#: src/components/emailing/content/EmailingSettingsContent.js:74
msgid "Your Emailing is not fully set up and verified. This can decrease the trust level and deliverability. You can fully set up and verify your Emailing in the settings. If you need help, please contact our support."
msgstr "Vaš e-poštni sistem še ni popolnoma nastavljen in preverjen. To lahko zmanjša raven zaupanja in dostavljivost. E-poštni sistem lahko popolnoma nastavite in preverite v nastavitvah. Če potrebujete pomoč, se obrnite na našo podporo."

#: src/components/misc/MntrEditor/modals/withModalEditSignature.tsx:96
msgid "Your HTML code"
msgstr "Vaša HTML koda"

#: src/components/layout/Header/MessageDirty/MessageDirty.js:10
msgid "Your news feed is being updated"
msgstr "Vaš vir novic se posodablja"

#: src/store/models/account/user/UserStore.js:247
msgid "Your password has been changed successfully."
msgstr "Vaše geslo je bilo uspešno spremenjeno."

#: src/store/models/newsroom/blogs/posts/postAiTools/PostAiCheckStore.ts:66
msgid "Your post is missing a title. A clear, concise title helps readers understand what your post is about at a glance. Please add a title that accurately represents your content."
msgstr "Vašemu prispevku manjka naslov. Jasen in jedrnat naslov pomaga bralcem na prvi pogled razumeti, o čem je vaš prispevek. Prosimo, dodajte naslov, ki natančno predstavlja vašo vsebino."
