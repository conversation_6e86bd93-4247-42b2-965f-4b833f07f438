'use server'

import { get } from '~/app/lib/request'

export async function getInit() {
  const req = await get('/init/')
  const res = await fetch(req)

  if (!res.ok) {
    if (res.status === 401) {
      // Unauthorized
      return {
        ...(await res.json()),
        isAuth: false,
      }
    }

    throw new Error('Failed to fetch data')
  }

  return {
    ...(await res.json()),
    isAuth: true,
  }
}
