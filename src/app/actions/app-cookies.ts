'use server'

import { read<PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON>, write<PERSON><PERSON>ie } from '~/app/lib/cookies'
import { COOKIE_THEME_KEY } from '~/constants/themes'

const COOKIE_AFTER_IMPERSONATE_URL_KEY = 'monitora-after-unimpersonate-route'
const COOKIE_LANG_KEY = 'monitora-language'
const COOKIE_TOKEN_KEY = 'monitora-token'

export async function getThemeCookie(): Promise<string | null> {
  return (await readCookie(COOKIE_THEME_KEY)) || null
}

export async function setThemeCookie(isDark: string): Promise<{
  success: boolean
  message?: string
  theme?: string
}> {
  try {
    await writeCookie(COOKIE_THEME_KEY, isDark)
    const themeName = isDark === 'true' ? 'dark' : 'light'
    return {
      success: true,
      message: `Theme changed to ${themeName} mode`,
      theme: themeName
    }
  } catch (error) {
    console.error('Failed to set theme cookie:', error)
    return {
      success: false,
      message: 'Failed to save theme preference. Please try again.'
    }
  }
}

export async function getLanguageCookie(): Promise<string | null> {
  return (await readCookie(COOKIE_LANG_KEY)) || null
}

export async function setLanguageCookie(language: string): Promise<{
  success: boolean
  message?: string
  language?: string
}> {
  try {
    await writeCookie(COOKIE_LANG_KEY, language)
    return {
      success: true,
      message: `Language changed to ${language}`,
      language
    }
  } catch (error) {
    console.error('Failed to set language cookie:', error)
    return {
      success: false,
      message: 'Failed to save language preference. Please try again.'
    }
  }
}

export async function getAuthTokens(): Promise<{ token?: string; adminToken?: string }> {
  const tokenCookie = await readCookie(COOKIE_TOKEN_KEY)

  if (!tokenCookie) {
    return {}
  }

  const [token, adminToken] = tokenCookie.split(',')
  return { token, adminToken }
}

export async function setAuthToken(token: string): Promise<{
  success: boolean
  message?: string
}> {
  try {
    await writeCookie(COOKIE_TOKEN_KEY, token)
    return {
      success: true,
      message: 'Authentication token saved successfully'
    }
  } catch (error) {
    console.error('Failed to set auth token:', error)
    return {
      success: false,
      message: 'Failed to save authentication token. Please try again.'
    }
  }
}

export async function clearAuthToken(): Promise<{
  success: boolean
  message?: string
}> {
  try {
    await removeCookie(COOKIE_TOKEN_KEY)
    return {
      success: true,
      message: 'Successfully signed out'
    }
  } catch (error) {
    console.error('Failed to clear auth token:', error)
    return {
      success: false,
      message: 'Failed to clear authentication. Please try again.'
    }
  }
}

export async function getAfterUnimpersonateRoute(): Promise<string> {
  return (await readCookie(COOKIE_AFTER_IMPERSONATE_URL_KEY)) || '/'
}

export async function setAfterUnimpersonateRoute(route: string): Promise<{ success: boolean }> {
  try {
    await writeCookie(COOKIE_AFTER_IMPERSONATE_URL_KEY, route)
    return { success: true }
  } catch (error) {
    console.error('Failed to set after unimpersonate route:', error)
    return { success: false }
  }
}

export async function clearAfterUnimpersonateRoute(): Promise<{ success: boolean }> {
  try {
    await removeCookie(COOKIE_AFTER_IMPERSONATE_URL_KEY)
    return { success: true }
  } catch (error) {
    console.error('Failed to clear after unimpersonate route:', error)
    return { success: false }
  }
}
