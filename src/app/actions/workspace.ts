'use server'

import { redirect } from 'next/navigation'
import { post } from '~/app/lib/request'

export async function switchWorkspace(workspace_uuid: string) {
  const req = await post('/workspaces/switch/', {
    workspace_uuid: workspace_uuid,
  })
  const res = await fetch(req)

  if (!res.ok) {
    const error = await res.text()
    throw new Error(`Failed to switch workspace: ${res.status} - ${error}`)
  }

  redirect('/')
}
