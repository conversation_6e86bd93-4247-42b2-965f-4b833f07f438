'use server'

import { redirect } from 'next/navigation'
import {
  clearAfterUnimpersonateRoute,
  getAfterUnimpersonateRoute,
  getAuthTokens,
  setAuthToken,
} from '~/app/actions/app-cookies'
import { patch } from '~/app/lib/request'

export async function updateUser(userData: Record<string, unknown>) {
  const req = await patch('/accounts/user/', userData)
  const res = await fetch(req)

  if (!res.ok) {
    const error = await res.text()
    throw new Error(`Failed to update user: ${res.status} - ${error}`)
  }

  return res.json()
}

export async function isImpersonating() {
  const { adminToken } = await getAuthTokens()
  return !!adminToken
}

export async function unimpersonate() {
  const { adminToken } = await getAuthTokens()

  if (!adminToken) {
    throw new Error('No admin token found for unimpersonation')
  }

  const afterUnimpersonateRoute = await getAfterUnimpersonateRoute()

  await setAuthToken(adminToken)
  await clearAfterUnimpersonateRoute()

  redirect(afterUnimpersonateRoute)
}
