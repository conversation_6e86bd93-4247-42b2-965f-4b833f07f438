import type { Metadata } from 'next'
import { headers } from 'next/headers'

import { getLang } from '~/app/actions/i18n'
import { Body } from '~/app/components/document'
import Notifications from '~/app/components/notifications'
import { allMessages } from '~/app/lib/app-router-i18n'
import { DeviceInfoProvider } from '~/app/lib/device-info-provider'
import { getDeviceInfo } from '~/app/lib/get-device-info'
import { getSessionAndSettings } from '~/app/lib/get-session-settings'
import { getTheme } from '~/app/lib/get-theme'
import { I18nProvider } from '~/app/lib/i18n-provider'
import { initLingui } from '~/app/lib/init-lingui'
import { NotificationProvider } from '~/app/lib/notification-provider'
import StyledComponentsRegistry from '~/app/lib/styled-components-registry'
import { ThemeProvider } from '~/app/lib/theme-provider'
import '~/styles/material-symbols.css'

export async function generateMetadata(): Promise<Metadata> {
  const { appSettings, requestIp, sessionInfo } = await getSessionAndSettings()
  const { appDomain, appName, og, gtmProductId, twitter, sitePath } = appSettings
  const { primaryApp } = sessionInfo
  const headersList = await headers()
  const asPath = headersList.get('x-mediaboard-as-path')

  return {
    title: {
      template: `%s — ${appName}`,
      default: appName,
    },
    other: {
      charset: 'utf-8',
      google: 'notranslate',
      'mntr:product_id': gtmProductId,
      'mntr:request_ip': requestIp,
      'mntr:primary_app': primaryApp,
      // PWA meta tags
      'apple-mobile-web-app-title': appName,
      'application-name': appName,
    },
    manifest: `/static/${sitePath}/app.webmanifest`,
    icons: {
      icon: `/static/${sitePath}/favicon.ico`,
    },
    openGraph: {
      // `og:title` and `og:description` are automatically added from `title`
      // and `description` by Next.js
      type: 'website',
      images: [
        {
          url: `https://${appDomain}${og?.image}`,
        },
      ],
      url: `https://${appDomain}${asPath ?? ''}`,
      locale: og?.locale,
    },
    twitter: twitter
      ? {
          card: 'summary',
          site: twitter.site,
        }
      : undefined,
    robots: {
      index: false,
      follow: false,
    },
  }
}

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const lang = await getLang()
  initLingui(lang)
  const theme = await getTheme()
  const deviceInfo = await getDeviceInfo()

  return (
    <I18nProvider locale={lang} messages={allMessages[lang]}>
      <StyledComponentsRegistry>
        <ThemeProvider theme={theme}>
          <DeviceInfoProvider deviceInfo={deviceInfo}>
            <NotificationProvider>
              <html lang={lang}>
                <Body>
                  <Notifications />
                  {children}
                  <div id="portal-overlay"></div>
                </Body>
              </html>
            </NotificationProvider>
          </DeviceInfoProvider>
        </ThemeProvider>
      </StyledComponentsRegistry>
    </I18nProvider>
  )
}
