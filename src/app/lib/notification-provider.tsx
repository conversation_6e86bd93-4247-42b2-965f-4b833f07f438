'use client'

import { useEffect, useState } from 'react'

// Access the global MobX store that's available in development
declare global {
  interface Window {
    store?: {
      appStore: {
        notification: {
          add: (message: string, type: string, autoHideDuration?: number) => void
          list: any[]
        }
      }
    }
  }
}

export function useNotifications() {
  const [store, setStore] = useState<typeof window.store | null>(null)

  useEffect(() => {
    // Wait for the store to be available
    const checkStore = () => {
      if (typeof window !== 'undefined' && window.store) {
        setStore(window.store)
      } else {
        // Retry after a short delay
        setTimeout(checkStore, 100)
      }
    }

    checkStore()
  }, [])

  const add = (message: string, type: 'info' | 'success' | 'error', autoHideDuration: number = 4000) => {
    if (store?.appStore?.notification) {
      store.appStore.notification.add(message, type, autoHideDuration)
    } else {
      console.warn('MobX store not available, notification not added:', { message, type })
    }
  }

  return {
    add,
    notifications: store?.appStore?.notification?.list || []
  }
}


