'use client'

import { nanoid } from 'nanoid'
import React, { createContext, useCallback, useContext, useState } from 'react'

export interface IAppRouterNotificationItem {
  id: string
  message: string
  type: 'info' | 'success' | 'error'
  autoHideDuration: number
  isVisible: boolean
  remove: () => void
  setVisible: (visible: boolean) => void
}

interface NotificationContextType {
  notifications: IAppRouterNotificationItem[]
  add: (message: string, type: 'info' | 'success' | 'error', autoHideDuration?: number) => void
}

const NotificationContext = createContext<NotificationContextType | null>(null)

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<IAppRouterNotificationItem[]>([])

  const add = useCallback(
    (message: string, type: 'info' | 'success' | 'error', autoHideDuration: number = 4000) => {
      const hasVisibleDuplicate = notifications.some(
        (item) => item.message === message && item.type === type && item.isVisible,
      )

      if (!hasVisibleDuplicate) {
        const id = nanoid()

        const newNotification: IAppRouterNotificationItem = {
          id,
          message,
          type,
          autoHideDuration,
          isVisible: true,
          remove: () => {
            setNotifications((prev) => prev.filter((item) => item.id !== id))
          },
          setVisible: (visible: boolean) => {
            setNotifications((prev) =>
              prev.map((item) => (item.id === id ? { ...item, isVisible: visible } : item)),
            )
          },
        }

        setNotifications((prev) => [...prev, newNotification])
      }
    },
    [notifications],
  )

  return (
    <NotificationContext.Provider value={{ notifications, add }}>
      {children}
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}
