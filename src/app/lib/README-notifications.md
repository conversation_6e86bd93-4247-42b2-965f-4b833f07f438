# App Router Notification System

This notification system provides a way to show notifications in App Router pages, separate from the Pages Router MobX-based system.

## Architecture

- **`NotificationProvider`**: React Context provider that manages notification state
- **`AppRouterNotifications`**: Client component that renders notifications
- **`useNotification`**: Hook for adding notifications in client components
- **`useNotifications`**: Hook for accessing full notification context

## Setup

The notification system is already set up in `src/app/layout.tsx`:

```tsx
<NotificationProvider>
  <AppRouterNotifications />
  {children}
</NotificationProvider>
```

## Usage

### In Client Components

```tsx
'use client'

import { useNotification } from '~/app/lib/use-notification'

export default function MyComponent() {
  const addNotification = useNotification()
  
  const handleSuccess = () => {
    addNotification('Operation completed successfully!', 'success')
  }
  
  const handleError = () => {
    addNotification('Something went wrong!', 'error')
  }
  
  const handleInfo = () => {
    addNotification('Here is some information', 'info')
  }
  
  return (
    <div>
      <button onClick={handleSuccess}>Success</button>
      <button onClick={handleError}>Error</button>
      <button onClick={handleInfo}>Info</button>
    </div>
  )
}
```

### API

```tsx
addNotification(message: string, type: 'info' | 'success' | 'error', autoHideDuration?: number)
```

- **`message`**: The notification text
- **`type`**: Notification type (affects styling)
- **`autoHideDuration`**: How long to show notification (default: 4000ms)

### Features

- ✅ **Auto-hide**: Notifications disappear after specified duration
- ✅ **Duplicate prevention**: Won't show identical notifications simultaneously
- ✅ **Multiple types**: Success (green), Error (red), Info (blue)
- ✅ **Manual close**: Users can close notifications manually
- ✅ **Consistent styling**: Uses same components as Pages Router

### Example Use Cases

```tsx
// Form submission
const handleSubmit = async (data) => {
  try {
    await submitForm(data)
    addNotification('Form submitted successfully!', 'success')
  } catch (error) {
    addNotification('Failed to submit form', 'error')
  }
}

// Data loading
const handleLoad = async () => {
  try {
    addNotification('Loading data...', 'info', 2000)
    const data = await loadData()
    addNotification('Data loaded successfully!', 'success')
  } catch (error) {
    addNotification('Failed to load data', 'error')
  }
}
```

## Testing

Visit `/testing-route` to see the notification system in action with interactive examples.
