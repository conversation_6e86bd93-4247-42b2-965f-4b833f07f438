import { usePathname } from 'next/navigation'
import { useMemo } from 'react'

// TODO: refactor `activeMainNav` to use this, keys can be delimited (e.g. 'monitoring:feed')
// TODO: refactor `activePath` in emailing and newsroom to use this
// TODO: refactor `pathname === ...` testing logic to use this
const activePaths: [string, (pathname: string) => boolean][] = [
  ['admin', (pathname) => pathname.startsWith('/staff')],
  ['analytics', (pathname) => pathname.startsWith('/analytics')],
  ['articles', (pathname) => pathname === '/'],
  ['authors', (pathname) => pathname === '/authors'],
  ['brand-tracking', (pathname) => pathname.startsWith('/brand-tracking')],
  ['dashboard', (pathname) => pathname.startsWith('/dashboard')],
  ['export', (pathname) => pathname.startsWith('/export')],
  ['external-analytics', (pathname) => pathname.startsWith('/external-analytics')],
  ['medialist-author-create', (pathname) => pathname === '/authors/create'],
  ['medialist', (pathname) => pathname.startsWith('/author/')],
  ['reports', (pathname) => pathname.startsWith('/reports')],
  ['topics', (pathname) => pathname.startsWith('/topics')],
  ['trash', (pathname) => pathname.startsWith('/trash')],
  ['tvr', (pathname) => pathname.startsWith('/crisis-communication')],
  ['workspace-articles', (pathname) => pathname.startsWith('/workspace-articles')],
]

export function useActivePath() {
  const pathname = usePathname()

  return useMemo(() => {
    if (!pathname) return ''

    return activePaths.find(([, test]) => test(pathname))?.[0] || ''
  }, [pathname])
}
