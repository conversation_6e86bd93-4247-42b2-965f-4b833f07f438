'use client'

import { useContext } from 'react'
import { useMedia } from 'react-use'
import { DeviceInfoContext } from './device-info-provider'

export function useBreakpoint() {
  const deviceInfo = useContext(DeviceInfoContext)

  // Follows src/store/models/Viewport.ts isMobile and isTablet logic
  const isMobileDevice = deviceInfo?.deviceType ? deviceInfo.deviceType === 'mobile' : false
  const isTabletDevice =
    isMobileDevice || (deviceInfo?.deviceType ? deviceInfo.deviceType === 'tablet' : false)

  const isMobile = useMedia('(max-width: 640px)', isMobileDevice)
  const isTablet = useMedia('(max-width: 1025px)', isTabletDevice)

  if (typeof window === 'undefined') {
    return {
      isMobile: isMobileDevice,
      isTablet: isTabletDevice,
    }
  }

  return {
    isMobile,
    isTablet,
  }
}
