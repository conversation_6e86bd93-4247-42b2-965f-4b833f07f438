import type { DefaultTheme } from 'styled-components'
import { getThemeCookie } from '~/app/actions/app-cookies'
import { DEFAULT_PRIMARY_COLOR, THEME_VARIANT } from '~/constants/themes'
import ciEquals from '~/helpers/ciEquals'
import customTheme from '~/styles/theme/customTheme'
import darkTheme from '~/styles/theme/darkTheme'
import defaultTheme from '~/styles/theme/defaultTheme'
import defaultThemeWithLogo from '~/styles/theme/defaultThemeWithLogo'

interface ICustomThemeProps {
  primary_color: string
  variant: string
  visible_header: boolean
}

export async function getTheme(
  logoImageUrl?: string,
  themeSettings?: ICustomThemeProps,
): Promise<DefaultTheme> {
  const { variant, primary_color, visible_header } = themeSettings || {}
  const darkModeCookie = await getThemeCookie()
  const isDarkMode = darkModeCookie === 'true'

  if (
    themeSettings &&
    (variant === THEME_VARIANT.custom ||
      (primary_color && !ciEquals(primary_color, DEFAULT_PRIMARY_COLOR)) ||
      isDarkMode)
  ) {
    return customTheme(primary_color || DEFAULT_PRIMARY_COLOR, isDarkMode, visible_header || false)
  }

  if (logoImageUrl) {
    return defaultThemeWithLogo as DefaultTheme
  }

  return isDarkMode ? (darkTheme as DefaultTheme) : (defaultTheme as DefaultTheme)
}
