'use client'

import isPropValid from '@emotion/is-prop-valid'
import color from 'color'
import { useEffect } from 'react'
import type { DefaultTheme } from 'styled-components'
import { ThemeProvider as StyledThemeProvider, StyleSheetManager } from 'styled-components'
import GlobalStyle from '~/styles/global-styles'

export function ThemeProvider({
  theme,
  children,
}: {
  theme: DefaultTheme
  children: React.ReactNode
}) {
  useEffect(() => {
    // This side effect sends the theme primary color to our mobile app's
    // WebView. It's only called when a theme change occurs and when the user is
    // logged in and the header is visible.
    //
    // TODO: implement logged in logic, move unsetting (important for logged out
    // state) from cleanup.
    window.ReactNativeWebView?.postMessage(
      JSON.stringify(['MNTR_THEME', { primary_color: color(theme.colors.primary).hex() }]),
    )

    return () => {
      window.ReactNativeWebView?.postMessage(
        JSON.stringify(['MNTR_THEME', { primary_color: 'none' }]),
      )
    }
  }, [theme])

  return (
    <StyleSheetManager
      shouldForwardProp={(propName, elementToBeRendered) => {
        return typeof elementToBeRendered === 'string' ? isPropValid(propName) : true
      }}
    >
      <StyledThemeProvider theme={theme}>
        <GlobalStyle />
        {children}
      </StyledThemeProvider>
    </StyleSheetManager>
  )
}
