'use client'

import { useNotifications } from './notification-provider'

/**
 * Hook to add notifications in App Router pages
 * Uses the same MobX store as the Pages Router for unified notifications
 *
 * Usage:
 * ```tsx
 * 'use client'
 *
 * import { useNotification } from '~/app/lib/use-notification'
 *
 * export default function MyPage() {
 *   const addNotification = useNotification()
 *
 *   const handleClick = () => {
 *     addNotification('Operation completed!', 'success')
 *   }
 *
 *   return <button onClick={handleClick}>Do Something</button>
 * }
 * ```
 */
export function useNotification() {
  const { add } = useNotifications()
  return add
}
