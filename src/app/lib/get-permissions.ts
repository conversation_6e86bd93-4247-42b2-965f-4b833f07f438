import { IAppPermissionStoreArrItem } from '~/store/models/account/enums/workspaces/AppPermissionStoreArrItem'
import PermissionsStore, {
  IPermissionsStore,
} from '~/store/models/account/workspace/permissions/PermissionsStore'

interface RawPermissions {
  [key: string]: number
}

export function getPermissions(
  rawPermissions: RawPermissions,
  appPermissionEnums: IAppPermissionStoreArrItem[],
): IPermissionsStore {
  const permissionKeys = Object.keys(PermissionsStore.properties) as string[]

  return permissionKeys.reduce((acc, permissionKey) => {
    const permissionId = rawPermissions[permissionKey]

    return {
      ...acc,
      [permissionKey]: appPermissionEnums.find((perm) => perm.id === permissionId),
    }
  }, {} as IPermissionsStore)
}
