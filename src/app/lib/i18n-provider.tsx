'use client'

import { type Messages, setupI18n } from '@lingui/core'
import { I18nProvider as LinguiI18nProvider } from '@lingui/react'
import { useEffect, useState } from 'react'

export function I18nProvider({
  children,
  locale,
  messages,
}: {
  children: React.ReactNode
  locale: string
  messages: Messages
}) {
  const [i18n, setI18n] = useState(() => {
    return setupI18n({
      locale: locale,
      messages: { [locale]: messages },
    })
  })

  useEffect(() => {
    const newI18n = setupI18n({
      locale: locale,
      messages: { [locale]: messages },
    })
    setI18n(newI18n)
  }, [locale, messages])

  return <LinguiI18nProvider i18n={i18n}>{children}</LinguiI18nProvider>
}
