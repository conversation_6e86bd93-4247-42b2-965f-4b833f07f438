import { cookies, headers } from 'next/headers'
import { getInit } from '~/app/actions/init'
import getAppSettings from '~/helpers/getAppSettings'
import getRequestIp from '~/helpers/getRequestIp'
import getSessionInfo from '~/helpers/getSessionInfo'
import { IAppSettings } from '~/store/models/appSettings/AppSettings'

export async function getSessionAndSettings() {
  const headersList = await headers()
  const cookieStore = await cookies()

  const ctx = {
    cookies: cookieStore,
    req: {
      headers: Object.fromEntries(headersList.entries()),
      hostname: headersList.get('host'),
    },
    query: Object.fromEntries(
      new URLSearchParams(headersList.get('x-mediaboard-as-path')?.split('?')[1] ?? '').entries(),
    ),
  }

  const requestIp = await getRequestIp(ctx.req)
  const sessionInfo = await getSessionInfo(requestIp, ctx)
  const { workspace } = await getInit()

  const appSettings = (await getAppSettings(
    workspace
      ? {
          primaryApp: workspace.primary_app,
          country: workspace.phone_number_region,
        }
      : sessionInfo,
    ctx,
  )) as IAppSettings

  return {
    appSettings,
    requestIp,
    sessionInfo,
  }
}
