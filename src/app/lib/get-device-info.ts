import { headers } from 'next/headers'
import { userAgent } from 'next/server'

export interface DeviceInfo {
  browserName: string | undefined
  browserVersion: string | undefined
  deviceType: string | undefined
  isBot: boolean
  osName: string | undefined
  osVersion: string | undefined
  userAgent: string
}

export async function getDeviceInfo(): Promise<DeviceInfo> {
  const headersList = await headers()
  const ua = userAgent({ headers: headersList })

  return {
    browserName: ua.browser.name,
    browserVersion: ua.browser.version,
    deviceType: ua.device.type,
    isBot: ua.isBot,
    osName: ua.os.name,
    osVersion: ua.os.version,
    userAgent: ua.ua,
  }
}
