import { ResponseCookie } from 'next/dist/compiled/@edge-runtime/cookies'
import { cookies } from 'next/headers'

export async function readCookie(name: string) {
  const cookieStore = await cookies()
  const cookie = cookieStore.get(name)
  return cookie?.value
}

export async function writeCookie(name: string, value: string, options?: Partial<ResponseCookie>) {
  const cookieStore = await cookies()
  cookieStore.set(name, value, options)
}

export async function removeCookie(name: string) {
  const cookieStore = await cookies()
  cookieStore.delete(name)
}

export async function hasCookie(name: string) {
  const cookieStore = await cookies()
  return cookieStore.has(name)
}
