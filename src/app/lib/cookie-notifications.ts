/**
 * Utility functions for handling cookie operations with notifications
 * These functions wrap the enhanced app-cookies.ts server actions
 * and provide easy notification integration for App Router components
 */

import { setThemeCookie, setLanguageCookie, setAuthToken, clearAuthToken } from '~/app/actions/app-cookies'

export interface NotificationCallback {
  (message: string, type: 'success' | 'error' | 'info'): void
}

/**
 * Set theme cookie with automatic notifications
 */
export async function setThemeWithNotification(
  isDark: boolean,
  onNotification: NotificationCallback
): Promise<boolean> {
  try {
    onNotification('Changing theme...', 'info')
    
    const result = await setThemeCookie(isDark ? 'true' : 'false')
    
    if (result.success) {
      onNotification(result.message || 'Theme changed successfully', 'success')
      return true
    } else {
      onNotification(result.message || 'Failed to change theme', 'error')
      return false
    }
  } catch (error) {
    console.error('Theme change error:', error)
    onNotification('An unexpected error occurred while changing theme', 'error')
    return false
  }
}

/**
 * Set language cookie with automatic notifications
 */
export async function setLanguageWithNotification(
  language: string,
  onNotification: NotificationCallback
): Promise<boolean> {
  try {
    onNotification('Changing language...', 'info')
    
    const result = await setLanguageCookie(language)
    
    if (result.success) {
      onNotification(result.message || 'Language changed successfully', 'success')
      return true
    } else {
      onNotification(result.message || 'Failed to change language', 'error')
      return false
    }
  } catch (error) {
    console.error('Language change error:', error)
    onNotification('An unexpected error occurred while changing language', 'error')
    return false
  }
}

/**
 * Set auth token with automatic notifications
 */
export async function setAuthTokenWithNotification(
  token: string,
  onNotification: NotificationCallback
): Promise<boolean> {
  try {
    onNotification('Saving authentication...', 'info')
    
    const result = await setAuthToken(token)
    
    if (result.success) {
      onNotification('Authentication saved successfully', 'success')
      return true
    } else {
      onNotification('Failed to save authentication', 'error')
      return false
    }
  } catch (error) {
    console.error('Auth token save error:', error)
    onNotification('An unexpected error occurred while saving authentication', 'error')
    return false
  }
}

/**
 * Clear auth token with automatic notifications
 */
export async function clearAuthTokenWithNotification(
  onNotification: NotificationCallback
): Promise<boolean> {
  try {
    onNotification('Signing out...', 'info')
    
    const result = await clearAuthToken()
    
    if (result.success) {
      onNotification('Signed out successfully', 'success')
      return true
    } else {
      onNotification('Failed to sign out completely', 'error')
      return false
    }
  } catch (error) {
    console.error('Auth token clear error:', error)
    onNotification('An unexpected error occurred while signing out', 'error')
    return false
  }
}

/**
 * Example usage in a component:
 * 
 * ```tsx
 * 'use client'
 * 
 * import { setThemeWithNotification } from '~/app/lib/cookie-notifications'
 * 
 * export default function MyComponent() {
 *   const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
 *     // Your notification implementation
 *     console.log(`${type.toUpperCase()}: ${message}`)
 *   }
 * 
 *   const handleThemeChange = async () => {
 *     const success = await setThemeWithNotification(true, showNotification)
 *     if (success) {
 *       // Theme changed successfully
 *     }
 *   }
 * 
 *   return <button onClick={handleThemeChange}>Switch to Dark Theme</button>
 * }
 * ```
 */
