import 'server-only'

import { getAuthTokens } from '~/app/actions/app-cookies'

interface RequestOptions extends RequestInit {
  headers?: Record<string, string>
}

async function request(endpoint: string, options?: RequestOptions): Promise<Request> {
  const { token } = await getAuthTokens()

  return new Request(`${process.env.API_URL}${endpoint}`, {
    ...options,
    headers: {
      ...options?.headers,
      Authorization: `Token ${token}`,
    },
  })
}

export async function get(endpoint: string, options?: RequestOptions): Promise<Request> {
  return request(endpoint, {
    ...options,
  })
}

export async function post(endpoint: string, body: Record<string, unknown> = {}): Promise<Request> {
  return request(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  })
}

export async function patch(
  endpoint: string,
  body: Record<string, unknown> = {},
): Promise<Request> {
  return request(endpoint, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  })
}
