import { useLingui as getLingui } from '@lingui/react/macro'
import type { Metadata } from 'next'

import { getLang } from '~/app/actions/i18n'
import { Content, Sidebar } from '~/app/components/sidebar-layout'
import { getSessionAndSettings } from '~/app/lib/get-session-settings'
import { initLingui } from '~/app/lib/init-lingui'
import HelpNavigation from '~/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation'
import HelpContent from './content'

export async function generateMetadata(): Promise<Metadata> {
  const lang = await getLang()
  initLingui(lang)
  const { t } = getLingui()

  return {
    title: t`Search help`,
  }
}

export default async function HelpPage() {
  const { appSettings } = await getSessionAndSettings()
  const { appName } = appSettings

  return (
    <>
      <Sidebar>
        <HelpNavigation />
      </Sidebar>
      <Content>
        <HelpContent appName={appName} />
      </Content>
    </>
  )
}
