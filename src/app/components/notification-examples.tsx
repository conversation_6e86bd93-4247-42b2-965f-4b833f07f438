'use client'

import { useNotification } from '~/app/lib/use-notification'

/**
 * Example component showing how to use notifications in App Router
 * Uses the same MobX notification store as Pages Router for unified notifications
 * This can be imported and used in any App Router page
 */
export function NotificationExamples() {
  const addNotification = useNotification()

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px', margin: '20px 0' }}>
      <h3>Unified Notification System</h3>
      <p>These notifications use the same MobX store as the Pages Router:</p>
      
      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginTop: '10px' }}>
        <button 
          onClick={() => addNotification('Form saved successfully!', 'success')}
          style={{ padding: '8px 16px', backgroundColor: '#4CAF50', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
        >
          Success
        </button>
        
        <button 
          onClick={() => addNotification('Something went wrong!', 'error')}
          style={{ padding: '8px 16px', backgroundColor: '#f44336', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
        >
          Error
        </button>
        
        <button 
          onClick={() => addNotification('Here is some information', 'info')}
          style={{ padding: '8px 16px', backgroundColor: '#2196F3', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
        >
          Info
        </button>
        
        <button 
          onClick={() => addNotification('Duplicate test', 'info')}
          style={{ padding: '8px 16px', backgroundColor: '#FF9800', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
        >
          Test Duplicate Prevention
        </button>
      </div>
      
      <div style={{ marginTop: '15px', fontSize: '14px', color: '#666' }}>
        <strong>✅ Unified System Benefits:</strong>
        <ul style={{ marginTop: '5px', paddingLeft: '20px' }}>
          <li>Same notification store for both Pages Router and App Router</li>
          <li>No duplicate notifications between routing systems</li>
          <li>Consistent behavior and styling</li>
          <li>Shared duplicate prevention logic</li>
        </ul>
        
        <strong style={{ display: 'block', marginTop: '10px' }}>Usage in App Router components:</strong>
        <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', marginTop: '5px', overflow: 'auto' }}>
{`'use client'

import { useNotification } from '~/app/lib/use-notification'

export default function MyComponent() {
  const addNotification = useNotification()
  
  const handleSubmit = async () => {
    try {
      await saveData()
      addNotification('Data saved!', 'success')
    } catch (error) {
      addNotification('Failed to save data', 'error')
    }
  }
  
  return <button onClick={handleSubmit}>Save</button>
}`}
        </pre>
      </div>
    </div>
  )
}
