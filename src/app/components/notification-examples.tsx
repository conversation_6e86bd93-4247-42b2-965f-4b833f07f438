'use client'

import { useNotification } from '~/app/lib/use-notification'

/**
 * Example component showing how to use notifications in App Router
 * This can be imported and used in any App Router page
 */
export function NotificationExamples() {
  const addNotification = useNotification()

  return (
    <div
      style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px', margin: '20px 0' }}
    >
      <h3>App Router Notification Examples</h3>
      <p>Click any button to test notifications:</p>

      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginTop: '10px' }}>
        <button
          onClick={() => addNotification('Form saved successfully!', 'success')}
          style={{
            padding: '8px 16px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Success
        </button>

        <button
          onClick={() => addNotification('Something went wrong!', 'error')}
          style={{
            padding: '8px 16px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Error
        </button>

        <button
          onClick={() => addNotification('Here is some information', 'info')}
          style={{
            padding: '8px 16px',
            backgroundColor: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Info
        </button>
      </div>

      <div style={{ marginTop: '15px', fontSize: '14px', color: '#666' }}>
        <strong>Usage in your components:</strong>
        <pre
          style={{
            backgroundColor: '#f5f5f5',
            padding: '10px',
            borderRadius: '4px',
            marginTop: '5px',
            overflow: 'auto',
          }}
        >
          {`'use client'

import { useNotification } from '~/app/lib/use-notification'

export default function MyComponent() {
  const addNotification = useNotification()
  
  const handleSubmit = async () => {
    try {
      // Your async operation
      await saveData()
      addNotification('Data saved!', 'success')
    } catch (error) {
      addNotification('Failed to save data', 'error')
    }
  }
  
  return <button onClick={handleSubmit}>Save</button>
}`}
        </pre>
      </div>
    </div>
  )
}
