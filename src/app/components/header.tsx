'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

import { setLanguageCookie, setThemeCookie } from '~/app/actions/app-cookies'
import { updateUser } from '~/app/actions/user'
import Logo from '~/app/components/logo'
import MonitoringNavigation from '~/app/components/monitoring-navigation'
import { UserProps } from '~/app/types/user'
import { CustomLogo, LogoWrapper } from '~/components/layout/Header'
import CommonHeader from '~/components/layout/Header/Header'
import UserMenu from '~/components/layout/Header/UserMenu/UserMenu'
import { IPermissionsStore } from '~/store/models/account/workspace/permissions/PermissionsStore'
import { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import { IWorkspacesStoreArrItem } from '~/store/models/account/workspaces/WorkspacesStoreArrItem'
import { IAppSettings } from '~/store/models/appSettings/AppSettings'

interface IHeaderProps {
  appearanceCookie: string | null
  appSettings: IAppSettings
  appLanguage: string
  isImpersonating: boolean
  isLoaded: boolean
  isMobileSearchVisible: boolean
  permissions: IPermissionsStore
  setIsMobileSearchVisible?: (isVisible: boolean) => void
  switchWorkspace?: (uuid: string) => void
  unimpersonate?: () => void
  user: UserProps
  workspace: IWorkspaceStore
  workspaces: IWorkspacesStoreArrItem[]
}

export default function Header({
  appearanceCookie,
  appSettings,
  appLanguage,
  isImpersonating,
  isLoaded,
  isMobileSearchVisible,
  permissions,
  setIsMobileSearchVisible,
  switchWorkspace,
  unimpersonate,
  user,
  workspace,
  workspaces,
}: IHeaderProps) {
  const pathname = usePathname()
  const customLogo = user.logo_image_url

  const handleAppearanceChange = async (isDark: boolean) => {
    try {
      const result = await setThemeCookie(isDark.toString())
      if (!result.success) {
        console.error('Failed to save theme preference')
      }
    } catch (error) {
      console.error('Error saving theme:', error)
    }
  }

  const handleLocaleChange = async (lang: string) => {
    try {
      await updateUser({ app_language: lang })
      const result = await setLanguageCookie(lang)
      if (!result.success) {
        console.error('Failed to save language preference')
      }
    } catch (error) {
      console.error('Error saving locale:', error)
    }
  }

  // we needed to rewrite getStartPath from '~/helpers/auth'
  const getStartPath = (permissions: IPermissionsStore) => {
    if (permissions) {
      const permissionPathMap = {
        monitoring: '/',
        tvr_feed: '/crisis-communication',
        authors_database: '/authors',
        newsroom_blog: '/newsroom',
        newsroom_emailing: '/emailing',
      }

      for (const [key, path] of Object.entries(permissionPathMap)) {
        const permission = (permissions as IPermissionsStore)[key as keyof IPermissionsStore]
        // Type guard to check if permission has an id property
        if (
          permission &&
          typeof permission === 'object' &&
          'id' in permission &&
          permission.id > 0
        ) {
          return path
        }
      }
    }
    return '/user/no-workspace'
  }

  return (
    <>
      <CommonHeader
        isImpersonating={isImpersonating}
        isLoaded={isLoaded}
        isMobileSearchVisible={isMobileSearchVisible}
        pathname={pathname}
        setIsMobileSearchVisible={setIsMobileSearchVisible}
        workspace={workspace}
        logoComponent={
          <Link href={getStartPath(permissions)}>
            {customLogo ? (
              <CustomLogo url={customLogo} />
            ) : (
              <LogoWrapper>
                <Logo mono sitePath={appSettings.sitePath} />
              </LogoWrapper>
            )}
          </Link>
        }
        monitoringNavigationComponent={(close) => {
          return (
            <MonitoringNavigation
              appSettings={appSettings}
              // exportBasketId={} // TODO
              // exportList={} // TODO
              // feedMap={} // TODO
              // filterUrl={} // TODO
              isImpersonating={isImpersonating}
              isSalesman={user.is_salesman}
              mobileNavigation
              onClick={close}
              workspace={workspace}
              permissions={permissions}
            />
          )
        }}
        searchInputComponent={<></>}
        user={user}
        userMenuComponent={(closePopup) => {
          return (
            <UserMenu
              appearanceCookie={appearanceCookie}
              appearanceCookieSet={handleAppearanceChange}
              appSettings={appSettings}
              appLanguage={appLanguage}
              closePopup={closePopup}
              isImpersonating={isImpersonating}
              permissions={permissions}
              searchBarReset={() => null} // TODO Search
              setLocale={handleLocaleChange}
              switchWorkspace={switchWorkspace}
              unimpersonate={unimpersonate}
              user={user}
              workspace={workspace}
              workspaces={workspaces}
            />
          )
        }}
      />
    </>
  )
}
