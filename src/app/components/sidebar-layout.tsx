'use client'

import { PropsWithChildren } from 'react'
import { styled } from 'styled-components'

import { useBreakpoint } from '~/app/lib/use-breakpoint'

export const Layout = styled.div`
  @media (min-width: ${({ theme }) => theme.breakpoints[2]}) {
    padding-left: calc(48px + 215px);
  }
`

const StyledSidebar = styled.nav`
  position: fixed;
  top: 54px;
  bottom: 0;
  left: 48px;
  padding: 8px 0 24px;
  width: 215px;
  overflow-y: auto;

  @media (max-width: ${({ theme }) => theme.breakpoints[2]}) {
    display: none;
  }
`

export function Sidebar({ children }: PropsWithChildren) {
  const { isTablet } = useBreakpoint()

  if (!isTablet) {
    return <StyledSidebar>{children}</StyledSidebar>
  }

  return null
}

export const Content = styled.main`
  padding: 25px 12px 42px;

  @media (min-width: ${({ theme }) => theme.breakpoints[0]}) {
    padding-right: 24px;
    padding-left: 24px;
  }

  @media (min-width: ${({ theme }) => theme.breakpoints[2]}) {
    padding-left: 15px;
  }
`
