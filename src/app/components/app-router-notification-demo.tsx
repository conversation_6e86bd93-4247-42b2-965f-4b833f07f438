'use client'

import { useState, useCallback } from 'react'
import { setThemeCookie, setLanguageCookie } from '~/app/actions/app-cookies'
import ThemeSwitcherWithNotifications from './theme-switcher-with-notifications'

interface NotificationItem {
  id: string
  message: string
  type: 'success' | 'error' | 'info'
  timestamp: number
}

export default function AppRouterNotificationDemo() {
  const [notifications, setNotifications] = useState<NotificationItem[]>([])
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light')

  const addNotification = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    const notification: NotificationItem = {
      id: Date.now().toString(),
      message,
      type,
      timestamp: Date.now()
    }
    
    setNotifications(prev => [...prev, notification])
    
    // Auto-remove after 4 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id))
    }, 4000)
  }, [])

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const testCookieOperations = async () => {
    // Test theme cookie
    addNotification('Testing theme cookie...', 'info')
    
    try {
      const themeResult = await setThemeCookie('true')
      if (themeResult.success) {
        addNotification(themeResult.message || 'Theme cookie set successfully', 'success')
      } else {
        addNotification(themeResult.message || 'Failed to set theme cookie', 'error')
      }
    } catch (error) {
      addNotification('Error setting theme cookie', 'error')
    }

    // Test language cookie
    setTimeout(async () => {
      addNotification('Testing language cookie...', 'info')
      
      try {
        const langResult = await setLanguageCookie('en')
        if (langResult.success) {
          addNotification(langResult.message || 'Language cookie set successfully', 'success')
        } else {
          addNotification(langResult.message || 'Failed to set language cookie', 'error')
        }
      } catch (error) {
        addNotification('Error setting language cookie', 'error')
      }
    }, 1000)
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>App Router Cookie Notifications Demo</h2>
      
      {/* Notification Display */}
      <div style={{ position: 'fixed', top: '20px', right: '20px', zIndex: 1000 }}>
        {notifications.map(notification => (
          <div
            key={notification.id}
            style={{
              backgroundColor: 
                notification.type === 'success' ? '#4CAF50' :
                notification.type === 'error' ? '#f44336' : '#2196F3',
              color: 'white',
              padding: '12px 16px',
              borderRadius: '4px',
              marginBottom: '8px',
              maxWidth: '300px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
              cursor: 'pointer'
            }}
            onClick={() => removeNotification(notification.id)}
          >
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
              {notification.type.toUpperCase()}
            </div>
            <div>{notification.message}</div>
            <div style={{ fontSize: '12px', opacity: 0.8, marginTop: '4px' }}>
              Click to dismiss
            </div>
          </div>
        ))}
      </div>

      {/* Test Buttons */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={testCookieOperations}
          style={{
            padding: '12px 24px',
            backgroundColor: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Test Cookie Operations with Notifications
        </button>
      </div>

      {/* Theme Switcher */}
      <ThemeSwitcherWithNotifications
        currentTheme={currentTheme}
        onNotification={addNotification}
      />

      {/* Manual Notification Tests */}
      <div style={{ marginTop: '20px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
        <h3>Manual Notification Tests</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={() => addNotification('This is a success message!', 'success')}
            style={{ padding: '8px 16px', backgroundColor: '#4CAF50', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Success Notification
          </button>
          
          <button
            onClick={() => addNotification('This is an error message!', 'error')}
            style={{ padding: '8px 16px', backgroundColor: '#f44336', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Error Notification
          </button>
          
          <button
            onClick={() => addNotification('This is an info message!', 'info')}
            style={{ padding: '8px 16px', backgroundColor: '#2196F3', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Info Notification
          </button>
        </div>
      </div>

      {/* Usage Instructions */}
      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h4>Enhanced app-cookies.ts Features:</h4>
        <ul>
          <li><strong>setThemeCookie()</strong> - Returns success/failure with descriptive messages</li>
          <li><strong>setLanguageCookie()</strong> - Returns success/failure with descriptive messages</li>
          <li><strong>Enhanced error handling</strong> - Detailed error messages for better UX</li>
          <li><strong>Notification integration</strong> - Easy to connect with notification systems</li>
        </ul>
      </div>
    </div>
  )
}
