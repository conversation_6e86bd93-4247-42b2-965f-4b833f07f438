'use client'

import { useState, useTransition } from 'react'
import { setThemeCookie } from '~/app/actions/app-cookies'

interface ThemeSwitcherProps {
  currentTheme: 'light' | 'dark'
  onNotification?: (message: string, type: 'success' | 'error' | 'info') => void
}

export default function ThemeSwitcherWithNotifications({ 
  currentTheme, 
  onNotification 
}: ThemeSwitcherProps) {
  const [isPending, startTransition] = useTransition()
  const [theme, setTheme] = useState(currentTheme)

  const handleThemeChange = (newTheme: 'light' | 'dark') => {
    startTransition(async () => {
      try {
        // Show loading notification
        onNotification?.('Changing theme...', 'info')
        
        const result = await setThemeCookie(newTheme === 'dark' ? 'true' : 'false')
        
        if (result.success) {
          setTheme(newTheme)
          onNotification?.(result.message || 'Theme changed successfully', 'success')
          
          // Trigger page refresh to apply theme
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        } else {
          onNotification?.(result.message || 'Failed to change theme', 'error')
        }
      } catch (error) {
        console.error('Theme change error:', error)
        onNotification?.('An unexpected error occurred while changing theme', 'error')
      }
    })
  }

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px', margin: '20px 0' }}>
      <h3>Theme Switcher with Notifications</h3>
      <p>Current theme: <strong>{theme}</strong></p>
      
      <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
        <button
          onClick={() => handleThemeChange('light')}
          disabled={isPending || theme === 'light'}
          style={{
            padding: '8px 16px',
            backgroundColor: theme === 'light' ? '#ccc' : '#f0f0f0',
            color: '#333',
            border: '1px solid #ddd',
            borderRadius: '4px',
            cursor: theme === 'light' || isPending ? 'not-allowed' : 'pointer'
          }}
        >
          {isPending ? 'Changing...' : 'Light Theme'}
        </button>
        
        <button
          onClick={() => handleThemeChange('dark')}
          disabled={isPending || theme === 'dark'}
          style={{
            padding: '8px 16px',
            backgroundColor: theme === 'dark' ? '#555' : '#333',
            color: 'white',
            border: '1px solid #666',
            borderRadius: '4px',
            cursor: theme === 'dark' || isPending ? 'not-allowed' : 'pointer'
          }}
        >
          {isPending ? 'Changing...' : 'Dark Theme'}
        </button>
      </div>
      
      <div style={{ marginTop: '15px', fontSize: '14px', color: '#666' }}>
        <strong>How it works:</strong>
        <ul style={{ marginTop: '5px', paddingLeft: '20px' }}>
          <li>Uses enhanced <code>setThemeCookie()</code> server action</li>
          <li>Shows loading notification during theme change</li>
          <li>Shows success notification with theme name</li>
          <li>Shows error notification if cookie save fails</li>
          <li>Automatically refreshes page to apply new theme</li>
        </ul>
      </div>
    </div>
  )
}
