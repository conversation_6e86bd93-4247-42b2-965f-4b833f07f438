'use client'

// import events from '~/constants/gtm'
// import { pushEvent } from '~/helpers/gtm'
import { useNotifications } from '~/app/lib/notification-provider'
import Notifications from '~/components/layout/Notifications/Notifications'

export default function AppRouterNotifications() {
  const { notifications } = useNotifications()

  return (
    <Notifications
      list={notifications}
      pushErrorToTracking={(msg: string) => {
        // pushEvent(events.ERROR_MESSAGE_SHOWN, { error_text: msg })
      }}
    />
  )
}
