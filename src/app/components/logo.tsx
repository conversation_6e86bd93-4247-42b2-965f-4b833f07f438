import dynamic from 'next/dynamic'
import type { DefaultTheme } from 'styled-components'
import { styled } from 'styled-components'

const MediaboardLogo = dynamic(() => import('~/assets/mediaboard/logo.svg'))
const SitaLogo = dynamic(() => import('~/assets/sita/logo.svg'))

const ThemeWrapper = styled.div`
  width: inherit;

  &,
  svg {
    display: block;
  }

  svg {
    path {
      fill: ${({ theme }) => theme.header.logo};
    }
  }
`

const LogoWrapper = styled.div`
  svg {
    overflow: visible;
  }
`

const Logo = ({
  mono,
  sitePath,
  theme,
}: {
  mono?: boolean
  sitePath: string
  theme?: DefaultTheme
}) => {
  let SvgLogo

  switch (sitePath) {
    case 'sita':
      SvgLogo = SitaLogo
      break

    case 'mediaboard':
    default:
      SvgLogo = MediaboardLogo
  }

  if (mono) {
    return (
      <ThemeWrapper theme={theme}>
        <LogoWrapper>
          <SvgLogo />
        </LogoWrapper>
      </ThemeWrapper>
    )
  }

  return (
    <LogoWrapper>
      <SvgLogo />
    </LogoWrapper>
  )
}

export default Logo
