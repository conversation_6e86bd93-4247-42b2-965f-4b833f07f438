'use client'

import { useLingui } from '@lingui/react/macro'
import { styled } from 'styled-components'

import { Heading, Text } from '~/components/misc/Mntr'
import Box from '~/components/misc/Mntr/Box'
import MntrButton from '~/components/misc/MntrButton/MntrButton'

const StyledContent = styled.main`
  margin: 0 auto;
  padding: 15px;
  max-width: 600px;
  text-align: center;
`

export default function NotFoundContent() {
  const { t } = useLingui()

  return (
    <StyledContent>
      <Heading mb={4} mt={6} lineHeight="1">
        404 - {t`Woop woop woop woop, page not found`}
      </Heading>
      <Box mb={4}>
        <img
          alt={t`There's nothing here...`}
          src="/static/404.gif"
          style={{ display: 'block', margin: '0 auto', maxWidth: '66%' }}
        />
      </Box>
      <Text mb={4}>
        {t`We're sorry, but the requested page was not found. It is possible that the page was either removed or moved somewhere else. Please make sure you entered the correct URL address.`}
      </Text>
      <MntrButton bg="tertiary" href="/" label={t`Back to the main page`} rounded />
    </StyledContent>
  )
}
