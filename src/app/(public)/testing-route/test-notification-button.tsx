'use client'

import { useNotifications } from '~/app/lib/notification-provider'

export default function TestNotificationButton() {
  const { add } = useNotifications()

  return (
    <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
      <button 
        onClick={() => add('Success notification from App Router!', 'success')}
        style={{ padding: '10px 20px', backgroundColor: '#4CAF50', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
      >
        Add Success Notification
      </button>
      
      <button 
        onClick={() => add('Error notification from App Router!', 'error')}
        style={{ padding: '10px 20px', backgroundColor: '#f44336', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
      >
        Add Error Notification
      </button>
      
      <button 
        onClick={() => add('Info notification from App Router!', 'info')}
        style={{ padding: '10px 20px', backgroundColor: '#2196F3', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
      >
        Add Info Notification
      </button>
      
      <button 
        onClick={() => add('This is a duplicate', 'info')}
        style={{ padding: '10px 20px', backgroundColor: '#FF9800', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
      >
        Add Duplicate (should be blocked)
      </button>
    </div>
  )
}
