import { cookies, headers } from 'next/headers'
import Link from 'next/link'
import { NotificationExamples } from '~/app/components/notification-examples'
import { Content, Layout } from '~/app/components/sidebar-layout'
import TestNotificationButton from './test-notification-button'

export default async function Feed({ searchParams }: { searchParams: Promise<unknown> }) {
  const cookieStore = await cookies()
  const headersList = await headers()

  // Using sidebar layout components here just to test
  return (
    <Layout>
      <Content>
        <Link href="/help/search">
          Go to <code>/help/search</code>
        </Link>
        <h1>AR testing route</h1>

        <h2>Notification Test</h2>
        <TestNotificationButton />

        <NotificationExamples />
        <h2>MB cookies</h2>
        <pre>
          {JSON.stringify(
            cookieStore.getAll().filter(({ name }) => name.startsWith('monitora')),
            null,
            2,
          )}
        </pre>
        <h2>MB headers</h2>
        <pre>
          {JSON.stringify(
            Array.from(headersList).filter(([name]) => name.startsWith('x-mediaboard')),
            null,
            2,
          )}
        </pre>
        <h2>
          <code>searchParams</code>
        </h2>
        <pre>{JSON.stringify(await searchParams, null, 2)}</pre>
      </Content>
    </Layout>
  )
}
