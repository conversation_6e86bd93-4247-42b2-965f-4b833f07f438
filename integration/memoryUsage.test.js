import autocannon from 'autocannon'
import { log } from 'console'
import execa from 'execa'
import getPort from 'get-port'
import isDocker from 'is-docker'
import { hostname } from 'os'
import pTimeout from 'p-timeout'
import path from 'path'
import { retry, sleep } from '~/helpers/async'
import prettyBytes from '~/helpers/prettyBytes'

async function isServerReady(url) {
  try {
    await execa('curl', ['--output', '/dev/null', '--silent', '--head', '--fail', url])
  } catch (ignoreErr) {
    return pTimeout(
      retry(() => isServerReady(url)),
      30000,
    ).catch(() => {
      throw new Error(`${url} is not ready after 30 seconds`)
    })
  }

  return true
}

// https://stackoverflow.com/a/23760013
function percentageChange(a, b) {
  return (b / a) * 100 - 100
}

async function startServer() {
  const PORT = await getPort()

  const serverProcess = execa(
    'node',
    [
      '--expose-gc',
      '-r',
      require.resolve('./memoryUsage.preload.js'),
      '.next/standalone/server.js',
    ],
    {
      env: {
        // Jest sets NODE_ENV=test and loads environment variables the Next.js
        // way, which is fine by default, but we need to override some stuff.
        API_URL: 'https://media.mediaboard.com/api/v1',
        NODE_ENV: 'production',
        PORT,
      },
      stdio: [
        'pipe',
        'pipe',
        'pipe', // 'inherit' to see server stdout
        'ipc', // for sending messages to the server (see `serverProcess.send` below)
      ],
    },
  )

  const baseUrl = `http://${isDocker() ? hostname() : 'localhost'}:${PORT}`
  await isServerReady(baseUrl)
  return { baseUrl, serverProcess }
}

const maxExpectedPercentageChange = 10

jest.retryTimes(3, { logErrorsBeforeRetry: true })

test.concurrent.each([
  '/',
  '/help/search',
  '/testing-route', // TODO: remove after rendering login component in AuthorizedLayout
  //
])(
  `changes less than ${maxExpectedPercentageChange}% over some time on %s`,
  async (asPath) => {
    const { baseUrl, serverProcess } = await startServer()
    const url = `${baseUrl}${asPath}`
    const memoryUsageOverTime = []

    serverProcess.on('message', ({ memoryUsage: { heapUsed } }) => {
      memoryUsageOverTime.push(heapUsed)
    })

    await autocannon({ amount: 100, url })
    serverProcess.send('memory_test:gc')
    serverProcess.send('memory_test:usage')

    await autocannon({ amount: 100, url })
    serverProcess.send('memory_test:gc')
    serverProcess.send('memory_test:usage')

    await autocannon({ amount: 100, url })
    serverProcess.send('memory_test:gc')
    serverProcess.send('memory_test:usage')

    await sleep(1000)

    const { 0: lowest, length, [length - 1]: highest } = memoryUsageOverTime.sort()
    const change = parseFloat(percentageChange(lowest, highest).toFixed(2))

    log(`
  ${path.basename(__filename)}
    url: ${url}
    percentage change: ${change}%
    memory usage diff: ${prettyBytes(highest - lowest)}
  `)

    expect(Math.abs(change)).toBeLessThan(maxExpectedPercentageChange)
    serverProcess.cancel()
  },
  30000, // 30s
)
